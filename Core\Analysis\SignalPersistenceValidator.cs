using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models.Analysis;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Original Phase 2: Signal Persistence Validator
    /// Validates that signals persist across multiple bars to reduce false breakouts and noise
    /// Priority 1 feature for immediate trading performance improvement
    /// </summary>
    public class SignalPersistenceValidator
    {
        private readonly Action<string> _logAction;
        private readonly SignalPersistenceConfig _config;
        private readonly Queue<HistoricalSignalData> _signalHistory;
        private readonly object _lockObject = new object();
        
        // Statistics tracking
        private int _totalValidations = 0;
        private int _validatedSignals = 0;
        private int _rejectedSignals = 0;
        
        public SignalPersistenceValidator(SignalPersistenceConfig config = null, Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _config = config ?? CreateDefaultConfig();
            _signalHistory = new Queue<HistoricalSignalData>();
        }
        
        /// <summary>
        /// Validate signal persistence across multiple bars
        /// </summary>
        public SignalPersistenceValidationResult ValidateSignalPersistence(
            TradingSignal currentSignal, 
            MarketDataPoint currentBar,
            List<MarketDataPoint> recentBars)
        {
            lock (_lockObject)
            {
                var startTime = DateTime.UtcNow;
                _totalValidations++;
                
                try
                {
                    _logAction("🔄 SIGNAL PERSISTENCE VALIDATION:");
                    _logAction($"  • Current signal: {currentSignal.Type} | Confidence: {currentSignal.Confidence:P1}");
                    _logAction($"  • Signal history count: {_signalHistory.Count}");
                    
                    // Add current signal to history
                    var currentSignalData = new HistoricalSignalData
                    {
                        Timestamp = currentBar.Timestamp,
                        Type = currentSignal.Type,
                        Confidence = currentSignal.Confidence,
                        Strength = currentSignal.Strength,
                        Quality = currentSignal.Quality,
                        Price = currentBar.Close,
                        PrimaryReason = currentSignal.PrimaryReason,
                        BarIndex = recentBars.Count - 1,
                        WasValidated = false
                    };
                    
                    _signalHistory.Enqueue(currentSignalData);
                    
                    // Clean old signals outside the persistence window
                    CleanOldSignals(currentBar.Timestamp);
                    
                    // Skip validation if signal is None or Exit
                    if (currentSignal.Type == SignalType.None || currentSignal.Type == SignalType.Exit)
                    {
                        return CreateInvalidResult("Signal type is None or Exit", currentSignalData);
                    }
                    
                    // Get recent signals of the same type
                    var recentSimilarSignals = GetRecentSimilarSignals(currentSignal.Type, currentBar.Timestamp);
                    
                    _logAction($"  • Recent similar signals: {recentSimilarSignals.Count}");
                    
                    // Check if we have enough signals for persistence validation
                    if (recentSimilarSignals.Count < _config.MinimumPersistenceBars)
                    {
                        var reason = $"Insufficient signal history: {recentSimilarSignals.Count} < {_config.MinimumPersistenceBars} required";
                        _logAction($"  • {reason}");
                        return CreateInvalidResult(reason, currentSignalData);
                    }
                    
                    // Validate direction consistency
                    var directionConsistency = ValidateDirectionConsistency(recentSimilarSignals, currentSignal.Type);
                    if (_config.RequireDirectionConsistency && !directionConsistency.IsConsistent)
                    {
                        _rejectedSignals++;
                        var reason = $"Direction inconsistency: {directionConsistency.Reason}";
                        _logAction($"  • ❌ {reason}");
                        return CreateInvalidResult(reason, currentSignalData);
                    }
                    
                    // Validate confidence stability
                    var confidenceStability = ValidateConfidenceStability(recentSimilarSignals);
                    if (confidenceStability.Stability < _config.MinimumConfidenceStability)
                    {
                        _rejectedSignals++;
                        var reason = $"Confidence instability: {confidenceStability.Stability:P1} < {_config.MinimumConfidenceStability:P1}";
                        _logAction($"  • ❌ {reason}");
                        return CreateInvalidResult(reason, currentSignalData);
                    }
                    
                    // Validate strength consistency
                    var strengthConsistency = ValidateStrengthConsistency(recentSimilarSignals);
                    if (_config.RequireStrengthConsistency && strengthConsistency.Stability < _config.MinimumStrengthStability)
                    {
                        _rejectedSignals++;
                        var reason = $"Strength inconsistency: {strengthConsistency.Stability:P1} < {_config.MinimumStrengthStability:P1}";
                        _logAction($"  • ❌ {reason}");
                        return CreateInvalidResult(reason, currentSignalData);
                    }
                    
                    // Calculate persistence duration
                    var persistenceDuration = currentBar.Timestamp - recentSimilarSignals.First().Timestamp;
                    
                    // Mark current signal as validated
                    currentSignalData.WasValidated = true;
                    _validatedSignals++;
                    
                    // Create successful validation result
                    var result = new SignalPersistenceValidationResult
                    {
                        IsValid = true,
                        ConsistentBars = recentSimilarSignals.Count,
                        RequiredBars = _config.MinimumPersistenceBars,
                        AverageConfidence = recentSimilarSignals.Average(s => s.Confidence),
                        ConfidenceStability = confidenceStability.Stability,
                        ValidationReason = $"Signal persisted across {recentSimilarSignals.Count} bars with {confidenceStability.Stability:P1} confidence stability",
                        HasDirectionConsistency = directionConsistency.IsConsistent,
                        HasStrengthConsistency = strengthConsistency.Stability >= _config.MinimumStrengthStability,
                        PersistenceDuration = persistenceDuration,
                        SupportingSignals = recentSimilarSignals.ToList()
                    };
                    
                    // Add persistence factors
                    result.PersistenceFactors.Add($"Direction consistency: {directionConsistency.IsConsistent}");
                    result.PersistenceFactors.Add($"Confidence stability: {confidenceStability.Stability:P1}");
                    result.PersistenceFactors.Add($"Strength consistency: {strengthConsistency.Stability:P1}");
                    result.PersistenceFactors.Add($"Persistence duration: {persistenceDuration.TotalMinutes:F1} minutes");
                    
                    _logAction($"  • ✅ PERSISTENCE VALIDATED: {recentSimilarSignals.Count} bars, {confidenceStability.Stability:P1} stability");
                    
                    return result;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Signal persistence validation error: {ex.Message}");
                    return CreateInvalidResult($"Validation error: {ex.Message}", null);
                }
            }
        }
        
        /// <summary>
        /// Get recent signals of the same type within the persistence window
        /// </summary>
        private List<HistoricalSignalData> GetRecentSimilarSignals(SignalType signalType, DateTime currentTime)
        {
            var cutoffTime = currentTime - _config.MaximumPersistenceWindow;
            
            return _signalHistory
                .Where(s => s.Type == signalType && 
                           s.Timestamp >= cutoffTime && 
                           s.Timestamp <= currentTime)
                .OrderBy(s => s.Timestamp)
                .ToList();
        }
        
        /// <summary>
        /// Validate direction consistency across signals
        /// </summary>
        private (bool IsConsistent, string Reason) ValidateDirectionConsistency(
            List<HistoricalSignalData> signals, SignalType expectedType)
        {
            if (!signals.Any())
                return (false, "No signals to validate");
                
            var inconsistentSignals = signals.Where(s => s.Type != expectedType).ToList();
            
            if (inconsistentSignals.Any())
            {
                return (false, $"{inconsistentSignals.Count} signals have different direction");
            }
            
            return (true, "All signals have consistent direction");
        }
        
        /// <summary>
        /// Validate confidence stability across signals
        /// </summary>
        private (decimal Stability, decimal AverageConfidence) ValidateConfidenceStability(
            List<HistoricalSignalData> signals)
        {
            if (!signals.Any())
                return (0m, 0m);
                
            var confidences = signals.Select(s => s.Confidence).ToList();
            var averageConfidence = confidences.Average();
            var maxDeviation = confidences.Max(c => Math.Abs(c - averageConfidence));
            
            // Stability = 1 - (max deviation / average confidence)
            var stability = Math.Max(0m, 1m - (maxDeviation / Math.Max(averageConfidence, 0.01m)));
            
            return (stability, averageConfidence);
        }
        
        /// <summary>
        /// Validate strength consistency across signals
        /// </summary>
        private (decimal Stability, decimal AverageStrength) ValidateStrengthConsistency(
            List<HistoricalSignalData> signals)
        {
            if (!signals.Any())
                return (0m, 0m);
                
            var strengths = signals.Select(s => s.Strength).ToList();
            var averageStrength = strengths.Average();
            var maxDeviation = strengths.Max(s => Math.Abs(s - averageStrength));
            
            // Stability = 1 - (max deviation / average strength)
            var stability = Math.Max(0m, 1m - (maxDeviation / Math.Max(averageStrength, 0.01m)));
            
            return (stability, averageStrength);
        }
        
        /// <summary>
        /// Clean old signals outside the persistence window
        /// </summary>
        private void CleanOldSignals(DateTime currentTime)
        {
            var cutoffTime = currentTime - _config.MaximumPersistenceWindow;
            
            while (_signalHistory.Count > 0 && _signalHistory.Peek().Timestamp < cutoffTime)
            {
                _signalHistory.Dequeue();
            }
            
            // Also limit total history size
            while (_signalHistory.Count > 100)
            {
                _signalHistory.Dequeue();
            }
        }
        
        /// <summary>
        /// Create invalid validation result
        /// </summary>
        private SignalPersistenceValidationResult CreateInvalidResult(string reason, HistoricalSignalData currentSignal)
        {
            return new SignalPersistenceValidationResult
            {
                IsValid = false,
                ConsistentBars = 0,
                RequiredBars = _config.MinimumPersistenceBars,
                AverageConfidence = 0m,
                ConfidenceStability = 0m,
                ValidationReason = reason,
                HasDirectionConsistency = false,
                HasStrengthConsistency = false,
                PersistenceDuration = TimeSpan.Zero,
                SupportingSignals = currentSignal != null ? new List<HistoricalSignalData> { currentSignal } : new List<HistoricalSignalData>()
            };
        }
        
        /// <summary>
        /// Create default configuration
        /// </summary>
        private SignalPersistenceConfig CreateDefaultConfig()
        {
            return new SignalPersistenceConfig
            {
                MinimumPersistenceBars = 2,
                MaximumPersistenceBars = 5,
                MinimumConfidenceStability = 0.8m,
                MinimumStrengthStability = 0.7m,
                MaximumPersistenceWindow = TimeSpan.FromMinutes(15),
                RequireDirectionConsistency = true,
                RequireStrengthConsistency = true,
                ConfidenceDecayTolerance = 0.1m
            };
        }
        
        /// <summary>
        /// Get validation statistics
        /// </summary>
        public OriginalPhase2Statistics GetStatistics()
        {
            lock (_lockObject)
            {
                var validationRate = _totalValidations > 0 ? (decimal)_validatedSignals / _totalValidations : 0m;
                
                return new OriginalPhase2Statistics
                {
                    TotalSignalsPersistenceChecked = _totalValidations,
                    SignalsPersistenceValidated = _validatedSignals,
                    SignalsPersistenceRejected = _rejectedSignals,
                    PersistenceValidationRate = validationRate,
                    AveragePersistenceBars = _signalHistory.Count > 0 ? _signalHistory.Count : 0m,
                    LastValidation = DateTime.UtcNow
                };
            }
        }
    }
}
