using System;
using System.Collections.Generic;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Models.Analysis
{
    /// <summary>
    /// Phase 2.2: Microstructure component enumeration
    /// </summary>
    public enum MicrostructureComponent
    {
        Volume,
        Delta,
        Volatility,
        CVDAlignment,
        OptimalTime
    }

    /// <summary>
    /// Component health status enumeration
    /// </summary>
    public enum ComponentHealth
    {
        Unknown,
        Critical,
        Degraded,
        Recovering,
        Healthy
    }

    /// <summary>
    /// Health trend direction enumeration
    /// </summary>
    public enum HealthTrendDirection
    {
        Unknown,
        Insufficient_Data,
        Declining,
        Stable,
        Improving
    }

    /// <summary>
    /// Microstructure filter quality enumeration
    /// </summary>
    public enum MicrostructureFilterQuality
    {
        VeryPoor,
        Poor,
        Fair,
        Good,
        Excellent
    }

    /// <summary>
    /// Microstructure component scores with health information
    /// </summary>
    public class MicrostructureComponentScores
    {
        public decimal VolumeScore { get; set; }
        public ComponentHealth VolumeHealth { get; set; }
        
        public decimal DeltaScore { get; set; }
        public ComponentHealth DeltaHealth { get; set; }
        
        public decimal VolatilityScore { get; set; }
        public ComponentHealth VolatilityHealth { get; set; }
        
        public decimal CVDAlignmentScore { get; set; }
        public ComponentHealth CVDAlignmentHealth { get; set; }
        
        public decimal OptimalTimeScore { get; set; }
        public ComponentHealth OptimalTimeHealth { get; set; }
    }

    /// <summary>
    /// Microstructure filter result
    /// </summary>
    public class MicrostructureFilterResult
    {
        public DateTime Timestamp { get; set; }
        public SignalType SignalType { get; set; }
        public decimal OverallScore { get; set; }
        public MicrostructureComponentScores ComponentScores { get; set; }
        public bool PassesFilter { get; set; }
        public MicrostructureFilterQuality FilterQuality { get; set; }
        public string FilterReason { get; set; }
        public bool HasHealthWarning { get; set; }
        public TimeSpan ProcessingTime { get; set; }
    }

    /// <summary>
    /// Adaptive microstructure thresholds
    /// </summary>
    public class MicrostructureThresholds
    {
        public decimal VolumeConfidence { get; set; }
        public decimal DeltaConfidence { get; set; }
        public decimal VolatilityConfidence { get; set; }
        public decimal CVDNeutralZone { get; set; }
        public decimal OverallFilterThreshold { get; set; }
        
        public MicrostructureThresholds Clone()
        {
            return new MicrostructureThresholds
            {
                VolumeConfidence = this.VolumeConfidence,
                DeltaConfidence = this.DeltaConfidence,
                VolatilityConfidence = this.VolatilityConfidence,
                CVDNeutralZone = this.CVDNeutralZone,
                OverallFilterThreshold = this.OverallFilterThreshold
            };
        }
    }

    /// <summary>
    /// Component weights for scoring
    /// </summary>
    public class MicrostructureWeights
    {
        public decimal Volume { get; set; }
        public decimal Delta { get; set; }
        public decimal Volatility { get; set; }
        public decimal CVDAlignment { get; set; }
        public decimal OptimalTime { get; set; }
    }

    /// <summary>
    /// Component health tracker
    /// </summary>
    public class ComponentHealthTracker
    {
        public MicrostructureComponent Component { get; set; }
        public ComponentHealth CurrentHealth { get; set; }
        public DateTime LastUpdate { get; set; }
        public Queue<decimal> ScoreHistory { get; set; } = new Queue<decimal>();
        public int ConsecutiveFailures { get; set; }
        public int ConsecutiveSuccesses { get; set; }
        public int TotalUpdates { get; set; }
        public int FailureCount { get; set; }
    }

    /// <summary>
    /// Microstructure health status
    /// </summary>
    public class MicrostructureHealthStatus
    {
        public DateTime Timestamp { get; set; }
        public ComponentHealth VolumeHealth { get; set; }
        public ComponentHealth DeltaHealth { get; set; }
        public ComponentHealth VolatilityHealth { get; set; }
        public ComponentHealth CVDAlignmentHealth { get; set; }
        public ComponentHealth OptimalTimeHealth { get; set; }
        public ComponentHealth OverallHealth { get; set; }
        public Dictionary<MicrostructureComponent, ComponentStatistics> ComponentStatistics { get; set; }
    }

    /// <summary>
    /// Health snapshot for history tracking
    /// </summary>
    public class MicrostructureHealthSnapshot
    {
        public DateTime Timestamp { get; set; }
        public ComponentHealth VolumeHealth { get; set; }
        public ComponentHealth DeltaHealth { get; set; }
        public ComponentHealth VolatilityHealth { get; set; }
        public ComponentHealth CVDAlignmentHealth { get; set; }
        public ComponentHealth OptimalTimeHealth { get; set; }
        public string MarketCondition { get; set; }
        public string TradingSession { get; set; }
    }

    /// <summary>
    /// Component statistics
    /// </summary>
    public class ComponentStatistics
    {
        public int TotalUpdates { get; set; }
        public int FailureCount { get; set; }
        public decimal FailureRate { get; set; }
        public decimal AverageScore { get; set; }
        public int ConsecutiveFailures { get; set; }
        public int ConsecutiveSuccesses { get; set; }
        public DateTime LastUpdate { get; set; }
    }

    /// <summary>
    /// Health trend analysis
    /// </summary>
    public class MicrostructureHealthTrend
    {
        public MicrostructureComponent Component { get; set; }
        public HealthTrendDirection Trend { get; set; }
        public decimal TrendStrength { get; set; }
        public decimal Volatility { get; set; }
        public decimal RecentAverage { get; set; }
        public int WindowSize { get; set; }
    }

    /// <summary>
    /// Microstructure filter statistics
    /// </summary>
    public class MicrostructureFilterStatistics
    {
        public int TotalFiltersApplied { get; set; }
        public int FiltersPassedCount { get; set; }
        public decimal PassRate => TotalFiltersApplied > 0 ? (decimal)FiltersPassedCount / TotalFiltersApplied : 0m;
        public decimal AverageFilterScore { get; set; }
        public DateTime LastFilterTime { get; set; }
        public Dictionary<MicrostructureFilterQuality, int> QualityDistribution { get; set; } = new Dictionary<MicrostructureFilterQuality, int>();
        
        public MicrostructureFilterStatistics Clone()
        {
            return new MicrostructureFilterStatistics
            {
                TotalFiltersApplied = this.TotalFiltersApplied,
                FiltersPassedCount = this.FiltersPassedCount,
                AverageFilterScore = this.AverageFilterScore,
                LastFilterTime = this.LastFilterTime,
                QualityDistribution = new Dictionary<MicrostructureFilterQuality, int>(this.QualityDistribution)
            };
        }
    }
}
