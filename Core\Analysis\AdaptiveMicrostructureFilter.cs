using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models.Analysis;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Phase 2.2: Advanced Microstructure Filtering with adaptive thresholds and component health monitoring
    /// Replaces simple pass/fail filtering with weighted scoring system
    /// </summary>
    public class AdaptiveMicrostructureFilter
    {
        private readonly Action<string> _logAction;
        private readonly MicrostructureHealthMonitor _healthMonitor;
        private readonly object _lockObject = new object();
        
        // Adaptive thresholds for each component
        private MicrostructureThresholds _currentThresholds;
        private MicrostructureThresholds _baseThresholds;
        private DateTime _lastThresholdUpdate;
        
        // Component weights for scoring
        private MicrostructureWeights _componentWeights;
        
        // Performance tracking
        private readonly Queue<MicrostructureFilterResult> _filterHistory;
        private MicrostructureFilterStatistics _statistics;
        
        // Configuration
        private const int MAX_FILTER_HISTORY = 100;
        private const int THRESHOLD_UPDATE_INTERVAL_MINUTES = 15;
        private const decimal THRESHOLD_ADJUSTMENT_FACTOR = 0.1m;

        public AdaptiveMicrostructureFilter(Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _healthMonitor = new MicrostructureHealthMonitor(logAction);
            _filterHistory = new Queue<MicrostructureFilterResult>();
            _statistics = new MicrostructureFilterStatistics();
            
            InitializeThresholds();
            InitializeWeights();
            
            _lastThresholdUpdate = DateTime.UtcNow;
        }

        /// <summary>
        /// Phase 2.2: Advanced microstructure filtering with weighted scoring
        /// </summary>
        public MicrostructureFilterResult ApplyFilter(
            SynthesizedSignal signal, 
            MarketContext context, 
            AnalysisState analysisState,
            SignalSynthesisConfig config)
        {
            lock (_lockObject)
            {
                try
                {
                    var filterStartTime = DateTime.UtcNow;
                    
                    // Phase 2.2: Calculate weighted component scores
                    var componentScores = CalculateComponentScores(signal, context, analysisState, config);
                    
                    // Phase 2.2: Update component health monitoring
                    _healthMonitor.UpdateComponentHealth(componentScores, context);
                    
                    // Phase 2.2: Calculate overall filter score
                    var overallScore = CalculateOverallFilterScore(componentScores);
                    
                    // Phase 2.2: Determine filter result based on adaptive thresholds
                    var filterResult = DetermineFilterResult(overallScore, componentScores, signal);
                    
                    // Phase 2.2: Apply health-based adjustments
                    ApplyHealthBasedAdjustments(filterResult, componentScores);
                    
                    // Update filter history and statistics
                    UpdateFilterHistory(filterResult);
                    
                    // Phase 2.2: Adaptive threshold adjustment
                    if (ShouldUpdateThresholds())
                    {
                        UpdateAdaptiveThresholds();
                    }
                    
                    // Log detailed filter analysis
                    LogFilterAnalysis(filterResult, componentScores);
                    
                    return filterResult;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ ERROR in AdaptiveMicrostructureFilter: {ex.Message}");
                    
                    // Fallback to basic filtering
                    return CreateFallbackFilterResult(signal, context);
                }
            }
        }

        /// <summary>
        /// Get current component health status
        /// </summary>
        public MicrostructureHealthStatus GetHealthStatus()
        {
            return _healthMonitor.GetCurrentHealthStatus();
        }

        /// <summary>
        /// Get filter performance statistics
        /// </summary>
        public MicrostructureFilterStatistics GetStatistics()
        {
            lock (_lockObject)
            {
                return _statistics.Clone();
            }
        }

        /// <summary>
        /// Phase 2.2: Calculate weighted scores for each microstructure component
        /// </summary>
        private MicrostructureComponentScores CalculateComponentScores(
            SynthesizedSignal signal, 
            MarketContext context, 
            AnalysisState analysisState,
            SignalSynthesisConfig config)
        {
            var scores = new MicrostructureComponentScores();
            
            // 1. Volume Confidence Score
            var volumeConfidence = analysisState?.Volume?.VolumeConfidence ?? 0.5m;
            scores.VolumeScore = CalculateVolumeScore(volumeConfidence, context);
            scores.VolumeHealth = _healthMonitor.GetComponentHealth(MicrostructureComponent.Volume);
            
            // 2. Delta Confidence Score
            var deltaConfidence = analysisState?.DeltaFlow?.DeltaConfidence ?? 0.5m;
            scores.DeltaScore = CalculateDeltaScore(deltaConfidence, context, signal);
            scores.DeltaHealth = _healthMonitor.GetComponentHealth(MicrostructureComponent.Delta);
            
            // 3. Volatility Confidence Score
            var volatilityConfidence = analysisState?.Volatility?.VolatilityConfidence ?? 0.5m;
            scores.VolatilityScore = CalculateVolatilityScore(volatilityConfidence, context);
            scores.VolatilityHealth = _healthMonitor.GetComponentHealth(MicrostructureComponent.Volatility);
            
            // 4. CVD Alignment Score
            scores.CVDAlignmentScore = CalculateCVDAlignmentScore(context, signal);
            scores.CVDAlignmentHealth = _healthMonitor.GetComponentHealth(MicrostructureComponent.CVDAlignment);
            
            // 5. Optimal Trading Time Score
            scores.OptimalTimeScore = CalculateOptimalTimeScore(context);
            scores.OptimalTimeHealth = _healthMonitor.GetComponentHealth(MicrostructureComponent.OptimalTime);
            
            return scores;
        }

        /// <summary>
        /// Calculate volume component score with adaptive thresholds
        /// </summary>
        private decimal CalculateVolumeScore(decimal volumeConfidence, MarketContext context)
        {
            var threshold = _currentThresholds.VolumeConfidence;
            var baseScore = Math.Max(0m, (volumeConfidence - threshold) / (1m - threshold));
            
            // Market condition adjustments
            var marketAdjustment = context.VolatilityRegime switch
            {
                VolatilityRegime.VeryLow => -0.1m,  // Penalize low volatility
                VolatilityRegime.Normal => 0m,     // No adjustment
                VolatilityRegime.High => 0.1m,     // Boost for high volatility
                VolatilityRegime.VeryHigh => -0.05m, // Slight penalty for extreme volatility
                _ => 0m
            };
            
            return Math.Max(0m, Math.Min(1m, baseScore + marketAdjustment));
        }

        /// <summary>
        /// Calculate delta component score with signal alignment
        /// </summary>
        private decimal CalculateDeltaScore(decimal deltaConfidence, MarketContext context, SynthesizedSignal signal)
        {
            var threshold = _currentThresholds.DeltaConfidence;
            var baseScore = Math.Max(0m, (deltaConfidence - threshold) / (1m - threshold));
            
            // Delta imbalance alignment bonus
            var deltaDirection = Math.Sign(context.DeltaImbalance);
            var signalDirection = signal.Type == SignalType.Long ? 1 : signal.Type == SignalType.Short ? -1 : 0;
            
            var alignmentBonus = deltaDirection == signalDirection ? 0.15m : 0m;
            
            return Math.Max(0m, Math.Min(1m, baseScore + alignmentBonus));
        }

        /// <summary>
        /// Calculate volatility component score
        /// </summary>
        private decimal CalculateVolatilityScore(decimal volatilityConfidence, MarketContext context)
        {
            var threshold = _currentThresholds.VolatilityConfidence;
            var baseScore = Math.Max(0m, (volatilityConfidence - threshold) / (1m - threshold));
            
            // Volatility regime appropriateness
            var regimeScore = context.VolatilityRegime switch
            {
                VolatilityRegime.VeryLow => 0.6m,    // Suboptimal for volume strategies
                VolatilityRegime.Low => 0.8m,       // Acceptable
                VolatilityRegime.Normal => 1.0m,    // Optimal
                VolatilityRegime.High => 0.9m,      // Good but elevated risk
                VolatilityRegime.VeryHigh => 0.7m,  // Higher risk
                VolatilityRegime.Extreme => 0.4m,   // Very risky
                _ => 0.8m
            };
            
            return Math.Max(0m, Math.Min(1m, baseScore * regimeScore));
        }

        /// <summary>
        /// Calculate CVD alignment score with enhanced adaptive tolerance and momentum analysis
        /// </summary>
        private decimal CalculateCVDAlignmentScore(MarketContext context, SynthesizedSignal signal)
        {
            // Enhanced CVD analysis with proper scaling and momentum
            var cvdValue = context.CVDTrend;
            var signalDirection = signal.Type == SignalType.Long ? 1 : signal.Type == SignalType.Short ? -1 : 0;

            // Calculate CVD momentum and strength with proper scaling
            var cvdMagnitude = Math.Abs(cvdValue);
            var cvdDirection = Math.Sign(cvdValue);

            // Dynamic scaling based on market conditions and volatility
            var volatilityMultiplier = context.VolatilityRegime == VolatilityRegime.VeryLow ? 0.5m :
                                     context.VolatilityRegime == VolatilityRegime.Low ? 0.7m :
                                     context.VolatilityRegime == VolatilityRegime.Medium ? 1.0m :
                                     context.VolatilityRegime == VolatilityRegime.High ? 1.3m : 1.5m;

            // Adaptive CVD scaling based on typical values for this symbol
            var adaptiveScaling = Math.Min(1m, cvdMagnitude / (100000m * volatilityMultiplier));

            // Perfect directional alignment
            if (cvdDirection == signalDirection && cvdDirection != 0)
            {
                var alignmentStrength = Math.Min(1m, adaptiveScaling);
                var baseScore = 0.75m + (alignmentStrength * 0.25m); // 0.75 to 1.0 range

                // Boost score for strong CVD momentum
                if (cvdMagnitude > 200000m * volatilityMultiplier)
                {
                    baseScore = Math.Min(1m, baseScore * 1.1m); // 10% boost for strong momentum
                }

                return baseScore;
            }

            // Neutral CVD with adaptive tolerance
            var neutralZone = _currentThresholds.CVDNeutralZone * volatilityMultiplier;
            if (cvdMagnitude < neutralZone)
            {
                return 0.65m; // Neutral score - more lenient than before
            }
            
            // Divergence - but allow some tolerance based on market conditions
            // In volatile markets, CVD divergence is more acceptable
            var divergenceTolerance = volatilityMultiplier > 1.0m ? 0.5m : 0.3m;

            // Weak divergence (opposite direction but low magnitude)
            if (cvdMagnitude < 50000m * volatilityMultiplier)
            {
                return 0.55m; // Weak divergence - acceptable in low volatility
            }

            // Strong divergence - penalize but don't eliminate
            var divergencePenalty = Math.Min(0.4m, adaptiveScaling * 0.4m);
            return Math.Max(0.2m, 0.5m - divergencePenalty); // Minimum 0.2, maximum 0.5
        }

        /// <summary>
        /// Calculate optimal trading time score with session weighting
        /// </summary>
        private decimal CalculateOptimalTimeScore(MarketContext context)
        {
            var baseScore = context.IsOptimalTradingTime ? 1.0m : 0.3m;
            
            // Session-based adjustments
            var sessionBonus = context.CurrentSession switch
            {
                TradingSession.Overlap_EuropeanUS => 0.2m,      // Highest activity
                TradingSession.Overlap_AsianEuropean => 0.1m,   // Good activity
                TradingSession.European => 0.1m,               // Major session
                TradingSession.US => 0.1m,                     // Major session
                TradingSession.Asian => 0.05m,                 // Lower activity
                TradingSession.Crypto24H => 0.0m,              // No specific bonus
                _ => 0.0m
            };
            
            return Math.Max(0m, Math.Min(1m, baseScore + sessionBonus));
        }

        /// <summary>
        /// Calculate overall weighted filter score
        /// </summary>
        private decimal CalculateOverallFilterScore(MicrostructureComponentScores scores)
        {
            var weightedSum = 
                (scores.VolumeScore * _componentWeights.Volume) +
                (scores.DeltaScore * _componentWeights.Delta) +
                (scores.VolatilityScore * _componentWeights.Volatility) +
                (scores.CVDAlignmentScore * _componentWeights.CVDAlignment) +
                (scores.OptimalTimeScore * _componentWeights.OptimalTime);
            
            var totalWeight = 
                _componentWeights.Volume +
                _componentWeights.Delta +
                _componentWeights.Volatility +
                _componentWeights.CVDAlignment +
                _componentWeights.OptimalTime;
            
            return totalWeight > 0 ? weightedSum / totalWeight : 0.5m;
        }

        /// <summary>
        /// Determine filter result based on score and thresholds
        /// </summary>
        private MicrostructureFilterResult DetermineFilterResult(
            decimal overallScore, 
            MicrostructureComponentScores componentScores,
            SynthesizedSignal signal)
        {
            var result = new MicrostructureFilterResult
            {
                Timestamp = DateTime.UtcNow,
                SignalType = signal.Type,
                OverallScore = overallScore,
                ComponentScores = componentScores,
                PassesFilter = overallScore >= _currentThresholds.OverallFilterThreshold,
                FilterReason = DetermineFilterReason(overallScore, componentScores)
            };
            
            // Determine filter quality
            result.FilterQuality = overallScore switch
            {
                >= 0.9m => MicrostructureFilterQuality.Excellent,
                >= 0.8m => MicrostructureFilterQuality.Good,
                >= 0.6m => MicrostructureFilterQuality.Fair,
                >= 0.4m => MicrostructureFilterQuality.Poor,
                _ => MicrostructureFilterQuality.VeryPoor
            };
            
            return result;
        }

        /// <summary>
        /// Apply health-based adjustments to filter result
        /// </summary>
        private void ApplyHealthBasedAdjustments(
            MicrostructureFilterResult result, 
            MicrostructureComponentScores scores)
        {
            var healthStatus = _healthMonitor.GetCurrentHealthStatus();
            
            // If any critical component is unhealthy, apply penalties
            if (healthStatus.VolumeHealth == ComponentHealth.Critical ||
                healthStatus.DeltaHealth == ComponentHealth.Critical)
            {
                result.OverallScore *= 0.8m; // 20% penalty for critical component failure
                result.FilterReason += " | Critical component failure detected";
                result.HasHealthWarning = true;
            }
            else if (healthStatus.OverallHealth == ComponentHealth.Degraded)
            {
                result.OverallScore *= 0.9m; // 10% penalty for degraded performance
                result.FilterReason += " | Component performance degraded";
                result.HasHealthWarning = true;
            }
            
            // Re-evaluate pass/fail after health adjustments
            result.PassesFilter = result.OverallScore >= _currentThresholds.OverallFilterThreshold;
        }

        /// <summary>
        /// Determine detailed filter reason
        /// </summary>
        private string DetermineFilterReason(decimal overallScore, MicrostructureComponentScores scores)
        {
            var reasons = new List<string>();

            if (scores.VolumeScore < 0.5m) reasons.Add("Volume confidence low");
            if (scores.DeltaScore < 0.5m) reasons.Add("Delta confidence low");
            if (scores.VolatilityScore < 0.5m) reasons.Add("Volatility confidence low");
            if (scores.CVDAlignmentScore < 0.5m) reasons.Add("CVD alignment poor");
            if (scores.OptimalTimeScore < 0.5m) reasons.Add("Non-optimal trading time");

            if (reasons.Count == 0)
            {
                return overallScore >= _currentThresholds.OverallFilterThreshold ?
                    "All microstructure components passed" :
                    "Overall score below threshold";
            }

            return string.Join(", ", reasons);
        }

        /// <summary>
        /// Update filter history and statistics
        /// </summary>
        private void UpdateFilterHistory(MicrostructureFilterResult result)
        {
            _filterHistory.Enqueue(result);

            // Maintain history size
            while (_filterHistory.Count > MAX_FILTER_HISTORY)
            {
                _filterHistory.Dequeue();
            }

            // Update statistics
            _statistics.TotalFiltersApplied++;
            if (result.PassesFilter) _statistics.FiltersPassedCount++;

            _statistics.AverageFilterScore = _filterHistory.Count > 0 ?
                _filterHistory.Average(f => f.OverallScore) : 0.5m;

            _statistics.LastFilterTime = result.Timestamp;
        }

        /// <summary>
        /// Check if thresholds should be updated
        /// </summary>
        private bool ShouldUpdateThresholds()
        {
            var timeSinceUpdate = DateTime.UtcNow - _lastThresholdUpdate;
            return timeSinceUpdate.TotalMinutes >= THRESHOLD_UPDATE_INTERVAL_MINUTES &&
                   _filterHistory.Count >= 20;
        }

        /// <summary>
        /// Update adaptive thresholds based on recent performance
        /// </summary>
        private void UpdateAdaptiveThresholds()
        {
            if (_filterHistory.Count < 20) return;

            var recentResults = _filterHistory.TakeLast(20).ToList();
            var passRate = (decimal)recentResults.Count(r => r.PassesFilter) / recentResults.Count;
            var avgScore = recentResults.Average(r => r.OverallScore);

            // Target pass rate of 60-70%
            var targetPassRate = 0.65m;
            var passRateDeviation = passRate - targetPassRate;

            // Adjust thresholds based on pass rate
            var adjustment = passRateDeviation * THRESHOLD_ADJUSTMENT_FACTOR;

            _currentThresholds = new MicrostructureThresholds
            {
                VolumeConfidence = Math.Max(0.2m, Math.Min(0.8m, _baseThresholds.VolumeConfidence + adjustment)),
                DeltaConfidence = Math.Max(0.2m, Math.Min(0.8m, _baseThresholds.DeltaConfidence + adjustment)),
                VolatilityConfidence = Math.Max(0.2m, Math.Min(0.8m, _baseThresholds.VolatilityConfidence + adjustment)),
                CVDNeutralZone = Math.Max(5m, Math.Min(20m, _baseThresholds.CVDNeutralZone)),
                OverallFilterThreshold = Math.Max(0.3m, Math.Min(0.8m, _baseThresholds.OverallFilterThreshold + adjustment))
            };

            _lastThresholdUpdate = DateTime.UtcNow;

            _logAction($"📊 Adaptive microstructure thresholds updated - Pass Rate: {passRate:P1}, Avg Score: {avgScore:F3}, Adjustment: {adjustment:F3}");
        }

        /// <summary>
        /// Create fallback filter result for error scenarios
        /// </summary>
        private MicrostructureFilterResult CreateFallbackFilterResult(SynthesizedSignal signal, MarketContext context)
        {
            return new MicrostructureFilterResult
            {
                Timestamp = DateTime.UtcNow,
                SignalType = signal.Type,
                OverallScore = 0.5m,
                ComponentScores = new MicrostructureComponentScores
                {
                    VolumeScore = 0.5m,
                    DeltaScore = 0.5m,
                    VolatilityScore = 0.5m,
                    CVDAlignmentScore = 0.5m,
                    OptimalTimeScore = context.IsOptimalTradingTime ? 1.0m : 0.3m
                },
                PassesFilter = true, // Conservative fallback
                FilterQuality = MicrostructureFilterQuality.Fair,
                FilterReason = "Fallback filter applied due to error",
                HasHealthWarning = true
            };
        }

        /// <summary>
        /// Log detailed filter analysis
        /// </summary>
        private void LogFilterAnalysis(MicrostructureFilterResult result, MicrostructureComponentScores scores)
        {
            _logAction($"🔍 Advanced Microstructure Filter: {(result.PassesFilter ? "PASS" : "FAIL")} (Score: {result.OverallScore:F3}, Quality: {result.FilterQuality})");
            _logAction($"  • Volume Score: {scores.VolumeScore:F3} (Health: {scores.VolumeHealth})");
            _logAction($"  • Delta Score: {scores.DeltaScore:F3} (Health: {scores.DeltaHealth})");
            _logAction($"  • Volatility Score: {scores.VolatilityScore:F3} (Health: {scores.VolatilityHealth})");
            _logAction($"  • CVD Alignment Score: {scores.CVDAlignmentScore:F3} (Health: {scores.CVDAlignmentHealth})");
            _logAction($"  • Optimal Time Score: {scores.OptimalTimeScore:F3} (Health: {scores.OptimalTimeHealth})");
            _logAction($"  • Filter Reason: {result.FilterReason}");

            if (result.HasHealthWarning)
            {
                _logAction($"  • ⚠️ Health Warning: Component performance issues detected");
            }
        }

        private void InitializeThresholds()
        {
            // More lenient base thresholds for better signal acceptance
            _baseThresholds = new MicrostructureThresholds
            {
                VolumeConfidence = 0.4m,        // Reduced from 0.5m
                DeltaConfidence = 0.4m,         // Reduced from 0.5m
                VolatilityConfidence = 0.3m,    // Reduced from 0.4m
                CVDNeutralZone = 15m,           // Increased from 10m for more tolerance
                OverallFilterThreshold = 0.45m  // Reduced from 0.6m to 0.45m
            };

            _currentThresholds = _baseThresholds.Clone();
        }

        private void InitializeWeights()
        {
            _componentWeights = new MicrostructureWeights
            {
                Volume = 0.25m,
                Delta = 0.25m,
                Volatility = 0.20m,
                CVDAlignment = 0.20m,
                OptimalTime = 0.10m
            };
        }
    }
}
