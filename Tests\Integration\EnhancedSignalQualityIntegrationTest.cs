using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartVolumeStrategy.Core.Analysis;
using SmartVolumeStrategy.Core.Models.Analysis;
using SmartVolumeStrategy.Core.Models.Resilience;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Tests.Integration
{
    /// <summary>
    /// Integration test for enhanced signal quality and confidence calculation fixes
    /// Tests the architectural improvements made to address log analysis issues
    /// </summary>
    public class EnhancedSignalQualityIntegrationTest
    {
        private readonly List<string> _testLogs;
        private readonly EnhancedConfidenceCalculator _confidenceCalculator;
        private readonly EnhancedSignalQualityAnalyzer _qualityAnalyzer;
        private readonly AdaptiveMicrostructureFilter _microstructureFilter;
        private readonly EnhancedCircuitBreakerManager _circuitBreakerManager;
        
        public EnhancedSignalQualityIntegrationTest()
        {
            _testLogs = new List<string>();
            _confidenceCalculator = new EnhancedConfidenceCalculator(LogMessage);
            _qualityAnalyzer = new EnhancedSignalQualityAnalyzer(LogMessage);
            _microstructureFilter = new AdaptiveMicrostructureFilter(LogMessage);
            _circuitBreakerManager = new EnhancedCircuitBreakerManager(LogMessage);
        }
        
        /// <summary>
        /// Test the complete enhanced signal processing pipeline
        /// </summary>
        public async Task<TestResult> RunEnhancedSignalProcessingTest()
        {
            var testResult = new TestResult { TestName = "Enhanced Signal Processing Integration" };
            
            try
            {
                LogMessage("🧪 Starting Enhanced Signal Processing Integration Test...");
                
                // Test 1: CVD Alignment Improvements
                var cvdTest = await TestCVDAlignmentImprovements();
                testResult.SubTests.Add(cvdTest);
                
                // Test 2: Signal Quality Threshold Adjustments
                var qualityTest = await TestSignalQualityImprovements();
                testResult.SubTests.Add(qualityTest);
                
                // Test 3: Enhanced Confidence Calculation
                var confidenceTest = await TestEnhancedConfidenceCalculation();
                testResult.SubTests.Add(confidenceTest);
                
                // Test 4: Microstructure Filter Optimization
                var filterTest = await TestMicrostructureFilterOptimization();
                testResult.SubTests.Add(filterTest);
                
                // Test 5: Circuit Breaker Fallback Strategies
                var circuitBreakerTest = await TestCircuitBreakerFallbacks();
                testResult.SubTests.Add(circuitBreakerTest);
                
                // Calculate overall success
                testResult.Success = testResult.SubTests.All(t => t.Success);
                testResult.Message = testResult.Success ? 
                    "All enhanced signal processing tests passed" : 
                    "Some enhanced signal processing tests failed";
                
                LogMessage($"✅ Enhanced Signal Processing Test Complete - Success: {testResult.Success}");
                
                return testResult;
            }
            catch (Exception ex)
            {
                testResult.Success = false;
                testResult.Message = $"Test failed with exception: {ex.Message}";
                LogMessage($"❌ Enhanced Signal Processing Test Failed: {ex.Message}");
                return testResult;
            }
        }
        
        /// <summary>
        /// Test CVD alignment improvements with realistic market data
        /// </summary>
        private async Task<TestResult> TestCVDAlignmentImprovements()
        {
            var testResult = new TestResult { TestName = "CVD Alignment Improvements" };
            
            try
            {
                LogMessage("🔍 Testing CVD Alignment Improvements...");
                
                // Create test scenarios that match log analysis findings
                var testScenarios = new[]
                {
                    // Scenario 1: Strong CVD alignment (should score high)
                    new { CVDTrend = 328633.33m, SignalType = SignalType.Long, ExpectedScore = 0.8m },
                    
                    // Scenario 2: Weak CVD divergence (should be more tolerant)
                    new { CVDTrend = -50000m, SignalType = SignalType.Long, ExpectedScore = 0.55m },
                    
                    // Scenario 3: Neutral CVD (should be acceptable)
                    new { CVDTrend = 10000m, SignalType = SignalType.Long, ExpectedScore = 0.65m }
                };
                
                var passedScenarios = 0;
                
                foreach (var scenario in testScenarios)
                {
                    var marketContext = CreateTestMarketContext(scenario.CVDTrend);
                    var signal = CreateTestSynthesizedSignal(scenario.SignalType);
                    
                    // Test the enhanced CVD alignment calculation
                    var filterResult = _microstructureFilter.ApplyFilter(signal, marketContext, null, CreateTestConfig());
                    
                    LogMessage($"   CVD: {scenario.CVDTrend}, Signal: {scenario.SignalType}, Score: {filterResult.ComponentScores.CVDAlignmentScore:F3}");
                    
                    // Check if the score is within acceptable range of expected
                    if (Math.Abs(filterResult.ComponentScores.CVDAlignmentScore - scenario.ExpectedScore) <= 0.15m)
                    {
                        passedScenarios++;
                    }
                }
                
                testResult.Success = passedScenarios >= testScenarios.Length * 0.8; // 80% pass rate
                testResult.Message = $"CVD Alignment: {passedScenarios}/{testScenarios.Length} scenarios passed";
                
                return testResult;
            }
            catch (Exception ex)
            {
                testResult.Success = false;
                testResult.Message = $"CVD Alignment test failed: {ex.Message}";
                return testResult;
            }
        }
        
        /// <summary>
        /// Test signal quality threshold improvements
        /// </summary>
        private async Task<TestResult> TestSignalQualityImprovements()
        {
            var testResult = new TestResult { TestName = "Signal Quality Improvements" };
            
            try
            {
                LogMessage("📊 Testing Signal Quality Improvements...");
                
                // Test signals that were previously rated as "Fair" but should now be "Good"
                var testSignals = new[]
                {
                    CreateTestTradingSignal(0.52m, SignalType.Long),  // From log analysis
                    CreateTestTradingSignal(0.51m, SignalType.Long),  // From log analysis
                    CreateTestTradingSignal(0.58m, SignalType.Long),  // Should be Good
                    CreateTestTradingSignal(0.45m, SignalType.Long)   // Should remain Fair
                };
                
                var improvedSignals = 0;
                
                foreach (var signal in testSignals)
                {
                    var marketContext = CreateTestMarketContext();
                    var qualityResult = _qualityAnalyzer.AnalyzeSignalQuality(signal, marketContext);
                    
                    LogMessage($"   Signal Confidence: {signal.Confidence:P1}, Quality: {qualityResult.Level}, Score: {qualityResult.Score:F3}");
                    
                    // Signals with confidence >= 0.51 should now achieve Good quality
                    if (signal.Confidence >= 0.51m && qualityResult.Level >= SignalQuality.Good)
                    {
                        improvedSignals++;
                    }
                    else if (signal.Confidence < 0.51m && qualityResult.Level == SignalQuality.Fair)
                    {
                        improvedSignals++; // Correctly classified as Fair
                    }
                }
                
                testResult.Success = improvedSignals >= testSignals.Length * 0.75; // 75% improvement rate
                testResult.Message = $"Signal Quality: {improvedSignals}/{testSignals.Length} signals correctly classified";
                
                return testResult;
            }
            catch (Exception ex)
            {
                testResult.Success = false;
                testResult.Message = $"Signal Quality test failed: {ex.Message}";
                return testResult;
            }
        }
        
        /// <summary>
        /// Test enhanced confidence calculation
        /// </summary>
        private async Task<TestResult> TestEnhancedConfidenceCalculation()
        {
            var testResult = new TestResult { TestName = "Enhanced Confidence Calculation" };
            
            try
            {
                LogMessage("🎯 Testing Enhanced Confidence Calculation...");
                
                // Test with signal characteristics from log analysis
                var baseSignal = CreateTestTradingSignal(0.52m, SignalType.Long);
                baseSignal.VolumeRatio = 1.8m; // From logs
                baseSignal.DeltaImbalance = 0.2m; // From logs
                
                var marketContext = CreateTestMarketContext(328633.33m);
                marketContext.VolatilityRegime = VolatilityRegime.VeryLow; // From logs
                marketContext.CurrentSession = TradingSession.European; // From logs
                
                var enhancedConfidence = _confidenceCalculator.CalculateEnhancedConfidence(
                    baseSignal, marketContext);
                
                LogMessage($"   Base Confidence: {baseSignal.Confidence:P1}");
                LogMessage($"   Enhanced Confidence: {enhancedConfidence:P1}");
                
                // Enhanced confidence should be higher than base confidence for good signals
                var confidenceImprovement = enhancedConfidence > baseSignal.Confidence;
                
                // Enhanced confidence should be above 60% for signals with good characteristics
                var adequateConfidence = enhancedConfidence >= 0.60m;
                
                testResult.Success = confidenceImprovement && adequateConfidence;
                testResult.Message = $"Enhanced Confidence: {enhancedConfidence:P1} (Improvement: {confidenceImprovement}, Adequate: {adequateConfidence})";
                
                return testResult;
            }
            catch (Exception ex)
            {
                testResult.Success = false;
                testResult.Message = $"Enhanced Confidence test failed: {ex.Message}";
                return testResult;
            }
        }
        
        /// <summary>
        /// Test microstructure filter optimization
        /// </summary>
        private async Task<TestResult> TestMicrostructureFilterOptimization()
        {
            var testResult = new TestResult { TestName = "Microstructure Filter Optimization" };
            
            try
            {
                LogMessage("🔍 Testing Microstructure Filter Optimization...");
                
                // Test with characteristics that were failing in logs (2/5 checks passing)
                var signal = CreateTestSynthesizedSignal(SignalType.Long);
                var marketContext = CreateTestMarketContext(328633.33m);
                marketContext.VolatilityRegime = VolatilityRegime.VeryLow;
                marketContext.IsOptimalTradingTime = false; // European session marked as non-optimal
                
                var filterResult = _microstructureFilter.ApplyFilter(signal, marketContext, null, CreateTestConfig());
                
                LogMessage($"   Filter Score: {filterResult.OverallScore:F3}");
                LogMessage($"   Passes Filter: {filterResult.PassesFilter}");
                LogMessage($"   Components Passing: {GetPassingComponentCount(filterResult)}/5");
                
                // With optimized thresholds, more signals should pass
                var improvementAchieved = filterResult.PassesFilter || filterResult.OverallScore >= 0.4m;
                
                testResult.Success = improvementAchieved;
                testResult.Message = $"Filter Optimization: Score {filterResult.OverallScore:F3}, Passes: {filterResult.PassesFilter}";
                
                return testResult;
            }
            catch (Exception ex)
            {
                testResult.Success = false;
                testResult.Message = $"Microstructure Filter test failed: {ex.Message}";
                return testResult;
            }
        }
        
        /// <summary>
        /// Test circuit breaker fallback strategies
        /// </summary>
        private async Task<TestResult> TestCircuitBreakerFallbacks()
        {
            var testResult = new TestResult { TestName = "Circuit Breaker Fallbacks" };
            
            try
            {
                LogMessage("🔄 Testing Circuit Breaker Fallback Strategies...");
                
                // Simulate component failures
                _circuitBreakerManager.RecordOperation(StrategyComponent.InstitutionalAnalysis, false, "Test failure", FailureSeverity.Medium);
                _circuitBreakerManager.RecordOperation(StrategyComponent.InstitutionalAnalysis, false, "Test failure", FailureSeverity.Medium);
                _circuitBreakerManager.RecordOperation(StrategyComponent.InstitutionalAnalysis, false, "Test failure", FailureSeverity.Medium);
                
                // Check if fallback is activated
                var canOperate = _circuitBreakerManager.CanOperateOrFallback(StrategyComponent.InstitutionalAnalysis, out bool useFallback);
                
                LogMessage($"   Can Operate: {canOperate}");
                LogMessage($"   Use Fallback: {useFallback}");
                
                // Should be able to operate with fallback
                var fallbackWorking = canOperate && useFallback;
                
                // Test fallback configuration
                var fallbackConfig = _circuitBreakerManager.GetFallbackConfiguration(StrategyComponent.InstitutionalAnalysis);
                var configValid = fallbackConfig.UseSimplifiedLogic && fallbackConfig.ReducedConfidenceMultiplier > 0;
                
                testResult.Success = fallbackWorking && configValid;
                testResult.Message = $"Circuit Breaker: Fallback Active: {useFallback}, Config Valid: {configValid}";
                
                return testResult;
            }
            catch (Exception ex)
            {
                testResult.Success = false;
                testResult.Message = $"Circuit Breaker test failed: {ex.Message}";
                return testResult;
            }
        }
        
        // Helper methods for creating test data
        private MarketContext CreateTestMarketContext(decimal cvdTrend = 100000m)
        {
            return new MarketContext
            {
                CVDTrend = cvdTrend,
                VolatilityRegime = VolatilityRegime.VeryLow,
                CurrentSession = TradingSession.European,
                IsOptimalTradingTime = false
            };
        }
        
        private TradingSignal CreateTestTradingSignal(decimal confidence, SignalType type)
        {
            return new TradingSignal
            {
                Confidence = confidence,
                Type = type,
                Strength = confidence,
                Quality = SignalQuality.Fair,
                VolumeRatio = 1.8m,
                DeltaImbalance = 0.2m,
                VolumeComponent = new VolumeSignalComponent { VolumeConfidence = confidence },
                DeltaComponent = new DeltaSignalComponent { DeltaConfidence = confidence }
            };
        }
        
        private SynthesizedSignal CreateTestSynthesizedSignal(SignalType type)
        {
            return new SynthesizedSignal
            {
                Type = type,
                Confidence = 0.52m,
                Strength = 0.52m,
                Quality = SignalQuality.Fair
            };
        }
        
        private SignalSynthesisConfig CreateTestConfig()
        {
            return new SignalSynthesisConfig
            {
                EnableMicrostructureFilter = true,
                MinVolumeConfidence = 0.4m,
                MinDeltaConfidence = 0.4m,
                MinVolatilityConfidence = 0.3m
            };
        }
        
        private int GetPassingComponentCount(MicrostructureFilterResult result)
        {
            int count = 0;
            if (result.ComponentScores.VolumeScore >= 0.4m) count++;
            if (result.ComponentScores.DeltaScore >= 0.4m) count++;
            if (result.ComponentScores.VolatilityScore >= 0.3m) count++;
            if (result.ComponentScores.CVDAlignmentScore >= 0.5m) count++;
            if (result.ComponentScores.OptimalTimeScore >= 0.5m) count++;
            return count;
        }
        
        private void LogMessage(string message)
        {
            _testLogs.Add($"{DateTime.UtcNow:HH:mm:ss.fff} {message}");
            Console.WriteLine(message);
        }
        
        public List<string> GetTestLogs() => new List<string>(_testLogs);
    }
    
    /// <summary>
    /// Test result structure
    /// </summary>
    public class TestResult
    {
        public string TestName { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<TestResult> SubTests { get; set; } = new List<TestResult>();
    }
}
