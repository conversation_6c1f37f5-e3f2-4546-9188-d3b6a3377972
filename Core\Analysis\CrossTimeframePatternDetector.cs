using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models.Analysis;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Phase 3.1: Cross-Timeframe Pattern Detector
    /// Advanced pattern detection and validation across multiple timeframes with circuit breaker integration
    /// </summary>
    public class CrossTimeframePatternDetector
    {
        private readonly Action<string> _logAction;
        private readonly MultiTimeframeAnalysisConfig _config;
        private readonly Queue<CrossTimeframePattern> _patternHistory;
        private readonly Dictionary<MultiTimeframePatternType, int> _patternSuccessRates;
        private readonly object _lockObject = new object();
        
        // Circuit breaker integration
        private CircuitBreakerManager _circuitBreakerManager;
        
        // Configuration
        private const int MAX_PATTERN_HISTORY = 150;
        private const decimal MIN_PATTERN_VALIDATION_SCORE = 0.6m;
        private const int PATTERN_VALIDATION_WINDOW = 20;

        public CrossTimeframePatternDetector(MultiTimeframeAnalysisConfig config = null, Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _config = config ?? new MultiTimeframeAnalysisConfig();
            _patternHistory = new Queue<CrossTimeframePattern>();
            _patternSuccessRates = new Dictionary<MultiTimeframePatternType, int>();
            
            InitializePatternSuccessRates();
        }

        /// <summary>
        /// Set circuit breaker manager for fault tolerance
        /// </summary>
        public void SetCircuitBreakerManager(CircuitBreakerManager circuitBreakerManager)
        {
            _circuitBreakerManager = circuitBreakerManager;
        }

        /// <summary>
        /// Detect patterns across multiple timeframes
        /// </summary>
        public List<CrossTimeframePattern> DetectPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals, 
            MultiTimeframeCorrelationAnalysis correlationAnalysis,
            MarketContext marketContext)
        {
            lock (_lockObject)
            {
                var startTime = DateTime.UtcNow;
                var detectedPatterns = new List<CrossTimeframePattern>();
                
                try
                {
                    // Phase 3.1: Check circuit breaker status
                    if (_circuitBreakerManager != null && !_circuitBreakerManager.IsOperationAllowed(StrategyComponent.MultiTimeframeAnalysis))
                    {
                        _logAction("🔴 Cross-timeframe pattern detection blocked by circuit breaker");
                        return detectedPatterns;
                    }

                    // Check degradation level
                    if (_circuitBreakerManager != null)
                    {
                        var degradationLevel = _circuitBreakerManager.GetDegradationManager().GetCurrentDegradationLevel();
                        if (degradationLevel >= DegradationLevel.Minimal)
                        {
                            _logAction("⚠️ Cross-timeframe pattern detection running in degraded mode");
                            return DetectBasicPatterns(timeframeSignals);
                        }
                    }

                    if (!_config.EnablePatternDetection)
                    {
                        return detectedPatterns;
                    }

                    _logAction("🔍 Starting cross-timeframe pattern detection...");

                    // Detect different pattern types
                    detectedPatterns.AddRange(DetectTrendPatterns(timeframeSignals, correlationAnalysis, marketContext));
                    detectedPatterns.AddRange(DetectReversalPatterns(timeframeSignals, correlationAnalysis, marketContext));
                    detectedPatterns.AddRange(DetectContinuationPatterns(timeframeSignals, correlationAnalysis, marketContext));
                    detectedPatterns.AddRange(DetectBreakoutPatterns(timeframeSignals, correlationAnalysis, marketContext));
                    detectedPatterns.AddRange(DetectConsolidationPatterns(timeframeSignals, correlationAnalysis, marketContext));

                    // Validate detected patterns
                    foreach (var pattern in detectedPatterns)
                    {
                        ValidatePattern(pattern, timeframeSignals, correlationAnalysis);
                    }

                    // Filter patterns by confidence threshold
                    var validPatterns = detectedPatterns
                        .Where(p => p.Confidence >= _config.MinPatternConfidence)
                        .OrderByDescending(p => p.Confidence)
                        .ToList();

                    // Update pattern history
                    foreach (var pattern in validPatterns)
                    {
                        UpdatePatternHistory(pattern);
                    }

                    // Record successful operation
                    if (_circuitBreakerManager != null)
                    {
                        var responseTime = DateTime.UtcNow - startTime;
                        _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, true, $"Detected {validPatterns.Count} patterns");
                        _circuitBreakerManager.GetComponentHealthTracker().UpdateResponseTime(StrategyComponent.MultiTimeframeAnalysis, responseTime);
                    }

                    _logAction($"✅ Cross-timeframe pattern detection completed - Found {validPatterns.Count} valid patterns");

                    return validPatterns;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Cross-timeframe pattern detection error: {ex.Message}");
                    
                    // Record failure with circuit breaker
                    if (_circuitBreakerManager != null)
                    {
                        _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, false, ex.Message, FailureSeverity.Medium);
                    }
                    
                    return detectedPatterns;
                }
            }
        }

        /// <summary>
        /// Get pattern detection statistics
        /// </summary>
        public MultiTimeframeAnalysisStatistics GetPatternStatistics()
        {
            lock (_lockObject)
            {
                var stats = new MultiTimeframeAnalysisStatistics
                {
                    DetectedPatterns = _patternHistory.Count,
                    ValidatedPatterns = _patternHistory.Count(p => p.IsValidated),
                    LastAnalysis = _patternHistory.Count > 0 ? _patternHistory.Last().Timestamp : DateTime.MinValue
                };

                if (_patternHistory.Count > 0)
                {
                    stats.AveragePatternConfidence = _patternHistory.Average(p => p.Confidence);
                    
                    // Pattern type distribution
                    foreach (var pattern in _patternHistory)
                    {
                        if (!stats.PatternTypeDistribution.ContainsKey(pattern.PatternType))
                            stats.PatternTypeDistribution[pattern.PatternType] = 0;
                        stats.PatternTypeDistribution[pattern.PatternType]++;
                    }
                }

                return stats;
            }
        }

        /// <summary>
        /// Detect trend alignment patterns
        /// </summary>
        private List<CrossTimeframePattern> DetectTrendPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            MultiTimeframeCorrelationAnalysis correlationAnalysis,
            MarketContext marketContext)
        {
            var patterns = new List<CrossTimeframePattern>();

            // Trend Alignment Pattern
            if (correlationAnalysis.AlignedTimeframes.Count >= 3)
            {
                var alignedSignals = timeframeSignals
                    .Where(kvp => correlationAnalysis.AlignedTimeframes.Contains(kvp.Key))
                    .ToList();

                var dominantType = alignedSignals.First().Value.Type;
                var avgConfidence = alignedSignals.Average(kvp => kvp.Value.Confidence);

                var trendPattern = new CrossTimeframePattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternType = MultiTimeframePatternType.TrendAlignment,
                    PatternName = $"{dominantType} Trend Alignment",
                    Confidence = avgConfidence * 0.9m, // Slight discount for multi-timeframe
                    SupportingTimeframes = correlationAnalysis.AlignedTimeframes,
                    ConflictingTimeframes = correlationAnalysis.DivergentTimeframes,
                    PatternStrength = correlationAnalysis.OverallCorrelation,
                    EstimatedDuration = EstimateTrendDuration(alignedSignals, marketContext)
                };

                CalculateTimeframeContributions(trendPattern, timeframeSignals);
                AddPatternFactors(trendPattern, "Strong trend alignment across multiple timeframes", marketContext);

                patterns.Add(trendPattern);
            }

            // Trend Divergence Pattern
            if (correlationAnalysis.HasSignificantDivergence && correlationAnalysis.DivergenceStrength > 0.6m)
            {
                var divergencePattern = new CrossTimeframePattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternType = MultiTimeframePatternType.TrendDivergence,
                    PatternName = "Multi-Timeframe Trend Divergence",
                    Confidence = correlationAnalysis.DivergenceStrength * 0.8m,
                    SupportingTimeframes = correlationAnalysis.DivergentTimeframes,
                    ConflictingTimeframes = correlationAnalysis.AlignedTimeframes,
                    PatternStrength = correlationAnalysis.DivergenceStrength,
                    EstimatedDuration = TimeSpan.FromMinutes(30) // Divergences typically resolve quickly
                };

                CalculateTimeframeContributions(divergencePattern, timeframeSignals);
                AddPatternFactors(divergencePattern, "Significant divergence between timeframes", marketContext);

                patterns.Add(divergencePattern);
            }

            return patterns;
        }

        /// <summary>
        /// Detect reversal patterns
        /// </summary>
        private List<CrossTimeframePattern> DetectReversalPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            MultiTimeframeCorrelationAnalysis correlationAnalysis,
            MarketContext marketContext)
        {
            var patterns = new List<CrossTimeframePattern>();

            // Look for reversal signals where shorter timeframes contradict longer ones
            var shortTimeframes = timeframeSignals.Where(kvp => kvp.Key <= Timeframe.M5).ToList();
            var longTimeframes = timeframeSignals.Where(kvp => kvp.Key >= Timeframe.M15).ToList();

            if (shortTimeframes.Count >= 1 && longTimeframes.Count >= 1)
            {
                var shortSignalTypes = shortTimeframes.Select(kvp => kvp.Value.Type).Distinct().ToList();
                var longSignalTypes = longTimeframes.Select(kvp => kvp.Value.Type).Distinct().ToList();

                // Check for opposing signals
                var hasOpposingSignals = shortSignalTypes.Any(st => longSignalTypes.Any(lt => AreOpposingSignals(st, lt)));

                if (hasOpposingSignals)
                {
                    var avgShortConfidence = shortTimeframes.Average(kvp => kvp.Value.Confidence);
                    var avgLongConfidence = longTimeframes.Average(kvp => kvp.Value.Confidence);

                    // Higher confidence in shorter timeframes suggests potential reversal
                    if (avgShortConfidence > avgLongConfidence + 0.2m)
                    {
                        var reversalPattern = new CrossTimeframePattern
                        {
                            Timestamp = DateTime.UtcNow,
                            PatternType = MultiTimeframePatternType.ReversalPattern,
                            PatternName = "Short-Term Reversal Signal",
                            Confidence = (avgShortConfidence - avgLongConfidence) * 0.7m,
                            SupportingTimeframes = shortTimeframes.Select(kvp => kvp.Key).ToList(),
                            ConflictingTimeframes = longTimeframes.Select(kvp => kvp.Key).ToList(),
                            PatternStrength = Math.Abs(avgShortConfidence - avgLongConfidence),
                            EstimatedDuration = TimeSpan.FromMinutes(15)
                        };

                        CalculateTimeframeContributions(reversalPattern, timeframeSignals);
                        AddPatternFactors(reversalPattern, "Short-term signals opposing long-term trend", marketContext);

                        patterns.Add(reversalPattern);
                    }
                }
            }

            return patterns;
        }

        /// <summary>
        /// Detect continuation patterns
        /// </summary>
        private List<CrossTimeframePattern> DetectContinuationPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            MultiTimeframeCorrelationAnalysis correlationAnalysis,
            MarketContext marketContext)
        {
            var patterns = new List<CrossTimeframePattern>();

            // Look for strong alignment with increasing confidence across timeframes
            if (correlationAnalysis.OverallCorrelation >= 0.8m)
            {
                var orderedSignals = timeframeSignals
                    .OrderBy(kvp => kvp.Key)
                    .ToList();

                // Check if confidence increases with timeframe
                var confidenceIncreasing = true;
                for (int i = 1; i < orderedSignals.Count; i++)
                {
                    if (orderedSignals[i].Value.Confidence < orderedSignals[i - 1].Value.Confidence)
                    {
                        confidenceIncreasing = false;
                        break;
                    }
                }

                if (confidenceIncreasing)
                {
                    var dominantType = orderedSignals.First().Value.Type;
                    var avgConfidence = orderedSignals.Average(kvp => kvp.Value.Confidence);

                    var continuationPattern = new CrossTimeframePattern
                    {
                        Timestamp = DateTime.UtcNow,
                        PatternType = MultiTimeframePatternType.ContinuationPattern,
                        PatternName = $"{dominantType} Trend Continuation",
                        Confidence = avgConfidence * 0.95m,
                        SupportingTimeframes = orderedSignals.Select(kvp => kvp.Key).ToList(),
                        PatternStrength = correlationAnalysis.OverallCorrelation,
                        EstimatedDuration = EstimateContinuationDuration(orderedSignals, marketContext)
                    };

                    CalculateTimeframeContributions(continuationPattern, timeframeSignals);
                    AddPatternFactors(continuationPattern, "Strong trend continuation across all timeframes", marketContext);

                    patterns.Add(continuationPattern);
                }
            }

            return patterns;
        }

        /// <summary>
        /// Detect breakout patterns across timeframes
        /// </summary>
        private List<CrossTimeframePattern> DetectBreakoutPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            MultiTimeframeCorrelationAnalysis correlationAnalysis,
            MarketContext marketContext)
        {
            var patterns = new List<CrossTimeframePattern>();

            // Look for high-confidence signals with volume confirmation
            var highConfidenceSignals = timeframeSignals
                .Where(kvp => kvp.Value.Confidence >= 0.8m)
                .ToList();

            if (highConfidenceSignals.Count >= 2 && marketContext.VolatilityRegime >= VolatilityRegime.High)
            {
                var avgConfidence = highConfidenceSignals.Average(kvp => kvp.Value.Confidence);
                var dominantType = highConfidenceSignals
                    .GroupBy(kvp => kvp.Value.Type)
                    .OrderByDescending(g => g.Count())
                    .First().Key;

                var breakoutPattern = new CrossTimeframePattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternType = MultiTimeframePatternType.BreakoutPattern,
                    PatternName = $"{dominantType} Breakout Pattern",
                    Confidence = avgConfidence * 0.85m,
                    SupportingTimeframes = highConfidenceSignals.Select(kvp => kvp.Key).ToList(),
                    PatternStrength = avgConfidence,
                    EstimatedDuration = TimeSpan.FromMinutes(10)
                };

                CalculateTimeframeContributions(breakoutPattern, timeframeSignals);
                AddPatternFactors(breakoutPattern, "High-confidence signals with volatility breakout", marketContext);

                patterns.Add(breakoutPattern);
            }

            return patterns;
        }

        /// <summary>
        /// Detect consolidation patterns across timeframes
        /// </summary>
        private List<CrossTimeframePattern> DetectConsolidationPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            MultiTimeframeCorrelationAnalysis correlationAnalysis,
            MarketContext marketContext)
        {
            var patterns = new List<CrossTimeframePattern>();

            // Look for low correlation with mixed signals (consolidation)
            if (correlationAnalysis.OverallCorrelation < 0.4m &&
                marketContext.VolatilityRegime <= VolatilityRegime.Low)
            {
                var signalTypes = timeframeSignals.Values.Select(s => s.Type).Distinct().Count();
                var avgConfidence = timeframeSignals.Values.Average(s => s.Confidence);

                if (signalTypes >= 2 && avgConfidence < 0.6m)
                {
                    var consolidationPattern = new CrossTimeframePattern
                    {
                        Timestamp = DateTime.UtcNow,
                        PatternType = MultiTimeframePatternType.ConsolidationPattern,
                        PatternName = "Multi-Timeframe Consolidation",
                        Confidence = (1m - correlationAnalysis.OverallCorrelation) * 0.7m,
                        SupportingTimeframes = timeframeSignals.Keys.ToList(),
                        PatternStrength = 1m - correlationAnalysis.OverallCorrelation,
                        EstimatedDuration = TimeSpan.FromMinutes(45)
                    };

                    CalculateTimeframeContributions(consolidationPattern, timeframeSignals);
                    AddPatternFactors(consolidationPattern, "Low correlation with mixed signals indicating consolidation", marketContext);

                    patterns.Add(consolidationPattern);
                }
            }

            return patterns;
        }

        /// <summary>
        /// Validate detected pattern
        /// </summary>
        private void ValidatePattern(
            CrossTimeframePattern pattern,
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            MultiTimeframeCorrelationAnalysis correlationAnalysis)
        {
            var validationScore = 0m;
            var validationFactors = new List<string>();

            // Validate based on supporting timeframes
            var supportingRatio = (decimal)pattern.SupportingTimeframes.Count / timeframeSignals.Count;
            validationScore += supportingRatio * 0.3m;
            validationFactors.Add($"Supporting timeframes: {supportingRatio:P0}");

            // Validate based on pattern strength
            validationScore += pattern.PatternStrength * 0.3m;
            validationFactors.Add($"Pattern strength: {pattern.PatternStrength:P1}");

            // Validate based on confidence
            validationScore += pattern.Confidence * 0.2m;
            validationFactors.Add($"Pattern confidence: {pattern.Confidence:P1}");

            // Validate based on correlation consistency
            var correlationConsistency = pattern.PatternType switch
            {
                MultiTimeframePatternType.TrendAlignment => correlationAnalysis.OverallCorrelation,
                MultiTimeframePatternType.TrendDivergence => 1m - correlationAnalysis.OverallCorrelation,
                MultiTimeframePatternType.ConsolidationPattern => 1m - correlationAnalysis.OverallCorrelation,
                _ => correlationAnalysis.OverallCorrelation
            };
            validationScore += correlationConsistency * 0.2m;
            validationFactors.Add($"Correlation consistency: {correlationConsistency:P1}");

            pattern.ValidationScore = validationScore;
            pattern.IsValidated = validationScore >= MIN_PATTERN_VALIDATION_SCORE;
            pattern.PatternFactors.AddRange(validationFactors);
        }

        /// <summary>
        /// Calculate timeframe contributions to pattern
        /// </summary>
        private void CalculateTimeframeContributions(CrossTimeframePattern pattern, Dictionary<Timeframe, TradingSignal> timeframeSignals)
        {
            foreach (var kvp in timeframeSignals)
            {
                var timeframe = kvp.Key;
                var signal = kvp.Value;

                // Base contribution on signal confidence and timeframe weight
                var baseWeight = _config.BaseTimeframeWeights.ContainsKey(timeframe) ?
                    _config.BaseTimeframeWeights[timeframe] : 0.25m;

                var contribution = signal.Confidence * baseWeight;
                pattern.TimeframeContributions[timeframe] = contribution;
                pattern.ContributingSignals[timeframe] = signal;
            }
        }

        /// <summary>
        /// Add pattern factors for analysis
        /// </summary>
        private void AddPatternFactors(CrossTimeframePattern pattern, string primaryFactor, MarketContext marketContext)
        {
            pattern.PatternFactors.Add(primaryFactor);
            pattern.PatternFactors.Add($"Market volatility: {marketContext.VolatilityRegime}");
            pattern.PatternFactors.Add($"Trading session: {marketContext.CurrentSession}");
            pattern.PatternFactors.Add($"Supporting timeframes: {pattern.SupportingTimeframes.Count}");
            pattern.PatternFactors.Add($"Conflicting timeframes: {pattern.ConflictingTimeframes.Count}");
        }

        /// <summary>
        /// Update pattern history
        /// </summary>
        private void UpdatePatternHistory(CrossTimeframePattern pattern)
        {
            _patternHistory.Enqueue(pattern);

            while (_patternHistory.Count > MAX_PATTERN_HISTORY)
            {
                _patternHistory.Dequeue();
            }
        }

        /// <summary>
        /// Detect basic patterns for degraded mode
        /// </summary>
        private List<CrossTimeframePattern> DetectBasicPatterns(Dictionary<Timeframe, TradingSignal> timeframeSignals)
        {
            var patterns = new List<CrossTimeframePattern>();

            // Simple trend alignment check
            var signalTypes = timeframeSignals.Values.Select(s => s.Type).ToList();
            var dominantType = signalTypes.GroupBy(t => t).OrderByDescending(g => g.Count()).First().Key;
            var alignmentRatio = (decimal)signalTypes.Count(t => t == dominantType) / timeframeSignals.Count;

            if (alignmentRatio >= 0.7m)
            {
                var basicPattern = new CrossTimeframePattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternType = MultiTimeframePatternType.TrendAlignment,
                    PatternName = $"Basic {dominantType} Alignment",
                    Confidence = alignmentRatio * 0.8m,
                    SupportingTimeframes = timeframeSignals.Keys.ToList(),
                    PatternStrength = alignmentRatio,
                    EstimatedDuration = TimeSpan.FromMinutes(20),
                    IsValidated = true,
                    ValidationScore = alignmentRatio
                };

                basicPattern.PatternFactors.Add("Basic pattern detection (degraded mode)");
                patterns.Add(basicPattern);
            }

            return patterns;
        }

        /// <summary>
        /// Check if two signal types are opposing
        /// </summary>
        private bool AreOpposingSignals(SignalType type1, SignalType type2)
        {
            return (type1 == SignalType.Long && type2 == SignalType.Short) ||
                   (type1 == SignalType.Short && type2 == SignalType.Long);
        }

        /// <summary>
        /// Estimate trend duration based on timeframe alignment
        /// </summary>
        private TimeSpan EstimateTrendDuration(List<KeyValuePair<Timeframe, TradingSignal>> alignedSignals, MarketContext marketContext)
        {
            var longestTimeframe = alignedSignals.Max(kvp => kvp.Key);
            var avgConfidence = alignedSignals.Average(kvp => kvp.Value.Confidence);

            var baseDuration = longestTimeframe switch
            {
                Timeframe.M1 => TimeSpan.FromMinutes(5),
                Timeframe.M5 => TimeSpan.FromMinutes(15),
                Timeframe.M15 => TimeSpan.FromMinutes(45),
                Timeframe.H1 => TimeSpan.FromHours(2),
                _ => TimeSpan.FromMinutes(30)
            };

            // Adjust based on confidence and volatility
            var confidenceMultiplier = avgConfidence;
            var volatilityMultiplier = marketContext.VolatilityRegime switch
            {
                VolatilityRegime.VeryLow => 1.5m,
                VolatilityRegime.Low => 1.2m,
                VolatilityRegime.Normal => 1.0m,
                VolatilityRegime.High => 0.8m,
                VolatilityRegime.VeryHigh => 0.6m,
                _ => 1.0m
            };

            var adjustedMinutes = baseDuration.TotalMinutes * (double)(confidenceMultiplier * volatilityMultiplier);
            return TimeSpan.FromMinutes(Math.Max(5, adjustedMinutes));
        }

        /// <summary>
        /// Estimate continuation duration
        /// </summary>
        private TimeSpan EstimateContinuationDuration(List<KeyValuePair<Timeframe, TradingSignal>> orderedSignals, MarketContext marketContext)
        {
            var longestTimeframe = orderedSignals.Last().Key;
            var strongestConfidence = orderedSignals.Max(kvp => kvp.Value.Confidence);

            var baseDuration = longestTimeframe switch
            {
                Timeframe.M1 => TimeSpan.FromMinutes(10),
                Timeframe.M5 => TimeSpan.FromMinutes(25),
                Timeframe.M15 => TimeSpan.FromMinutes(60),
                Timeframe.H1 => TimeSpan.FromHours(3),
                _ => TimeSpan.FromMinutes(45)
            };

            // Strong continuation patterns last longer
            var strengthMultiplier = strongestConfidence * 1.5m;
            var adjustedMinutes = baseDuration.TotalMinutes * (double)strengthMultiplier;

            return TimeSpan.FromMinutes(Math.Max(10, adjustedMinutes));
        }

        private void InitializePatternSuccessRates()
        {
            foreach (MultiTimeframePatternType patternType in Enum.GetValues<MultiTimeframePatternType>())
            {
                _patternSuccessRates[patternType] = 0;
            }
        }
    }
}
