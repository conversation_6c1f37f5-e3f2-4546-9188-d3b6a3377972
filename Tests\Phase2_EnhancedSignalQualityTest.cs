using System;
using System.Collections.Generic;
using SmartVolumeStrategy.Core.Analysis;
using SmartVolumeStrategy.Core.Models.Analysis;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Tests
{
    /// <summary>
    /// Phase 2 Test: Enhanced Signal Quality Analysis
    /// Tests the new multi-factor quality scoring system
    /// </summary>
    public class Phase2_EnhancedSignalQualityTest
    {
        private readonly List<string> _testLogs;
        private readonly EnhancedSignalQualityAnalyzer _qualityAnalyzer;

        public Phase2_EnhancedSignalQualityTest()
        {
            _testLogs = new List<string>();
            _qualityAnalyzer = new EnhancedSignalQualityAnalyzer(log => _testLogs.Add(log));
        }

        /// <summary>
        /// Test enhanced quality analysis with various signal scenarios
        /// </summary>
        public void RunEnhancedQualityTests()
        {
            Console.WriteLine("🧪 PHASE 2 TEST: Enhanced Signal Quality Analysis");
            Console.WriteLine(new string('=', 60));

            // Test 1: High confidence signal with optimal market conditions
            TestHighQualitySignal();

            // Test 2: Medium confidence signal with mixed conditions
            TestMediumQualitySignal();

            // Test 3: Low confidence signal with poor conditions
            TestLowQualitySignal();

            // Test 4: Quality trend analysis
            TestQualityTrendAnalysis();

            // Test 5: Adaptive threshold adjustment
            TestAdaptiveThresholds();

            Console.WriteLine("\n✅ Phase 2 Enhanced Signal Quality Tests Completed");
            Console.WriteLine($"📊 Total test logs generated: {_testLogs.Count}");
        }

        private void TestHighQualitySignal()
        {
            Console.WriteLine("\n🎯 Test 1: High Quality Signal Analysis");
            
            var signal = CreateTestSignal(0.85m, SignalType.Long);
            var marketContext = CreateOptimalMarketContext();
            
            var enhancedQuality = _qualityAnalyzer.AnalyzeSignalQuality(signal, marketContext);
            
            Console.WriteLine($"  • Signal Confidence: {signal.Confidence:F3}");
            Console.WriteLine($"  • Enhanced Quality Level: {enhancedQuality.Level}");
            Console.WriteLine($"  • Enhanced Quality Score: {enhancedQuality.Score:F3}");
            Console.WriteLine($"  • Quality Trend: {enhancedQuality.Trend}");
            Console.WriteLine($"  • Market Adjustment Applied: {enhancedQuality.MarketConditionAdjustment}");
            Console.WriteLine($"  • Analysis Time: {enhancedQuality.AnalysisTime.TotalMilliseconds:F1}ms");
            
            // Verify high quality signal gets appropriate rating
            if (enhancedQuality.Level >= SignalQuality.Good && enhancedQuality.Score >= 0.7m)
            {
                Console.WriteLine("  ✅ High quality signal correctly identified");
            }
            else
            {
                Console.WriteLine("  ❌ High quality signal not properly rated");
            }
        }

        private void TestMediumQualitySignal()
        {
            Console.WriteLine("\n🎯 Test 2: Medium Quality Signal Analysis");
            
            var signal = CreateTestSignal(0.65m, SignalType.Short);
            var marketContext = CreateMixedMarketContext();
            
            var enhancedQuality = _qualityAnalyzer.AnalyzeSignalQuality(signal, marketContext);
            
            Console.WriteLine($"  • Signal Confidence: {signal.Confidence:F3}");
            Console.WriteLine($"  • Enhanced Quality Level: {enhancedQuality.Level}");
            Console.WriteLine($"  • Enhanced Quality Score: {enhancedQuality.Score:F3}");
            Console.WriteLine($"  • Quality Factors:");
            Console.WriteLine($"    - Signal Confidence: {enhancedQuality.Factors.SignalConfidence:F3}");
            Console.WriteLine($"    - Market Alignment: {enhancedQuality.Factors.MarketAlignment:F3}");
            Console.WriteLine($"    - Timeframe Consistency: {enhancedQuality.Factors.TimeframeConsistency:F3}");
            
            // Verify medium quality signal gets appropriate rating
            if (enhancedQuality.Level == SignalQuality.Fair || enhancedQuality.Level == SignalQuality.Good)
            {
                Console.WriteLine("  ✅ Medium quality signal correctly identified");
            }
            else
            {
                Console.WriteLine("  ❌ Medium quality signal not properly rated");
            }
        }

        private void TestLowQualitySignal()
        {
            Console.WriteLine("\n🎯 Test 3: Low Quality Signal Analysis");
            
            var signal = CreateTestSignal(0.35m, SignalType.Long);
            var marketContext = CreatePoorMarketContext();
            
            var enhancedQuality = _qualityAnalyzer.AnalyzeSignalQuality(signal, marketContext);
            
            Console.WriteLine($"  • Signal Confidence: {signal.Confidence:F3}");
            Console.WriteLine($"  • Enhanced Quality Level: {enhancedQuality.Level}");
            Console.WriteLine($"  • Enhanced Quality Score: {enhancedQuality.Score:F3}");
            Console.WriteLine($"  • Quality Confidence: {enhancedQuality.Confidence:F3}");
            
            // Verify low quality signal gets appropriate rating
            if (enhancedQuality.Level <= SignalQuality.Fair && enhancedQuality.Score <= 0.5m)
            {
                Console.WriteLine("  ✅ Low quality signal correctly identified");
            }
            else
            {
                Console.WriteLine("  ❌ Low quality signal not properly rated");
            }
        }

        private void TestQualityTrendAnalysis()
        {
            Console.WriteLine("\n🎯 Test 4: Quality Trend Analysis");
            
            // Generate multiple signals to build trend history
            for (int i = 0; i < 25; i++)
            {
                var confidence = 0.5m + (i * 0.01m); // Gradually improving confidence
                var signal = CreateTestSignal(confidence, SignalType.Long);
                var marketContext = CreateOptimalMarketContext();
                
                var enhancedQuality = _qualityAnalyzer.AnalyzeSignalQuality(signal, marketContext);
                
                if (i == 24) // Last signal
                {
                    Console.WriteLine($"  • Final Signal Confidence: {confidence:F3}");
                    Console.WriteLine($"  • Quality Trend: {enhancedQuality.Trend}");
                    
                    if (enhancedQuality.Trend == QualityTrend.Improving)
                    {
                        Console.WriteLine("  ✅ Improving quality trend correctly detected");
                    }
                    else
                    {
                        Console.WriteLine("  ❌ Quality trend not properly detected");
                    }
                }
            }
        }

        private void TestAdaptiveThresholds()
        {
            Console.WriteLine("\n🎯 Test 5: Adaptive Threshold Testing");
            
            // Generate signals over time to trigger threshold adaptation
            var initialSignal = CreateTestSignal(0.7m, SignalType.Long);
            var marketContext = CreateOptimalMarketContext();
            
            var initialQuality = _qualityAnalyzer.AnalyzeSignalQuality(initialSignal, marketContext);
            Console.WriteLine($"  • Initial Quality Level: {initialQuality.Level}");
            
            // Simulate time passage and multiple signals to trigger threshold update
            // (In real implementation, this would happen over 10+ minutes)
            Console.WriteLine("  • Simulating threshold adaptation over time...");
            
            // Generate many signals to build history
            for (int i = 0; i < 50; i++)
            {
                var signal = CreateTestSignal(0.6m + (i * 0.005m), SignalType.Long);
                _qualityAnalyzer.AnalyzeSignalQuality(signal, marketContext);
            }
            
            var finalSignal = CreateTestSignal(0.7m, SignalType.Long);
            var finalQuality = _qualityAnalyzer.AnalyzeSignalQuality(finalSignal, marketContext);
            
            Console.WriteLine($"  • Final Quality Level: {finalQuality.Level}");
            Console.WriteLine($"  • Adaptive thresholds working: {finalQuality.Level != initialQuality.Level || finalQuality.Score != initialQuality.Score}");
        }

        private TradingSignal CreateTestSignal(decimal confidence, SignalType type)
        {
            return new TradingSignal
            {
                Timestamp = DateTime.UtcNow,
                CorrelationId = Guid.NewGuid().ToString("N")[..8],
                Type = type,
                Action = SignalAction.Entry,
                Confidence = confidence,
                CurrentPrice = 100.0m,
                Strength = 0.05m,
                PrimaryReason = "Test signal",
                VolumeComponent = new VolumeSignalComponent
                {
                    VolumeRatio = 1.5m,
                    VolumeConfidence = confidence,
                    IsVolumeSpike = true
                },
                DeltaComponent = new DeltaSignalComponent
                {
                    DeltaImbalance = 0.3m,
                    DeltaConfidence = confidence,
                    CVDTrend = type == SignalType.Long ? 50m : -50m
                }
            };
        }

        private MarketContext CreateOptimalMarketContext()
        {
            return new MarketContext
            {
                VolatilityRegime = VolatilityRegime.Normal,
                CVDTrend = 25m,
                IsOptimalTradingTime = true,
                CurrentSession = TradingSession.Overlap_EuropeanUS,
                DeltaImbalance = 0.4m,
                MarketConditions = new List<string> { "trending", "high_volume", "institutional_activity" }
            };
        }

        private MarketContext CreateMixedMarketContext()
        {
            return new MarketContext
            {
                VolatilityRegime = VolatilityRegime.High,
                CVDTrend = -10m,
                IsOptimalTradingTime = false,
                CurrentSession = TradingSession.Asian,
                DeltaImbalance = 0.2m,
                MarketConditions = new List<string> { "choppy", "medium_volume" }
            };
        }

        private MarketContext CreatePoorMarketContext()
        {
            return new MarketContext
            {
                VolatilityRegime = VolatilityRegime.VeryHigh,
                CVDTrend = 5m,
                IsOptimalTradingTime = false,
                CurrentSession = TradingSession.Asian,
                DeltaImbalance = 0.1m,
                MarketConditions = new List<string> { "ranging", "low_volume", "news_event" }
            };
        }
    }
}
