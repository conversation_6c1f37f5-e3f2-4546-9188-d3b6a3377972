using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models.Analysis;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Phase 2.2: Microstructure Component Health Monitoring
    /// Tracks individual component performance, detects failures, and monitors recovery
    /// </summary>
    public class MicrostructureHealthMonitor
    {
        private readonly Action<string> _logAction;
        private readonly Dictionary<MicrostructureComponent, ComponentHealthTracker> _componentTrackers;
        private readonly Queue<MicrostructureHealthSnapshot> _healthHistory;
        private readonly object _lockObject = new object();
        
        // Health monitoring configuration
        private const int MAX_HEALTH_HISTORY = 200;
        private const int HEALTH_EVALUATION_WINDOW = 20;
        private const decimal CRITICAL_FAILURE_THRESHOLD = 0.2m;
        private const decimal DEGRADED_PERFORMANCE_THRESHOLD = 0.5m;
        private const decimal RECOVERY_THRESHOLD = 0.7m;
        private const int CONSECUTIVE_FAILURES_FOR_CRITICAL = 5;
        private const int CONSECUTIVE_SUCCESSES_FOR_RECOVERY = 10;

        public MicrostructureHealthMonitor(Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _componentTrackers = new Dictionary<MicrostructureComponent, ComponentHealthTracker>();
            _healthHistory = new Queue<MicrostructureHealthSnapshot>();
            
            InitializeComponentTrackers();
        }

        /// <summary>
        /// Update component health based on latest scores
        /// </summary>
        public void UpdateComponentHealth(MicrostructureComponentScores scores, MarketContext context)
        {
            lock (_lockObject)
            {
                try
                {
                    var timestamp = DateTime.UtcNow;
                    
                    // Update individual component health
                    UpdateComponentTracker(MicrostructureComponent.Volume, scores.VolumeScore, timestamp);
                    UpdateComponentTracker(MicrostructureComponent.Delta, scores.DeltaScore, timestamp);
                    UpdateComponentTracker(MicrostructureComponent.Volatility, scores.VolatilityScore, timestamp);
                    UpdateComponentTracker(MicrostructureComponent.CVDAlignment, scores.CVDAlignmentScore, timestamp);
                    UpdateComponentTracker(MicrostructureComponent.OptimalTime, scores.OptimalTimeScore, timestamp);
                    
                    // Create health snapshot
                    var snapshot = CreateHealthSnapshot(timestamp, context);
                    _healthHistory.Enqueue(snapshot);
                    
                    // Maintain history size
                    while (_healthHistory.Count > MAX_HEALTH_HISTORY)
                    {
                        _healthHistory.Dequeue();
                    }
                    
                    // Check for health changes and log alerts
                    CheckForHealthAlerts(snapshot);
                }
                catch (Exception ex)
                {
                    _logAction($"❌ ERROR in MicrostructureHealthMonitor: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Get current health status for a specific component
        /// </summary>
        public ComponentHealth GetComponentHealth(MicrostructureComponent component)
        {
            lock (_lockObject)
            {
                if (_componentTrackers.ContainsKey(component))
                {
                    return _componentTrackers[component].CurrentHealth;
                }
                return ComponentHealth.Unknown;
            }
        }

        /// <summary>
        /// Get comprehensive health status for all components
        /// </summary>
        public MicrostructureHealthStatus GetCurrentHealthStatus()
        {
            lock (_lockObject)
            {
                var status = new MicrostructureHealthStatus
                {
                    Timestamp = DateTime.UtcNow,
                    VolumeHealth = GetComponentHealth(MicrostructureComponent.Volume),
                    DeltaHealth = GetComponentHealth(MicrostructureComponent.Delta),
                    VolatilityHealth = GetComponentHealth(MicrostructureComponent.Volatility),
                    CVDAlignmentHealth = GetComponentHealth(MicrostructureComponent.CVDAlignment),
                    OptimalTimeHealth = GetComponentHealth(MicrostructureComponent.OptimalTime)
                };
                
                // Determine overall health
                status.OverallHealth = DetermineOverallHealth(status);
                
                // Get component statistics
                status.ComponentStatistics = GetComponentStatistics();
                
                return status;
            }
        }

        /// <summary>
        /// Get health trend analysis
        /// </summary>
        public MicrostructureHealthTrend GetHealthTrend(MicrostructureComponent component, int windowSize = 20)
        {
            lock (_lockObject)
            {
                if (!_componentTrackers.ContainsKey(component))
                    return new MicrostructureHealthTrend { Component = component, Trend = HealthTrendDirection.Unknown };
                
                var tracker = _componentTrackers[component];
                var recentScores = tracker.ScoreHistory.TakeLast(windowSize).ToList();
                
                if (recentScores.Count < 5)
                    return new MicrostructureHealthTrend { Component = component, Trend = HealthTrendDirection.Insufficient_Data };
                
                // Calculate trend
                var trend = CalculateHealthTrend(recentScores);
                var volatility = CalculateHealthVolatility(recentScores);
                
                return new MicrostructureHealthTrend
                {
                    Component = component,
                    Trend = trend,
                    TrendStrength = CalculateTrendStrength(recentScores),
                    Volatility = volatility,
                    RecentAverage = recentScores.Average(),
                    WindowSize = recentScores.Count
                };
            }
        }

        /// <summary>
        /// Reset health monitoring for a specific component
        /// </summary>
        public void ResetComponentHealth(MicrostructureComponent component)
        {
            lock (_lockObject)
            {
                if (_componentTrackers.ContainsKey(component))
                {
                    _componentTrackers[component] = new ComponentHealthTracker
                    {
                        Component = component,
                        CurrentHealth = ComponentHealth.Healthy,
                        LastUpdate = DateTime.UtcNow,
                        ScoreHistory = new Queue<decimal>(),
                        ConsecutiveFailures = 0,
                        ConsecutiveSuccesses = 0,
                        TotalUpdates = 0,
                        FailureCount = 0
                    };
                    
                    _logAction($"🔄 Component health reset: {component}");
                }
            }
        }

        /// <summary>
        /// Initialize component trackers
        /// </summary>
        private void InitializeComponentTrackers()
        {
            foreach (MicrostructureComponent component in Enum.GetValues<MicrostructureComponent>())
            {
                _componentTrackers[component] = new ComponentHealthTracker
                {
                    Component = component,
                    CurrentHealth = ComponentHealth.Healthy,
                    LastUpdate = DateTime.UtcNow,
                    ScoreHistory = new Queue<decimal>(),
                    ConsecutiveFailures = 0,
                    ConsecutiveSuccesses = 0,
                    TotalUpdates = 0,
                    FailureCount = 0
                };
            }
        }

        /// <summary>
        /// Update individual component tracker
        /// </summary>
        private void UpdateComponentTracker(MicrostructureComponent component, decimal score, DateTime timestamp)
        {
            if (!_componentTrackers.ContainsKey(component)) return;
            
            var tracker = _componentTrackers[component];
            
            // Add score to history
            tracker.ScoreHistory.Enqueue(score);
            while (tracker.ScoreHistory.Count > HEALTH_EVALUATION_WINDOW * 2) // Keep extra history
            {
                tracker.ScoreHistory.Dequeue();
            }
            
            // Update counters
            tracker.TotalUpdates++;
            tracker.LastUpdate = timestamp;
            
            // Determine if this is a failure or success
            var isFailure = score < CRITICAL_FAILURE_THRESHOLD;
            var isDegraded = score < DEGRADED_PERFORMANCE_THRESHOLD;
            
            if (isFailure)
            {
                tracker.FailureCount++;
                tracker.ConsecutiveFailures++;
                tracker.ConsecutiveSuccesses = 0;
            }
            else
            {
                tracker.ConsecutiveFailures = 0;
                if (score >= RECOVERY_THRESHOLD)
                {
                    tracker.ConsecutiveSuccesses++;
                }
            }
            
            // Update health status
            var previousHealth = tracker.CurrentHealth;
            tracker.CurrentHealth = DetermineComponentHealth(tracker, score);
            
            // Log health changes
            if (tracker.CurrentHealth != previousHealth)
            {
                _logAction($"🏥 Component health changed: {component} {previousHealth} → {tracker.CurrentHealth} (Score: {score:F3})");
            }
        }

        /// <summary>
        /// Determine component health based on tracker state
        /// </summary>
        private ComponentHealth DetermineComponentHealth(ComponentHealthTracker tracker, decimal currentScore)
        {
            // Critical: Multiple consecutive failures or very low score
            if (tracker.ConsecutiveFailures >= CONSECUTIVE_FAILURES_FOR_CRITICAL || currentScore < CRITICAL_FAILURE_THRESHOLD)
            {
                return ComponentHealth.Critical;
            }
            
            // Degraded: Recent poor performance
            if (tracker.ScoreHistory.Count >= HEALTH_EVALUATION_WINDOW)
            {
                var recentAverage = tracker.ScoreHistory.TakeLast(HEALTH_EVALUATION_WINDOW).Average();
                if (recentAverage < DEGRADED_PERFORMANCE_THRESHOLD)
                {
                    return ComponentHealth.Degraded;
                }
            }
            
            // Recovering: Consecutive successes after problems
            if (tracker.ConsecutiveSuccesses >= CONSECUTIVE_SUCCESSES_FOR_RECOVERY && 
                tracker.FailureCount > 0)
            {
                return ComponentHealth.Recovering;
            }
            
            // Healthy: Good recent performance
            return ComponentHealth.Healthy;
        }

        /// <summary>
        /// Create health snapshot
        /// </summary>
        private MicrostructureHealthSnapshot CreateHealthSnapshot(DateTime timestamp, MarketContext context)
        {
            return new MicrostructureHealthSnapshot
            {
                Timestamp = timestamp,
                VolumeHealth = GetComponentHealth(MicrostructureComponent.Volume),
                DeltaHealth = GetComponentHealth(MicrostructureComponent.Delta),
                VolatilityHealth = GetComponentHealth(MicrostructureComponent.Volatility),
                CVDAlignmentHealth = GetComponentHealth(MicrostructureComponent.CVDAlignment),
                OptimalTimeHealth = GetComponentHealth(MicrostructureComponent.OptimalTime),
                MarketCondition = context.VolatilityRegime.ToString(),
                TradingSession = context.CurrentSession.ToString()
            };
        }

        /// <summary>
        /// Check for health alerts and log warnings
        /// </summary>
        private void CheckForHealthAlerts(MicrostructureHealthSnapshot snapshot)
        {
            var criticalComponents = new List<string>();
            var degradedComponents = new List<string>();
            
            CheckComponentForAlert(MicrostructureComponent.Volume, snapshot.VolumeHealth, criticalComponents, degradedComponents);
            CheckComponentForAlert(MicrostructureComponent.Delta, snapshot.DeltaHealth, criticalComponents, degradedComponents);
            CheckComponentForAlert(MicrostructureComponent.Volatility, snapshot.VolatilityHealth, criticalComponents, degradedComponents);
            CheckComponentForAlert(MicrostructureComponent.CVDAlignment, snapshot.CVDAlignmentHealth, criticalComponents, degradedComponents);
            CheckComponentForAlert(MicrostructureComponent.OptimalTime, snapshot.OptimalTimeHealth, criticalComponents, degradedComponents);
            
            // Log alerts
            if (criticalComponents.Count > 0)
            {
                _logAction($"🚨 CRITICAL HEALTH ALERT: {string.Join(", ", criticalComponents)} components failing");
            }
            
            if (degradedComponents.Count > 0)
            {
                _logAction($"⚠️ DEGRADED PERFORMANCE: {string.Join(", ", degradedComponents)} components underperforming");
            }
        }

        /// <summary>
        /// Check individual component for alert conditions
        /// </summary>
        private void CheckComponentForAlert(
            MicrostructureComponent component, 
            ComponentHealth health, 
            List<string> criticalComponents, 
            List<string> degradedComponents)
        {
            switch (health)
            {
                case ComponentHealth.Critical:
                    criticalComponents.Add(component.ToString());
                    break;
                case ComponentHealth.Degraded:
                    degradedComponents.Add(component.ToString());
                    break;
            }
        }

        /// <summary>
        /// Determine overall system health
        /// </summary>
        private ComponentHealth DetermineOverallHealth(MicrostructureHealthStatus status)
        {
            var healthValues = new[]
            {
                status.VolumeHealth,
                status.DeltaHealth,
                status.VolatilityHealth,
                status.CVDAlignmentHealth,
                status.OptimalTimeHealth
            };
            
            // If any component is critical, overall is critical
            if (healthValues.Any(h => h == ComponentHealth.Critical))
                return ComponentHealth.Critical;
            
            // If majority are degraded, overall is degraded
            var degradedCount = healthValues.Count(h => h == ComponentHealth.Degraded);
            if (degradedCount >= 3)
                return ComponentHealth.Degraded;
            
            // If any are recovering, overall is recovering
            if (healthValues.Any(h => h == ComponentHealth.Recovering))
                return ComponentHealth.Recovering;
            
            return ComponentHealth.Healthy;
        }

        /// <summary>
        /// Calculate health trend direction
        /// </summary>
        private HealthTrendDirection CalculateHealthTrend(List<decimal> scores)
        {
            if (scores.Count < 5) return HealthTrendDirection.Insufficient_Data;

            // Calculate linear trend
            var n = scores.Count;
            var sumX = n * (n - 1) / 2; // Sum of indices 0,1,2...n-1
            var sumY = scores.Sum();
            var sumXY = scores.Select((y, x) => x * (double)y).Sum();
            var sumX2 = Enumerable.Range(0, n).Sum(x => x * x);

            var slope = (n * sumXY - sumX * (double)sumY) / (n * sumX2 - sumX * sumX);

            // Determine trend direction
            if (slope > 0.01) return HealthTrendDirection.Improving;
            if (slope < -0.01) return HealthTrendDirection.Declining;
            return HealthTrendDirection.Stable;
        }

        /// <summary>
        /// Calculate trend strength
        /// </summary>
        private decimal CalculateTrendStrength(List<decimal> scores)
        {
            if (scores.Count < 3) return 0m;

            var first = scores.Take(scores.Count / 3).Average();
            var last = scores.TakeLast(scores.Count / 3).Average();

            return Math.Abs(last - first);
        }

        /// <summary>
        /// Calculate health volatility
        /// </summary>
        private decimal CalculateHealthVolatility(List<decimal> scores)
        {
            if (scores.Count < 2) return 0m;

            var mean = scores.Average();
            var variance = scores.Sum(s => (s - mean) * (s - mean)) / scores.Count;
            return (decimal)Math.Sqrt((double)variance);
        }

        private Dictionary<MicrostructureComponent, ComponentStatistics> GetComponentStatistics()
        {
            var stats = new Dictionary<MicrostructureComponent, ComponentStatistics>();

            foreach (var kvp in _componentTrackers)
            {
                var tracker = kvp.Value;
                stats[kvp.Key] = new ComponentStatistics
                {
                    TotalUpdates = tracker.TotalUpdates,
                    FailureCount = tracker.FailureCount,
                    FailureRate = tracker.TotalUpdates > 0 ? (decimal)tracker.FailureCount / tracker.TotalUpdates : 0m,
                    AverageScore = tracker.ScoreHistory.Count > 0 ? tracker.ScoreHistory.Average() : 0.5m,
                    ConsecutiveFailures = tracker.ConsecutiveFailures,
                    ConsecutiveSuccesses = tracker.ConsecutiveSuccesses,
                    LastUpdate = tracker.LastUpdate
                };
            }

            return stats;
        }
    }
}
