using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Analyzes market profile and session characteristics to optimize trading timing and session-based parameters
    /// </summary>
    public class MarketProfileAnalyzer : IMarketProfileAnalyzer
    {
        // Trading session time ranges (UTC)
        private static readonly Dictionary<TradingSession, (TimeSpan Start, TimeSpan End)> SessionTimes = new()
        {
            { TradingSession.Asian, (new TimeSpan(0, 0, 0), new TimeSpan(8, 0, 0)) },
            { TradingSession.European, (new TimeSpan(7, 0, 0), new TimeSpan(16, 0, 0)) },
            { TradingSession.US, (new TimeSpan(13, 0, 0), new TimeSpan(22, 0, 0)) },
            { TradingSession.Overlap_AsianEuropean, (new TimeSpan(7, 0, 0), new TimeSpan(8, 0, 0)) },
            { TradingSession.Overlap_EuropeanUS, (new TimeSpan(13, 0, 0), new TimeSpan(16, 0, 0)) }
        };

        /// <summary>
        /// Analyze market profile and session characteristics
        /// </summary>
        public MarketProfileCharacteristics AnalyzeMarketProfile(List<MarketDataPoint> marketData)
        {
            if (marketData == null || marketData.Count < 50)
                throw new ArgumentException("Need at least 50 bars for market profile analysis", nameof(marketData));

            var characteristics = new MarketProfileCharacteristics();

            // Analyze session activity
            var (asianScore, europeanScore, usScore) = AnalyzeSessionActivity(marketData);
            characteristics.AsianSessionActivity = asianScore;
            characteristics.EuropeanSessionActivity = europeanScore;
            characteristics.USSessionActivity = usScore;

            // Determine most active session
            characteristics.MostActiveSession = DetermineMostActiveSession(asianScore, europeanScore, usScore);

            // Analyze hourly activity patterns
            AnalyzeHourlyActivity(marketData, characteristics);

            // Calculate session volatility variation
            characteristics.SessionVolatilityVariation = CalculateSessionVolatilityVariation(marketData);

            // Identify optimal trading windows
            characteristics.OptimalTradingWindows = IdentifyOptimalTradingWindows(marketData);

            // Determine if this is a 24-hour market (crypto) vs traditional market
            characteristics.Is24HourMarket = DetermineIf24HourMarket(marketData);

            return characteristics;
        }

        /// <summary>
        /// Identify optimal trading windows based on activity patterns
        /// </summary>
        public List<TimeRange> IdentifyOptimalTradingWindows(List<MarketDataPoint> marketData)
        {
            var hourlyStats = CalculateHourlyStatistics(marketData);
            var optimalWindows = new List<TimeRange>();

            // Find hours with above-average activity
            var averageActivity = hourlyStats.Values.Average(s => s.ActivityScore);
            var threshold = averageActivity * 1.2m; // 20% above average

            var currentWindow = new List<int>();
            
            for (int hour = 0; hour < 24; hour++)
            {
                if (hourlyStats.ContainsKey(hour) && hourlyStats[hour].ActivityScore >= threshold)
                {
                    currentWindow.Add(hour);
                }
                else
                {
                    if (currentWindow.Count >= 2) // At least 2 consecutive hours
                    {
                        var window = CreateTimeRange(currentWindow, hourlyStats);
                        optimalWindows.Add(window);
                    }
                    currentWindow.Clear();
                }
            }

            // Handle wrap-around (e.g., 22, 23, 0, 1)
            if (currentWindow.Count >= 2)
            {
                var window = CreateTimeRange(currentWindow, hourlyStats);
                optimalWindows.Add(window);
            }

            return optimalWindows.OrderByDescending(w => w.ActivityScore).ToList();
        }

        /// <summary>
        /// Analyze session-based performance patterns
        /// </summary>
        public (decimal AsianScore, decimal EuropeanScore, decimal USScore) AnalyzeSessionActivity(List<MarketDataPoint> marketData)
        {
            var sessionStats = new Dictionary<TradingSession, List<decimal>>();
            
            foreach (var session in new[] { TradingSession.Asian, TradingSession.European, TradingSession.US })
            {
                sessionStats[session] = new List<decimal>();
            }

            foreach (var bar in marketData)
            {
                var session = GetCurrentSession(bar.Timestamp);
                if (sessionStats.ContainsKey(session))
                {
                    var activity = CalculateBarActivity(bar);
                    sessionStats[session].Add(activity);
                }
            }

            var asianScore = sessionStats[TradingSession.Asian].Count > 0 ? sessionStats[TradingSession.Asian].Average() : 0;
            var europeanScore = sessionStats[TradingSession.European].Count > 0 ? sessionStats[TradingSession.European].Average() : 0;
            var usScore = sessionStats[TradingSession.US].Count > 0 ? sessionStats[TradingSession.US].Average() : 0;

            // Normalize scores to 0-1 scale
            var maxScore = Math.Max(asianScore, Math.Max(europeanScore, usScore));
            if (maxScore > 0)
            {
                asianScore /= maxScore;
                europeanScore /= maxScore;
                usScore /= maxScore;
            }

            return (asianScore, europeanScore, usScore);
        }

        /// <summary>
        /// Get current session information
        /// </summary>
        public TradingSession GetCurrentSession(DateTime currentTime)
        {
            var utcTime = currentTime.Kind == DateTimeKind.Utc ? currentTime : currentTime.ToUniversalTime();
            var timeOfDay = utcTime.TimeOfDay;

            // Check for overlaps first (more specific)
            if (IsTimeInRange(timeOfDay, SessionTimes[TradingSession.Overlap_AsianEuropean]))
                return TradingSession.Overlap_AsianEuropean;
            
            if (IsTimeInRange(timeOfDay, SessionTimes[TradingSession.Overlap_EuropeanUS]))
                return TradingSession.Overlap_EuropeanUS;

            // Check individual sessions
            if (IsTimeInRange(timeOfDay, SessionTimes[TradingSession.Asian]))
                return TradingSession.Asian;
            
            if (IsTimeInRange(timeOfDay, SessionTimes[TradingSession.European]))
                return TradingSession.European;
            
            if (IsTimeInRange(timeOfDay, SessionTimes[TradingSession.US]))
                return TradingSession.US;

            // For crypto markets, default to 24H
            return TradingSession.Crypto24H;
        }

        /// <summary>
        /// Check if current time is within optimal trading window
        /// </summary>
        public bool IsOptimalTradingTime(DateTime currentTime, List<TimeRange> optimalWindows)
        {
            if (optimalWindows == null || optimalWindows.Count == 0)
                return true; // If no windows defined, assume always optimal

            var utcTime = currentTime.Kind == DateTimeKind.Utc ? currentTime : currentTime.ToUniversalTime();
            var timeOfDay = utcTime.TimeOfDay;

            return optimalWindows.Any(window => IsTimeInRange(timeOfDay, (window.StartTime, window.EndTime)));
        }

        /// <summary>
        /// Get current real-time market profile analysis state
        /// </summary>
        public MarketProfileAnalysisState GetCurrentMarketProfileState(DateTime currentTime, List<MarketDataPoint> recentData)
        {
            if (recentData == null || recentData.Count < 10)
            {
                return new MarketProfileAnalysisState
                {
                    CurrentSession = TradingSession.Unknown,
                    IsOptimalTradingTime = false,
                    MarketProfileConfidence = 0.3m,
                    MarketCondition = "Insufficient Data"
                };
            }

            var currentSession = GetCurrentSession(currentTime);
            var (sessionActivity, isOptimal) = AnalyzeCurrentSessionActivity(currentTime, recentData);
            var (condition, confidence) = DetermineMarketCondition(currentTime, recentData);

            // Calculate session activities from recent data
            var (asianActivity, europeanActivity, usActivity) = AnalyzeSessionActivity(recentData);

            // Determine time in current session
            var timeInSession = CalculateTimeInCurrentSession(currentTime, currentSession);

            // Analyze activity hours
            var hourlyStats = CalculateHourlyStatistics(recentData);
            var currentHour = currentTime.Hour;
            var highActivityHours = hourlyStats.Where(kvp => kvp.Value.ActivityScore > hourlyStats.Values.Average(s => s.ActivityScore) * 1.3m)
                                              .Select(kvp => kvp.Key).ToList();
            var lowActivityHours = hourlyStats.Where(kvp => kvp.Value.ActivityScore < hourlyStats.Values.Average(s => s.ActivityScore) * 0.7m)
                                             .Select(kvp => kvp.Key).ToList();

            // Calculate session volatility variation
            var sessionVolatilityVariation = CalculateSessionVolatilityVariation(recentData);

            // Determine if in optimal window
            var optimalWindows = IdentifyOptimalTradingWindows(recentData);
            var isInOptimalWindow = IsOptimalTradingTime(currentTime, optimalWindows);
            var optimalWindowConfidence = CalculateOptimalWindowConfidence(currentTime, optimalWindows);

            return new MarketProfileAnalysisState
            {
                LastUpdate = DateTime.UtcNow,
                CurrentSession = currentSession,
                IsOptimalTradingTime = isOptimal,
                CurrentSessionActivity = sessionActivity,
                TimeInCurrentSession = timeInSession,
                AsianSessionActivity = asianActivity,
                EuropeanSessionActivity = europeanActivity,
                USSessionActivity = usActivity,
                SessionVolatilityVariation = sessionVolatilityVariation,
                CurrentHighActivityHours = highActivityHours,
                CurrentLowActivityHours = lowActivityHours,
                IsInOptimalWindow = isInOptimalWindow,
                OptimalWindowConfidence = optimalWindowConfidence,
                Is24HourMarket = DetermineIf24HourMarket(recentData),
                MarketCondition = condition,
                MarketProfileConfidence = confidence
            };
        }

        /// <summary>
        /// Analyze real-time session activity
        /// </summary>
        public (TradingSession CurrentSession, decimal ActivityLevel, bool IsOptimal) AnalyzeRealtimeSessionActivity(DateTime currentTime, List<MarketDataPoint> recentData)
        {
            if (recentData == null || recentData.Count < 5)
                return (TradingSession.Unknown, 0, false);

            var currentSession = GetCurrentSession(currentTime);
            var (activityLevel, isOptimal) = AnalyzeCurrentSessionActivity(currentTime, recentData);

            return (currentSession, activityLevel, isOptimal);
        }

        /// <summary>
        /// Calculate market condition based on current activity
        /// </summary>
        public (string Condition, decimal Confidence) DetermineMarketCondition(DateTime currentTime, List<MarketDataPoint> recentData)
        {
            if (recentData == null || recentData.Count < 10)
                return ("Unknown", 0.3m);

            var currentSession = GetCurrentSession(currentTime);
            var recentActivity = recentData.TakeLast(5).Average(b => CalculateBarActivity(b));
            var overallActivity = recentData.Average(b => CalculateBarActivity(b));
            var activityRatio = overallActivity > 0 ? recentActivity / overallActivity : 1;

            string condition;
            decimal confidence;

            // Determine condition based on session and activity
            if (currentSession == TradingSession.Overlap_AsianEuropean || currentSession == TradingSession.Overlap_EuropeanUS)
            {
                condition = "Session Overlap";
                confidence = 0.9m;
            }
            else if (activityRatio > 1.5m)
            {
                condition = "Active";
                confidence = Math.Min(0.95m, activityRatio / 2.0m);
            }
            else if (activityRatio < 0.6m)
            {
                condition = "Quiet";
                confidence = Math.Min(0.9m, (1.0m - activityRatio) * 1.5m);
            }
            else if (currentSession == TradingSession.Asian || currentSession == TradingSession.European || currentSession == TradingSession.US)
            {
                condition = "Normal Session";
                confidence = 0.8m;
            }
            else
            {
                condition = "Transition";
                confidence = 0.6m;
            }

            return (condition, Math.Max(0.3m, confidence));
        }

        #region Private Methods

        private void AnalyzeHourlyActivity(List<MarketDataPoint> marketData, MarketProfileCharacteristics characteristics)
        {
            var hourlyStats = CalculateHourlyStatistics(marketData);
            
            // Find high and low activity hours
            var averageActivity = hourlyStats.Values.Average(s => s.ActivityScore);
            
            characteristics.HighActivityHours = hourlyStats
                .Where(kvp => kvp.Value.ActivityScore > averageActivity * 1.3m)
                .Select(kvp => kvp.Key)
                .OrderBy(h => h)
                .ToList();

            characteristics.LowActivityHours = hourlyStats
                .Where(kvp => kvp.Value.ActivityScore < averageActivity * 0.7m)
                .Select(kvp => kvp.Key)
                .OrderBy(h => h)
                .ToList();
        }

        private Dictionary<int, HourlyStatistics> CalculateHourlyStatistics(List<MarketDataPoint> marketData)
        {
            var hourlyData = new Dictionary<int, List<MarketDataPoint>>();
            
            // Group data by hour
            foreach (var bar in marketData)
            {
                var utcTime = bar.Timestamp.Kind == DateTimeKind.Utc ? bar.Timestamp : bar.Timestamp.ToUniversalTime();
                var hour = utcTime.Hour;
                
                if (!hourlyData.ContainsKey(hour))
                    hourlyData[hour] = new List<MarketDataPoint>();
                
                hourlyData[hour].Add(bar);
            }

            // Calculate statistics for each hour
            var hourlyStats = new Dictionary<int, HourlyStatistics>();
            
            foreach (var kvp in hourlyData)
            {
                var hour = kvp.Key;
                var bars = kvp.Value;
                
                var avgVolume = bars.Average(b => b.Volume);
                var avgVolatility = bars.Average(b => CalculateBarVolatility(b));
                var avgDelta = bars.Average(b => Math.Abs(b.Delta));
                
                // Combined activity score
                var activityScore = (avgVolume * 0.4m + avgVolatility * 100 * 0.3m + avgDelta * 0.3m);
                
                hourlyStats[hour] = new HourlyStatistics
                {
                    Hour = hour,
                    AverageVolume = avgVolume,
                    AverageVolatility = avgVolatility,
                    AverageDelta = avgDelta,
                    ActivityScore = activityScore,
                    BarCount = bars.Count
                };
            }

            return hourlyStats;
        }

        private decimal CalculateSessionVolatilityVariation(List<MarketDataPoint> marketData)
        {
            var sessionVolatilities = new Dictionary<TradingSession, List<decimal>>();
            
            foreach (var session in new[] { TradingSession.Asian, TradingSession.European, TradingSession.US })
            {
                sessionVolatilities[session] = new List<decimal>();
            }

            foreach (var bar in marketData)
            {
                var session = GetCurrentSession(bar.Timestamp);
                if (sessionVolatilities.ContainsKey(session))
                {
                    var volatility = CalculateBarVolatility(bar);
                    sessionVolatilities[session].Add(volatility);
                }
            }

            // Calculate average volatility for each session
            var sessionAverages = sessionVolatilities
                .Where(kvp => kvp.Value.Count > 0)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Average());

            if (sessionAverages.Count < 2)
                return 0; // Not enough data

            var maxVolatility = sessionAverages.Values.Max();
            var minVolatility = sessionAverages.Values.Min();
            var avgVolatility = sessionAverages.Values.Average();

            // Return coefficient of variation
            return avgVolatility > 0 ? (maxVolatility - minVolatility) / avgVolatility : 0;
        }

        private TradingSession DetermineMostActiveSession(decimal asianScore, decimal europeanScore, decimal usScore)
        {
            var maxScore = Math.Max(asianScore, Math.Max(europeanScore, usScore));
            
            if (Math.Abs(maxScore - asianScore) < 0.01m)
                return TradingSession.Asian;
            
            if (Math.Abs(maxScore - europeanScore) < 0.01m)
                return TradingSession.European;
            
            if (Math.Abs(maxScore - usScore) < 0.01m)
                return TradingSession.US;

            return TradingSession.Crypto24H; // Default for crypto markets
        }

        private bool DetermineIf24HourMarket(List<MarketDataPoint> marketData)
        {
            // Check if there's significant activity outside traditional trading hours
            var weekendBars = marketData.Where(b => 
                b.Timestamp.DayOfWeek == DayOfWeek.Saturday || 
                b.Timestamp.DayOfWeek == DayOfWeek.Sunday).ToList();

            var weekdayBars = marketData.Where(b => 
                b.Timestamp.DayOfWeek != DayOfWeek.Saturday && 
                b.Timestamp.DayOfWeek != DayOfWeek.Sunday).ToList();

            if (weekendBars.Count == 0 || weekdayBars.Count == 0)
                return true; // Assume crypto if we can't determine

            var weekendActivity = weekendBars.Average(b => CalculateBarActivity(b));
            var weekdayActivity = weekdayBars.Average(b => CalculateBarActivity(b));

            // If weekend activity is more than 30% of weekday activity, consider it 24-hour market
            return weekdayActivity > 0 && (weekendActivity / weekdayActivity) > 0.3m;
        }

        private TimeRange CreateTimeRange(List<int> hours, Dictionary<int, HourlyStatistics> hourlyStats)
        {
            var startHour = hours.Min();
            var endHour = hours.Max();
            var avgActivityScore = hours.Average(h => hourlyStats.ContainsKey(h) ? hourlyStats[h].ActivityScore : 0);

            var description = $"High Activity Window ({startHour:D2}:00 - {endHour + 1:D2}:00 UTC)";

            return new TimeRange
            {
                StartTime = new TimeSpan(startHour, 0, 0),
                EndTime = new TimeSpan(endHour + 1, 0, 0), // End of the hour
                ActivityScore = avgActivityScore,
                Description = description
            };
        }

        private decimal CalculateBarActivity(MarketDataPoint bar)
        {
            var volatility = CalculateBarVolatility(bar);
            var volumeComponent = bar.Volume;
            var deltaComponent = Math.Abs(bar.Delta);

            // Normalize and combine components
            return volatility * 100 + volumeComponent * 0.001m + deltaComponent * 0.001m;
        }

        private decimal CalculateBarVolatility(MarketDataPoint bar)
        {
            if (bar.Open <= 0)
                return 0;

            return Math.Abs(bar.Close - bar.Open) / bar.Open;
        }

        private bool IsTimeInRange(TimeSpan timeOfDay, (TimeSpan Start, TimeSpan End) range)
        {
            if (range.Start <= range.End)
            {
                // Normal range (e.g., 9:00 - 17:00)
                return timeOfDay >= range.Start && timeOfDay < range.End;
            }
            else
            {
                // Overnight range (e.g., 22:00 - 06:00)
                return timeOfDay >= range.Start || timeOfDay < range.End;
            }
        }

        private (decimal ActivityLevel, bool IsOptimal) AnalyzeCurrentSessionActivity(DateTime currentTime, List<MarketDataPoint> recentData)
        {
            var currentSession = GetCurrentSession(currentTime);
            var sessionBars = recentData.Where(b => GetCurrentSession(b.Timestamp) == currentSession).ToList();

            if (sessionBars.Count == 0)
                return (0, false);

            var sessionActivity = sessionBars.Average(b => CalculateBarActivity(b));
            var overallActivity = recentData.Average(b => CalculateBarActivity(b));
            var activityLevel = overallActivity > 0 ? sessionActivity / overallActivity : 1;

            // Consider optimal if activity is above average and in a major session
            var isOptimal = activityLevel > 1.0m &&
                           (currentSession == TradingSession.European ||
                            currentSession == TradingSession.US ||
                            currentSession == TradingSession.Overlap_EuropeanUS);

            return (activityLevel, isOptimal);
        }

        private TimeSpan CalculateTimeInCurrentSession(DateTime currentTime, TradingSession currentSession)
        {
            if (!SessionTimes.ContainsKey(currentSession))
                return TimeSpan.Zero;

            var utcTime = currentTime.Kind == DateTimeKind.Utc ? currentTime : currentTime.ToUniversalTime();
            var timeOfDay = utcTime.TimeOfDay;
            var sessionRange = SessionTimes[currentSession];

            if (sessionRange.Start <= sessionRange.End)
            {
                // Normal range
                if (timeOfDay >= sessionRange.Start && timeOfDay < sessionRange.End)
                    return timeOfDay - sessionRange.Start;
            }
            else
            {
                // Overnight range
                if (timeOfDay >= sessionRange.Start)
                    return timeOfDay - sessionRange.Start;
                else if (timeOfDay < sessionRange.End)
                    return TimeSpan.FromDays(1) - sessionRange.Start + timeOfDay;
            }

            return TimeSpan.Zero;
        }

        private decimal CalculateOptimalWindowConfidence(DateTime currentTime, List<TimeRange> optimalWindows)
        {
            if (optimalWindows == null || optimalWindows.Count == 0)
                return 0.5m;

            var utcTime = currentTime.Kind == DateTimeKind.Utc ? currentTime : currentTime.ToUniversalTime();
            var timeOfDay = utcTime.TimeOfDay;

            var currentWindow = optimalWindows.FirstOrDefault(window =>
                IsTimeInRange(timeOfDay, (window.StartTime, window.EndTime)));

            if (currentWindow != null)
            {
                // In optimal window - confidence based on activity score
                var maxActivityScore = optimalWindows.Max(w => w.ActivityScore);
                return maxActivityScore > 0 ? Math.Min(0.95m, currentWindow.ActivityScore / maxActivityScore) : 0.8m;
            }

            // Not in optimal window - lower confidence
            return 0.3m;
        }

        #endregion

        #region Helper Classes

        private class HourlyStatistics
        {
            public int Hour { get; set; }
            public decimal AverageVolume { get; set; }
            public decimal AverageVolatility { get; set; }
            public decimal AverageDelta { get; set; }
            public decimal ActivityScore { get; set; }
            public int BarCount { get; set; }
        }

        #endregion
    }
}
