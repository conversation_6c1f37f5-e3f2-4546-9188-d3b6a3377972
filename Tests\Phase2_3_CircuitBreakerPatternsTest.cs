using System;
using System.Collections.Generic;
using System.Threading;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;
using SmartVolumeStrategy.Core.Strategy;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Tests
{
    /// <summary>
    /// Phase 2.3 Test: Circuit Breaker Patterns
    /// Tests circuit breaker functionality, graceful degradation, and integration with existing systems
    /// </summary>
    public class Phase2_3_CircuitBreakerPatternsTest
    {
        private readonly List<string> _testLogs;
        private readonly CircuitBreakerManager _circuitBreakerManager;
        private readonly ComponentHealthTracker _healthTracker;
        private readonly GracefulDegradationManager _degradationManager;
        private readonly SignalSynthesizer _signalSynthesizer;

        public Phase2_3_CircuitBreakerPatternsTest()
        {
            _testLogs = new List<string>();
            _circuitBreakerManager = new CircuitBreakerManager(log => _testLogs.Add(log));
            _healthTracker = _circuitBreakerManager.GetComponentHealthTracker();
            _degradationManager = _circuitBreakerManager.GetDegradationManager();
            _signalSynthesizer = new SignalSynthesizer(log => _testLogs.Add(log));
            
            // Integrate circuit breaker with signal synthesizer
            _signalSynthesizer.SetCircuitBreakerManager(_circuitBreakerManager);
        }

        /// <summary>
        /// Run comprehensive Phase 2.3 tests
        /// </summary>
        public void RunCircuitBreakerPatternsTests()
        {
            Console.WriteLine("🧪 PHASE 2.3 TEST: Circuit Breaker Patterns");
            Console.WriteLine(new string('=', 60));

            // Test 1: Circuit breaker state transitions
            TestCircuitBreakerStateTransitions();

            // Test 2: Component health tracking
            TestComponentHealthTracking();

            // Test 3: Graceful degradation modes
            TestGracefulDegradationModes();

            // Test 4: Integration with signal synthesis
            TestSignalSynthesisIntegration();

            // Test 5: Recovery mechanisms
            TestRecoveryMechanisms();

            // Test 6: Emergency mode activation
            TestEmergencyModeActivation();

            // Test 7: Integration with Phase 2.1 and 2.2
            TestIntegrationWithPreviousPhases();

            Console.WriteLine("\n✅ Phase 2.3 Circuit Breaker Patterns Tests Completed");
            Console.WriteLine($"📊 Total test logs generated: {_testLogs.Count}");
        }

        private void TestCircuitBreakerStateTransitions()
        {
            Console.WriteLine("\n🎯 Test 1: Circuit Breaker State Transitions");
            
            var component = StrategyComponent.SignalGeneration;
            
            // Initial state should be Closed
            var initialAllowed = _circuitBreakerManager.IsOperationAllowed(component);
            Console.WriteLine($"  • Initial state allows operations: {initialAllowed}");
            
            // Simulate failures to trigger Open state
            for (int i = 0; i < 6; i++)
            {
                _circuitBreakerManager.RecordOperation(component, false, $"Test failure {i + 1}", FailureSeverity.High);
            }
            
            var afterFailuresAllowed = _circuitBreakerManager.IsOperationAllowed(component);
            Console.WriteLine($"  • After failures, operations allowed: {afterFailuresAllowed}");
            
            // Wait for timeout and check Half-Open transition
            Thread.Sleep(100); // Simulate timeout
            var afterTimeoutAllowed = _circuitBreakerManager.IsOperationAllowed(component);
            Console.WriteLine($"  • After timeout, operations allowed: {afterTimeoutAllowed}");
            
            // Simulate successful operations to close circuit breaker
            for (int i = 0; i < 4; i++)
            {
                _circuitBreakerManager.RecordOperation(component, true, $"Test success {i + 1}");
            }
            
            var afterRecoveryAllowed = _circuitBreakerManager.IsOperationAllowed(component);
            Console.WriteLine($"  • After recovery, operations allowed: {afterRecoveryAllowed}");
            
            if (initialAllowed && !afterFailuresAllowed && afterRecoveryAllowed)
            {
                Console.WriteLine("  ✅ Circuit breaker state transitions working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Circuit breaker state transitions not working as expected");
            }
        }

        private void TestComponentHealthTracking()
        {
            Console.WriteLine("\n🎯 Test 2: Component Health Tracking");
            
            var component = StrategyComponent.DataFeeds;
            
            // Record mixed operations
            for (int i = 0; i < 20; i++)
            {
                var isSuccess = i % 3 != 0; // 2/3 success rate
                var responseTime = TimeSpan.FromMilliseconds(50 + (i * 10));
                
                _healthTracker.UpdateComponentHealth(component, isSuccess, 
                    isSuccess ? "Data received" : "Connection timeout", 
                    isSuccess ? FailureSeverity.Low : FailureSeverity.Medium);
                    
                _healthTracker.UpdateResponseTime(component, responseTime);
            }
            
            var healthMetrics = _healthTracker.GetComponentHealth(component);
            var healthTrend = _healthTracker.GetHealthTrend(component);
            var overallHealth = _healthTracker.GetOverallSystemHealth();
            
            Console.WriteLine($"  • Component Health Score: {healthMetrics.HealthScore:P1}");
            Console.WriteLine($"  • Performance Score: {healthMetrics.PerformanceScore:P1}");
            Console.WriteLine($"  • Error Rate: {healthMetrics.ErrorRate:P1}");
            Console.WriteLine($"  • Average Response Time: {healthMetrics.AverageResponseTime.TotalMilliseconds:F0}ms");
            Console.WriteLine($"  • Health Trend: {healthTrend.Direction} (Strength: {healthTrend.Strength:F3})");
            Console.WriteLine($"  • Overall System Health: {overallHealth:P1}");
            
            if (healthMetrics.HealthScore > 0.5m && healthMetrics.ErrorRate < 0.5m)
            {
                Console.WriteLine("  ✅ Component health tracking working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Component health tracking not working as expected");
            }
        }

        private void TestGracefulDegradationModes()
        {
            Console.WriteLine("\n🎯 Test 3: Graceful Degradation Modes");
            
            var initialLevel = _degradationManager.GetCurrentDegradationLevel();
            Console.WriteLine($"  • Initial degradation level: {initialLevel}");
            
            // Simulate component failure to trigger degradation
            _degradationManager.OnComponentFailure(StrategyComponent.MicrostructureFiltering, FailureSeverity.Medium);
            var reducedLevel = _degradationManager.GetCurrentDegradationLevel();
            Console.WriteLine($"  • After component failure: {reducedLevel}");
            
            // Simulate critical component failure
            _degradationManager.OnComponentFailure(StrategyComponent.PositionManagement, FailureSeverity.Critical);
            var emergencyLevel = _degradationManager.GetCurrentDegradationLevel();
            Console.WriteLine($"  • After critical failure: {emergencyLevel}");
            
            // Check feature availability
            var signalGenAvailable = _degradationManager.IsFeatureAvailable("SignalGeneration");
            var patternDetectionAvailable = _degradationManager.IsFeatureAvailable("PatternDetection");
            var positionMgmtAvailable = _degradationManager.IsFeatureAvailable("PositionManagement");
            
            Console.WriteLine($"  • Signal Generation Available: {signalGenAvailable}");
            Console.WriteLine($"  • Pattern Detection Available: {patternDetectionAvailable}");
            Console.WriteLine($"  • Position Management Available: {positionMgmtAvailable}");
            
            // Test recovery
            _degradationManager.OnComponentRecovery(StrategyComponent.PositionManagement);
            _degradationManager.OnComponentRecovery(StrategyComponent.MicrostructureFiltering);
            var recoveredLevel = _degradationManager.GetCurrentDegradationLevel();
            Console.WriteLine($"  • After recovery: {recoveredLevel}");
            
            if (initialLevel == DegradationLevel.Normal && 
                emergencyLevel == DegradationLevel.Emergency && 
                recoveredLevel < emergencyLevel)
            {
                Console.WriteLine("  ✅ Graceful degradation working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Graceful degradation not working as expected");
            }
        }

        private void TestSignalSynthesisIntegration()
        {
            Console.WriteLine("\n🎯 Test 4: Signal Synthesis Integration");
            
            // Initialize signal synthesizer
            _signalSynthesizer.Initialize();
            
            // Test normal operation
            var multiTimeframeState = CreateTestMultiTimeframeState();
            var marketContext = CreateTestMarketContext();
            
            var normalSignal = _signalSynthesizer.SynthesizeSignal(multiTimeframeState, marketContext);
            Console.WriteLine($"  • Normal operation signal: {normalSignal?.Type ?? SignalType.None}");
            
            // Force circuit breaker open
            _circuitBreakerManager.ForceOpen(StrategyComponent.SignalGeneration, "Test circuit breaker blocking");
            
            var blockedSignal = _signalSynthesizer.SynthesizeSignal(multiTimeframeState, marketContext);
            Console.WriteLine($"  • Blocked operation signal: {blockedSignal?.Type ?? SignalType.None}");
            
            // Force emergency mode
            _degradationManager.ForceDegradationLevel(DegradationLevel.Emergency, "Test emergency mode");
            
            var emergencySignal = _signalSynthesizer.SynthesizeSignal(multiTimeframeState, marketContext);
            Console.WriteLine($"  • Emergency mode signal: {emergencySignal?.Type ?? SignalType.None}");
            
            // Restore normal operation
            _circuitBreakerManager.ForceClose(StrategyComponent.SignalGeneration, "Test recovery");
            _degradationManager.ForceDegradationLevel(DegradationLevel.Normal, "Test recovery");
            
            var restoredSignal = _signalSynthesizer.SynthesizeSignal(multiTimeframeState, marketContext);
            Console.WriteLine($"  • Restored operation signal: {restoredSignal?.Type ?? SignalType.None}");
            
            if (normalSignal != null && blockedSignal == null && emergencySignal == null && restoredSignal != null)
            {
                Console.WriteLine("  ✅ Signal synthesis integration working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Signal synthesis integration not working as expected");
            }
        }

        private void TestRecoveryMechanisms()
        {
            Console.WriteLine("\n🎯 Test 5: Recovery Mechanisms");
            
            var component = StrategyComponent.AnalysisEngines;
            
            // Trigger circuit breaker
            for (int i = 0; i < 6; i++)
            {
                _circuitBreakerManager.RecordOperation(component, false, "Analysis timeout", FailureSeverity.High);
            }
            
            var stats = _circuitBreakerManager.GetComponentStatistics(component);
            Console.WriteLine($"  • Circuit breaker activations: {stats.TotalActivations}");
            
            // Simulate recovery attempts
            Thread.Sleep(50); // Wait for timeout
            
            for (int i = 0; i < 5; i++)
            {
                _circuitBreakerManager.RecordOperation(component, true, "Analysis completed successfully");
            }
            
            var recoveryStats = _circuitBreakerManager.GetComponentStatistics(component);
            Console.WriteLine($"  • Successful recoveries: {recoveryStats.TotalRecoveries}");
            Console.WriteLine($"  • Availability: {recoveryStats.AvailabilityPercentage:P1}");
            
            var isOperationAllowed = _circuitBreakerManager.IsOperationAllowed(component);
            Console.WriteLine($"  • Operations allowed after recovery: {isOperationAllowed}");
            
            if (stats.TotalActivations > 0 && recoveryStats.TotalRecoveries > 0 && isOperationAllowed)
            {
                Console.WriteLine("  ✅ Recovery mechanisms working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Recovery mechanisms not working as expected");
            }
        }

        private void TestEmergencyModeActivation()
        {
            Console.WriteLine("\n🎯 Test 6: Emergency Mode Activation");
            
            var systemStatus = _circuitBreakerManager.GetSystemStatus();
            Console.WriteLine($"  • Initial system health: {systemStatus.OverallSystemHealth:P1}");
            
            // Trigger emergency mode with critical component failure
            _degradationManager.OnComponentFailure(StrategyComponent.RiskControls, FailureSeverity.Critical);
            
            var emergencyStatus = _circuitBreakerManager.GetSystemStatus();
            var degradationStatus = _degradationManager.GetDegradationStatus();
            
            Console.WriteLine($"  • Emergency system health: {emergencyStatus.OverallSystemHealth:P1}");
            Console.WriteLine($"  • Emergency mode active: {degradationStatus.IsEmergencyMode}");
            Console.WriteLine($"  • Failed components: {emergencyStatus.FailedComponents}");
            Console.WriteLine($"  • Active alerts: {emergencyStatus.ActiveAlerts.Count}");
            
            var availableFeatures = _degradationManager.GetAvailableFeatures();
            var disabledFeatures = _degradationManager.GetDisabledFeatures();
            
            Console.WriteLine($"  • Available features: {availableFeatures.Count}");
            Console.WriteLine($"  • Disabled features: {disabledFeatures.Count}");
            
            if (degradationStatus.IsEmergencyMode && 
                emergencyStatus.OverallSystemHealth < systemStatus.OverallSystemHealth &&
                disabledFeatures.Count > availableFeatures.Count)
            {
                Console.WriteLine("  ✅ Emergency mode activation working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Emergency mode activation not working as expected");
            }
        }

        private void TestIntegrationWithPreviousPhases()
        {
            Console.WriteLine("\n🎯 Test 7: Integration with Phase 2.1 and 2.2");
            
            // Test microstructure health integration
            var microHealthStatus = _signalSynthesizer.GetMicrostructureHealthStatus();
            Console.WriteLine($"  • Microstructure overall health: {microHealthStatus.OverallHealth}");
            
            // Test system resilience status
            var resilienceStatus = _signalSynthesizer.GetSystemResilienceStatus();
            Console.WriteLine($"  • System resilience health: {resilienceStatus.OverallSystemHealth:P1}");
            Console.WriteLine($"  • Current degradation level: {resilienceStatus.DegradationLevel}");
            
            // Test feature availability checks
            var signalQualityAvailable = _signalSynthesizer.IsFeatureAvailable("EnhancedSignalQuality");
            var microFilterAvailable = _signalSynthesizer.IsFeatureAvailable("AdvancedMicrostructureFilter");
            var circuitBreakersAvailable = _signalSynthesizer.IsFeatureAvailable("CircuitBreakers");
            
            Console.WriteLine($"  • Enhanced Signal Quality Available: {signalQualityAvailable}");
            Console.WriteLine($"  • Advanced Microstructure Filter Available: {microFilterAvailable}");
            Console.WriteLine($"  • Circuit Breakers Available: {circuitBreakersAvailable}");
            
            if (microHealthStatus != null && 
                resilienceStatus.OverallSystemHealth > 0 &&
                signalQualityAvailable && microFilterAvailable && circuitBreakersAvailable)
            {
                Console.WriteLine("  ✅ Integration with previous phases working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Integration with previous phases not working as expected");
            }
        }

        // Helper methods for creating test data
        private MultiTimeframeAnalysisState CreateTestMultiTimeframeState()
        {
            return new MultiTimeframeAnalysisState
            {
                TimeframeStates = new Dictionary<Timeframe, AnalysisState>
                {
                    [Timeframe.M1] = new AnalysisState
                    {
                        Volume = new VolumeAnalysisState { VolumeConfidence = 0.8m, IsVolumeSpikeActive = true },
                        DeltaFlow = new DeltaAnalysisState { DeltaConfidence = 0.75m, CurrentBias = DeltaBias.BuyPressure }
                    }
                }
            };
        }

        private MarketContext CreateTestMarketContext()
        {
            return new MarketContext
            {
                VolatilityRegime = VolatilityRegime.Normal,
                CVDTrend = 25m,
                IsOptimalTradingTime = true,
                CurrentSession = TradingSession.Overlap_EuropeanUS
            };
        }
    }
}
