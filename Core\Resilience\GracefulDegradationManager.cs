using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Core.Models.Resilience;

namespace SmartVolumeStrategy.Core.Resilience
{
    /// <summary>
    /// Phase 2.3: Graceful Degradation Manager - Intelligent degradation modes for enhanced fault tolerance
    /// Manages system degradation levels and feature availability during component failures
    /// </summary>
    public class GracefulDegradationManager
    {
        private readonly Action<string> _logAction;
        private readonly FeatureAvailability _featureAvailability;
        private readonly object _lockObject = new object();
        
        // Current degradation state
        private DegradationStatus _currentStatus;
        private readonly Queue<DegradationEvent> _degradationHistory;
        private readonly Dictionary<StrategyComponent, FailureSeverity> _componentFailures;
        
        // Configuration
        private const int MAX_DEGRADATION_HISTORY = 200;
        private Dictionary<DegradationLevel, int> _degradationThresholds;

        public GracefulDegradationManager(Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _featureAvailability = new FeatureAvailability();
            _currentStatus = new DegradationStatus
            {
                CurrentLevel = DegradationLevel.Normal,
                PreviousLevel = DegradationLevel.Normal,
                LastLevelChange = DateTime.UtcNow
            };
            _degradationHistory = new Queue<DegradationEvent>();
            _componentFailures = new Dictionary<StrategyComponent, FailureSeverity>();
            
            InitializeDegradationThresholds();
        }

        /// <summary>
        /// Handle component failure and evaluate degradation level
        /// </summary>
        public void OnComponentFailure(StrategyComponent component, FailureSeverity severity)
        {
            lock (_lockObject)
            {
                try
                {
                    _componentFailures[component] = severity;
                    
                    var previousLevel = _currentStatus.CurrentLevel;
                    var newLevel = EvaluateDegradationLevel();
                    
                    if (newLevel != previousLevel)
                    {
                        TransitionToDegradationLevel(newLevel, $"Component failure: {component} ({severity})");
                    }
                    
                    _logAction($"🔧 Component failure recorded: {component} ({severity}) - Current degradation: {_currentStatus.CurrentLevel}");
                }
                catch (Exception ex)
                {
                    _logAction($"❌ ERROR in GracefulDegradationManager.OnComponentFailure: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Handle component recovery and evaluate degradation level
        /// </summary>
        public void OnComponentRecovery(StrategyComponent component)
        {
            lock (_lockObject)
            {
                try
                {
                    if (_componentFailures.ContainsKey(component))
                    {
                        _componentFailures.Remove(component);
                        
                        var previousLevel = _currentStatus.CurrentLevel;
                        var newLevel = EvaluateDegradationLevel();
                        
                        if (newLevel != previousLevel)
                        {
                            TransitionToDegradationLevel(newLevel, $"Component recovery: {component}");
                        }
                        
                        _logAction($"✅ Component recovery recorded: {component} - Current degradation: {_currentStatus.CurrentLevel}");
                    }
                }
                catch (Exception ex)
                {
                    _logAction($"❌ ERROR in GracefulDegradationManager.OnComponentRecovery: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Check if a feature is available at current degradation level
        /// </summary>
        public bool IsFeatureAvailable(string feature)
        {
            lock (_lockObject)
            {
                return _featureAvailability.IsFeatureAvailable(feature, _currentStatus.CurrentLevel);
            }
        }

        /// <summary>
        /// Get current degradation level
        /// </summary>
        public DegradationLevel GetCurrentDegradationLevel()
        {
            lock (_lockObject)
            {
                return _currentStatus.CurrentLevel;
            }
        }

        /// <summary>
        /// Get current degradation status
        /// </summary>
        public DegradationStatus GetDegradationStatus()
        {
            lock (_lockObject)
            {
                return new DegradationStatus
                {
                    CurrentLevel = _currentStatus.CurrentLevel,
                    PreviousLevel = _currentStatus.PreviousLevel,
                    LastLevelChange = _currentStatus.LastLevelChange,
                    FailedComponents = new List<StrategyComponent>(_currentStatus.FailedComponents),
                    DisabledFeatures = new List<string>(_currentStatus.DisabledFeatures),
                    DegradationReasons = new List<string>(_currentStatus.DegradationReasons),
                    IsEmergencyMode = _currentStatus.IsEmergencyMode,
                    EmergencyModeActivated = _currentStatus.EmergencyModeActivated
                };
            }
        }

        /// <summary>
        /// Get available features for current degradation level
        /// </summary>
        public List<string> GetAvailableFeatures()
        {
            lock (_lockObject)
            {
                return _featureAvailability.AvailableFeatures.ContainsKey(_currentStatus.CurrentLevel) ?
                    new List<string>(_featureAvailability.AvailableFeatures[_currentStatus.CurrentLevel]) :
                    new List<string>();
            }
        }

        /// <summary>
        /// Get disabled features for current degradation level
        /// </summary>
        public List<string> GetDisabledFeatures()
        {
            lock (_lockObject)
            {
                return _featureAvailability.DisabledFeatures.ContainsKey(_currentStatus.CurrentLevel) ?
                    new List<string>(_featureAvailability.DisabledFeatures[_currentStatus.CurrentLevel]) :
                    new List<string>();
            }
        }

        /// <summary>
        /// Force degradation to a specific level (for testing or emergency)
        /// </summary>
        public void ForceDegradationLevel(DegradationLevel level, string reason)
        {
            lock (_lockObject)
            {
                TransitionToDegradationLevel(level, $"FORCED: {reason}");
                _logAction($"🚨 Degradation level FORCED to {level}: {reason}");
            }
        }

        /// <summary>
        /// Get degradation statistics and history
        /// </summary>
        public DegradationStatistics GetDegradationStatistics()
        {
            lock (_lockObject)
            {
                var stats = new DegradationStatistics
                {
                    CurrentLevel = _currentStatus.CurrentLevel,
                    TotalDegradationEvents = _degradationHistory.Count,
                    TimeInCurrentLevel = DateTime.UtcNow - _currentStatus.LastLevelChange,
                    ActiveFailures = _componentFailures.Count,
                    CriticalFailures = _componentFailures.Count(kvp => kvp.Value == FailureSeverity.Critical),
                    HighSeverityFailures = _componentFailures.Count(kvp => kvp.Value == FailureSeverity.High)
                };

                // Calculate time spent in each degradation level
                stats.TimeInLevels = new Dictionary<DegradationLevel, TimeSpan>();
                foreach (DegradationLevel level in Enum.GetValues<DegradationLevel>())
                {
                    stats.TimeInLevels[level] = TimeSpan.Zero;
                }

                var events = _degradationHistory.ToList();
                for (int i = 0; i < events.Count; i++)
                {
                    var currentEvent = events[i];
                    var nextEvent = i < events.Count - 1 ? events[i + 1] : null;
                    var endTime = nextEvent?.Timestamp ?? DateTime.UtcNow;
                    var duration = endTime - currentEvent.Timestamp;
                    
                    stats.TimeInLevels[currentEvent.NewLevel] += duration;
                }

                return stats;
            }
        }

        /// <summary>
        /// Initialize degradation thresholds for different levels
        /// </summary>
        private void InitializeDegradationThresholds()
        {
            _degradationThresholds = new Dictionary<DegradationLevel, int>
            {
                [DegradationLevel.Normal] = 0,     // No failures
                [DegradationLevel.Reduced] = 1,    // 1+ non-critical component failure
                [DegradationLevel.Minimal] = 2,    // 2+ component failures or 1 critical
                [DegradationLevel.Emergency] = 3   // 3+ failures or critical system components
            };
        }

        /// <summary>
        /// Evaluate appropriate degradation level based on current failures
        /// </summary>
        private DegradationLevel EvaluateDegradationLevel()
        {
            if (_componentFailures.Count == 0)
                return DegradationLevel.Normal;

            // Check for critical component failures
            var criticalComponents = new[]
            {
                StrategyComponent.PositionManagement,
                StrategyComponent.RiskControls,
                StrategyComponent.DataFeeds
            };

            var hasCriticalFailure = _componentFailures.Keys.Any(c => criticalComponents.Contains(c));
            var hasCriticalSeverity = _componentFailures.Values.Any(s => s == FailureSeverity.Critical);
            var highSeverityCount = _componentFailures.Count(kvp => kvp.Value >= FailureSeverity.High);
            var totalFailures = _componentFailures.Count;

            // Emergency mode: Critical components failed or critical severity
            if (hasCriticalFailure || hasCriticalSeverity || highSeverityCount >= 3)
            {
                return DegradationLevel.Emergency;
            }

            // Minimal mode: Multiple failures or high severity
            if (totalFailures >= 3 || highSeverityCount >= 2)
            {
                return DegradationLevel.Minimal;
            }

            // Reduced mode: Some failures
            if (totalFailures >= 1 || highSeverityCount >= 1)
            {
                return DegradationLevel.Reduced;
            }

            return DegradationLevel.Normal;
        }

        /// <summary>
        /// Transition to a new degradation level
        /// </summary>
        private void TransitionToDegradationLevel(DegradationLevel newLevel, string reason)
        {
            var previousLevel = _currentStatus.CurrentLevel;
            var timestamp = DateTime.UtcNow;

            // Update status
            _currentStatus.PreviousLevel = previousLevel;
            _currentStatus.CurrentLevel = newLevel;
            _currentStatus.LastLevelChange = timestamp;
            _currentStatus.FailedComponents = _componentFailures.Keys.ToList();
            _currentStatus.DisabledFeatures = GetDisabledFeatures();
            
            // Add reason
            _currentStatus.DegradationReasons.Add($"{timestamp:HH:mm:ss}: {reason}");
            while (_currentStatus.DegradationReasons.Count > 10)
            {
                _currentStatus.DegradationReasons.RemoveAt(0);
            }

            // Handle emergency mode
            if (newLevel == DegradationLevel.Emergency && previousLevel != DegradationLevel.Emergency)
            {
                _currentStatus.IsEmergencyMode = true;
                _currentStatus.EmergencyModeActivated = timestamp;
            }
            else if (newLevel != DegradationLevel.Emergency && previousLevel == DegradationLevel.Emergency)
            {
                _currentStatus.IsEmergencyMode = false;
                _currentStatus.EmergencyModeActivated = null;
            }

            // Record degradation event
            var degradationEvent = new DegradationEvent
            {
                Timestamp = timestamp,
                PreviousLevel = previousLevel,
                NewLevel = newLevel,
                Reason = reason,
                FailedComponents = _componentFailures.Keys.ToList(),
                ComponentFailures = new Dictionary<StrategyComponent, FailureSeverity>(_componentFailures)
            };

            _degradationHistory.Enqueue(degradationEvent);
            while (_degradationHistory.Count > MAX_DEGRADATION_HISTORY)
            {
                _degradationHistory.Dequeue();
            }

            // Log transition
            LogDegradationTransition(previousLevel, newLevel, reason);
        }

        /// <summary>
        /// Log degradation level transitions
        /// </summary>
        private void LogDegradationTransition(DegradationLevel previousLevel, DegradationLevel newLevel, string reason)
        {
            var levelIcon = newLevel switch
            {
                DegradationLevel.Normal => "🟢",
                DegradationLevel.Reduced => "🟡",
                DegradationLevel.Minimal => "🟠",
                DegradationLevel.Emergency => "🔴",
                _ => "⚪"
            };

            var direction = newLevel > previousLevel ? "DEGRADED" : "IMPROVED";
            
            _logAction($"{levelIcon} System {direction}: {previousLevel} → {newLevel} | {reason}");
            
            if (newLevel == DegradationLevel.Emergency)
            {
                _logAction($"🚨 EMERGENCY MODE ACTIVATED - Only position management and risk controls available");
            }
            
            var disabledFeatures = GetDisabledFeatures();
            if (disabledFeatures.Count > 0)
            {
                _logAction($"⚠️ Features disabled: {string.Join(", ", disabledFeatures)}");
            }
        }
    }

    /// <summary>
    /// Degradation event for history tracking
    /// </summary>
    public class DegradationEvent
    {
        public DateTime Timestamp { get; set; }
        public DegradationLevel PreviousLevel { get; set; }
        public DegradationLevel NewLevel { get; set; }
        public string Reason { get; set; }
        public List<StrategyComponent> FailedComponents { get; set; } = new List<StrategyComponent>();
        public Dictionary<StrategyComponent, FailureSeverity> ComponentFailures { get; set; } = new Dictionary<StrategyComponent, FailureSeverity>();
    }

    /// <summary>
    /// Degradation statistics
    /// </summary>
    public class DegradationStatistics
    {
        public DegradationLevel CurrentLevel { get; set; }
        public int TotalDegradationEvents { get; set; }
        public TimeSpan TimeInCurrentLevel { get; set; }
        public int ActiveFailures { get; set; }
        public int CriticalFailures { get; set; }
        public int HighSeverityFailures { get; set; }
        public Dictionary<DegradationLevel, TimeSpan> TimeInLevels { get; set; } = new Dictionary<DegradationLevel, TimeSpan>();
    }
}
