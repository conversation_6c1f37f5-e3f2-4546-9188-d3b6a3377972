using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Calibration
{
    /// <summary>
    /// Optimizes volume and signal thresholds based on historical performance analysis
    /// </summary>
    public class ThresholdOptimizer : IThresholdOptimizer
    {
        private readonly decimal[] DEFAULT_VOLUME_THRESHOLDS = { 1.2m, 1.5m, 1.8m, 2.0m, 2.2m, 2.5m, 3.0m, 3.5m, 4.0m };
        private readonly decimal[] DEFAULT_SIGNAL_THRESHOLDS = { 0.3m, 0.5m, 0.7m, 0.8m, 1.0m, 1.2m, 1.5m, 2.0m };

        /// <summary>
        /// Optimize thresholds based on volume and delta flow characteristics
        /// </summary>
        public async Task<ThresholdOptimizationResult> OptimizeThresholdsAsync(
            List<MarketDataPoint> marketData,
            VolumeCharacteristics volumeCharacteristics,
            DeltaFlowCharacteristics deltaFlowCharacteristics)
        {
            if (marketData == null || marketData.Count < 50)
                throw new ArgumentException("Need at least 50 bars for threshold optimization", nameof(marketData));

            var result = new ThresholdOptimizationResult
            {
                OptimizationTimestamp = DateTime.UtcNow,
                BarsAnalyzed = marketData.Count
            };

            // Optimize volume threshold
            var volumeOptimization = await OptimizeVolumeThresholdAsync(marketData, volumeCharacteristics);
            result.OptimalVolumeThreshold = volumeOptimization.OptimalThreshold;
            result.VolumeThresholdConfidence = volumeOptimization.Confidence;

            // Optimize signal threshold
            var signalOptimization = await OptimizeSignalThresholdAsync(marketData, deltaFlowCharacteristics);
            result.OptimalSignalThreshold = signalOptimization.OptimalThreshold;
            result.SignalThresholdConfidence = signalOptimization.Confidence;

            // Optimize delta-specific thresholds
            var deltaOptimization = await OptimizeDeltaThresholdsAsync(marketData, deltaFlowCharacteristics);
            result.OptimalDeltaImbalanceThreshold = deltaOptimization.OptimalImbalanceThreshold;
            result.OptimalDeltaSignificanceThreshold = deltaOptimization.OptimalSignificanceThreshold;

            // Calculate overall optimization confidence
            result.OptimizationConfidence = new[] {
                result.VolumeThresholdConfidence,
                result.SignalThresholdConfidence,
                deltaOptimization.Confidence
            }.Average();

            return result;
        }

        /// <summary>
        /// Quick threshold optimization for recalibration
        /// </summary>
        public async Task<ThresholdOptimizationResult> QuickOptimizeAsync(
            List<MarketDataPoint> recentData,
            OptimalSettings currentSettings)
        {
            if (recentData == null || recentData.Count < 20)
                throw new ArgumentException("Need at least 20 bars for quick optimization", nameof(recentData));

            // Use a subset of thresholds around current settings for quick optimization
            var volumeThresholds = GenerateThresholdsAroundValue(currentSettings.VolumeThreshold, 5);
            var signalThresholds = GenerateThresholdsAroundValue(currentSettings.SignalThreshold, 5);

            var result = new ThresholdOptimizationResult
            {
                OptimizationTimestamp = DateTime.UtcNow,
                BarsAnalyzed = recentData.Count
            };

            // Quick volume threshold test
            var volumeResults = await TestVolumeThresholds(recentData, volumeThresholds);
            var bestVolumeResult = volumeResults.OrderByDescending(r => r.Score).First();
            result.OptimalVolumeThreshold = bestVolumeResult.Threshold;
            result.VolumeThresholdConfidence = bestVolumeResult.Score;

            // Quick signal threshold test
            var signalResults = await TestSignalThresholds(recentData, signalThresholds, result.OptimalVolumeThreshold);
            var bestSignalResult = signalResults.OrderByDescending(r => r.Score).First();
            result.OptimalSignalThreshold = bestSignalResult.Threshold;
            result.SignalThresholdConfidence = bestSignalResult.Score;

            // Use current delta thresholds (quick optimization doesn't change these)
            result.OptimalDeltaImbalanceThreshold = currentSettings.DeltaImbalanceThreshold;
            result.OptimalDeltaSignificanceThreshold = currentSettings.DeltaSignificanceThreshold;

            result.OptimizationConfidence = (result.VolumeThresholdConfidence + result.SignalThresholdConfidence) / 2;

            return result;
        }

        #region Private Methods

        private async Task<(decimal OptimalThreshold, decimal Confidence)> OptimizeVolumeThresholdAsync(
            List<MarketDataPoint> marketData,
            VolumeCharacteristics volumeCharacteristics)
        {
            var testResults = await TestVolumeThresholds(marketData, DEFAULT_VOLUME_THRESHOLDS);
            var bestResult = testResults.OrderByDescending(r => r.Score).First();

            // Adjust confidence based on volume characteristics
            var confidenceAdjustment = volumeCharacteristics.VolumeConsistency;
            var adjustedConfidence = bestResult.Score * confidenceAdjustment;

            return (bestResult.Threshold, adjustedConfidence);
        }

        private async Task<(decimal OptimalThreshold, decimal Confidence)> OptimizeSignalThresholdAsync(
            List<MarketDataPoint> marketData,
            DeltaFlowCharacteristics deltaFlowCharacteristics)
        {
            // Use the optimal volume threshold for signal testing
            var volumeThreshold = 2.0m; // Default, would use optimized value in real implementation
            
            var testResults = await TestSignalThresholds(marketData, DEFAULT_SIGNAL_THRESHOLDS, volumeThreshold);
            var bestResult = testResults.OrderByDescending(r => r.Score).First();

            // Adjust confidence based on delta flow characteristics
            var confidenceAdjustment = deltaFlowCharacteristics.HasStrongInstitutionalActivity ? 0.9m : 0.7m;
            var adjustedConfidence = bestResult.Score * confidenceAdjustment;

            return (bestResult.Threshold, adjustedConfidence);
        }

        private async Task<(decimal OptimalImbalanceThreshold, decimal OptimalSignificanceThreshold, decimal Confidence)> 
            OptimizeDeltaThresholdsAsync(List<MarketDataPoint> marketData, DeltaFlowCharacteristics deltaFlowCharacteristics)
        {
            var imbalanceThresholds = new[] { 0.05m, 0.10m, 0.15m, 0.20m, 0.25m, 0.30m }; // CRYPTO OPTIMIZED: realistic thresholds
            var averageVolume = marketData.Average(x => x.Volume);
            var significanceThresholds = new[] { averageVolume * 0.1m, averageVolume * 0.2m, averageVolume * 0.3m };

            decimal bestImbalanceThreshold = 0.15m; // CRYPTO OPTIMIZED: realistic default
            decimal bestSignificanceThreshold = averageVolume * 0.2m;
            decimal bestScore = 0;

            foreach (var imbalanceThreshold in imbalanceThresholds)
            {
                foreach (var significanceThreshold in significanceThresholds)
                {
                    var score = await TestDeltaThresholdCombination(marketData, imbalanceThreshold, significanceThreshold);
                    
                    if (score > bestScore)
                    {
                        bestScore = score;
                        bestImbalanceThreshold = imbalanceThreshold;
                        bestSignificanceThreshold = significanceThreshold;
                    }
                }
            }

            var confidence = Math.Min(1.0m, bestScore);
            return (bestImbalanceThreshold, bestSignificanceThreshold, confidence);
        }

        private async Task<List<ThresholdTestResult>> TestVolumeThresholds(
            List<MarketDataPoint> marketData,
            decimal[] thresholds)
        {
            var results = new List<ThresholdTestResult>();

            foreach (var threshold in thresholds)
            {
                var result = await TestVolumeThreshold(marketData, threshold);
                results.Add(result);
            }

            return results;
        }

        private async Task<ThresholdTestResult> TestVolumeThreshold(
            List<MarketDataPoint> marketData,
            decimal threshold)
        {
            var averageVolume = marketData.Average(x => x.Volume);
            var signals = 0;
            var profitableSignals = 0;
            var totalProfit = 0m;
            var totalLoss = 0m;

            for (int i = 1; i < marketData.Count - 1; i++)
            {
                var currentBar = marketData[i];
                var nextBar = marketData[i + 1];

                var volumeRatio = currentBar.Volume / averageVolume;
                
                if (volumeRatio >= threshold)
                {
                    signals++;
                    
                    var priceChange = nextBar.Close - currentBar.Close;
                    var priceChangePercent = currentBar.Close > 0 ? Math.Abs(priceChange) / currentBar.Close : 0;
                    
                    if (priceChangePercent > 0.001m) // Minimum meaningful price movement
                    {
                        profitableSignals++;
                        totalProfit += priceChangePercent;
                    }
                    else
                    {
                        totalLoss += priceChangePercent;
                    }
                }
            }

            var winRate = signals > 0 ? (decimal)profitableSignals / signals : 0;
            var profitFactor = totalLoss > 0 ? totalProfit / totalLoss : (totalProfit > 0 ? 10 : 1);
            var frequency = (decimal)signals / marketData.Count;

            // Combined score balancing win rate, profit factor, and frequency
            var score = (winRate * 0.4m + Math.Min(1.0m, profitFactor / 3.0m) * 0.4m + Math.Min(1.0m, frequency * 20) * 0.2m);

            return new ThresholdTestResult
            {
                Threshold = threshold,
                Score = score,
                WinRate = winRate,
                ProfitFactor = profitFactor,
                SignalFrequency = frequency,
                SignalsGenerated = signals
            };
        }

        private async Task<List<ThresholdTestResult>> TestSignalThresholds(
            List<MarketDataPoint> marketData,
            decimal[] thresholds,
            decimal volumeThreshold)
        {
            var results = new List<ThresholdTestResult>();

            foreach (var threshold in thresholds)
            {
                var result = await TestSignalThreshold(marketData, threshold, volumeThreshold);
                results.Add(result);
            }

            return results;
        }

        private async Task<ThresholdTestResult> TestSignalThreshold(
            List<MarketDataPoint> marketData,
            decimal signalThreshold,
            decimal volumeThreshold)
        {
            var averageVolume = marketData.Average(x => x.Volume);
            var signals = 0;
            var profitableSignals = 0;
            var totalProfit = 0m;
            var totalLoss = 0m;

            // Simple cumulative impact calculation for testing
            var cumulativeImpact = 0m;
            var decayFactor = 0.9m;

            for (int i = 1; i < marketData.Count - 1; i++)
            {
                var currentBar = marketData[i];
                var nextBar = marketData[i + 1];

                // Update cumulative impact
                var volumeRatio = currentBar.Volume / averageVolume;
                if (volumeRatio >= volumeThreshold)
                {
                    var priceChangePercent = currentBar.Open > 0 ? Math.Abs(currentBar.Close - currentBar.Open) / currentBar.Open : 0;
                    var impact = priceChangePercent * volumeRatio;
                    cumulativeImpact = cumulativeImpact * decayFactor + impact;
                }
                else
                {
                    cumulativeImpact *= decayFactor;
                }

                // Check for signal
                if (cumulativeImpact >= signalThreshold)
                {
                    signals++;
                    
                    var priceChange = nextBar.Close - currentBar.Close;
                    var priceChangePercent = currentBar.Close > 0 ? priceChange / currentBar.Close : 0;
                    
                    if (Math.Abs(priceChangePercent) > 0.001m)
                    {
                        if (priceChangePercent > 0)
                        {
                            profitableSignals++;
                            totalProfit += Math.Abs(priceChangePercent);
                        }
                        else
                        {
                            totalLoss += Math.Abs(priceChangePercent);
                        }
                    }
                    
                    cumulativeImpact = 0; // Reset after signal
                }
            }

            var winRate = signals > 0 ? (decimal)profitableSignals / signals : 0;
            var profitFactor = totalLoss > 0 ? totalProfit / totalLoss : (totalProfit > 0 ? 10 : 1);
            var frequency = (decimal)signals / marketData.Count;

            var score = (winRate * 0.5m + Math.Min(1.0m, profitFactor / 2.0m) * 0.3m + Math.Min(1.0m, frequency * 15) * 0.2m);

            return new ThresholdTestResult
            {
                Threshold = signalThreshold,
                Score = score,
                WinRate = winRate,
                ProfitFactor = profitFactor,
                SignalFrequency = frequency,
                SignalsGenerated = signals
            };
        }

        private async Task<decimal> TestDeltaThresholdCombination(
            List<MarketDataPoint> marketData,
            decimal imbalanceThreshold,
            decimal significanceThreshold)
        {
            var signals = 0;
            var profitableSignals = 0;

            for (int i = 1; i < marketData.Count - 1; i++)
            {
                var currentBar = marketData[i];
                var nextBar = marketData[i + 1];

                if (currentBar.Volume > significanceThreshold)
                {
                    var deltaImbalance = Math.Abs(currentBar.Delta) / currentBar.Volume;
                    
                    if (deltaImbalance >= imbalanceThreshold)
                    {
                        signals++;
                        
                        var priceChange = nextBar.Close - currentBar.Close;
                        var expectedDirection = Math.Sign(currentBar.Delta);
                        var actualDirection = Math.Sign(priceChange);
                        
                        if (expectedDirection == actualDirection && Math.Abs(priceChange / currentBar.Close) > 0.001m)
                        {
                            profitableSignals++;
                        }
                    }
                }
            }

            var winRate = signals > 0 ? (decimal)profitableSignals / signals : 0;
            var frequency = (decimal)signals / marketData.Count;
            
            return winRate * 0.7m + Math.Min(1.0m, frequency * 10) * 0.3m;
        }

        private decimal[] GenerateThresholdsAroundValue(decimal centerValue, int count)
        {
            var thresholds = new List<decimal>();
            var step = centerValue * 0.1m; // 10% steps
            
            for (int i = -(count / 2); i <= (count / 2); i++)
            {
                var threshold = centerValue + (i * step);
                if (threshold > 0)
                {
                    thresholds.Add(threshold);
                }
            }

            return thresholds.ToArray();
        }

        #endregion
    }
}
