using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Core.Models.Resilience;
using SmartVolumeStrategy.Core.Analysis;
using CircuitBreakerState = SmartVolumeStrategy.Core.Models.Resilience.CircuitBreakerState;

namespace SmartVolumeStrategy.Core.Resilience
{
    /// <summary>
    /// Phase 2.3: Circuit Breaker Manager - Central coordinator for strategy component resilience
    /// Monitors all strategy components and manages circuit breaker states for enhanced fault tolerance
    /// </summary>
    public class CircuitBreakerManager
    {
        private readonly Action<string> _logAction;
        private readonly Dictionary<StrategyComponent, CircuitBreaker> _circuitBreakers;
        private readonly ComponentHealthTracker _componentHealthTracker;
        private readonly GracefulDegradationManager _degradationManager;
        private readonly Queue<CircuitBreakerEvent> _eventHistory;
        private readonly object _lockObject = new object();
        
        // Configuration
        private const int MAX_EVENT_HISTORY = 500;
        private const int HEALTH_CHECK_INTERVAL_SECONDS = 30;
        private DateTime _lastHealthCheck;
        
        // Statistics
        private readonly Dictionary<StrategyComponent, CircuitBreakerStatistics> _statistics;
        private DateTime _systemStartTime;

        public CircuitBreakerManager(Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _circuitBreakers = new Dictionary<StrategyComponent, CircuitBreaker>();
            _componentHealthTracker = new ComponentHealthTracker(logAction);
            _degradationManager = new GracefulDegradationManager(logAction);
            _eventHistory = new Queue<CircuitBreakerEvent>();
            _statistics = new Dictionary<StrategyComponent, CircuitBreakerStatistics>();
            
            _systemStartTime = DateTime.UtcNow;
            _lastHealthCheck = DateTime.UtcNow;
            
            InitializeCircuitBreakers();
        }

        /// <summary>
        /// Initialize circuit breakers for all strategy components
        /// </summary>
        private void InitializeCircuitBreakers()
        {
            foreach (StrategyComponent component in Enum.GetValues<StrategyComponent>())
            {
                var config = CreateDefaultConfig(component);
                _circuitBreakers[component] = new CircuitBreaker
                {
                    Component = component,
                    State = CircuitBreakerState.Closed,
                    LastStateChange = DateTime.UtcNow,
                    Config = config
                };
                
                _statistics[component] = new CircuitBreakerStatistics
                {
                    Component = component
                };
            }
            
            _logAction("🔧 Circuit Breaker Manager initialized with monitoring for all strategy components");
        }

        /// <summary>
        /// Record a component operation result for circuit breaker evaluation
        /// </summary>
        public void RecordOperation(StrategyComponent component, bool wasSuccessful, string details = null, FailureSeverity severity = FailureSeverity.Low)
        {
            lock (_lockObject)
            {
                try
                {
                    if (!_circuitBreakers.ContainsKey(component))
                        return;

                    var circuitBreaker = _circuitBreakers[component];
                    var timestamp = DateTime.UtcNow;

                    if (wasSuccessful)
                    {
                        RecordSuccess(circuitBreaker, timestamp);
                    }
                    else
                    {
                        RecordFailure(circuitBreaker, timestamp, details, severity);
                    }

                    // Update component health
                    _componentHealthTracker.UpdateComponentHealth(component, wasSuccessful, details, severity);

                    // Evaluate circuit breaker state
                    EvaluateCircuitBreakerState(circuitBreaker, timestamp);

                    // Perform periodic health checks
                    if ((timestamp - _lastHealthCheck).TotalSeconds >= HEALTH_CHECK_INTERVAL_SECONDS)
                    {
                        PerformSystemHealthCheck();
                        _lastHealthCheck = timestamp;
                    }
                }
                catch (Exception ex)
                {
                    _logAction($"❌ ERROR in CircuitBreakerManager.RecordOperation: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Check if a component operation is allowed based on circuit breaker state
        /// </summary>
        public bool IsOperationAllowed(StrategyComponent component)
        {
            lock (_lockObject)
            {
                if (!_circuitBreakers.ContainsKey(component))
                    return true;

                var circuitBreaker = _circuitBreakers[component];
                
                switch (circuitBreaker.State)
                {
                    case CircuitBreakerState.Closed:
                        return true;
                        
                    case CircuitBreakerState.Open:
                        // Check if enough time has passed to attempt half-open
                        if (DateTime.UtcNow - circuitBreaker.LastStateChange >= circuitBreaker.Config.OpenTimeout)
                        {
                            TransitionToHalfOpen(circuitBreaker);
                            return true;
                        }
                        return false;
                        
                    case CircuitBreakerState.HalfOpen:
                        // Allow limited operations in half-open state
                        return true;
                        
                    default:
                        return false;
                }
            }
        }

        /// <summary>
        /// Get current system resilience status
        /// </summary>
        public SystemResilienceStatus GetSystemStatus()
        {
            lock (_lockObject)
            {
                var status = new SystemResilienceStatus
                {
                    Timestamp = DateTime.UtcNow,
                    DegradationLevel = _degradationManager.GetCurrentDegradationLevel(),
                    TotalSystemUptime = DateTime.UtcNow - _systemStartTime
                };

                // Count component states
                foreach (var kvp in _circuitBreakers)
                {
                    status.ComponentStates[kvp.Key] = kvp.Value.State;
                    
                    switch (kvp.Value.State)
                    {
                        case CircuitBreakerState.Open:
                            status.ActiveCircuitBreakers++;
                            status.FailedComponents++;
                            break;
                        case CircuitBreakerState.HalfOpen:
                            status.DegradedComponents++;
                            break;
                        case CircuitBreakerState.Closed:
                            var health = _componentHealthTracker.GetComponentHealth(kvp.Key);
                            if (health.HealthScore < 0.5m)
                                status.DegradedComponents++;
                            else
                                status.HealthyComponents++;
                            break;
                    }
                }

                // Calculate overall system health
                var totalComponents = _circuitBreakers.Count;
                var healthyWeight = status.HealthyComponents * 1.0m;
                var degradedWeight = status.DegradedComponents * 0.5m;
                var failedWeight = status.FailedComponents * 0.0m;
                
                status.OverallSystemHealth = totalComponents > 0 ? 
                    (healthyWeight + degradedWeight + failedWeight) / totalComponents : 1.0m;

                // Identify critical components
                status.CriticalComponents = _circuitBreakers
                    .Where(kvp => kvp.Value.State == CircuitBreakerState.Open && IsCriticalComponent(kvp.Key))
                    .Select(kvp => kvp.Key)
                    .ToList();

                // Generate active alerts
                status.ActiveAlerts = GenerateActiveAlerts();

                // Calculate total downtime
                status.TotalSystemDowntime = _statistics.Values.Aggregate(TimeSpan.Zero, (sum, stat) => sum + stat.TotalDowntime);

                return status;
            }
        }

        /// <summary>
        /// Get circuit breaker statistics for a specific component
        /// </summary>
        public CircuitBreakerStatistics GetComponentStatistics(StrategyComponent component)
        {
            lock (_lockObject)
            {
                if (_statistics.ContainsKey(component))
                {
                    var stats = _statistics[component];
                    var circuitBreaker = _circuitBreakers[component];
                    
                    // Update availability percentage
                    var totalTime = DateTime.UtcNow - _systemStartTime;
                    stats.AvailabilityPercentage = totalTime.TotalMilliseconds > 0 ? 
                        (decimal)(1.0 - (stats.TotalDowntime.TotalMilliseconds / totalTime.TotalMilliseconds)) : 1.0m;
                    
                    return stats;
                }
                
                return new CircuitBreakerStatistics { Component = component };
            }
        }

        /// <summary>
        /// Force a circuit breaker to open (for testing or emergency situations)
        /// </summary>
        public void ForceOpen(StrategyComponent component, string reason)
        {
            lock (_lockObject)
            {
                if (_circuitBreakers.ContainsKey(component))
                {
                    var circuitBreaker = _circuitBreakers[component];
                    TransitionToOpen(circuitBreaker, reason, FailureSeverity.Critical);
                    _logAction($"🚨 Circuit breaker FORCE OPENED for {component}: {reason}");
                }
            }
        }

        /// <summary>
        /// Force a circuit breaker to close (for testing or manual recovery)
        /// </summary>
        public void ForceClose(StrategyComponent component, string reason)
        {
            lock (_lockObject)
            {
                if (_circuitBreakers.ContainsKey(component))
                {
                    var circuitBreaker = _circuitBreakers[component];
                    TransitionToClosed(circuitBreaker, reason);
                    _logAction($"🔧 Circuit breaker FORCE CLOSED for {component}: {reason}");
                }
            }
        }

        /// <summary>
        /// Get degradation manager for external access
        /// </summary>
        public GracefulDegradationManager GetDegradationManager()
        {
            return _degradationManager;
        }

        /// <summary>
        /// Get component health tracker for external access
        /// </summary>
        public ComponentHealthTracker GetComponentHealthTracker()
        {
            return _componentHealthTracker;
        }

        /// <summary>
        /// Record a successful operation
        /// </summary>
        private void RecordSuccess(CircuitBreaker circuitBreaker, DateTime timestamp)
        {
            circuitBreaker.SuccessCount++;
            circuitBreaker.ConsecutiveSuccesses++;
            circuitBreaker.ConsecutiveFailures = 0;
            circuitBreaker.LastSuccess = timestamp;
        }

        /// <summary>
        /// Record a failed operation
        /// </summary>
        private void RecordFailure(CircuitBreaker circuitBreaker, DateTime timestamp, string details, FailureSeverity severity)
        {
            circuitBreaker.FailureCount++;
            circuitBreaker.ConsecutiveFailures++;
            circuitBreaker.ConsecutiveSuccesses = 0;
            circuitBreaker.LastFailure = timestamp;

            if (!string.IsNullOrEmpty(details))
            {
                circuitBreaker.RecentFailureReasons.Add($"{timestamp:HH:mm:ss}: {details}");
                while (circuitBreaker.RecentFailureReasons.Count > 10)
                {
                    circuitBreaker.RecentFailureReasons.RemoveAt(0);
                }
            }

            // Update statistics
            if (_statistics.ContainsKey(circuitBreaker.Component))
            {
                var stats = _statistics[circuitBreaker.Component];
                if (!stats.FailuresBySeverity.ContainsKey(severity))
                    stats.FailuresBySeverity[severity] = 0;
                stats.FailuresBySeverity[severity]++;
            }
        }

        /// <summary>
        /// Evaluate and potentially change circuit breaker state
        /// </summary>
        private void EvaluateCircuitBreakerState(CircuitBreaker circuitBreaker, DateTime timestamp)
        {
            switch (circuitBreaker.State)
            {
                case CircuitBreakerState.Closed:
                    if (circuitBreaker.ConsecutiveFailures >= circuitBreaker.Config.FailureThreshold)
                    {
                        TransitionToOpen(circuitBreaker, $"Failure threshold exceeded ({circuitBreaker.ConsecutiveFailures} consecutive failures)", FailureSeverity.High);
                    }
                    break;

                case CircuitBreakerState.HalfOpen:
                    if (circuitBreaker.ConsecutiveSuccesses >= circuitBreaker.Config.SuccessThreshold)
                    {
                        TransitionToClosed(circuitBreaker, $"Recovery confirmed ({circuitBreaker.ConsecutiveSuccesses} consecutive successes)");
                    }
                    else if (circuitBreaker.ConsecutiveFailures > 0)
                    {
                        TransitionToOpen(circuitBreaker, "Recovery attempt failed", FailureSeverity.High);
                    }
                    break;
            }
        }

        /// <summary>
        /// Transition circuit breaker to Open state
        /// </summary>
        private void TransitionToOpen(CircuitBreaker circuitBreaker, string reason, FailureSeverity severity)
        {
            var previousState = circuitBreaker.State;
            circuitBreaker.State = CircuitBreakerState.Open;
            circuitBreaker.LastStateChange = DateTime.UtcNow;
            circuitBreaker.OpenedAt = DateTime.UtcNow;

            LogStateTransition(circuitBreaker, previousState, reason, severity);
            UpdateStatistics(circuitBreaker, previousState);

            // Notify degradation manager
            _degradationManager.OnComponentFailure(circuitBreaker.Component, severity);
        }

        /// <summary>
        /// Transition circuit breaker to Half-Open state
        /// </summary>
        private void TransitionToHalfOpen(CircuitBreaker circuitBreaker)
        {
            var previousState = circuitBreaker.State;
            circuitBreaker.State = CircuitBreakerState.HalfOpen;
            circuitBreaker.LastStateChange = DateTime.UtcNow;
            circuitBreaker.LastHalfOpenAttempt = DateTime.UtcNow;
            circuitBreaker.ConsecutiveSuccesses = 0;
            circuitBreaker.ConsecutiveFailures = 0;

            LogStateTransition(circuitBreaker, previousState, "Attempting recovery", FailureSeverity.Medium);
        }

        /// <summary>
        /// Transition circuit breaker to Closed state
        /// </summary>
        private void TransitionToClosed(CircuitBreaker circuitBreaker, string reason)
        {
            var previousState = circuitBreaker.State;
            circuitBreaker.State = CircuitBreakerState.Closed;
            circuitBreaker.LastStateChange = DateTime.UtcNow;

            // Calculate downtime if recovering from Open state
            if (previousState == CircuitBreakerState.Open && circuitBreaker.OpenedAt.HasValue)
            {
                var downtime = DateTime.UtcNow - circuitBreaker.OpenedAt.Value;
                circuitBreaker.TotalDowntime += downtime;

                if (_statistics.ContainsKey(circuitBreaker.Component))
                {
                    _statistics[circuitBreaker.Component].TotalDowntime += downtime;
                    _statistics[circuitBreaker.Component].TotalRecoveries++;
                    _statistics[circuitBreaker.Component].LastRecovery = DateTime.UtcNow;
                }
            }

            circuitBreaker.OpenedAt = null;

            LogStateTransition(circuitBreaker, previousState, reason, FailureSeverity.Low);
            UpdateStatistics(circuitBreaker, previousState);

            // Notify degradation manager
            _degradationManager.OnComponentRecovery(circuitBreaker.Component);
        }

        /// <summary>
        /// Log circuit breaker state transitions
        /// </summary>
        private void LogStateTransition(CircuitBreaker circuitBreaker, SmartVolumeStrategy.Core.Models.Resilience.CircuitBreakerState previousState, string reason, FailureSeverity severity)
        {
            var eventData = new CircuitBreakerEvent
            {
                Timestamp = DateTime.UtcNow,
                Component = circuitBreaker.Component,
                PreviousState = previousState,
                NewState = circuitBreaker.State,
                Reason = reason,
                Severity = severity,
                HealthMetrics = _componentHealthTracker.GetComponentHealth(circuitBreaker.Component)
            };

            _eventHistory.Enqueue(eventData);
            while (_eventHistory.Count > MAX_EVENT_HISTORY)
            {
                _eventHistory.Dequeue();
            }

            var stateIcon = circuitBreaker.State switch
            {
                CircuitBreakerState.Open => "🔴",
                CircuitBreakerState.HalfOpen => "🟡",
                CircuitBreakerState.Closed => "🟢",
                _ => "⚪"
            };

            _logAction($"{stateIcon} Circuit Breaker {circuitBreaker.Component}: {previousState} → {circuitBreaker.State} | {reason}");
        }

        /// <summary>
        /// Update circuit breaker statistics
        /// </summary>
        private void UpdateStatistics(CircuitBreaker circuitBreaker, SmartVolumeStrategy.Core.Models.Resilience.CircuitBreakerState previousState)
        {
            if (!_statistics.ContainsKey(circuitBreaker.Component))
                return;

            var stats = _statistics[circuitBreaker.Component];

            if (circuitBreaker.State == CircuitBreakerState.Open && previousState != CircuitBreakerState.Open)
            {
                stats.TotalActivations++;
                stats.LastActivation = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Perform periodic system health check
        /// </summary>
        private void PerformSystemHealthCheck()
        {
            var systemStatus = GetSystemStatus();

            if (systemStatus.OverallSystemHealth < 0.5m)
            {
                _logAction($"⚠️ System health degraded: {systemStatus.OverallSystemHealth:P1} | Active circuit breakers: {systemStatus.ActiveCircuitBreakers}");
            }

            if (systemStatus.CriticalComponents.Count > 0)
            {
                _logAction($"🚨 Critical components failed: {string.Join(", ", systemStatus.CriticalComponents)}");
            }
        }

        /// <summary>
        /// Generate active alerts based on current system state
        /// </summary>
        private List<string> GenerateActiveAlerts()
        {
            var alerts = new List<string>();

            foreach (var kvp in _circuitBreakers)
            {
                var component = kvp.Key;
                var circuitBreaker = kvp.Value;

                if (circuitBreaker.State == CircuitBreakerState.Open)
                {
                    var downtime = DateTime.UtcNow - (circuitBreaker.OpenedAt ?? DateTime.UtcNow);
                    alerts.Add($"{component} circuit breaker OPEN for {downtime.TotalMinutes:F1} minutes");
                }
                else if (circuitBreaker.State == CircuitBreakerState.HalfOpen)
                {
                    alerts.Add($"{component} circuit breaker in RECOVERY mode");
                }

                if (circuitBreaker.ConsecutiveFailures >= circuitBreaker.Config.FailureThreshold * 0.8)
                {
                    alerts.Add($"{component} approaching failure threshold ({circuitBreaker.ConsecutiveFailures}/{circuitBreaker.Config.FailureThreshold})");
                }
            }

            return alerts;
        }

        private CircuitBreakerConfig CreateDefaultConfig(StrategyComponent component)
        {
            // Different components may have different thresholds
            return component switch
            {
                StrategyComponent.SignalGeneration => new CircuitBreakerConfig
                {
                    FailureThreshold = 5,
                    SuccessThreshold = 3,
                    OpenTimeout = TimeSpan.FromMinutes(2),
                    TriggerSeverity = FailureSeverity.High
                },
                StrategyComponent.MicrostructureFiltering => new CircuitBreakerConfig
                {
                    FailureThreshold = 8,
                    SuccessThreshold = 5,
                    OpenTimeout = TimeSpan.FromMinutes(1),
                    TriggerSeverity = FailureSeverity.Medium
                },
                StrategyComponent.DataFeeds => new CircuitBreakerConfig
                {
                    FailureThreshold = 3,
                    SuccessThreshold = 2,
                    OpenTimeout = TimeSpan.FromSeconds(30),
                    TriggerSeverity = FailureSeverity.High
                },
                StrategyComponent.PositionManagement => new CircuitBreakerConfig
                {
                    FailureThreshold = 2,
                    SuccessThreshold = 1,
                    OpenTimeout = TimeSpan.FromSeconds(15),
                    TriggerSeverity = FailureSeverity.Critical
                },
                _ => new CircuitBreakerConfig()
            };
        }

        private bool IsCriticalComponent(StrategyComponent component)
        {
            return component == StrategyComponent.PositionManagement ||
                   component == StrategyComponent.RiskControls ||
                   component == StrategyComponent.DataFeeds;
        }
    }
}
