using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Monitoring
{
    /// <summary>
    /// Intelligent auto-adjustment logic that monitors performance and suggests parameter changes using the calibration system
    /// </summary>
    public class AdaptiveAdjuster : IAdaptiveAdjuster
    {
        private readonly ISettingsCalibrator _settingsCalibrator;
        private AdaptiveParameters _parameters;
        private readonly List<SettingsAdjustment> _adjustmentHistory;
        private readonly object _lockObject = new object();
        private DateTime _lastAdjustmentTime;
        private DateTime _lastPerformanceCheck;

        public AdaptiveAdjuster(ISettingsCalibrator settingsCalibrator)
        {
            _settingsCalibrator = settingsCalibrator ?? throw new ArgumentNullException(nameof(settingsCalibrator));
            _adjustmentHistory = new List<SettingsAdjustment>();
            _lastAdjustmentTime = DateTime.MinValue;
            _lastPerformanceCheck = DateTime.UtcNow;
            
            // Initialize with default parameters
            _parameters = new AdaptiveParameters
            {
                EnableAutoAdjustment = true,
                MinTradesBeforeAdjustment = 10,
                MinWinRateThreshold = 0.4m,
                MaxDrawdownThreshold = 0.15m,
                AdjustmentSensitivity = 0.5m,
                AdjustmentCooldown = TimeSpan.FromHours(1),
                ConservativeMode = false
            };
        }

        /// <summary>
        /// Check if settings need adjustment based on performance metrics and market conditions
        /// </summary>
        public bool NeedsAdjustment(
            OptimalSettings currentSettings,
            PerformanceMetrics performance,
            List<MarketDataPoint> marketData)
        {
            if (currentSettings == null)
                throw new ArgumentNullException(nameof(currentSettings));
            
            if (performance == null)
                throw new ArgumentNullException(nameof(performance));

            lock (_lockObject)
            {
                // Check if auto-adjustment is enabled
                if (!_parameters.EnableAutoAdjustment)
                    return false;

                // Check cooldown period
                var timeSinceLastAdjustment = DateTime.UtcNow - _lastAdjustmentTime;
                if (timeSinceLastAdjustment < _parameters.AdjustmentCooldown)
                    return false;

                // CRITICAL FIX: Skip minimum trades requirement for zero signal generation (emergency case)
                if (performance.TotalTrades < _parameters.MinTradesBeforeAdjustment && !IsZeroSignalGeneration(performance))
                    return false;

                // Check performance degradation triggers
                return IsPerformanceBelowThreshold(performance) || 
                       IsMarketConditionChanged(marketData) ||
                       IsSignalQualityDegraded(performance);
            }
        }

        /// <summary>
        /// Suggest intelligent settings adjustments based on performance analysis and market conditions
        /// </summary>
        public async Task<List<SettingsAdjustment>> SuggestAdjustmentsAsync(
            OptimalSettings currentSettings,
            PerformanceMetrics performance,
            List<MarketDataPoint> marketData)
        {
            if (currentSettings == null)
                throw new ArgumentNullException(nameof(currentSettings));
            
            if (performance == null)
                throw new ArgumentNullException(nameof(performance));

            var adjustments = new List<SettingsAdjustment>();

            // Analyze performance issues and suggest targeted adjustments
            await AnalyzeWinRateIssues(currentSettings, performance, adjustments);
            await AnalyzeProfitFactorIssues(currentSettings, performance, adjustments);
            await AnalyzeDrawdownIssues(currentSettings, performance, adjustments);
            await AnalyzeSignalQualityIssues(currentSettings, performance, adjustments);
            await AnalyzeVolumeThresholdIssues(currentSettings, performance, adjustments); // CRITICAL FIX: Add volume threshold analysis
            await AnalyzeExecutionRateIssues(currentSettings, performance, adjustments); // NEW: Critical execution rate analysis
            await AnalyzeMarketConditionChanges(currentSettings, marketData, adjustments);

            // Apply sensitivity scaling to adjustments
            ApplySensitivityScaling(adjustments);

            // Apply conservative mode constraints if enabled
            if (_parameters.ConservativeMode)
            {
                ApplyConservativeConstraints(adjustments);
            }

            // Validate adjustments don't conflict with each other
            ValidateAdjustmentConsistency(adjustments);

            return adjustments;
        }

        /// <summary>
        /// Apply automatic adjustments to current settings
        /// </summary>
        public OptimalSettings ApplyAdjustments(
            OptimalSettings currentSettings,
            List<SettingsAdjustment> adjustments)
        {
            if (currentSettings == null)
                throw new ArgumentNullException(nameof(currentSettings));
            
            if (adjustments == null)
                throw new ArgumentNullException(nameof(adjustments));

            lock (_lockObject)
            {
                var adjustedSettings = CloneSettings(currentSettings);

                foreach (var adjustment in adjustments.Where(a => a.IsApplied))
                {
                    ApplyIndividualAdjustment(adjustedSettings, adjustment);
                    
                    // Record adjustment in history
                    _adjustmentHistory.Add(adjustment);
                }

                // Update last adjustment time
                if (adjustments.Any(a => a.IsApplied))
                {
                    _lastAdjustmentTime = DateTime.UtcNow;
                }

                // Keep only last 50 adjustments
                while (_adjustmentHistory.Count > 50)
                {
                    _adjustmentHistory.RemoveAt(0);
                }

                return adjustedSettings;
            }
        }

        /// <summary>
        /// Get history of all adjustments made
        /// </summary>
        public List<SettingsAdjustment> GetAdjustmentHistory()
        {
            lock (_lockObject)
            {
                return new List<SettingsAdjustment>(_adjustmentHistory);
            }
        }

        /// <summary>
        /// Set adjustment parameters for customizing behavior
        /// </summary>
        public void SetAdjustmentParameters(AdaptiveParameters parameters)
        {
            lock (_lockObject)
            {
                _parameters = parameters ?? throw new ArgumentNullException(nameof(parameters));
            }
        }

        #region Private Methods

        private bool IsPerformanceBelowThreshold(PerformanceMetrics performance)
        {
            return performance.WinRate < _parameters.MinWinRateThreshold * 100 ||
                   performance.CurrentDrawdown > _parameters.MaxDrawdownThreshold * 100 ||
                   performance.ProfitFactor < 1.0m;
        }

        private bool IsMarketConditionChanged(List<MarketDataPoint> marketData)
        {
            if (marketData == null || marketData.Count < 20)
                return false;

            // Check for significant volatility changes in recent data
            var recentBars = marketData.TakeLast(20).ToList();
            var olderBars = marketData.Count > 40 ? marketData.Skip(marketData.Count - 40).Take(20).ToList() : recentBars;

            var recentVolatility = CalculateAverageVolatility(recentBars);
            var olderVolatility = CalculateAverageVolatility(olderBars);

            // Consider market condition changed if volatility changed by more than 50%
            return olderVolatility > 0 && Math.Abs(recentVolatility - olderVolatility) / olderVolatility > 0.5m;
        }

        private bool IsSignalQualityDegraded(PerformanceMetrics performance)
        {
            return performance.AverageSignalConfidence < 0.6m ||
                   performance.SignalExecutionRate < 50 ||
                   IsZeroSignalGeneration(performance) ||
                   IsZeroExecutionScenario(performance) ||  // NEW: Critical execution detection
                   IsLowExecutionRate(performance);         // NEW: Low execution detection
        }

        /// <summary>
        /// CRITICAL FIX: Detect when no signals are being generated (thresholds too high)
        /// </summary>
        private bool IsZeroSignalGeneration(PerformanceMetrics performance)
        {
            // If we have been running for a while but no signals, thresholds are likely too restrictive
            var runtimeMinutes = (DateTime.UtcNow - performance.StartTime).TotalMinutes;
            return performance.SignalsGenerated == 0 && runtimeMinutes > 5; // 5 minutes with no signals
        }

        /// <summary>
        /// CRITICAL FIX: Detect when signals are generated but none execute (confidence threshold too high)
        /// </summary>
        private bool IsZeroExecutionScenario(PerformanceMetrics performance)
        {
            var runtimeMinutes = (DateTime.UtcNow - performance.StartTime).TotalMinutes;

            // Zero execution scenario: signals generated but very low execution rate
            return performance.SignalsGenerated >= 5 &&
                   performance.SignalExecutionRate < 10 &&
                   runtimeMinutes > 10;
        }

        /// <summary>
        /// CRITICAL FIX: Detect when execution rate is critically low
        /// </summary>
        private bool IsLowExecutionRate(PerformanceMetrics performance)
        {
            var runtimeMinutes = (DateTime.UtcNow - performance.StartTime).TotalMinutes;

            // Low execution scenario: some signals executing but rate is poor
            return performance.SignalsGenerated >= 10 &&
                   performance.SignalExecutionRate < 30 &&
                   runtimeMinutes > 15;
        }

        private async Task AnalyzeWinRateIssues(OptimalSettings currentSettings, PerformanceMetrics performance, List<SettingsAdjustment> adjustments)
        {
            if (performance.WinRate < _parameters.MinWinRateThreshold * 100)
            {
                // Low win rate - increase selectivity
                var newSignalThreshold = currentSettings.SignalThreshold * 1.2m;
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "SignalThreshold",
                    OldValue = currentSettings.SignalThreshold.ToString("F2"),
                    AdjustedValue = newSignalThreshold.ToString("F2"),
                    Reason = $"Low win rate ({performance.WinRate:F1}%) - increasing selectivity",
                    Confidence = 0.8m,
                    Type = AdjustmentType.Performance,
                    IsApplied = true
                });

                // Also consider increasing volume threshold for better quality signals
                var newVolumeThreshold = currentSettings.VolumeThreshold * 1.1m;
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "VolumeThreshold",
                    OldValue = currentSettings.VolumeThreshold.ToString("F2"),
                    AdjustedValue = newVolumeThreshold.ToString("F2"),
                    Reason = "Supporting signal quality improvement",
                    Confidence = 0.6m,
                    Type = AdjustmentType.Performance,
                    IsApplied = true
                });
            }
            else if (performance.WinRate > 75 && performance.TotalTrades < 20)
            {
                // High win rate but few trades - decrease selectivity for more opportunities
                var newSignalThreshold = currentSettings.SignalThreshold * 0.9m;
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "SignalThreshold",
                    OldValue = currentSettings.SignalThreshold.ToString("F2"),
                    AdjustedValue = newSignalThreshold.ToString("F2"),
                    Reason = $"High win rate ({performance.WinRate:F1}%) but low trade frequency - decreasing selectivity",
                    Confidence = 0.7m,
                    Type = AdjustmentType.Optimization,
                    IsApplied = true
                });
            }
        }

        private async Task AnalyzeProfitFactorIssues(OptimalSettings currentSettings, PerformanceMetrics performance, List<SettingsAdjustment> adjustments)
        {
            if (performance.ProfitFactor < 1.2m)
            {
                // Poor profit factor - adjust risk management
                var newStopLoss = currentSettings.StopLossPercent * 0.9m; // Tighter stops
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "StopLossPercent",
                    OldValue = currentSettings.StopLossPercent.ToString("F2"),
                    AdjustedValue = newStopLoss.ToString("F2"),
                    Reason = $"Low profit factor ({performance.ProfitFactor:F2}) - tightening stops",
                    Confidence = 0.7m,
                    Type = AdjustmentType.RiskManagement,
                    IsApplied = true
                });
            }
        }

        private async Task AnalyzeDrawdownIssues(OptimalSettings currentSettings, PerformanceMetrics performance, List<SettingsAdjustment> adjustments)
        {
            if (performance.CurrentDrawdown > _parameters.MaxDrawdownThreshold * 100)
            {
                // High drawdown - reduce position size and increase selectivity
                var newPositionSize = currentSettings.PositionSizeUSDT * 0.8m;
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "PositionSizeUSDT",
                    OldValue = currentSettings.PositionSizeUSDT.ToString("F0"),
                    AdjustedValue = newPositionSize.ToString("F0"),
                    Reason = $"High drawdown ({performance.CurrentDrawdown:F1}%) - reducing position size",
                    Confidence = 0.9m,
                    Type = AdjustmentType.RiskManagement,
                    IsApplied = true
                });

                var newRiskAdjustment = currentSettings.RiskAdjustmentFactor * 0.8m;
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "RiskAdjustmentFactor",
                    OldValue = currentSettings.RiskAdjustmentFactor.ToString("F2"),
                    AdjustedValue = newRiskAdjustment.ToString("F2"),
                    Reason = "Supporting risk reduction due to drawdown",
                    Confidence = 0.8m,
                    Type = AdjustmentType.RiskManagement,
                    IsApplied = true
                });
            }
        }

        private async Task AnalyzeSignalQualityIssues(OptimalSettings currentSettings, PerformanceMetrics performance, List<SettingsAdjustment> adjustments)
        {
            if (performance.AverageSignalConfidence < 0.6m)
            {
                // Low signal confidence - increase thresholds
                var newDeltaThreshold = currentSettings.DeltaImbalanceThreshold * 1.15m;
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "DeltaImbalanceThreshold",
                    OldValue = currentSettings.DeltaImbalanceThreshold.ToString("F2"),
                    AdjustedValue = newDeltaThreshold.ToString("F2"),
                    Reason = $"Low signal confidence ({performance.AverageSignalConfidence:P1}) - increasing delta threshold",
                    Confidence = 0.7m,
                    Type = AdjustmentType.Optimization,
                    IsApplied = true
                });
            }
        }

        /// <summary>
        /// CRITICAL FIX: Analyze volume threshold issues and adjust when no volume blocks are detected
        /// </summary>
        private async Task AnalyzeVolumeThresholdIssues(OptimalSettings currentSettings, PerformanceMetrics performance, List<SettingsAdjustment> adjustments)
        {
            var runtimeMinutes = (DateTime.UtcNow - performance.StartTime).TotalMinutes;

            // CRITICAL: If no signals are being generated, volume threshold is likely too high
            if (IsZeroSignalGeneration(performance))
            {
                // Dramatically reduce volume threshold to enable signal generation
                var newVolumeThreshold = Math.Max(0.5m, currentSettings.VolumeThreshold * 0.6m); // Reduce by 40%, minimum 0.5x
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "VolumeThreshold",
                    OldValue = currentSettings.VolumeThreshold.ToString("F2"),
                    AdjustedValue = newVolumeThreshold.ToString("F2"),
                    Reason = $"Zero signals generated in {runtimeMinutes:F1} minutes - volume threshold too restrictive",
                    Confidence = 0.95m, // High confidence this is the issue
                    Type = AdjustmentType.Emergency, // Mark as emergency adjustment
                    IsApplied = true
                });
            }
            // If very few signals but some trades exist
            else if (performance.SignalsGenerated > 0 && performance.SignalsGenerated < 5 && runtimeMinutes > 10)
            {
                // Moderately reduce volume threshold
                var newVolumeThreshold = Math.Max(0.7m, currentSettings.VolumeThreshold * 0.8m); // Reduce by 20%, minimum 0.7x
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "VolumeThreshold",
                    OldValue = currentSettings.VolumeThreshold.ToString("F2"),
                    AdjustedValue = newVolumeThreshold.ToString("F2"),
                    Reason = $"Very few signals ({performance.SignalsGenerated}) in {runtimeMinutes:F1} minutes - reducing volume threshold",
                    Confidence = 0.8m,
                    Type = AdjustmentType.Optimization,
                    IsApplied = true
                });
            }
        }

        /// <summary>
        /// CRITICAL FIX: Analyze execution rate issues and adjust confidence thresholds
        /// </summary>
        private async Task AnalyzeExecutionRateIssues(OptimalSettings currentSettings, PerformanceMetrics performance, List<SettingsAdjustment> adjustments)
        {
            var runtimeMinutes = (DateTime.UtcNow - performance.StartTime).TotalMinutes;

            // CRITICAL: Zero execution scenario - dramatically reduce confidence requirements
            if (IsZeroExecutionScenario(performance))
            {
                // Reduce signal threshold to enable execution
                var newSignalThreshold = Math.Max(0.15m, currentSettings.SignalThreshold * 0.7m); // Reduce by 30%, minimum 0.15
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "SignalThreshold",
                    OldValue = currentSettings.SignalThreshold.ToString("F2"),
                    AdjustedValue = newSignalThreshold.ToString("F2"),
                    Reason = $"CRITICAL: Zero execution with {performance.SignalsGenerated} signals in {runtimeMinutes:F1} minutes - reducing signal threshold",
                    Confidence = 0.98m, // Very high confidence this is the issue
                    Type = AdjustmentType.Emergency,
                    IsApplied = true
                });

                // Also reduce volume threshold to increase signal opportunities
                var newVolumeThreshold = Math.Max(0.8m, currentSettings.VolumeThreshold * 0.8m); // Reduce by 20%, minimum 0.8x
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "VolumeThreshold",
                    OldValue = currentSettings.VolumeThreshold.ToString("F2"),
                    AdjustedValue = newVolumeThreshold.ToString("F2"),
                    Reason = "Supporting signal threshold reduction for execution improvement",
                    Confidence = 0.85m,
                    Type = AdjustmentType.Emergency,
                    IsApplied = true
                });
            }
            // Low execution rate - moderate adjustments
            else if (IsLowExecutionRate(performance))
            {
                var newSignalThreshold = Math.Max(0.20m, currentSettings.SignalThreshold * 0.85m); // Reduce by 15%, minimum 0.20
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "SignalThreshold",
                    OldValue = currentSettings.SignalThreshold.ToString("F2"),
                    AdjustedValue = newSignalThreshold.ToString("F2"),
                    Reason = $"Low execution rate ({performance.SignalExecutionRate:F1}%) with {performance.SignalsGenerated} signals - reducing signal threshold",
                    Confidence = 0.80m,
                    Type = AdjustmentType.Performance,
                    IsApplied = true
                });
            }
        }

        private async Task AnalyzeMarketConditionChanges(OptimalSettings currentSettings, List<MarketDataPoint> marketData, List<SettingsAdjustment> adjustments)
        {
            if (marketData == null || marketData.Count < 20)
                return;

            var recentVolatility = CalculateAverageVolatility(marketData.TakeLast(20).ToList());
            
            // If volatility is very high, suggest more conservative settings
            if (recentVolatility > 0.02m) // 2%+ average volatility
            {
                var newRiskAdjustment = Math.Min(currentSettings.RiskAdjustmentFactor * 0.9m, 0.8m);
                adjustments.Add(new SettingsAdjustment
                {
                    Parameter = "RiskAdjustmentFactor",
                    OldValue = currentSettings.RiskAdjustmentFactor.ToString("F2"),
                    AdjustedValue = newRiskAdjustment.ToString("F2"),
                    Reason = $"High market volatility ({recentVolatility:P2}) - reducing risk exposure",
                    Confidence = 0.8m,
                    Type = AdjustmentType.MarketCondition,
                    IsApplied = true
                });
            }
        }

        private void ApplySensitivityScaling(List<SettingsAdjustment> adjustments)
        {
            foreach (var adjustment in adjustments)
            {
                // Scale adjustment magnitude based on sensitivity setting
                if (decimal.TryParse(adjustment.OldValue, out var oldValue) && 
                    decimal.TryParse(adjustment.AdjustedValue, out var newValue))
                {
                    var change = newValue - oldValue;
                    var scaledChange = change * _parameters.AdjustmentSensitivity;
                    var scaledNewValue = oldValue + scaledChange;
                    
                    adjustment.AdjustedValue = scaledNewValue.ToString("F2");
                    adjustment.Confidence *= _parameters.AdjustmentSensitivity;
                }
            }
        }

        private void ApplyConservativeConstraints(List<SettingsAdjustment> adjustments)
        {
            // In conservative mode, limit the magnitude of adjustments
            foreach (var adjustment in adjustments)
            {
                if (decimal.TryParse(adjustment.OldValue, out var oldValue) && 
                    decimal.TryParse(adjustment.AdjustedValue, out var newValue))
                {
                    var changePercent = Math.Abs(newValue - oldValue) / oldValue;
                    
                    // Limit changes to maximum 10% in conservative mode
                    if (changePercent > 0.1m)
                    {
                        var direction = Math.Sign(newValue - oldValue);
                        var constrainedNewValue = oldValue * (1 + direction * 0.1m);
                        adjustment.AdjustedValue = constrainedNewValue.ToString("F2");
                        adjustment.Reason += " (Conservative mode - limited change)";
                        adjustment.Confidence *= 0.8m;
                    }
                }
            }
        }

        private void ValidateAdjustmentConsistency(List<SettingsAdjustment> adjustments)
        {
            // Check for conflicting adjustments and resolve them
            var parameterGroups = adjustments.GroupBy(a => a.Parameter);
            
            foreach (var group in parameterGroups)
            {
                if (group.Count() > 1)
                {
                    // Multiple adjustments for same parameter - keep the one with highest confidence
                    var bestAdjustment = group.OrderByDescending(a => a.Confidence).First();
                    foreach (var adjustment in group.Where(a => a != bestAdjustment))
                    {
                        adjustment.IsApplied = false;
                        adjustment.Reason += " (Superseded by higher confidence adjustment)";
                    }
                }
            }
        }

        private OptimalSettings CloneSettings(OptimalSettings original)
        {
            return new OptimalSettings
            {
                VolumeThreshold = original.VolumeThreshold,
                SignalThreshold = original.SignalThreshold,
                LookbackPeriod = original.LookbackPeriod,
                DeltaImbalanceThreshold = original.DeltaImbalanceThreshold,
                DeltaSignificanceThreshold = original.DeltaSignificanceThreshold,
                CVDLookbackPeriod = original.CVDLookbackPeriod,
                TakeProfitPercent = original.TakeProfitPercent,
                StopLossPercent = original.StopLossPercent,
                PositionSizeUSDT = original.PositionSizeUSDT,
                RiskAdjustmentFactor = original.RiskAdjustmentFactor,
                ImpactDecay = original.ImpactDecay,
                PriceChangeMultiplier = original.PriceChangeMultiplier,
                UseAbsolutePrice = original.UseAbsolutePrice,
                EnableSessionAdjustments = original.EnableSessionAdjustments,
                AsianSessionMultiplier = original.AsianSessionMultiplier,
                EuropeanSessionMultiplier = original.EuropeanSessionMultiplier,
                USSessionMultiplier = original.USSessionMultiplier
            };
        }

        private void ApplyIndividualAdjustment(OptimalSettings settings, SettingsAdjustment adjustment)
        {
            if (!decimal.TryParse(adjustment.AdjustedValue, out var newValue))
                return;

            switch (adjustment.Parameter)
            {
                case "VolumeThreshold":
                    settings.VolumeThreshold = newValue;
                    break;
                case "SignalThreshold":
                    settings.SignalThreshold = newValue;
                    break;
                case "DeltaImbalanceThreshold":
                    settings.DeltaImbalanceThreshold = newValue;
                    break;
                case "TakeProfitPercent":
                    settings.TakeProfitPercent = newValue;
                    break;
                case "StopLossPercent":
                    settings.StopLossPercent = newValue;
                    break;
                case "PositionSizeUSDT":
                    settings.PositionSizeUSDT = newValue;
                    break;
                case "RiskAdjustmentFactor":
                    settings.RiskAdjustmentFactor = newValue;
                    break;
            }
        }

        private decimal CalculateAverageVolatility(List<MarketDataPoint> bars)
        {
            if (bars.Count == 0)
                return 0;

            return bars.Average(bar => bar.Open > 0 ? Math.Abs(bar.Close - bar.Open) / bar.Open : 0);
        }

        #endregion
    }
}
