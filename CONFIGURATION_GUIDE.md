# SmartVolumeStrategy Configuration Guide

## 📋 Table of Contents
1. [Configuration Overview](#configuration-overview)
2. [Strategy Control Settings](#strategy-control-settings)
3. [Risk Management Settings](#risk-management-settings)
4. [Calibration Settings](#calibration-settings)
5. [Position Management Settings](#position-management-settings)
6. [Signal Filtering Settings](#signal-filtering-settings)
7. [Manual Override Settings](#manual-override-settings)
8. [Recommended Configurations](#recommended-configurations)

---

## ⚙️ Configuration Overview

SmartVolumeStrategy uses a **layered configuration approach**:

1. **Auto-Calibrated Settings**: Automatically optimized based on symbol analysis
2. **User Constraints**: Risk limits and preferences set by the user
3. **Manual Overrides**: Optional manual control over specific parameters
4. **Adaptive Adjustments**: Real-time modifications based on performance

### Configuration Philosophy
- **Safety First**: All settings have conservative defaults
- **Auto-Optimization**: Most parameters are automatically calibrated
- **User Control**: Critical risk parameters remain user-controlled
- **Adaptive Learning**: Settings improve over time based on performance

---

## 🎮 Strategy Control Settings

### Enable Strategy
**Setting**: `EnableStrategy`
**Type**: Boolean
**Default**: `false`
**Description**: Master switch to enable/disable trading

**Usage**:
- **false**: Strategy analyzes market but doesn't place trades (safe mode)
- **true**: Strategy actively trades based on signals

**Best Practices**:
- Start with `false` to observe calibration and signals
- Enable only after reviewing initial performance
- Disable during major news events or market volatility

### Enable Auto-Calibration
**Setting**: `EnableAutoCalibration`
**Type**: Boolean
**Default**: `true`
**Description**: Automatically optimize settings based on symbol analysis

**Usage**:
- **true**: Strategy analyzes symbol and optimizes parameters (recommended)
- **false**: Uses default settings without optimization

**When to Disable**:
- Testing specific parameter combinations
- Using strategy on well-known symbols with proven settings
- Debugging or development purposes

---

## 🛡️ Risk Management Settings

### Max Position Size (USDT)
**Setting**: `MaxPositionSizeUSDT`
**Type**: Decimal
**Range**: 100 - 10,000 USDT
**Default**: 2,000 USDT
**Description**: Maximum position size in USDT value

**Calculation**:
```
Actual Position Size = Min(MaxPositionSizeUSDT, Account_Balance * RiskPercentPerTrade / 100)
```

**Recommendations by Account Size**:
- **Small Account ($1,000-$5,000)**: 100-500 USDT
- **Medium Account ($5,000-$25,000)**: 500-2,000 USDT
- **Large Account ($25,000+)**: 2,000-10,000 USDT

### Risk Per Trade (%)
**Setting**: `RiskPercentPerTrade`
**Type**: Decimal
**Range**: 0.5% - 5.0%
**Default**: 2.0%
**Description**: Maximum percentage of account to risk per trade

**Risk Levels**:
- **Conservative (0.5-1.0%)**: Slow growth, minimal risk
- **Moderate (1.5-2.5%)**: Balanced risk/reward (recommended)
- **Aggressive (3.0-5.0%)**: Higher returns, higher risk

**Impact on Position Sizing**:
```
Position Size = (Account Balance * RiskPercentPerTrade) / StopLossPercent
```

### Conservative Mode
**Setting**: `ConservativeMode`
**Type**: Boolean
**Default**: `false`
**Description**: Use more conservative parameter adjustments

**Conservative Mode Effects**:
- **Signal Thresholds**: 20% higher than normal
- **Position Sizing**: 15% smaller than calculated
- **Adaptive Adjustments**: Slower and more gradual
- **Risk Limits**: Tighter stop losses

**When to Enable**:
- New to algorithmic trading
- Volatile market conditions
- Testing new symbols
- Risk-averse trading approach

---

## 🔬 Calibration Settings

### Calibration Bars
**Setting**: `CalibrationBars`
**Type**: Integer
**Range**: 200 - 500
**Default**: 300
**Description**: Number of historical bars to analyze for calibration

**Recommendations by Timeframe**:
- **1-minute charts**: 300-400 bars (5-7 hours of data)
- **5-minute charts**: 250-350 bars (20-30 hours of data)
- **15-minute charts**: 200-300 bars (2-3 days of data)
- **1-hour charts**: 200-250 bars (8-10 days of data)

**Trade-offs**:
- **More Bars**: Better statistical significance, slower startup
- **Fewer Bars**: Faster startup, may miss long-term patterns

**Market-Specific Adjustments**:
- **24/7 Crypto Markets**: Use standard settings
- **Traditional Markets**: Increase bars to account for gaps
- **Low Volume Symbols**: Increase bars for better analysis

---

## 💼 Position Management Settings

### Take Profit (%)
**Setting**: `TakeProfitPercent`
**Type**: Decimal
**Range**: 0.1% - 10.0%
**Default**: 1.2%
**Description**: Profit target as percentage of entry price

**Recommendations by Market**:
- **Crypto Scalping (1-5m)**: 0.5-1.0%
- **Crypto Swing (15m-1h)**: 1.0-2.5%
- **Forex Scalping**: 0.2-0.5%
- **Stock Trading**: 1.0-3.0%

**Volatility Adjustments**:
```
High Volatility: TP = 1.5-2.5% (wider targets)
Medium Volatility: TP = 0.8-1.5% (balanced)
Low Volatility: TP = 0.3-0.8% (tighter targets)
```

### Stop Loss (%)
**Setting**: `StopLossPercent`
**Type**: Decimal
**Range**: 0.1% - 5.0%
**Default**: 0.8%
**Description**: Maximum loss as percentage of entry price

**Risk/Reward Ratios**:
- **Conservative**: SL = TP * 0.5 (2:1 ratio)
- **Balanced**: SL = TP * 0.67 (1.5:1 ratio)
- **Aggressive**: SL = TP * 0.8 (1.25:1 ratio)

### Position Cooldown (seconds)
**Setting**: `PositionCooldownSeconds`
**Type**: Integer
**Range**: 5 - 300 seconds
**Default**: 10 seconds
**Description**: Minimum time between position openings

**Recommendations**:
- **Scalping (1-5m)**: 10-30 seconds
- **Day Trading (15m-1h)**: 60-300 seconds
- **Swing Trading**: 300+ seconds

**Purpose**:
- Prevents signal spam
- Allows market to settle after trades
- Reduces transaction costs
- Improves signal quality

---

## 🎯 Signal Filtering Settings

### Minimum Signal Quality
**Setting**: `MinimumSignalQuality`
**Type**: Enum (Poor, Fair, Good, Excellent)
**Default**: `Good`
**Description**: Minimum required signal quality level

**Quality Levels**:
- **Poor (0-49%)**: Very weak signals, high risk
- **Fair (50-69%)**: Moderate signals, medium risk
- **Good (70-89%)**: Strong signals, lower risk (recommended)
- **Excellent (90-100%)**: Highest quality, very selective

**Trade-offs**:
- **Higher Quality**: Fewer signals, higher win rate
- **Lower Quality**: More signals, lower win rate

### Minimum Signal Confidence (%)
**Setting**: `MinimumSignalConfidence`
**Type**: Decimal
**Range**: 50% - 95%
**Default**: 70%
**Description**: Minimum signal confidence percentage

**Confidence Levels**:
- **50-60%**: Very permissive, many signals
- **60-70%**: Moderate filtering, balanced approach
- **70-80%**: Conservative filtering (recommended)
- **80-95%**: Very selective, few high-quality signals

**Dynamic Adjustment**:
The strategy automatically adjusts this threshold based on performance:
```
If win_rate < 45%: Increase confidence requirement
If win_rate > 70%: Decrease confidence requirement
```

---

## 🔧 Manual Override Settings

### Manual Volume Threshold
**Setting**: `ManualVolumeThreshold`
**Type**: Decimal
**Range**: 0 - 5.0
**Default**: 0 (auto-calibrated)
**Description**: Override auto-calibrated volume threshold

**Usage**:
- **0**: Use auto-calibrated threshold (recommended)
- **1.5-2.0**: More signals, lower quality
- **2.0-2.5**: Balanced approach
- **2.5-3.0**: Fewer signals, higher quality

**When to Override**:
- Testing specific threshold values
- Symbol-specific optimizations
- Market condition adjustments

### Manual Signal Threshold
**Setting**: `ManualSignalThreshold`
**Type**: Decimal
**Range**: 0 - 3.0
**Default**: 0 (auto-calibrated)
**Description**: Override auto-calibrated signal threshold

**Threshold Effects**:
- **0.3-0.5**: Very permissive, many signals
- **0.8-1.2**: Balanced approach (typical auto-calibrated range)
- **1.5-2.0**: Conservative, fewer signals
- **2.0+**: Very selective, minimal signals

---

## 📊 Recommended Configurations

### Crypto Scalping (1-5 minute charts)
```yaml
Strategy Control:
  EnableStrategy: false (start with observation)
  EnableAutoCalibration: true

Risk Management:
  MaxPositionSizeUSDT: 1000-2000
  RiskPercentPerTrade: 1.5-2.0%
  ConservativeMode: false

Position Management:
  TakeProfitPercent: 0.8-1.2%
  StopLossPercent: 0.5-0.8%
  PositionCooldownSeconds: 10-30

Signal Filtering:
  MinimumSignalQuality: Good
  MinimumSignalConfidence: 70-75%

Calibration:
  CalibrationBars: 300-400
```

### Crypto Swing Trading (15m-1h charts)
```yaml
Strategy Control:
  EnableStrategy: false (start with observation)
  EnableAutoCalibration: true

Risk Management:
  MaxPositionSizeUSDT: 2000-5000
  RiskPercentPerTrade: 2.0-3.0%
  ConservativeMode: false

Position Management:
  TakeProfitPercent: 1.5-2.5%
  StopLossPercent: 1.0-1.5%
  PositionCooldownSeconds: 60-300

Signal Filtering:
  MinimumSignalQuality: Good
  MinimumSignalConfidence: 65-70%

Calibration:
  CalibrationBars: 250-300
```

### Conservative Setup (Risk-Averse)
```yaml
Strategy Control:
  EnableStrategy: false (start with observation)
  EnableAutoCalibration: true

Risk Management:
  MaxPositionSizeUSDT: 500-1000
  RiskPercentPerTrade: 1.0-1.5%
  ConservativeMode: true

Position Management:
  TakeProfitPercent: 1.0-1.5%
  StopLossPercent: 0.5-0.7%
  PositionCooldownSeconds: 30-60

Signal Filtering:
  MinimumSignalQuality: Excellent
  MinimumSignalConfidence: 80-85%

Calibration:
  CalibrationBars: 400-500
```

### Aggressive Setup (High Risk/Reward)
```yaml
Strategy Control:
  EnableStrategy: false (start with observation)
  EnableAutoCalibration: true

Risk Management:
  MaxPositionSizeUSDT: 3000-10000
  RiskPercentPerTrade: 3.0-5.0%
  ConservativeMode: false

Position Management:
  TakeProfitPercent: 1.5-3.0%
  StopLossPercent: 1.0-2.0%
  PositionCooldownSeconds: 5-15

Signal Filtering:
  MinimumSignalQuality: Fair
  MinimumSignalConfidence: 60-65%

Calibration:
  CalibrationBars: 200-300
```

---

## 🔄 Configuration Workflow

### Initial Setup
1. **Start Conservative**: Use conservative settings initially
2. **Enable Observation**: Set `EnableStrategy = false` to observe
3. **Monitor Calibration**: Wait for successful calibration
4. **Review Signals**: Analyze signal quality and frequency
5. **Gradual Activation**: Enable trading with small position sizes

### Optimization Process
1. **Collect Data**: Run for 24-48 hours to gather performance data
2. **Analyze Results**: Review win rate, profit factor, drawdown
3. **Adjust Parameters**: Fine-tune based on performance
4. **Test Changes**: Implement one change at a time
5. **Monitor Impact**: Evaluate effect of each adjustment

### Ongoing Maintenance
1. **Weekly Review**: Check performance metrics weekly
2. **Market Adaptation**: Adjust for changing market conditions
3. **Symbol Rotation**: Recalibrate when switching symbols
4. **Performance Monitoring**: Watch for degradation in performance

---

*This configuration guide provides comprehensive details for optimizing SmartVolumeStrategy settings. For basic usage, see the User Guide.*
