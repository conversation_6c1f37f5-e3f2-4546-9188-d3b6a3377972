using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models;
using SmartVolumeStrategy.Core.Models.Analysis;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Phase 2: Enhanced Signal Quality Analysis
    /// Provides multi-factor signal quality scoring with adaptive thresholds and trend analysis
    /// </summary>
    public class EnhancedSignalQualityAnalyzer
    {
        private readonly Action<string> _logAction;
        private readonly Queue<QualityHistoryEntry> _qualityHistory;
        private readonly Dictionary<SignalType, QualityStatistics> _qualityStats;
        private readonly object _lockObject = new object();
        
        // Phase 2: Adaptive quality thresholds
        private QualityThresholds _currentThresholds;
        private QualityThresholds _baseThresholds;
        private DateTime _lastThresholdUpdate;
        
        // Phase 2: Quality trend analysis
        private decimal _qualityTrend;
        private decimal _qualityVolatility;
        private int _consecutiveQualityDeclines;
        
        // Configuration
        private const int MAX_QUALITY_HISTORY = 100;
        private const int QUALITY_TREND_WINDOW = 20;
        private const decimal QUALITY_TREND_THRESHOLD = 0.05m;
        private const int MAX_CONSECUTIVE_DECLINES = 5;

        public EnhancedSignalQualityAnalyzer(Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _qualityHistory = new Queue<QualityHistoryEntry>();
            _qualityStats = new Dictionary<SignalType, QualityStatistics>();
            
            // Initialize more realistic base thresholds for current market conditions
            _baseThresholds = new QualityThresholds
            {
                Poor = 0.2m,        // Reduced from 0.3m
                Fair = 0.4m,        // Reduced from 0.5m
                Good = 0.55m,       // Reduced from 0.7m - key change for current signals
                Excellent = 0.7m,   // Reduced from 0.8m
                Exceptional = 0.85m // Reduced from 0.9m
            };
            
            _currentThresholds = _baseThresholds.Clone();
            _lastThresholdUpdate = DateTime.UtcNow;
            
            InitializeQualityStats();
        }

        /// <summary>
        /// Phase 2: Enhanced signal quality determination with multi-factor analysis
        /// </summary>
        public EnhancedSignalQuality AnalyzeSignalQuality(
            TradingSignal signal, 
            MarketContext marketContext, 
            MultiTimeframeAnalysisState multiTimeframeState = null,
            List<MarketDataPoint> recentHistory = null)
        {
            lock (_lockObject)
            {
                try
                {
                    var analysisStartTime = DateTime.UtcNow;
                    
                    // Phase 2: Multi-factor quality scoring
                    var qualityFactors = CalculateQualityFactors(signal, marketContext, multiTimeframeState, recentHistory);
                    
                    // Phase 2: Weighted quality score calculation
                    var weightedScore = CalculateWeightedQualityScore(qualityFactors);
                    
                    // Phase 2: Adaptive threshold application
                    var qualityLevel = DetermineQualityLevel(weightedScore);
                    
                    // Phase 2: Quality trend analysis
                    var qualityTrend = AnalyzeQualityTrend(weightedScore);
                    
                    // Phase 2: Market condition adjustment
                    var adjustedQuality = ApplyMarketConditionAdjustment(qualityLevel, marketContext, qualityTrend);
                    
                    // Create enhanced quality result
                    var enhancedQuality = new EnhancedSignalQuality
                    {
                        Level = adjustedQuality,
                        Score = weightedScore,
                        Factors = qualityFactors,
                        Trend = qualityTrend,
                        MarketConditionAdjustment = adjustedQuality != qualityLevel,
                        AnalysisTime = DateTime.UtcNow - analysisStartTime,
                        Confidence = CalculateQualityConfidence(qualityFactors, qualityTrend)
                    };
                    
                    // Update quality history and statistics
                    UpdateQualityHistory(enhancedQuality, signal.Type);
                    
                    // Phase 2: Adaptive threshold adjustment
                    if (ShouldUpdateThresholds())
                    {
                        UpdateAdaptiveThresholds();
                    }
                    
                    LogQualityAnalysis(enhancedQuality, signal);
                    
                    return enhancedQuality;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ ERROR in EnhancedSignalQualityAnalyzer: {ex.Message}");
                    
                    // Fallback to basic quality determination
                    return new EnhancedSignalQuality
                    {
                        Level = DetermineBasicQuality(signal.Confidence),
                        Score = signal.Confidence,
                        Factors = new QualityFactors(),
                        Trend = QualityTrend.Stable,
                        MarketConditionAdjustment = false,
                        AnalysisTime = TimeSpan.Zero,
                        Confidence = 0.5m
                    };
                }
            }
        }

        /// <summary>
        /// Phase 2: Calculate multi-factor quality components
        /// </summary>
        private QualityFactors CalculateQualityFactors(
            TradingSignal signal, 
            MarketContext marketContext, 
            MultiTimeframeAnalysisState multiTimeframeState,
            List<MarketDataPoint> recentHistory)
        {
            var factors = new QualityFactors();
            
            // Factor 1: Signal Confidence (Weight: 25%)
            factors.SignalConfidence = signal.Confidence;
            factors.SignalConfidenceWeight = 0.25m;
            
            // Factor 2: Market Context Alignment (Weight: 20%)
            factors.MarketAlignment = CalculateMarketAlignment(signal, marketContext);
            factors.MarketAlignmentWeight = 0.20m;
            
            // Factor 3: Multi-timeframe Consistency (Weight: 20%)
            if (multiTimeframeState != null)
            {
                factors.TimeframeConsistency = multiTimeframeState.TrendAlignment;
                factors.TimeframeConsistencyWeight = 0.20m;
            }
            else
            {
                factors.TimeframeConsistency = 0.7m; // Default neutral
                factors.TimeframeConsistencyWeight = 0.10m; // Reduced weight
            }
            
            // Factor 4: Historical Performance (Weight: 15%)
            factors.HistoricalPerformance = CalculateHistoricalPerformance(signal.Type);
            factors.HistoricalPerformanceWeight = 0.15m;
            
            // Factor 5: Signal Strength (Weight: 10%)
            factors.SignalStrength = CalculateSignalStrength(signal, recentHistory);
            factors.SignalStrengthWeight = 0.10m;
            
            // Factor 6: Market Volatility Adjustment (Weight: 10%)
            factors.VolatilityAdjustment = CalculateVolatilityAdjustment(marketContext);
            factors.VolatilityAdjustmentWeight = 0.10m;
            
            return factors;
        }

        /// <summary>
        /// Phase 2: Calculate weighted quality score from factors
        /// </summary>
        private decimal CalculateWeightedQualityScore(QualityFactors factors)
        {
            var weightedSum = 
                (factors.SignalConfidence * factors.SignalConfidenceWeight) +
                (factors.MarketAlignment * factors.MarketAlignmentWeight) +
                (factors.TimeframeConsistency * factors.TimeframeConsistencyWeight) +
                (factors.HistoricalPerformance * factors.HistoricalPerformanceWeight) +
                (factors.SignalStrength * factors.SignalStrengthWeight) +
                (factors.VolatilityAdjustment * factors.VolatilityAdjustmentWeight);
            
            var totalWeight = 
                factors.SignalConfidenceWeight +
                factors.MarketAlignmentWeight +
                factors.TimeframeConsistencyWeight +
                factors.HistoricalPerformanceWeight +
                factors.SignalStrengthWeight +
                factors.VolatilityAdjustmentWeight;
            
            return totalWeight > 0 ? weightedSum / totalWeight : 0.5m;
        }

        /// <summary>
        /// Phase 2: Determine quality level using adaptive thresholds
        /// </summary>
        private SignalQuality DetermineQualityLevel(decimal score)
        {
            if (score >= _currentThresholds.Exceptional)
                return SignalQuality.Exceptional;
            if (score >= _currentThresholds.Excellent)
                return SignalQuality.Excellent;
            if (score >= _currentThresholds.Good)
                return SignalQuality.Good;
            if (score >= _currentThresholds.Fair)
                return SignalQuality.Fair;
            
            return SignalQuality.Poor;
        }

        /// <summary>
        /// Phase 2: Analyze quality trend over recent history
        /// </summary>
        private QualityTrend AnalyzeQualityTrend(decimal currentScore)
        {
            if (_qualityHistory.Count < QUALITY_TREND_WINDOW)
                return QualityTrend.Stable;
            
            var recentScores = _qualityHistory.TakeLast(QUALITY_TREND_WINDOW).Select(q => q.Score).ToList();
            var trend = CalculateLinearTrend(recentScores);
            
            _qualityTrend = trend;
            _qualityVolatility = CalculateVolatility(recentScores);
            
            if (trend > QUALITY_TREND_THRESHOLD)
            {
                _consecutiveQualityDeclines = 0;
                return QualityTrend.Improving;
            }
            
            if (trend < -QUALITY_TREND_THRESHOLD)
            {
                _consecutiveQualityDeclines++;
                return QualityTrend.Declining;
            }
            
            _consecutiveQualityDeclines = 0;
            return QualityTrend.Stable;
        }

        /// <summary>
        /// Phase 2: Apply market condition adjustments to quality
        /// </summary>
        private SignalQuality ApplyMarketConditionAdjustment(
            SignalQuality baseQuality, 
            MarketContext marketContext, 
            QualityTrend trend)
        {
            var adjustment = 0;
            
            // Upgrade for optimal trading conditions
            if (marketContext.IsOptimalTradingTime && marketContext.VolatilityRegime == VolatilityRegime.Normal)
            {
                adjustment += 1;
            }
            
            // Upgrade for improving quality trend
            if (trend == QualityTrend.Improving)
            {
                adjustment += 1;
            }
            
            // Downgrade for declining quality trend
            if (trend == QualityTrend.Declining && _consecutiveQualityDeclines >= MAX_CONSECUTIVE_DECLINES)
            {
                adjustment -= 1;
            }
            
            // Downgrade for extreme volatility
            if (marketContext.VolatilityRegime == VolatilityRegime.High)
            {
                adjustment -= 1;
            }
            
            // Apply adjustment
            var adjustedQuality = (int)baseQuality + adjustment;
            adjustedQuality = Math.Max(0, Math.Min(4, adjustedQuality)); // Clamp to valid range
            
            return (SignalQuality)adjustedQuality;
        }

        /// <summary>
        /// Calculate market alignment factor
        /// </summary>
        private decimal CalculateMarketAlignment(TradingSignal signal, MarketContext marketContext)
        {
            var alignmentScore = 0.5m; // Base neutral score

            // CVD alignment
            var cvdAlignment = Math.Sign(marketContext.CVDTrend) == (signal.Type == SignalType.Long ? 1 : -1);
            if (cvdAlignment) alignmentScore += 0.2m;

            // Volatility regime alignment
            if (marketContext.VolatilityRegime == VolatilityRegime.Normal) alignmentScore += 0.15m;

            // Trading session alignment
            if (marketContext.IsOptimalTradingTime) alignmentScore += 0.15m;

            return Math.Max(0m, Math.Min(1m, alignmentScore));
        }

        /// <summary>
        /// Calculate historical performance factor
        /// </summary>
        private decimal CalculateHistoricalPerformance(SignalType signalType)
        {
            if (!_qualityStats.ContainsKey(signalType))
                return 0.7m; // Default neutral

            var stats = _qualityStats[signalType];
            if (stats.TotalSignals == 0)
                return 0.7m;

            return Math.Max(0.3m, Math.Min(1.0m, stats.AverageQuality));
        }

        /// <summary>
        /// Calculate signal strength factor
        /// </summary>
        private decimal CalculateSignalStrength(TradingSignal signal, List<MarketDataPoint> recentHistory)
        {
            if (recentHistory == null || recentHistory.Count < 5)
                return 0.7m; // Default neutral

            // Analyze volume and price action strength
            var avgVolume = recentHistory.Average(h => h.Volume);
            var currentVolumeRatio = recentHistory.Last().Volume / Math.Max(avgVolume, 1);

            var strengthScore = Math.Min(1.0m, currentVolumeRatio / 2.0m); // Normalize volume ratio

            return Math.Max(0.3m, strengthScore);
        }

        /// <summary>
        /// Calculate volatility adjustment factor
        /// </summary>
        private decimal CalculateVolatilityAdjustment(MarketContext marketContext)
        {
            return marketContext.VolatilityRegime switch
            {
                VolatilityRegime.Low => 0.8m,      // Slightly reduce quality in low volatility
                VolatilityRegime.Normal => 1.0m,   // No adjustment
                VolatilityRegime.High => 0.6m,     // Reduce quality in high volatility
                _ => 0.7m
            };
        }

        /// <summary>
        /// Calculate quality confidence
        /// </summary>
        private decimal CalculateQualityConfidence(QualityFactors factors, QualityTrend trend)
        {
            var baseConfidence = factors.SignalConfidence;

            // Adjust based on factor consistency
            var factorValues = new[]
            {
                factors.SignalConfidence,
                factors.MarketAlignment,
                factors.TimeframeConsistency,
                factors.HistoricalPerformance,
                factors.SignalStrength,
                factors.VolatilityAdjustment
            };

            var factorVariance = CalculateVariance(factorValues);
            var consistencyBonus = Math.Max(0m, 0.2m - factorVariance); // Bonus for consistent factors

            // Trend adjustment
            var trendAdjustment = trend switch
            {
                QualityTrend.Improving => 0.1m,
                QualityTrend.Declining => -0.1m,
                _ => 0m
            };

            return Math.Max(0.1m, Math.Min(1.0m, baseConfidence + consistencyBonus + trendAdjustment));
        }

        /// <summary>
        /// Update quality history and statistics
        /// </summary>
        private void UpdateQualityHistory(EnhancedSignalQuality quality, SignalType signalType)
        {
            // Add to history
            _qualityHistory.Enqueue(new QualityHistoryEntry
            {
                Timestamp = DateTime.UtcNow,
                Score = quality.Score,
                Level = quality.Level,
                SignalType = signalType,
                Trend = quality.Trend
            });

            // Maintain history size
            while (_qualityHistory.Count > MAX_QUALITY_HISTORY)
            {
                _qualityHistory.Dequeue();
            }

            // Update statistics
            if (_qualityStats.ContainsKey(signalType))
            {
                var stats = _qualityStats[signalType];
                stats.TotalSignals++;
                stats.TotalQualityScore += quality.Score;
                stats.AverageQuality = stats.TotalQualityScore / stats.TotalSignals;
                stats.LastUpdate = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Check if thresholds should be updated
        /// </summary>
        private bool ShouldUpdateThresholds()
        {
            var timeSinceUpdate = DateTime.UtcNow - _lastThresholdUpdate;
            return timeSinceUpdate.TotalMinutes >= 10 && _qualityHistory.Count >= QUALITY_TREND_WINDOW;
        }

        /// <summary>
        /// Update adaptive thresholds based on recent performance
        /// </summary>
        private void UpdateAdaptiveThresholds()
        {
            if (_qualityHistory.Count < QUALITY_TREND_WINDOW)
                return;

            var recentScores = _qualityHistory.TakeLast(QUALITY_TREND_WINDOW).Select(q => q.Score).ToList();
            var avgScore = recentScores.Average();
            var scoreVolatility = CalculateVolatility(recentScores);

            // Adjust thresholds based on recent performance
            var adjustment = (avgScore - 0.7m) * 0.1m; // Adjust by 10% of deviation from target

            _currentThresholds = new QualityThresholds
            {
                Poor = Math.Max(0.1m, _baseThresholds.Poor + adjustment),
                Fair = Math.Max(0.2m, _baseThresholds.Fair + adjustment),
                Good = Math.Max(0.4m, _baseThresholds.Good + adjustment),
                Excellent = Math.Max(0.6m, _baseThresholds.Excellent + adjustment),
                Exceptional = Math.Max(0.8m, _baseThresholds.Exceptional + adjustment)
            };

            _lastThresholdUpdate = DateTime.UtcNow;

            _logAction($"📊 Adaptive thresholds updated - Avg Score: {avgScore:F3}, Adjustment: {adjustment:F3}");
        }

        /// <summary>
        /// Calculate linear trend from data points
        /// </summary>
        private decimal CalculateLinearTrend(List<decimal> values)
        {
            if (values.Count < 2) return 0;

            var n = values.Count;
            var sumX = n * (n - 1) / 2; // Sum of indices 0,1,2...n-1
            var sumY = values.Sum();
            var sumXY = values.Select((y, x) => x * y).Sum();
            var sumX2 = Enumerable.Range(0, n).Sum(x => x * x);

            var slope = (decimal)(n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
            return slope;
        }

        /// <summary>
        /// Calculate variance of values
        /// </summary>
        private decimal CalculateVariance(decimal[] values)
        {
            if (values.Length < 2) return 0;

            var mean = values.Average();
            var variance = values.Sum(v => (v - mean) * (v - mean)) / values.Length;
            return variance;
        }

        /// <summary>
        /// Calculate volatility (standard deviation) of values
        /// </summary>
        private decimal CalculateVolatility(List<decimal> values)
        {
            if (values.Count < 2) return 0;

            var mean = values.Average();
            var variance = values.Sum(v => (v - mean) * (v - mean)) / values.Count;
            return (decimal)Math.Sqrt((double)variance);
        }

        /// <summary>
        /// Log quality analysis results
        /// </summary>
        private void LogQualityAnalysis(EnhancedSignalQuality quality, TradingSignal signal)
        {
            _logAction($"🎯 Enhanced Quality Analysis: {quality.Level} (Score: {quality.Score:F3}, Confidence: {quality.Confidence:F3})");
            _logAction($"  • Signal Confidence: {quality.Factors.SignalConfidence:F3} (Weight: {quality.Factors.SignalConfidenceWeight:F2})");
            _logAction($"  • Market Alignment: {quality.Factors.MarketAlignment:F3} (Weight: {quality.Factors.MarketAlignmentWeight:F2})");
            _logAction($"  • Timeframe Consistency: {quality.Factors.TimeframeConsistency:F3} (Weight: {quality.Factors.TimeframeConsistencyWeight:F2})");
            _logAction($"  • Quality Trend: {quality.Trend}");
            if (quality.MarketConditionAdjustment)
            {
                _logAction($"  • Market Condition Adjustment Applied");
            }
        }

        private void InitializeQualityStats()
        {
            foreach (SignalType signalType in Enum.GetValues<SignalType>())
            {
                _qualityStats[signalType] = new QualityStatistics();
            }
        }

        private SignalQuality DetermineBasicQuality(decimal confidence)
        {
            if (confidence >= 0.9m) return SignalQuality.Exceptional;
            if (confidence >= 0.8m) return SignalQuality.Excellent;
            if (confidence >= 0.7m) return SignalQuality.Good;
            if (confidence >= 0.5m) return SignalQuality.Fair;
            return SignalQuality.Poor;
        }
    }
}
