using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Master symbol analyzer that coordinates all analysis components to build comprehensive symbol profile
    /// </summary>
    public class SymbolAnalyzer : ISymbolAnalyzer
    {
        private readonly IVolumePatternAnalyzer _volumeAnalyzer;
        private readonly IDeltaFlowAnalyzer _deltaAnalyzer;
        private readonly IVolatilityAnalyzer _volatilityAnalyzer;
        private readonly IMarketProfileAnalyzer _marketProfileAnalyzer;
        
        private CalibrationStatus _analysisStatus;
        private CancellationTokenSource _cancellationTokenSource;
        private readonly object _statusLock = new object();

        public SymbolAnalyzer(
            IVolumePatternAnalyzer volumeAnalyzer,
            IDeltaFlowAnalyzer deltaAnalyzer,
            IVolatilityAnalyzer volatilityAnalyzer,
            IMarketProfileAnalyzer marketProfileAnalyzer)
        {
            _volumeAnalyzer = volumeAnalyzer ?? throw new ArgumentNullException(nameof(volumeAnalyzer));
            _deltaAnalyzer = deltaAnalyzer ?? throw new ArgumentNullException(nameof(deltaAnalyzer));
            _volatilityAnalyzer = volatilityAnalyzer ?? throw new ArgumentNullException(nameof(volatilityAnalyzer));
            _marketProfileAnalyzer = marketProfileAnalyzer ?? throw new ArgumentNullException(nameof(marketProfileAnalyzer));
            
            InitializeAnalysisStatus();
        }

        /// <summary>
        /// Analyze symbol characteristics from historical data
        /// </summary>
        public async Task<SymbolProfile> AnalyzeSymbolAsync(string symbol, List<MarketDataPoint> marketData, int barsToAnalyze = 300)
        {
            if (string.IsNullOrEmpty(symbol))
                throw new ArgumentException("Symbol cannot be null or empty", nameof(symbol));
            
            if (marketData == null || marketData.Count == 0)
                throw new ArgumentException("Market data cannot be null or empty", nameof(marketData));

            var correlationId = Guid.NewGuid().ToString("N")[..8];
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                var cancellationToken = _cancellationTokenSource.Token;

                UpdateAnalysisStatus(CalibrationPhase.DataCollection, 0, "Preparing market data for analysis");

                // Prepare data for analysis (take most recent bars)
                var analysisData = PrepareAnalysisData(marketData, barsToAnalyze);
                
                UpdateAnalysisStatus(CalibrationPhase.DataCollection, 10, $"Analyzing {analysisData.Count} bars for {symbol}");

                var symbolProfile = new SymbolProfile
                {
                    Symbol = symbol,
                    AnalysisTimestamp = DateTime.UtcNow,
                    BarsAnalyzed = analysisData.Count,
                    CorrelationId = correlationId
                };

                // Phase 1: Volume Pattern Analysis (20% of progress)
                UpdateAnalysisStatus(CalibrationPhase.VolumeAnalysis, 20, "Analyzing volume patterns and characteristics");
                cancellationToken.ThrowIfCancellationRequested();
                
                symbolProfile.Volume = await AnalyzeVolumeAsync(analysisData, cancellationToken);

                // Phase 2: Delta Flow Analysis (40% of progress)
                UpdateAnalysisStatus(CalibrationPhase.DeltaAnalysis, 40, "Analyzing delta flow and buy/sell pressure");
                cancellationToken.ThrowIfCancellationRequested();
                
                symbolProfile.DeltaFlow = await AnalyzeDeltaFlowAsync(analysisData, cancellationToken);

                // Phase 3: Volatility Analysis (60% of progress)
                UpdateAnalysisStatus(CalibrationPhase.VolatilityAnalysis, 60, "Analyzing volatility and price movement patterns");
                cancellationToken.ThrowIfCancellationRequested();
                
                symbolProfile.Volatility = await AnalyzeVolatilityAsync(analysisData, cancellationToken);

                // Phase 4: Market Profile Analysis (80% of progress)
                UpdateAnalysisStatus(CalibrationPhase.MarketProfileAnalysis, 80, "Analyzing market profile and session characteristics");
                cancellationToken.ThrowIfCancellationRequested();
                
                symbolProfile.MarketProfile = await AnalyzeMarketProfileAsync(analysisData, cancellationToken);

                // Phase 5: Calculate Overall Confidence (100% of progress)
                UpdateAnalysisStatus(CalibrationPhase.SettingsFinalization, 95, "Calculating analysis confidence and finalizing profile");
                
                symbolProfile.AnalysisConfidence = CalculateOverallConfidence(symbolProfile, analysisData.Count);
                symbolProfile.AnalysisDuration = stopwatch.Elapsed;

                // Add any analysis warnings
                AddAnalysisWarnings(symbolProfile, analysisData.Count);

                UpdateAnalysisStatus(CalibrationPhase.Completed, 100, $"Analysis completed for {symbol}");

                return symbolProfile;
            }
            catch (OperationCanceledException)
            {
                UpdateAnalysisStatus(CalibrationPhase.Failed, 0, "Analysis was cancelled");
                throw;
            }
            catch (Exception ex)
            {
                UpdateAnalysisStatus(CalibrationPhase.Failed, 0, $"Analysis failed: {ex.Message}");
                throw;
            }
            finally
            {
                stopwatch.Stop();
                _cancellationTokenSource?.Dispose();
            }
        }

        /// <summary>
        /// Quick analysis for real-time updates
        /// </summary>
        public async Task<SymbolProfile> QuickAnalysisAsync(string symbol, List<MarketDataPoint> recentData)
        {
            if (string.IsNullOrEmpty(symbol))
                throw new ArgumentException("Symbol cannot be null or empty", nameof(symbol));
            
            if (recentData == null || recentData.Count < 50)
                throw new ArgumentException("Need at least 50 bars for quick analysis", nameof(recentData));

            // Use fewer bars for quick analysis
            return await AnalyzeSymbolAsync(symbol, recentData, Math.Min(100, recentData.Count));
        }

        /// <summary>
        /// Get analysis progress for UI updates
        /// </summary>
        public CalibrationStatus GetAnalysisProgress()
        {
            lock (_statusLock)
            {
                return new CalibrationStatus
                {
                    CurrentPhase = _analysisStatus.CurrentPhase,
                    ProgressPercent = _analysisStatus.ProgressPercent,
                    CurrentTask = _analysisStatus.CurrentTask,
                    StartTime = _analysisStatus.StartTime,
                    EstimatedTimeRemaining = _analysisStatus.EstimatedTimeRemaining,
                    CompletedTasks = new List<string>(_analysisStatus.CompletedTasks),
                    PendingTasks = new List<string>(_analysisStatus.PendingTasks),
                    HasErrors = _analysisStatus.HasErrors,
                    Errors = new List<string>(_analysisStatus.Errors)
                };
            }
        }

        /// <summary>
        /// Cancel ongoing analysis
        /// </summary>
        public void CancelAnalysis()
        {
            _cancellationTokenSource?.Cancel();
            UpdateAnalysisStatus(CalibrationPhase.Failed, 0, "Analysis cancelled by user");
        }

        #region Private Methods

        private void InitializeAnalysisStatus()
        {
            lock (_statusLock)
            {
                _analysisStatus = new CalibrationStatus
                {
                    CurrentPhase = CalibrationPhase.NotStarted,
                    ProgressPercent = 0,
                    CurrentTask = "Ready to analyze",
                    StartTime = DateTime.UtcNow,
                    PendingTasks = new List<string>
                    {
                        "Data Collection",
                        "Volume Pattern Analysis",
                        "Delta Flow Analysis", 
                        "Volatility Analysis",
                        "Market Profile Analysis",
                        "Confidence Calculation"
                    }
                };
            }
        }

        private void UpdateAnalysisStatus(CalibrationPhase phase, decimal progressPercent, string currentTask)
        {
            lock (_statusLock)
            {
                _analysisStatus.CurrentPhase = phase;
                _analysisStatus.ProgressPercent = progressPercent;
                _analysisStatus.CurrentTask = currentTask;
                
                // Update completed/pending tasks
                if (progressPercent > _analysisStatus.ProgressPercent)
                {
                    var taskToComplete = _analysisStatus.PendingTasks.FirstOrDefault();
                    if (taskToComplete != null)
                    {
                        _analysisStatus.CompletedTasks.Add(taskToComplete);
                        _analysisStatus.PendingTasks.Remove(taskToComplete);
                    }
                }

                // Estimate time remaining
                if (progressPercent > 0)
                {
                    var elapsed = DateTime.UtcNow - _analysisStatus.StartTime;
                    var totalEstimated = TimeSpan.FromTicks((long)(elapsed.Ticks / (double)progressPercent * 100));
                    _analysisStatus.EstimatedTimeRemaining = totalEstimated - elapsed;
                }
            }
        }

        private List<MarketDataPoint> PrepareAnalysisData(List<MarketDataPoint> marketData, int barsToAnalyze)
        {
            // Take the most recent bars for analysis
            var dataToAnalyze = marketData
                .OrderBy(x => x.Timestamp)
                .TakeLast(barsToAnalyze)
                .ToList();

            // Ensure data is properly indexed
            for (int i = 0; i < dataToAnalyze.Count; i++)
            {
                dataToAnalyze[i].BarIndex = i;
            }

            return dataToAnalyze;
        }

        private async Task<VolumeCharacteristics> AnalyzeVolumeAsync(List<MarketDataPoint> data, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                return _volumeAnalyzer.AnalyzeVolumePatterns(data);
            }, cancellationToken);
        }

        private async Task<DeltaFlowCharacteristics> AnalyzeDeltaFlowAsync(List<MarketDataPoint> data, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                return _deltaAnalyzer.AnalyzeDeltaFlow(data);
            }, cancellationToken);
        }

        private async Task<VolatilityCharacteristics> AnalyzeVolatilityAsync(List<MarketDataPoint> data, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                return _volatilityAnalyzer.AnalyzeVolatility(data);
            }, cancellationToken);
        }

        private async Task<MarketProfileCharacteristics> AnalyzeMarketProfileAsync(List<MarketDataPoint> data, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                return _marketProfileAnalyzer.AnalyzeMarketProfile(data);
            }, cancellationToken);
        }

        private decimal CalculateOverallConfidence(SymbolProfile profile, int barsAnalyzed)
        {
            var confidenceFactors = new List<decimal>();

            // Data quality factor (more bars = higher confidence)
            var dataQualityFactor = Math.Min(1.0m, barsAnalyzed / 300.0m);
            confidenceFactors.Add(dataQualityFactor);

            // Volume analysis confidence
            var volumeConfidence = profile.Volume.VolumeConsistency;
            confidenceFactors.Add(volumeConfidence);

            // Delta analysis confidence (based on institutional activity detection)
            var deltaConfidence = profile.DeltaFlow.HasStrongInstitutionalActivity ? 0.9m : 0.7m;
            confidenceFactors.Add(deltaConfidence);

            // Volatility analysis confidence (based on regime classification)
            var volatilityConfidence = profile.Volatility.Regime != VolatilityRegime.Extreme ? 0.8m : 0.6m;
            confidenceFactors.Add(volatilityConfidence);

            // Market profile confidence (24h markets have higher confidence)
            var marketProfileConfidence = profile.MarketProfile.Is24HourMarket ? 0.9m : 0.7m;
            confidenceFactors.Add(marketProfileConfidence);

            // Calculate weighted average
            return confidenceFactors.Average();
        }

        private void AddAnalysisWarnings(SymbolProfile profile, int barsAnalyzed)
        {
            if (barsAnalyzed < 200)
            {
                profile.AnalysisWarnings.Add($"Limited data: Only {barsAnalyzed} bars analyzed. Recommend 200+ bars for optimal accuracy.");
            }

            if (profile.Volume.Regime == VolumeRegime.Erratic)
            {
                profile.AnalysisWarnings.Add("Erratic volume patterns detected. Signal quality may be inconsistent.");
            }

            if (profile.DeltaFlow.Regime == DeltaFlowRegime.Erratic)
            {
                profile.AnalysisWarnings.Add("Erratic delta flow patterns detected. Consider higher thresholds for signal filtering.");
            }

            if (profile.Volatility.Regime == VolatilityRegime.Extreme)
            {
                profile.AnalysisWarnings.Add("Extreme volatility detected. Consider wider stop losses and smaller position sizes.");
            }

            if (profile.AnalysisConfidence < 0.7m)
            {
                profile.AnalysisWarnings.Add($"Low analysis confidence ({profile.AnalysisConfidence:P1}). Consider manual review of calibrated settings.");
            }
        }

        #endregion
    }
}
