using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Analyzes volatility patterns and price movements to optimize risk parameters and position sizing
    /// </summary>
    public class VolatilityAnalyzer : IVolatilityAnalyzer
    {
        private const decimal DEFAULT_RISK_REWARD_RATIO = 2.0m;
        private readonly decimal[] DEFAULT_RISK_REWARD_RATIOS = { 1.0m, 1.5m, 2.0m, 2.5m, 3.0m };
        private readonly decimal[] DEFAULT_TP_PERCENTAGES = { 0.3m, 0.4m, 0.5m, 0.6m, 0.8m, 1.0m, 1.2m, 1.5m };

        /// <summary>
        /// Analyze volatility patterns and price movement characteristics
        /// </summary>
        public VolatilityCharacteristics AnalyzeVolatility(List<MarketDataPoint> marketData)
        {
            if (marketData == null || marketData.Count < 20)
                throw new ArgumentException("Need at least 20 bars for volatility analysis", nameof(marketData));

            var characteristics = new VolatilityCharacteristics();

            // Calculate basic volatility statistics
            CalculateVolatilityStatistics(marketData, characteristics);

            // Calculate True Range statistics
            CalculateTrueRangeStatistics(marketData, characteristics);

            // Analyze trending vs ranging behavior
            AnalyzeTrendingBehavior(marketData, characteristics);

            // Optimize TP/SL parameters
            var (optimalTP, optimalSL, _) = OptimizeRiskParameters(marketData, DEFAULT_RISK_REWARD_RATIOS);
            characteristics.OptimalTakeProfitPercent = optimalTP;
            characteristics.OptimalStopLossPercent = optimalSL;

            // Calculate risk adjustment factor
            characteristics.RiskAdjustmentFactor = CalculateRiskAdjustmentFactor(characteristics);

            // Classify volatility regime
            characteristics.Regime = ClassifyVolatilityRegime(characteristics);
            characteristics.IsHighVolatilitySymbol = DetermineIfHighVolatility(characteristics);

            return characteristics;
        }

        /// <summary>
        /// Calculate optimal take profit and stop loss percentages
        /// </summary>
        public (decimal OptimalTakeProfit, decimal OptimalStopLoss, List<RiskTestResult> TestResults) 
            OptimizeRiskParameters(List<MarketDataPoint> marketData, decimal[] riskRewardRatios = null)
        {
            riskRewardRatios ??= DEFAULT_RISK_REWARD_RATIOS;
            var testResults = new List<RiskTestResult>();

            decimal bestTP = 0.6m;
            decimal bestSL = 0.3m;
            decimal bestScore = 0;

            foreach (var tpPercent in DEFAULT_TP_PERCENTAGES)
            {
                foreach (var rrRatio in riskRewardRatios)
                {
                    var slPercent = tpPercent / rrRatio;
                    var result = TestRiskParameters(marketData, tpPercent, slPercent);
                    testResults.Add(result);

                    if (result.Score > bestScore)
                    {
                        bestScore = result.Score;
                        bestTP = tpPercent;
                        bestSL = slPercent;
                    }
                }
            }

            // Mark optimal result
            var optimalResult = testResults.FirstOrDefault(r => 
                Math.Abs(r.TakeProfitPercent - bestTP) < 0.01m && Math.Abs(r.StopLossPercent - bestSL) < 0.01m);
            if (optimalResult != null)
                optimalResult.IsOptimal = true;

            return (bestTP, bestSL, testResults);
        }

        /// <summary>
        /// Calculate position size adjustment factor based on volatility
        /// </summary>
        public decimal CalculateVolatilityAdjustedPositionSize(decimal currentVolatility, decimal averageVolatility, decimal basePositionSize)
        {
            if (averageVolatility <= 0)
                return basePositionSize;

            var volatilityRatio = currentVolatility / averageVolatility;
            
            // Reduce position size for higher volatility
            var adjustmentFactor = 1.0m / Math.Max(0.5m, Math.Min(2.0m, volatilityRatio));
            
            return basePositionSize * adjustmentFactor;
        }

        /// <summary>
        /// Detect volatility regime changes
        /// </summary>
        public (VolatilityRegime Regime, decimal Confidence) DetectVolatilityRegime(List<MarketDataPoint> marketData)
        {
            if (marketData.Count < 20)
                return (VolatilityRegime.Normal, 0.5m);

            var recentVolatilities = CalculateRecentVolatilities(marketData.TakeLast(20).ToList());
            var averageVolatility = recentVolatilities.Average();
            var volatilityStdDev = CalculateStandardDeviation(recentVolatilities);

            var regime = ClassifyVolatilityFromValues(averageVolatility, volatilityStdDev, recentVolatilities);
            var confidence = CalculateRegimeConfidence(recentVolatilities, regime);

            return (regime, confidence);
        }

        /// <summary>
        /// Analyze real-time volatility
        /// </summary>
        public VolatilitySignalComponent AnalyzeRealtimeVolatility(MarketDataPoint currentBar, List<MarketDataPoint> recentBars)
        {
            if (recentBars == null || recentBars.Count < 10)
                throw new ArgumentException("Need at least 10 recent bars for real-time volatility analysis", nameof(recentBars));

            var component = new VolatilitySignalComponent();

            // Calculate current volatility
            component.CurrentVolatility = CalculateBarVolatility(currentBar);

            // Calculate average volatility from recent bars
            var recentVolatilities = recentBars.Select(CalculateBarVolatility).ToList();
            component.AverageVolatility = recentVolatilities.Average();

            // Detect volatility regime
            var (regime, confidence) = DetectVolatilityRegime(recentBars.Concat(new[] { currentBar }).ToList());
            component.Regime = regime;
            component.VolatilityConfidence = confidence;

            // Check for volatility expansion
            component.IsVolatilityExpansion = component.CurrentVolatility > component.AverageVolatility * 1.5m;

            // Determine volatility pattern
            component.VolatilityPattern = DetermineVolatilityPattern(currentBar, recentBars);

            return component;
        }

        /// <summary>
        /// Get current real-time volatility analysis state
        /// </summary>
        public VolatilityAnalysisState GetCurrentVolatilityState(List<MarketDataPoint> recentData)
        {
            if (recentData == null || recentData.Count < 10)
            {
                return new VolatilityAnalysisState
                {
                    CurrentRegime = VolatilityRegime.Normal,
                    VolatilityConfidence = 0.3m,
                    VolatilityPattern = "Insufficient Data"
                };
            }

            var currentBar = recentData.Last();
            var currentVolatility = CalculateBarVolatility(currentBar);
            var recentVolatilities = recentData.Select(CalculateBarVolatility).ToList();
            var averageVolatility = recentVolatilities.Average();

            // Calculate volatility percentile
            var sortedVolatilities = recentVolatilities.OrderBy(v => v).ToList();
            var currentPercentile = CalculatePercentile(currentVolatility, sortedVolatilities);

            // Detect regime and confidence
            var (regime, confidence) = DetectVolatilityRegime(recentData);

            // Calculate True Range
            var currentTR = CalculateCurrentTrueRange(currentBar, recentData.Count > 1 ? recentData[recentData.Count - 2] : currentBar);
            var averageTR = CalculateAverageTrueRange(recentData);

            // Detect expansion/contraction
            var (isExpansion, isContraction, changeRate) = DetectVolatilityChange(recentData, 14);

            // Calculate risk adjustment factor
            var riskFactor = CalculateRiskAdjustmentFromRegime(regime);

            // Determine trending probability
            var trendingProb = CalculateTrendingProbability(recentData);

            return new VolatilityAnalysisState
            {
                LastUpdate = DateTime.UtcNow,
                CurrentRegime = regime,
                CurrentVolatility = currentVolatility,
                AverageVolatility = averageVolatility,
                VolatilityPercentile = currentPercentile,
                IsVolatilityExpansion = isExpansion,
                IsVolatilityContraction = isContraction,
                VolatilityPattern = DetermineVolatilityPattern(currentBar, recentData.Take(recentData.Count - 1).ToList()),
                VolatilityConfidence = confidence,
                CurrentTrueRange = currentTR,
                AverageTrueRange = averageTR,
                RiskAdjustmentFactor = riskFactor,
                TrendingProbability = trendingProb,
                RangingProbability = 1.0m - trendingProb,
                VolatilityTrend = DetermineVolatilityTrend(recentVolatilities)
            };
        }

        /// <summary>
        /// Calculate real-time volatility metrics
        /// </summary>
        public (decimal CurrentVolatility, decimal AverageTrueRange, decimal VolatilityPercentile) CalculateRealtimeVolatility(MarketDataPoint currentBar, List<MarketDataPoint> recentData)
        {
            if (recentData == null || recentData.Count < 5)
                return (0, 0, 50);

            var currentVolatility = CalculateBarVolatility(currentBar);
            var averageTR = CalculateAverageTrueRange(recentData.Concat(new[] { currentBar }).ToList());

            var recentVolatilities = recentData.Select(CalculateBarVolatility).ToList();
            var percentile = CalculatePercentile(currentVolatility, recentVolatilities);

            return (currentVolatility, averageTR, percentile);
        }

        /// <summary>
        /// Detect volatility expansion or contraction in real-time
        /// </summary>
        public (bool IsExpansion, bool IsContraction, decimal ChangeRate) DetectVolatilityChange(List<MarketDataPoint> recentData, int lookbackPeriod = 14)
        {
            if (recentData == null || recentData.Count < lookbackPeriod)
                return (false, false, 0);

            var dataToUse = recentData.TakeLast(lookbackPeriod).ToList();
            var volatilities = dataToUse.Select(CalculateBarVolatility).ToList();

            var firstHalf = volatilities.Take(lookbackPeriod / 2).Average();
            var secondHalf = volatilities.Skip(lookbackPeriod / 2).Average();

            var changeRate = firstHalf > 0 ? (secondHalf - firstHalf) / firstHalf : 0;

            var isExpansion = changeRate > 0.5m; // 50% increase
            var isContraction = changeRate < -0.3m; // 30% decrease

            return (isExpansion, isContraction, changeRate);
        }

        #region Private Methods

        private void CalculateVolatilityStatistics(List<MarketDataPoint> marketData, VolatilityCharacteristics characteristics)
        {
            var volatilities = marketData.Select(CalculateBarVolatility).ToList();

            characteristics.AverageVolatility = volatilities.Average();
            characteristics.VolatilityStandardDeviation = CalculateStandardDeviation(volatilities);
            characteristics.MaxVolatility = volatilities.Max();
            
            var sortedVolatilities = volatilities.OrderBy(v => v).ToList();
            var percentile95Index = (int)Math.Ceiling(sortedVolatilities.Count * 0.95) - 1;
            characteristics.VolatilityPercentile95 = sortedVolatilities[Math.Min(percentile95Index, sortedVolatilities.Count - 1)];
        }

        private void CalculateTrueRangeStatistics(List<MarketDataPoint> marketData, VolatilityCharacteristics characteristics)
        {
            var trueRanges = new List<decimal>();

            for (int i = 1; i < marketData.Count; i++)
            {
                var current = marketData[i];
                var previous = marketData[i - 1];
                
                var tr1 = current.High - current.Low;
                var tr2 = Math.Abs(current.High - previous.Close);
                var tr3 = Math.Abs(current.Low - previous.Close);
                
                var trueRange = Math.Max(tr1, Math.Max(tr2, tr3));
                trueRanges.Add(trueRange);
            }

            characteristics.AverageTrueRange = trueRanges.Count > 0 ? trueRanges.Average() : 0;
        }

        private void AnalyzeTrendingBehavior(List<MarketDataPoint> marketData, VolatilityCharacteristics characteristics)
        {
            var trendingBars = 0;
            var rangingBars = 0;

            for (int i = 5; i < marketData.Count; i++) // Use 5-bar lookback
            {
                var recentBars = marketData.Skip(i - 5).Take(5).ToList();
                var priceRange = recentBars.Max(x => x.High) - recentBars.Min(x => x.Low);
                var averageBarRange = recentBars.Average(x => x.High - x.Low);

                // If 5-bar range is significantly larger than average bar range, consider it trending
                if (priceRange > averageBarRange * 3)
                {
                    trendingBars++;
                }
                else
                {
                    rangingBars++;
                }
            }

            var totalBars = trendingBars + rangingBars;
            characteristics.TrendingFrequency = totalBars > 0 ? (decimal)trendingBars / totalBars : 0;
            characteristics.RangingFrequency = totalBars > 0 ? (decimal)rangingBars / totalBars : 0;
        }

        private decimal CalculateRiskAdjustmentFactor(VolatilityCharacteristics characteristics)
        {
            // Base adjustment on volatility regime
            var baseAdjustment = characteristics.Regime switch
            {
                VolatilityRegime.VeryLow => 1.2m,    // Can use larger positions
                VolatilityRegime.Low => 1.1m,
                VolatilityRegime.Normal => 1.0m,
                VolatilityRegime.High => 0.8m,      // Reduce position size
                VolatilityRegime.VeryHigh => 0.6m,
                VolatilityRegime.Extreme => 0.4m,   // Significantly reduce position size
                _ => 1.0m
            };

            // Adjust based on trending vs ranging behavior
            var trendingAdjustment = characteristics.TrendingFrequency > 0.7m ? 1.1m : 0.9m;

            return baseAdjustment * trendingAdjustment;
        }

        private VolatilityRegime ClassifyVolatilityRegime(VolatilityCharacteristics characteristics)
        {
            return ClassifyVolatilityFromValues(
                characteristics.AverageVolatility,
                characteristics.VolatilityStandardDeviation,
                new List<decimal> { characteristics.AverageVolatility });
        }

        private VolatilityRegime ClassifyVolatilityFromValues(decimal averageVolatility, decimal volatilityStdDev, List<decimal> recentVolatilities)
        {
            var coefficientOfVariation = averageVolatility > 0 ? volatilityStdDev / averageVolatility : 0;
            var maxRecentVolatility = recentVolatilities.Max();

            // Extreme volatility: very high CV or recent spikes
            if (coefficientOfVariation > 2.0m || maxRecentVolatility > averageVolatility * 5)
                return VolatilityRegime.Extreme;

            // Classify based on average volatility percentiles
            if (averageVolatility > 0.02m) // 2%+ average volatility
                return VolatilityRegime.VeryHigh;
            
            if (averageVolatility > 0.015m) // 1.5%+ average volatility
                return VolatilityRegime.High;
            
            if (averageVolatility < 0.003m) // 0.3%- average volatility
                return VolatilityRegime.VeryLow;
            
            if (averageVolatility < 0.006m) // 0.6%- average volatility
                return VolatilityRegime.Low;

            return VolatilityRegime.Normal;
        }

        private bool DetermineIfHighVolatility(VolatilityCharacteristics characteristics)
        {
            return characteristics.Regime == VolatilityRegime.High ||
                   characteristics.Regime == VolatilityRegime.VeryHigh ||
                   characteristics.Regime == VolatilityRegime.Extreme;
        }

        private RiskTestResult TestRiskParameters(List<MarketDataPoint> marketData, decimal tpPercent, decimal slPercent)
        {
            var trades = 0;
            var wins = 0;
            var totalWinAmount = 0m;
            var totalLossAmount = 0m;
            var maxDrawdown = 0m;
            var currentDrawdown = 0m;
            var equity = 1000m; // Starting equity

            for (int i = 1; i < marketData.Count - 10; i++) // Leave room for trade completion
            {
                var entryBar = marketData[i];
                var entryPrice = entryBar.Close;
                
                // Simulate both long and short trades
                foreach (var direction in new[] { 1, -1 }) // 1 = long, -1 = short
                {
                    trades++;
                    var tpPrice = entryPrice * (1 + direction * tpPercent / 100);
                    var slPrice = entryPrice * (1 - direction * slPercent / 100);
                    
                    var tradeResult = SimulateTrade(marketData, i + 1, entryPrice, tpPrice, slPrice, direction);
                    
                    if (tradeResult > 0)
                    {
                        wins++;
                        totalWinAmount += tradeResult;
                        equity += tradeResult;
                        currentDrawdown = 0;
                    }
                    else
                    {
                        totalLossAmount += Math.Abs(tradeResult);
                        equity += tradeResult; // tradeResult is negative
                        currentDrawdown += Math.Abs(tradeResult);
                        maxDrawdown = Math.Max(maxDrawdown, currentDrawdown);
                    }
                }
            }

            var winRate = trades > 0 ? (decimal)wins / trades : 0;
            var averageRR = wins > 0 && (trades - wins) > 0 ? (totalWinAmount / wins) / (totalLossAmount / (trades - wins)) : 0;
            var profitFactor = totalLossAmount > 0 ? totalWinAmount / totalLossAmount : (totalWinAmount > 0 ? 10 : 1);

            // Combined score: balance win rate, profit factor, and drawdown
            var winRateScore = winRate;
            var profitScore = Math.Min(1.0m, profitFactor / 3.0m);
            var drawdownScore = maxDrawdown < 100 ? 1.0m - (maxDrawdown / 100) : 0; // Penalize high drawdown

            var combinedScore = (winRateScore * 0.4m + profitScore * 0.4m + drawdownScore * 0.2m);

            return new RiskTestResult
            {
                TakeProfitPercent = tpPercent,
                StopLossPercent = slPercent,
                WinRate = winRate,
                AverageRR = averageRR,
                ProfitFactor = profitFactor,
                MaxDrawdown = maxDrawdown,
                Score = combinedScore,
                IsOptimal = false
            };
        }

        private decimal SimulateTrade(List<MarketDataPoint> marketData, int startIndex, decimal entryPrice, decimal tpPrice, decimal slPrice, int direction)
        {
            for (int i = startIndex; i < Math.Min(startIndex + 20, marketData.Count); i++) // Max 20 bars per trade
            {
                var bar = marketData[i];
                
                if (direction == 1) // Long trade
                {
                    if (bar.High >= tpPrice)
                        return tpPrice - entryPrice; // Profit
                    if (bar.Low <= slPrice)
                        return slPrice - entryPrice; // Loss
                }
                else // Short trade
                {
                    if (bar.Low <= tpPrice)
                        return entryPrice - tpPrice; // Profit
                    if (bar.High >= slPrice)
                        return entryPrice - slPrice; // Loss
                }
            }
            
            // Trade didn't hit TP or SL within time limit
            var exitPrice = marketData[Math.Min(startIndex + 19, marketData.Count - 1)].Close;
            return direction * (exitPrice - entryPrice);
        }

        private decimal CalculateBarVolatility(MarketDataPoint bar)
        {
            if (bar.Open <= 0)
                return 0;

            return Math.Abs(bar.Close - bar.Open) / bar.Open;
        }

        private List<decimal> CalculateRecentVolatilities(List<MarketDataPoint> bars)
        {
            return bars.Select(CalculateBarVolatility).ToList();
        }

        private decimal CalculateStandardDeviation(List<decimal> values)
        {
            if (values.Count == 0)
                return 0;

            var average = values.Average();
            var variance = values.Sum(v => (v - average) * (v - average)) / values.Count;
            return (decimal)Math.Sqrt((double)variance);
        }

        private decimal CalculateRegimeConfidence(List<decimal> recentVolatilities, VolatilityRegime regime)
        {
            var consistency = CalculateVolatilityConsistency(recentVolatilities);
            var expectedRange = GetExpectedVolatilityRange(regime);
            var actualAverage = recentVolatilities.Average();
            
            var rangeMatch = actualAverage >= expectedRange.Min && actualAverage <= expectedRange.Max ? 1.0m : 0.5m;
            
            return (consistency + rangeMatch) / 2;
        }

        private decimal CalculateVolatilityConsistency(List<decimal> volatilities)
        {
            if (volatilities.Count == 0)
                return 0;

            var average = volatilities.Average();
            var stdDev = CalculateStandardDeviation(volatilities);
            var coefficientOfVariation = average > 0 ? stdDev / average : 0;

            return Math.Max(0, 1.0m - coefficientOfVariation);
        }

        private (decimal Min, decimal Max) GetExpectedVolatilityRange(VolatilityRegime regime)
        {
            return regime switch
            {
                VolatilityRegime.VeryLow => (0, 0.003m),
                VolatilityRegime.Low => (0.003m, 0.006m),
                VolatilityRegime.Normal => (0.006m, 0.015m),
                VolatilityRegime.High => (0.015m, 0.02m),
                VolatilityRegime.VeryHigh => (0.02m, 0.05m),
                VolatilityRegime.Extreme => (0.05m, 1.0m),
                _ => (0, 1.0m)
            };
        }

        private string DetermineVolatilityPattern(MarketDataPoint currentBar, List<MarketDataPoint> recentBars)
        {
            var currentVolatility = CalculateBarVolatility(currentBar);
            var averageVolatility = recentBars.Select(CalculateBarVolatility).Average();
            var volatilityRatio = averageVolatility > 0 ? currentVolatility / averageVolatility : 1;

            if (volatilityRatio > 3.0m)
                return "Volatility Explosion";
            
            if (volatilityRatio > 2.0m)
                return "Volatility Expansion";
            
            if (volatilityRatio > 1.5m)
                return "Increased Volatility";
            
            if (volatilityRatio < 0.5m)
                return "Volatility Contraction";
            
            if (volatilityRatio < 0.3m)
                return "Low Volatility";

            return "Normal Volatility";
        }

        private decimal CalculatePercentile(decimal value, List<decimal> sortedValues)
        {
            if (sortedValues.Count == 0)
                return 50;

            var position = sortedValues.BinarySearch(value);
            if (position < 0)
                position = ~position;

            return (decimal)position / sortedValues.Count * 100;
        }

        private decimal CalculateCurrentTrueRange(MarketDataPoint current, MarketDataPoint previous)
        {
            var tr1 = current.High - current.Low;
            var tr2 = Math.Abs(current.High - previous.Close);
            var tr3 = Math.Abs(current.Low - previous.Close);

            return Math.Max(tr1, Math.Max(tr2, tr3));
        }

        private decimal CalculateAverageTrueRange(List<MarketDataPoint> data)
        {
            if (data.Count < 2)
                return 0;

            var trueRanges = new List<decimal>();
            for (int i = 1; i < data.Count; i++)
            {
                var tr = CalculateCurrentTrueRange(data[i], data[i - 1]);
                trueRanges.Add(tr);
            }

            return trueRanges.Count > 0 ? trueRanges.Average() : 0;
        }

        private decimal CalculateRiskAdjustmentFromRegime(VolatilityRegime regime)
        {
            return regime switch
            {
                VolatilityRegime.VeryLow => 1.2m,
                VolatilityRegime.Low => 1.1m,
                VolatilityRegime.Normal => 1.0m,
                VolatilityRegime.High => 0.8m,
                VolatilityRegime.VeryHigh => 0.6m,
                VolatilityRegime.Extreme => 0.4m,
                _ => 1.0m
            };
        }

        private decimal CalculateTrendingProbability(List<MarketDataPoint> recentData)
        {
            if (recentData.Count < 10)
                return 0.5m;

            var trendingBars = 0;
            var totalBars = 0;

            for (int i = 5; i < recentData.Count; i++)
            {
                var window = recentData.Skip(i - 5).Take(5).ToList();
                var priceRange = window.Max(x => x.High) - window.Min(x => x.Low);
                var averageBarRange = window.Average(x => x.High - x.Low);

                totalBars++;
                if (priceRange > averageBarRange * 2.5m)
                    trendingBars++;
            }

            return totalBars > 0 ? (decimal)trendingBars / totalBars : 0.5m;
        }

        private VolatilityTrend DetermineVolatilityTrend(List<decimal> recentVolatilities)
        {
            if (recentVolatilities.Count < 5)
                return VolatilityTrend.Stable;

            var recent5 = recentVolatilities.TakeLast(5).ToList();
            var firstHalf = recent5.Take(2).Average();
            var secondHalf = recent5.TakeLast(2).Average();

            var changePercent = firstHalf > 0 ? (secondHalf - firstHalf) / firstHalf : 0;

            if (changePercent > 1.0m)
                return VolatilityTrend.Extreme;
            if (changePercent > 0.5m)
                return VolatilityTrend.Expanding;
            if (changePercent < -0.5m)
                return VolatilityTrend.Contracting;
            if (Math.Abs(changePercent) < 0.1m)
                return VolatilityTrend.Stable;

            return VolatilityTrend.Normalizing;
        }

        #endregion
    }
}
