using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Strategy
{
    /// <summary>
    /// Enhanced signal generator that integrates Phase 4 multi-timeframe analysis and signal synthesis
    /// Provides sophisticated signal generation using multi-dimensional analysis
    /// </summary>
    public class EnhancedSignalGenerator : ISignalGenerator
    {
        #region Fields

        private readonly ISignalGenerator _baseSignalGenerator;
        private readonly IMultiTimeframeAnalyzer _multiTimeframeAnalyzer;
        private readonly ISignalSynthesizer _signalSynthesizer;
        private readonly Action<string> _logAction;

        private OptimalSettings _currentSettings;
        private SignalStatistics _statistics;
        private readonly Queue<TradingSignal> _recentSignals;
        private readonly object _lockObject = new object();

        // Phase 4: Enhanced signal tracking
        private readonly Queue<SynthesizedSignal> _recentSynthesizedSignals;
        private DateTime _lastMultiTimeframeUpdate;

        #endregion

        #region Constructor

        public EnhancedSignalGenerator(
            ISignalGenerator baseSignalGenerator,
            IMultiTimeframeAnalyzer multiTimeframeAnalyzer,
            ISignalSynthesizer signalSynthesizer,
            Action<string> logAction = null)
        {
            _baseSignalGenerator = baseSignalGenerator ?? throw new ArgumentNullException(nameof(baseSignalGenerator));
            _multiTimeframeAnalyzer = multiTimeframeAnalyzer ?? throw new ArgumentNullException(nameof(multiTimeframeAnalyzer));
            _signalSynthesizer = signalSynthesizer ?? throw new ArgumentNullException(nameof(signalSynthesizer));
            _logAction = logAction ?? (_ => { });

            _statistics = new SignalStatistics();
            _recentSignals = new Queue<TradingSignal>();
            _recentSynthesizedSignals = new Queue<SynthesizedSignal>();
            _currentSettings = new OptimalSettings();
            _lastMultiTimeframeUpdate = DateTime.MinValue;
        }

        #endregion

        #region Public Methods

        public TradingSignal GenerateSignal(
            MarketDataPoint currentBar,
            VolumeBlockResult volumeBlock,
            MarketContext marketContext,
            OptimalSettings settings)
        {
            if (currentBar == null || volumeBlock == null || marketContext == null || settings == null)
                return CreateNoSignal(currentBar, "Invalid input parameters");

            lock (_lockObject)
            {
                _currentSettings = settings;
                _logAction("🚀 Enhanced Signal Generation Starting...");

                try
                {
                    // Step 1: Generate base signal using existing logic
                    var baseSignal = _baseSignalGenerator.GenerateSignal(currentBar, volumeBlock, marketContext, settings);
                    _logAction($"📊 Base signal generated: {baseSignal.Type} (Confidence: {baseSignal.Confidence:P1})");

                    // Step 2: Update multi-timeframe analyzer
                    UpdateMultiTimeframeAnalyzer(currentBar);

                    // Step 3: Get multi-timeframe analysis state
                    var multiTimeframeState = _multiTimeframeAnalyzer.GetCurrentAnalysisState();
                    _logAction($"🔍 Multi-timeframe analysis: {multiTimeframeState.TimeframeStates.Count} timeframes, Confidence: {multiTimeframeState.OverallConfidence:P1}");

                    // Step 4: Synthesize enhanced signal
                    var synthesizedSignal = _signalSynthesizer.SynthesizeSignal(multiTimeframeState, marketContext);
                    
                    // Step 5: Create enhanced trading signal
                    var enhancedSignal = CreateEnhancedTradingSignal(baseSignal, synthesizedSignal, currentBar, multiTimeframeState);

                    // Step 6: Apply final validation and filtering
                    var finalSignal = ApplyFinalValidation(enhancedSignal, multiTimeframeState, marketContext);

                    // Step 7: Update tracking and statistics
                    UpdateSignalTracking(finalSignal, synthesizedSignal);

                    _logAction($"✅ Enhanced signal completed: {finalSignal.Type} (Quality: {finalSignal.Quality}, Confidence: {finalSignal.Confidence:P1})");
                    return finalSignal;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Enhanced signal generation error: {ex.Message}");
                    // Fallback to base signal generation
                    return _baseSignalGenerator.GenerateSignal(currentBar, volumeBlock, marketContext, settings);
                }
            }
        }

        public TradingSignal GenerateExitSignal(
            MarketDataPoint currentBar,
            PositionInfo currentPosition,
            OptimalSettings settings)
        {
            // For exit signals, we can enhance with multi-timeframe confirmation
            lock (_lockObject)
            {
                try
                {
                    _logAction("🚪 Enhanced Exit Signal Generation...");

                    // Generate base exit signal
                    var baseExitSignal = _baseSignalGenerator.GenerateExitSignal(currentBar, currentPosition, settings);

                    // Get multi-timeframe confirmation
                    var multiTimeframeState = _multiTimeframeAnalyzer.GetCurrentAnalysisState();
                    
                    // Enhance exit signal with multi-timeframe analysis
                    var enhancedExitSignal = EnhanceExitSignal(baseExitSignal, multiTimeframeState, currentBar, currentPosition);

                    _logAction($"✅ Enhanced exit signal: {enhancedExitSignal.Action} (Confidence: {enhancedExitSignal.Confidence:P1})");
                    return enhancedExitSignal;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Enhanced exit signal error: {ex.Message}");
                    return _baseSignalGenerator.GenerateExitSignal(currentBar, currentPosition, settings);
                }
            }
        }

        public void UpdateSettings(OptimalSettings newSettings)
        {
            lock (_lockObject)
            {
                _currentSettings = newSettings ?? throw new ArgumentNullException(nameof(newSettings));
                _baseSignalGenerator.UpdateSettings(newSettings);
                _logAction("⚙️ Enhanced signal generator settings updated");
            }
        }

        public SignalStatistics GetSignalStatistics()
        {
            lock (_lockObject)
            {
                // Combine base statistics with enhanced statistics
                var baseStats = _baseSignalGenerator.GetSignalStatistics();
                var enhancedStats = new SignalStatistics
                {
                    TotalSignalsGenerated = baseStats.TotalSignalsGenerated + _statistics.TotalSignalsGenerated,
                    SignalsExecuted = baseStats.SignalsExecuted + _statistics.SignalsExecuted,
                    AverageConfidence = (baseStats.AverageConfidence + _statistics.AverageConfidence) / 2,
                    AverageStrength = (baseStats.AverageStrength + _statistics.AverageStrength) / 2,
                    SignalsByType = CombineSignalTypeDictionaries(baseStats.SignalsByType, _statistics.SignalsByType),
                    SignalsByQuality = CombineSignalQualityDictionaries(baseStats.SignalsByQuality, _statistics.SignalsByQuality),
                    LastSignalTime = baseStats.LastSignalTime > _statistics.LastSignalTime ? baseStats.LastSignalTime : _statistics.LastSignalTime,
                    AverageTimeBetweenSignals = (baseStats.AverageTimeBetweenSignals + _statistics.AverageTimeBetweenSignals) / 2
                };

                return enhancedStats;
            }
        }

        public void Reset()
        {
            lock (_lockObject)
            {
                _baseSignalGenerator.Reset();
                _statistics = new SignalStatistics();
                _recentSignals.Clear();
                _recentSynthesizedSignals.Clear();
                _lastMultiTimeframeUpdate = DateTime.MinValue;
                _logAction("🔄 Enhanced signal generator reset");
            }
        }

        #endregion

        #region Private Methods

        private void UpdateMultiTimeframeAnalyzer(MarketDataPoint currentBar)
        {
            try
            {
                // Update multi-timeframe analyzer with new data
                _multiTimeframeAnalyzer.UpdateMarketData(currentBar);
                _lastMultiTimeframeUpdate = DateTime.UtcNow;
                _logAction($"📈 Multi-timeframe analyzer updated with bar at {currentBar.Timestamp:HH:mm:ss}");
            }
            catch (Exception ex)
            {
                _logAction($"⚠️ Multi-timeframe update error: {ex.Message}");
            }
        }

        private TradingSignal CreateEnhancedTradingSignal(
            TradingSignal baseSignal,
            SynthesizedSignal synthesizedSignal,
            MarketDataPoint currentBar,
            MultiTimeframeAnalysisState multiTimeframeState)
        {
            var enhancedSignal = new TradingSignal
            {
                Timestamp = DateTime.UtcNow,
                CorrelationId = baseSignal.CorrelationId,
                CurrentPrice = currentBar.Close
            };

            // Determine final signal type using synthesis
            if (synthesizedSignal != null)
            {
                enhancedSignal.Type = synthesizedSignal.Type;
                enhancedSignal.Action = synthesizedSignal.Type != SignalType.None ? SignalAction.Entry : SignalAction.None;
                
                // Enhanced confidence calculation
                enhancedSignal.Confidence = CalculateEnhancedConfidence(baseSignal, synthesizedSignal, multiTimeframeState);
                
                // Enhanced strength calculation
                enhancedSignal.Strength = synthesizedSignal.Strength;
                
                // Enhanced quality determination
                enhancedSignal.Quality = DetermineEnhancedQuality(synthesizedSignal, multiTimeframeState);
                
                // Enhanced reasoning
                enhancedSignal.PrimaryReason = CreateEnhancedReasoning(baseSignal, synthesizedSignal, multiTimeframeState);
                enhancedSignal.SecondaryReasons = CreateEnhancedSecondaryReasons(synthesizedSignal, multiTimeframeState);
            }
            else
            {
                // Improved fallback strategy - don't penalize confidence as much
                enhancedSignal.Type = baseSignal.Type;
                enhancedSignal.Action = baseSignal.Action;
                enhancedSignal.Confidence = baseSignal.Confidence * 0.95m; // Minimal confidence reduction (was 0.8m)
                enhancedSignal.Strength = baseSignal.Strength;
                enhancedSignal.Quality = baseSignal.Quality;

                // More informative fallback reasoning
                enhancedSignal.PrimaryReason = $"Base signal analysis: {baseSignal.PrimaryReason}";
                enhancedSignal.SecondaryReasons = new List<string>(baseSignal.SecondaryReasons)
                {
                    "Enhanced analysis temporarily unavailable - using reliable base signal"
                };

                // Log the fallback for monitoring
                _logAction?.Invoke($"📊 Enhanced analysis fallback - Base signal confidence: {baseSignal.Confidence:P1}");
            }

            // Copy base signal components
            enhancedSignal.VolumeComponent = baseSignal.VolumeComponent;
            enhancedSignal.DeltaComponent = baseSignal.DeltaComponent;
            enhancedSignal.VolatilityComponent = baseSignal.VolatilityComponent;
            enhancedSignal.VolumeRatio = baseSignal.VolumeRatio;
            enhancedSignal.DeltaImbalance = baseSignal.DeltaImbalance;
            enhancedSignal.CumulativeImpact = baseSignal.CumulativeImpact;

            return enhancedSignal;
        }

        private decimal CalculateEnhancedConfidence(
            TradingSignal baseSignal,
            SynthesizedSignal synthesizedSignal,
            MultiTimeframeAnalysisState multiTimeframeState)
        {
            if (synthesizedSignal == null)
                return baseSignal.Confidence * 0.8m;

            // Weighted combination of base signal and synthesized signal confidence
            var baseWeight = 0.4m;
            var synthesizedWeight = 0.6m;

            var enhancedConfidence = (baseSignal.Confidence * baseWeight) + (synthesizedSignal.Confidence * synthesizedWeight);

            // Apply multi-timeframe consistency bonus
            if (multiTimeframeState.IsTimeframeConsistent)
            {
                enhancedConfidence *= 1.1m; // 10% bonus for consistency
            }

            // Apply trend alignment bonus
            if (multiTimeframeState.TrendAlignment > 0.8m)
            {
                enhancedConfidence *= 1.05m; // 5% bonus for strong alignment
            }

            return Math.Max(0.1m, Math.Min(1.0m, enhancedConfidence));
        }

        private SignalQuality DetermineEnhancedQuality(SynthesizedSignal synthesizedSignal, MultiTimeframeAnalysisState multiTimeframeState)
        {
            if (synthesizedSignal == null)
                return SignalQuality.Poor;

            // Base quality from synthesized signal
            var baseQuality = synthesizedSignal.Quality;

            // Upgrade quality based on multi-timeframe factors
            if (multiTimeframeState.IsTimeframeConsistent && multiTimeframeState.TrendAlignment > 0.8m)
            {
                return baseQuality switch
                {
                    SignalQuality.Poor => SignalQuality.Fair,
                    SignalQuality.Fair => SignalQuality.Good,
                    SignalQuality.Good => SignalQuality.Excellent,
                    SignalQuality.Excellent => SignalQuality.Excellent,
                    _ => baseQuality
                };
            }

            return baseQuality;
        }

        private string CreateEnhancedReasoning(
            TradingSignal baseSignal,
            SynthesizedSignal synthesizedSignal,
            MultiTimeframeAnalysisState multiTimeframeState)
        {
            if (synthesizedSignal == null)
                return $"Enhanced analysis unavailable: {baseSignal.PrimaryReason}";

            var reasoning = $"Multi-timeframe {synthesizedSignal.Type} signal";
            
            if (synthesizedSignal.SupportingPatterns.Any())
            {
                reasoning += $" supported by {synthesizedSignal.PrimaryPattern}";
            }

            if (multiTimeframeState.IsTimeframeConsistent)
            {
                reasoning += $" with {multiTimeframeState.TimeframeStates.Count} timeframe consensus";
            }

            reasoning += $" (Confidence: {synthesizedSignal.Confidence:P1})";

            return reasoning;
        }

        private List<string> CreateEnhancedSecondaryReasons(SynthesizedSignal synthesizedSignal, MultiTimeframeAnalysisState multiTimeframeState)
        {
            var reasons = new List<string>();

            if (synthesizedSignal != null)
            {
                reasons.AddRange(synthesizedSignal.SupportingFactors);
                
                if (synthesizedSignal.TimeframeContributions.Any())
                {
                    var contributions = synthesizedSignal.TimeframeContributions
                        .OrderByDescending(kvp => kvp.Value)
                        .Take(3)
                        .Select(kvp => $"{kvp.Key}: {kvp.Value:P1}")
                        .ToList();
                    reasons.Add($"Timeframe contributions: {string.Join(", ", contributions)}");
                }
            }

            if (multiTimeframeState.DetectedPatterns.Any())
            {
                var patterns = multiTimeframeState.DetectedPatterns
                    .Where(p => p.Confidence > 0.6m)
                    .Select(p => p.PatternName)
                    .Take(3);
                reasons.Add($"Detected patterns: {string.Join(", ", patterns)}");
            }

            return reasons;
        }

        private TradingSignal ApplyFinalValidation(
            TradingSignal enhancedSignal,
            MultiTimeframeAnalysisState multiTimeframeState,
            MarketContext marketContext)
        {
            // Apply final filters and validations
            if (enhancedSignal.Type == SignalType.None)
                return enhancedSignal;

            // Check minimum confidence threshold
            if (enhancedSignal.Confidence < 0.5m)
            {
                enhancedSignal.Type = SignalType.None;
                enhancedSignal.Action = SignalAction.None;
                enhancedSignal.PrimaryReason = $"Signal filtered: Confidence {enhancedSignal.Confidence:P1} below minimum threshold";
                enhancedSignal.Quality = SignalQuality.Poor;
            }

            // Check timeframe consistency requirement
            if (_currentSettings.EnableSessionAdjustments && !multiTimeframeState.IsTimeframeConsistent)
            {
                enhancedSignal.Confidence *= 0.8m; // Reduce confidence for inconsistent timeframes
                enhancedSignal.SecondaryReasons.Add("Timeframe inconsistency detected - confidence reduced");
            }

            return enhancedSignal;
        }

        private TradingSignal EnhanceExitSignal(
            TradingSignal baseExitSignal,
            MultiTimeframeAnalysisState multiTimeframeState,
            MarketDataPoint currentBar,
            PositionInfo currentPosition)
        {
            var enhancedExitSignal = new TradingSignal
            {
                Timestamp = baseExitSignal.Timestamp,
                CorrelationId = baseExitSignal.CorrelationId,
                Type = baseExitSignal.Type,
                Action = baseExitSignal.Action,
                CurrentPrice = baseExitSignal.CurrentPrice,
                Confidence = baseExitSignal.Confidence,
                Quality = baseExitSignal.Quality,
                PrimaryReason = baseExitSignal.PrimaryReason,
                SecondaryReasons = new List<string>(baseExitSignal.SecondaryReasons)
            };

            // Add multi-timeframe exit confirmation
            if (multiTimeframeState.IsTimeframeConsistent)
            {
                var trendAlignment = multiTimeframeState.TrendAlignment;
                var positionDirection = currentPosition.Direction;

                // Check if trend is against position
                if ((positionDirection == SignalType.Long && trendAlignment < 0.3m) ||
                    (positionDirection == SignalType.Short && trendAlignment > 0.7m))
                {
                    enhancedExitSignal.Confidence = Math.Min(1.0m, enhancedExitSignal.Confidence * 1.2m);
                    enhancedExitSignal.SecondaryReasons.Add("Multi-timeframe trend reversal detected");
                }
            }

            return enhancedExitSignal;
        }

        private void UpdateSignalTracking(TradingSignal finalSignal, SynthesizedSignal synthesizedSignal)
        {
            // Update recent signals
            _recentSignals.Enqueue(finalSignal);
            while (_recentSignals.Count > 50)
            {
                _recentSignals.Dequeue();
            }

            // Update recent synthesized signals
            if (synthesizedSignal != null)
            {
                _recentSynthesizedSignals.Enqueue(synthesizedSignal);
                while (_recentSynthesizedSignals.Count > 50)
                {
                    _recentSynthesizedSignals.Dequeue();
                }
            }

            // Update statistics
            UpdateEnhancedStatistics(finalSignal);
        }

        private void UpdateEnhancedStatistics(TradingSignal signal)
        {
            _statistics.TotalSignalsGenerated++;
            _statistics.LastSignalTime = signal.Timestamp;

            if (!_statistics.SignalsByType.ContainsKey(signal.Type))
                _statistics.SignalsByType[signal.Type] = 0;
            _statistics.SignalsByType[signal.Type]++;

            if (!_statistics.SignalsByQuality.ContainsKey(signal.Quality))
                _statistics.SignalsByQuality[signal.Quality] = 0;
            _statistics.SignalsByQuality[signal.Quality]++;

            // Update averages
            _statistics.AverageConfidence = (_statistics.AverageConfidence * (_statistics.TotalSignalsGenerated - 1) + signal.Confidence) / _statistics.TotalSignalsGenerated;
            _statistics.AverageStrength = (_statistics.AverageStrength * (_statistics.TotalSignalsGenerated - 1) + signal.Strength) / _statistics.TotalSignalsGenerated;
        }

        private TradingSignal CreateNoSignal(MarketDataPoint currentBar, string reason)
        {
            return new TradingSignal
            {
                Timestamp = DateTime.UtcNow,
                Type = SignalType.None,
                Action = SignalAction.None,
                CurrentPrice = currentBar?.Close ?? 0,
                Confidence = 0,
                Quality = SignalQuality.Poor,
                PrimaryReason = reason
            };
        }

        private Dictionary<SignalType, int> CombineSignalTypeDictionaries(Dictionary<SignalType, int> dict1, Dictionary<SignalType, int> dict2)
        {
            var combined = new Dictionary<SignalType, int>(dict1);
            foreach (var kvp in dict2)
            {
                if (combined.ContainsKey(kvp.Key))
                    combined[kvp.Key] += kvp.Value;
                else
                    combined[kvp.Key] = kvp.Value;
            }
            return combined;
        }

        private Dictionary<SignalQuality, int> CombineSignalQualityDictionaries(Dictionary<SignalQuality, int> dict1, Dictionary<SignalQuality, int> dict2)
        {
            var combined = new Dictionary<SignalQuality, int>(dict1);
            foreach (var kvp in dict2)
            {
                if (combined.ContainsKey(kvp.Key))
                    combined[kvp.Key] += kvp.Value;
                else
                    combined[kvp.Key] = kvp.Value;
            }
            return combined;
        }

        #endregion
    }
}
