using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models.Analysis;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Original Phase 2: Enhanced Noise Filtering Analyzer
    /// Detects market conditions and filters low-quality signals during choppy/sideways markets
    /// Priority 2 feature for immediate trading performance improvement
    /// </summary>
    public class NoiseFilteringAnalyzer
    {
        private readonly Action<string> _logAction;
        private readonly NoiseFilteringConfig _config;
        private readonly Queue<MarketConditionAnalysis> _conditionHistory;
        private readonly object _lockObject = new object();
        
        // Statistics tracking
        private int _totalAnalyses = 0;
        private int _validatedSignals = 0;
        private int _rejectedSignals = 0;
        
        public NoiseFilteringAnalyzer(NoiseFilteringConfig config = null, Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _config = config ?? CreateDefaultConfig();
            _conditionHistory = new Queue<MarketConditionAnalysis>();
        }
        
        /// <summary>
        /// Analyze market conditions and validate signal against noise filtering criteria
        /// </summary>
        public NoiseFilteringValidationResult ValidateSignalAgainstNoise(
            TradingSignal signal,
            MarketDataPoint currentBar,
            List<MarketDataPoint> recentBars)
        {
            lock (_lockObject)
            {
                _totalAnalyses++;
                
                try
                {
                    _logAction("🔍 ENHANCED NOISE FILTERING VALIDATION:");
                    _logAction($"  • Signal: {signal.Type} | Confidence: {signal.Confidence:P1} | Quality: {signal.Quality}");
                    
                    // Analyze current market conditions
                    var marketCondition = AnalyzeMarketCondition(recentBars, currentBar);
                    var volatilityAnalysis = AnalyzeVolatility(recentBars);
                    var trendAnalysis = AnalyzeTrend(recentBars);
                    
                    // Store market condition in history
                    _conditionHistory.Enqueue(marketCondition);
                    CleanOldConditions();
                    
                    _logAction($"  • Market condition: {marketCondition.Condition}");
                    _logAction($"  • Noise level: {marketCondition.NoiseLevel}");
                    _logAction($"  • Trend strength: {trendAnalysis.TrendStrength:P1}");
                    _logAction($"  • Volatility score: {volatilityAnalysis.VolatilityRatio:F2}");
                    
                    // Calculate noise score
                    var noiseScore = CalculateNoiseScore(marketCondition, volatilityAnalysis, trendAnalysis);
                    
                    // Calculate signal-to-noise ratio
                    var signalToNoiseRatio = CalculateSignalToNoiseRatio(signal, noiseScore);
                    
                    _logAction($"  • Noise score: {noiseScore:F2} (Max: {_config.MaximumNoiseScore:F2})");
                    _logAction($"  • Signal-to-noise ratio: {signalToNoiseRatio:F2} (Min: {_config.MinimumSignalToNoiseRatio:F2})");
                    
                    // Validate against filtering criteria
                    var validationResult = ValidateFilteringCriteria(
                        signal, marketCondition, volatilityAnalysis, trendAnalysis, 
                        noiseScore, signalToNoiseRatio);
                    
                    if (validationResult.IsValid)
                    {
                        _validatedSignals++;
                        _logAction($"  • ✅ NOISE FILTERING PASSED: {validationResult.ValidationReason}");
                    }
                    else
                    {
                        _rejectedSignals++;
                        _logAction($"  • ❌ NOISE FILTERING FAILED: {validationResult.ValidationReason}");
                    }
                    
                    return validationResult;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Noise filtering validation error: {ex.Message}");
                    return CreateInvalidResult($"Validation error: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// Analyze current market condition
        /// </summary>
        private MarketConditionAnalysis AnalyzeMarketCondition(List<MarketDataPoint> recentBars, MarketDataPoint currentBar)
        {
            if (recentBars.Count < _config.MarketConditionLookback)
            {
                return new MarketConditionAnalysis
                {
                    Timestamp = currentBar.Timestamp,
                    Condition = MarketCondition.Unknown,
                    NoiseLevel = NoiseLevel.Moderate,
                    TrendStrength = 0.5m,
                    VolatilityScore = 0.5m,
                    ConditionFactors = new List<string> { "Insufficient data for analysis" }
                };
            }
            
            var analysisWindow = recentBars.TakeLast(_config.MarketConditionLookback).ToList();
            
            // Calculate price movement metrics
            var priceRange = analysisWindow.Max(b => b.High) - analysisWindow.Min(b => b.Low);
            var totalMovement = analysisWindow.Zip(analysisWindow.Skip(1), (prev, curr) => Math.Abs(curr.Close - prev.Close)).Sum();
            var directionalMovement = Math.Abs(analysisWindow.Last().Close - analysisWindow.First().Close);
            
            var rangeEfficiency = totalMovement > 0 ? directionalMovement / totalMovement : 0m;
            var priceStability = CalculatePriceStability(analysisWindow);
            
            // Determine market condition
            var condition = DetermineMarketCondition(rangeEfficiency, priceStability, analysisWindow);
            var noiseLevel = DetermineNoiseLevel(rangeEfficiency, priceStability);
            
            return new MarketConditionAnalysis
            {
                Timestamp = currentBar.Timestamp,
                Condition = condition,
                NoiseLevel = noiseLevel,
                TrendStrength = rangeEfficiency,
                VolatilityScore = 1m - priceStability,
                RangeEfficiency = rangeEfficiency,
                DirectionalMovement = directionalMovement,
                PriceStability = priceStability,
                AnalysisWindow = TimeSpan.FromMinutes(analysisWindow.Count),
                BarsAnalyzed = analysisWindow.Count
            };
        }
        
        /// <summary>
        /// Analyze volatility
        /// </summary>
        private VolatilityAnalysisData AnalyzeVolatility(List<MarketDataPoint> recentBars)
        {
            if (recentBars.Count < 10)
            {
                return new VolatilityAnalysisData
                {
                    CurrentVolatility = 0.5m,
                    AverageVolatility = 0.5m,
                    VolatilityRatio = 1.0m,
                    IsHighVolatility = false,
                    IsLowVolatility = false
                };
            }
            
            var analysisWindow = recentBars.TakeLast(20).ToList();
            
            // Calculate true ranges
            var trueRanges = new List<decimal>();
            for (int i = 1; i < analysisWindow.Count; i++)
            {
                var current = analysisWindow[i];
                var previous = analysisWindow[i - 1];
                
                var tr = Math.Max(
                    current.High - current.Low,
                    Math.Max(
                        Math.Abs(current.High - previous.Close),
                        Math.Abs(current.Low - previous.Close)
                    )
                );
                trueRanges.Add(tr);
            }
            
            var currentVolatility = trueRanges.TakeLast(5).Average();
            var averageVolatility = trueRanges.Average();
            var volatilityRatio = averageVolatility > 0 ? currentVolatility / averageVolatility : 1.0m;
            
            return new VolatilityAnalysisData
            {
                CurrentVolatility = currentVolatility,
                AverageVolatility = averageVolatility,
                VolatilityRatio = volatilityRatio,
                AverageTrueRange = averageVolatility,
                IsHighVolatility = volatilityRatio > 1.5m,
                IsLowVolatility = volatilityRatio < 0.7m,
                VolatilityHistory = trueRanges,
                AnalysisWindow = TimeSpan.FromMinutes(analysisWindow.Count)
            };
        }
        
        /// <summary>
        /// Analyze trend
        /// </summary>
        private TrendAnalysisData AnalyzeTrend(List<MarketDataPoint> recentBars)
        {
            if (recentBars.Count < 10)
            {
                return new TrendAnalysisData
                {
                    TrendStrength = 0.5m,
                    TrendDirection = 0m,
                    TrendConsistency = 0.5m,
                    IsSideways = true
                };
            }
            
            var analysisWindow = recentBars.TakeLast(20).ToList();
            
            // Calculate moving average slope
            var prices = analysisWindow.Select(b => b.Close).ToList();
            var maSlope = CalculateMovingAverageSlope(prices);
            
            // Calculate price movement efficiency
            var totalMovement = 0m;
            var directionalMovement = 0m;
            
            for (int i = 1; i < analysisWindow.Count; i++)
            {
                var movement = analysisWindow[i].Close - analysisWindow[i - 1].Close;
                totalMovement += Math.Abs(movement);
                directionalMovement += movement;
            }
            
            var efficiency = totalMovement > 0 ? Math.Abs(directionalMovement) / totalMovement : 0m;
            var trendStrength = Math.Min(1m, efficiency * 2m); // Scale to 0-1
            
            return new TrendAnalysisData
            {
                TrendStrength = trendStrength,
                TrendDirection = Math.Sign(directionalMovement),
                TrendConsistency = efficiency,
                IsUptrend = directionalMovement > 0 && trendStrength > 0.6m,
                IsDowntrend = directionalMovement < 0 && trendStrength > 0.6m,
                IsSideways = trendStrength < 0.4m,
                MovingAverageSlope = maSlope,
                PriceMovementEfficiency = efficiency,
                AnalysisWindow = TimeSpan.FromMinutes(analysisWindow.Count)
            };
        }
        
        /// <summary>
        /// Calculate noise score
        /// </summary>
        private decimal CalculateNoiseScore(
            MarketConditionAnalysis marketCondition,
            VolatilityAnalysisData volatility,
            TrendAnalysisData trend)
        {
            // Base noise from market condition
            var conditionNoise = marketCondition.Condition switch
            {
                MarketCondition.Trending => 0.2m,
                MarketCondition.Ranging => 0.6m,
                MarketCondition.Volatile => 0.8m,
                MarketCondition.Consolidating => 0.4m,
                MarketCondition.Breakout => 0.3m,
                MarketCondition.Reversal => 0.5m,
                _ => 0.5m
            };
            
            // Volatility contribution
            var volatilityNoise = Math.Min(1m, volatility.VolatilityRatio * 0.4m);
            
            // Trend contribution (lower trend strength = higher noise)
            var trendNoise = 1m - trend.TrendStrength;
            
            // Weighted combination
            var noiseScore = (conditionNoise * 0.4m) + (volatilityNoise * 0.3m) + (trendNoise * 0.3m);
            
            return Math.Min(1m, Math.Max(0m, noiseScore));
        }
        
        /// <summary>
        /// Calculate signal-to-noise ratio
        /// </summary>
        private decimal CalculateSignalToNoiseRatio(TradingSignal signal, decimal noiseScore)
        {
            var signalStrength = (signal.Confidence + signal.Strength) / 2m;
            var adjustedNoiseScore = Math.Max(0.1m, noiseScore); // Prevent division by zero
            
            return signalStrength / adjustedNoiseScore;
        }
        
        /// <summary>
        /// Validate filtering criteria
        /// </summary>
        private NoiseFilteringValidationResult ValidateFilteringCriteria(
            TradingSignal signal,
            MarketConditionAnalysis marketCondition,
            VolatilityAnalysisData volatility,
            TrendAnalysisData trend,
            decimal noiseScore,
            decimal signalToNoiseRatio)
        {
            var filteringFactors = new List<string>();
            var marketMetrics = new Dictionary<string, decimal>
            {
                ["NoiseScore"] = noiseScore,
                ["SignalToNoiseRatio"] = signalToNoiseRatio,
                ["TrendStrength"] = trend.TrendStrength,
                ["VolatilityRatio"] = volatility.VolatilityRatio,
                ["RangeEfficiency"] = marketCondition.RangeEfficiency
            };
            
            // Check noise score threshold
            if (noiseScore > _config.MaximumNoiseScore)
            {
                return CreateInvalidResult(
                    $"Noise score too high: {noiseScore:F2} > {_config.MaximumNoiseScore:F2}",
                    marketCondition, noiseScore, signalToNoiseRatio, trend.TrendStrength, 
                    volatility.VolatilityRatio, filteringFactors, marketMetrics);
            }
            
            // Check signal-to-noise ratio
            if (signalToNoiseRatio < _config.MinimumSignalToNoiseRatio)
            {
                return CreateInvalidResult(
                    $"Signal-to-noise ratio too low: {signalToNoiseRatio:F2} < {_config.MinimumSignalToNoiseRatio:F2}",
                    marketCondition, noiseScore, signalToNoiseRatio, trend.TrendStrength, 
                    volatility.VolatilityRatio, filteringFactors, marketMetrics);
            }
            
            // Check trend strength
            if (_config.FilterDuringLowTrend && trend.TrendStrength < _config.MinimumTrendStrength)
            {
                return CreateInvalidResult(
                    $"Trend strength too low: {trend.TrendStrength:P1} < {_config.MinimumTrendStrength:P1}",
                    marketCondition, noiseScore, signalToNoiseRatio, trend.TrendStrength, 
                    volatility.VolatilityRatio, filteringFactors, marketMetrics);
            }
            
            // Check volatility
            if (_config.FilterDuringHighVolatility && volatility.VolatilityRatio > _config.MaximumVolatilityScore)
            {
                return CreateInvalidResult(
                    $"Volatility too high: {volatility.VolatilityRatio:F2} > {_config.MaximumVolatilityScore:F2}",
                    marketCondition, noiseScore, signalToNoiseRatio, trend.TrendStrength, 
                    volatility.VolatilityRatio, filteringFactors, marketMetrics);
            }
            
            // Check choppy market conditions
            if (_config.FilterDuringChoppyMarkets && 
                (marketCondition.Condition == MarketCondition.Ranging || marketCondition.NoiseLevel == NoiseLevel.High))
            {
                return CreateInvalidResult(
                    $"Choppy market conditions: {marketCondition.Condition}, Noise: {marketCondition.NoiseLevel}",
                    marketCondition, noiseScore, signalToNoiseRatio, trend.TrendStrength, 
                    volatility.VolatilityRatio, filteringFactors, marketMetrics);
            }
            
            // All criteria passed
            filteringFactors.Add($"Noise score acceptable: {noiseScore:F2}");
            filteringFactors.Add($"Signal-to-noise ratio good: {signalToNoiseRatio:F2}");
            filteringFactors.Add($"Trend strength sufficient: {trend.TrendStrength:P1}");
            filteringFactors.Add($"Volatility manageable: {volatility.VolatilityRatio:F2}");
            
            return new NoiseFilteringValidationResult
            {
                IsValid = true,
                MarketCondition = marketCondition.Condition,
                NoiseLevel = marketCondition.NoiseLevel,
                NoiseScore = noiseScore,
                TrendStrength = trend.TrendStrength,
                VolatilityScore = volatility.VolatilityRatio,
                ValidationReason = $"Signal passed noise filtering: {signalToNoiseRatio:F2} S/N ratio, {trend.TrendStrength:P1} trend strength",
                FilteringFactors = filteringFactors,
                IsTrendingMarket = trend.TrendStrength > 0.6m,
                IsChoppyMarket = marketCondition.Condition == MarketCondition.Ranging,
                IsHighVolatility = volatility.IsHighVolatility,
                SignalToNoiseRatio = signalToNoiseRatio,
                MarketMetrics = marketMetrics
            };
        }
        
        // Helper methods for market analysis
        private decimal CalculatePriceStability(List<MarketDataPoint> bars)
        {
            if (bars.Count < 2) return 1m;
            
            var priceChanges = bars.Zip(bars.Skip(1), (prev, curr) => Math.Abs(curr.Close - prev.Close) / prev.Close).ToList();
            var averageChange = priceChanges.Average();
            
            return Math.Max(0m, 1m - (averageChange * 10m)); // Scale to 0-1
        }
        
        private MarketCondition DetermineMarketCondition(decimal rangeEfficiency, decimal priceStability, List<MarketDataPoint> bars)
        {
            if (rangeEfficiency > 0.7m && priceStability > 0.6m)
                return MarketCondition.Trending;
            else if (rangeEfficiency < 0.3m)
                return MarketCondition.Ranging;
            else if (priceStability < 0.4m)
                return MarketCondition.Volatile;
            else if (priceStability > 0.8m)
                return MarketCondition.Consolidating;
            else
                return MarketCondition.Unknown;
        }
        
        private NoiseLevel DetermineNoiseLevel(decimal rangeEfficiency, decimal priceStability)
        {
            var noiseScore = (1m - rangeEfficiency) + (1m - priceStability);
            
            return noiseScore switch
            {
                < 0.5m => NoiseLevel.Low,
                < 1.0m => NoiseLevel.Moderate,
                < 1.5m => NoiseLevel.High,
                _ => NoiseLevel.Extreme
            };
        }
        
        private decimal CalculateMovingAverageSlope(List<decimal> prices)
        {
            if (prices.Count < 2) return 0m;
            
            var ma1 = prices.Take(prices.Count / 2).Average();
            var ma2 = prices.Skip(prices.Count / 2).Average();
            
            return ma2 - ma1;
        }
        
        private void CleanOldConditions()
        {
            while (_conditionHistory.Count > 50)
            {
                _conditionHistory.Dequeue();
            }
        }
        
        private NoiseFilteringValidationResult CreateInvalidResult(
            string reason,
            MarketConditionAnalysis marketCondition = null,
            decimal noiseScore = 0m,
            decimal signalToNoiseRatio = 0m,
            decimal trendStrength = 0m,
            decimal volatilityScore = 0m,
            List<string> filteringFactors = null,
            Dictionary<string, decimal> marketMetrics = null)
        {
            return new NoiseFilteringValidationResult
            {
                IsValid = false,
                MarketCondition = marketCondition?.Condition ?? MarketCondition.Unknown,
                NoiseLevel = marketCondition?.NoiseLevel ?? NoiseLevel.High,
                NoiseScore = noiseScore,
                TrendStrength = trendStrength,
                VolatilityScore = volatilityScore,
                ValidationReason = reason,
                FilteringFactors = filteringFactors ?? new List<string> { reason },
                SignalToNoiseRatio = signalToNoiseRatio,
                MarketMetrics = marketMetrics ?? new Dictionary<string, decimal>()
            };
        }
        
        private NoiseFilteringConfig CreateDefaultConfig()
        {
            return new NoiseFilteringConfig
            {
                MaximumNoiseScore = 0.7m,
                MinimumTrendStrength = 0.3m,
                MaximumVolatilityScore = 0.8m,
                MinimumSignalToNoiseRatio = 1.5m,
                MarketConditionLookback = 20,
                FilterDuringHighVolatility = true,
                FilterDuringChoppyMarkets = true,
                FilterDuringLowTrend = true
            };
        }
        
        public OriginalPhase2Statistics GetStatistics()
        {
            lock (_lockObject)
            {
                var filteringRate = _totalAnalyses > 0 ? (decimal)_validatedSignals / _totalAnalyses : 0m;
                
                return new OriginalPhase2Statistics
                {
                    TotalSignalsNoiseFiltered = _totalAnalyses,
                    SignalsNoiseValidated = _validatedSignals,
                    SignalsNoiseRejected = _rejectedSignals,
                    NoiseFilteringRate = filteringRate,
                    LastValidation = DateTime.UtcNow
                };
            }
        }
    }
}
