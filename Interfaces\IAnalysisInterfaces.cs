using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartVolumeStrategy.Models;
using ATAS.Indicators;

namespace SmartVolumeStrategy.Interfaces
{
    /// <summary>
    /// Master symbol analyzer that coordinates all analysis components
    /// </summary>
    public interface ISymbolAnalyzer
    {
        /// <summary>
        /// Analyze symbol characteristics from historical data
        /// </summary>
        /// <param name="symbol">Symbol to analyze</param>
        /// <param name="marketData">Historical market data</param>
        /// <param name="barsToAnalyze">Number of bars to analyze (default: 300)</param>
        /// <returns>Comprehensive symbol profile</returns>
        Task<SymbolProfile> AnalyzeSymbolAsync(string symbol, List<MarketDataPoint> marketData, int barsToAnalyze = 300);

        /// <summary>
        /// Quick analysis for real-time updates
        /// </summary>
        /// <param name="symbol">Symbol to analyze</param>
        /// <param name="recentData">Recent market data (last 50-100 bars)</param>
        /// <returns>Updated symbol characteristics</returns>
        Task<SymbolProfile> QuickAnalysisAsync(string symbol, List<MarketDataPoint> recentData);

        /// <summary>
        /// Get analysis progress for UI updates
        /// </summary>
        CalibrationStatus GetAnalysisProgress();

        /// <summary>
        /// Cancel ongoing analysis
        /// </summary>
        void CancelAnalysis();
    }

    /// <summary>
    /// Volume pattern analysis interface
    /// </summary>
    public interface IVolumePatternAnalyzer
    {
        /// <summary>
        /// Analyze volume patterns and characteristics
        /// </summary>
        /// <param name="marketData">Historical market data</param>
        /// <returns>Volume characteristics</returns>
        VolumeCharacteristics AnalyzeVolumePatterns(List<MarketDataPoint> marketData);

        /// <summary>
        /// Calculate optimal volume threshold for signal generation
        /// </summary>
        /// <param name="marketData">Historical market data</param>
        /// <param name="testThresholds">Thresholds to test (e.g., 1.5, 2.0, 2.5, 3.0)</param>
        /// <returns>Optimal threshold and test results</returns>
        (decimal OptimalThreshold, List<ThresholdTestResult> TestResults) OptimizeVolumeThreshold(
            List<MarketDataPoint> marketData, 
            decimal[] testThresholds = null);

        /// <summary>
        /// Detect volume spikes in real-time
        /// </summary>
        /// <param name="currentBar">Current market data</param>
        /// <param name="averageVolume">Average volume baseline</param>
        /// <param name="threshold">Volume threshold multiplier</param>
        /// <returns>Volume spike information</returns>
        VolumeSignalComponent DetectVolumeSpike(MarketDataPoint currentBar, decimal averageVolume, decimal threshold);

        /// <summary>
        /// Calculate volume statistics
        /// </summary>
        /// <param name="marketData">Historical market data</param>
        /// <returns>Volume statistics</returns>
        (decimal Average, decimal StdDev, decimal Median, decimal Percentile95) CalculateVolumeStatistics(List<MarketDataPoint> marketData);

        /// <summary>
        /// Get current real-time volume analysis state
        /// </summary>
        /// <param name="recentData">Recent market data for analysis</param>
        /// <returns>Current volume analysis state</returns>
        VolumeAnalysisState GetCurrentVolumeState(List<MarketDataPoint> recentData);

        /// <summary>
        /// Determine current volume regime in real-time
        /// </summary>
        /// <param name="currentBar">Current market data</param>
        /// <param name="history">Historical data for context</param>
        /// <returns>Current volume regime and confidence</returns>
        (VolumeRegime Regime, decimal Confidence) DetermineCurrentVolumeRegime(MarketDataPoint currentBar, List<MarketDataPoint> history);

        /// <summary>
        /// Analyze volume trend in real-time
        /// </summary>
        /// <param name="recentData">Recent market data</param>
        /// <param name="lookbackPeriod">Period for trend analysis</param>
        /// <returns>Volume trend analysis</returns>
        (VolumeTrend Trend, decimal Strength, int Duration) AnalyzeVolumeTrend(List<MarketDataPoint> recentData, int lookbackPeriod = 10);
    }

    /// <summary>
    /// Delta flow analysis interface
    /// </summary>
    public interface IDeltaFlowAnalyzer
    {
        /// <summary>
        /// Analyze delta flow patterns and buy/sell pressure
        /// </summary>
        /// <param name="marketData">Historical market data</param>
        /// <returns>Delta flow characteristics</returns>
        DeltaFlowCharacteristics AnalyzeDeltaFlow(List<MarketDataPoint> marketData);

        /// <summary>
        /// Calculate cumulative volume delta (CVD) trends
        /// </summary>
        /// <param name="marketData">Historical market data</param>
        /// <param name="lookbackPeriod">Period for CVD calculation</param>
        /// <returns>CVD trend analysis</returns>
        (decimal TrendStrength, decimal ReversalFrequency, List<decimal> CVDValues) CalculateCVDTrends(
            List<MarketDataPoint> marketData, 
            int lookbackPeriod = 30);

        /// <summary>
        /// Detect institutional activity patterns
        /// </summary>
        /// <param name="marketData">Historical market data</param>
        /// <returns>Institutional activity analysis</returns>
        (decimal ActivityLevel, bool HasStrongActivity, List<string> Patterns) DetectInstitutionalActivity(List<MarketDataPoint> marketData);

        /// <summary>
        /// Optimize delta thresholds for signal generation
        /// </summary>
        /// <param name="marketData">Historical market data</param>
        /// <param name="testThresholds">Delta thresholds to test</param>
        /// <returns>Optimal thresholds and test results</returns>
        (decimal OptimalImbalanceThreshold, decimal OptimalSignificanceThreshold, List<ThresholdTestResult> TestResults) 
            OptimizeDeltaThresholds(List<MarketDataPoint> marketData, decimal[] testThresholds = null);

        /// <summary>
        /// Analyze real-time delta flow
        /// </summary>
        /// <param name="currentBar">Current market data</param>
        /// <param name="recentBars">Recent bars for context</param>
        /// <returns>Real-time delta analysis</returns>
        DeltaSignalComponent AnalyzeRealtimeDelta(MarketDataPoint currentBar, List<MarketDataPoint> recentBars);

        /// <summary>
        /// Get current real-time delta analysis state
        /// </summary>
        /// <param name="recentData">Recent market data for analysis</param>
        /// <returns>Current delta analysis state</returns>
        DeltaAnalysisState GetCurrentDeltaState(List<MarketDataPoint> recentData);

        /// <summary>
        /// Calculate real-time CVD trend
        /// </summary>
        /// <param name="recentData">Recent market data</param>
        /// <param name="lookbackPeriod">Period for CVD calculation</param>
        /// <returns>Current CVD trend and strength</returns>
        (decimal CVDTrend, decimal TrendStrength, bool IsDivergence) CalculateRealtimeCVD(List<MarketDataPoint> recentData, int lookbackPeriod = 30);

        /// <summary>
        /// Detect real-time institutional activity
        /// </summary>
        /// <param name="currentBar">Current market data</param>
        /// <param name="recentData">Recent market data for context</param>
        /// <returns>Institutional activity detection</returns>
        (bool IsDetected, decimal ActivityLevel, string Pattern) DetectRealtimeInstitutionalActivity(MarketDataPoint currentBar, List<MarketDataPoint> recentData);
    }

    /// <summary>
    /// Volatility analysis interface
    /// </summary>
    public interface IVolatilityAnalyzer
    {
        /// <summary>
        /// Analyze volatility patterns and price movement characteristics
        /// </summary>
        /// <param name="marketData">Historical market data</param>
        /// <returns>Volatility characteristics</returns>
        VolatilityCharacteristics AnalyzeVolatility(List<MarketDataPoint> marketData);

        /// <summary>
        /// Calculate optimal take profit and stop loss percentages
        /// </summary>
        /// <param name="marketData">Historical market data</param>
        /// <param name="riskRewardRatios">Risk/reward ratios to test (e.g., 1:1.5, 1:2, 1:3)</param>
        /// <returns>Optimal TP/SL percentages and test results</returns>
        (decimal OptimalTakeProfit, decimal OptimalStopLoss, List<RiskTestResult> TestResults) 
            OptimizeRiskParameters(List<MarketDataPoint> marketData, decimal[] riskRewardRatios = null);

        /// <summary>
        /// Calculate position size adjustment factor based on volatility
        /// </summary>
        /// <param name="currentVolatility">Current volatility level</param>
        /// <param name="averageVolatility">Average volatility baseline</param>
        /// <param name="basePositionSize">Base position size in USDT</param>
        /// <returns>Adjusted position size</returns>
        decimal CalculateVolatilityAdjustedPositionSize(decimal currentVolatility, decimal averageVolatility, decimal basePositionSize);

        /// <summary>
        /// Detect volatility regime changes
        /// </summary>
        /// <param name="marketData">Recent market data</param>
        /// <returns>Current volatility regime and confidence</returns>
        (VolatilityRegime Regime, decimal Confidence) DetectVolatilityRegime(List<MarketDataPoint> marketData);

        /// <summary>
        /// Analyze real-time volatility
        /// </summary>
        /// <param name="currentBar">Current market data</param>
        /// <param name="recentBars">Recent bars for context</param>
        /// <returns>Real-time volatility analysis</returns>
        VolatilitySignalComponent AnalyzeRealtimeVolatility(MarketDataPoint currentBar, List<MarketDataPoint> recentBars);

        /// <summary>
        /// Get current real-time volatility analysis state
        /// </summary>
        /// <param name="recentData">Recent market data for analysis</param>
        /// <returns>Current volatility analysis state</returns>
        VolatilityAnalysisState GetCurrentVolatilityState(List<MarketDataPoint> recentData);

        /// <summary>
        /// Calculate real-time volatility metrics
        /// </summary>
        /// <param name="currentBar">Current market data</param>
        /// <param name="recentData">Recent market data for context</param>
        /// <returns>Current volatility metrics</returns>
        (decimal CurrentVolatility, decimal AverageTrueRange, decimal VolatilityPercentile) CalculateRealtimeVolatility(MarketDataPoint currentBar, List<MarketDataPoint> recentData);

        /// <summary>
        /// Detect volatility expansion or contraction in real-time
        /// </summary>
        /// <param name="recentData">Recent market data</param>
        /// <param name="lookbackPeriod">Period for volatility comparison</param>
        /// <returns>Volatility expansion/contraction analysis</returns>
        (bool IsExpansion, bool IsContraction, decimal ChangeRate) DetectVolatilityChange(List<MarketDataPoint> recentData, int lookbackPeriod = 14);
    }

    /// <summary>
    /// Market profile analysis interface
    /// </summary>
    public interface IMarketProfileAnalyzer
    {
        /// <summary>
        /// Analyze market profile and session characteristics
        /// </summary>
        /// <param name="marketData">Historical market data with timestamps</param>
        /// <returns>Market profile characteristics</returns>
        MarketProfileCharacteristics AnalyzeMarketProfile(List<MarketDataPoint> marketData);

        /// <summary>
        /// Identify optimal trading windows based on activity patterns
        /// </summary>
        /// <param name="marketData">Historical market data</param>
        /// <returns>Optimal trading windows</returns>
        List<TimeRange> IdentifyOptimalTradingWindows(List<MarketDataPoint> marketData);

        /// <summary>
        /// Analyze session-based performance patterns
        /// </summary>
        /// <param name="marketData">Historical market data</param>
        /// <returns>Session activity scores</returns>
        (decimal AsianScore, decimal EuropeanScore, decimal USScore) AnalyzeSessionActivity(List<MarketDataPoint> marketData);

        /// <summary>
        /// Get current session information
        /// </summary>
        /// <param name="currentTime">Current UTC time</param>
        /// <returns>Current trading session</returns>
        TradingSession GetCurrentSession(DateTime currentTime);

        /// <summary>
        /// Check if current time is within optimal trading window
        /// </summary>
        /// <param name="currentTime">Current UTC time</param>
        /// <param name="optimalWindows">Optimal trading windows</param>
        /// <returns>True if within optimal window</returns>
        bool IsOptimalTradingTime(DateTime currentTime, List<TimeRange> optimalWindows);

        /// <summary>
        /// Get current real-time market profile analysis state
        /// </summary>
        /// <param name="currentTime">Current UTC time</param>
        /// <param name="recentData">Recent market data for context</param>
        /// <returns>Current market profile analysis state</returns>
        MarketProfileAnalysisState GetCurrentMarketProfileState(DateTime currentTime, List<MarketDataPoint> recentData);

        /// <summary>
        /// Analyze real-time session activity
        /// </summary>
        /// <param name="currentTime">Current UTC time</param>
        /// <param name="recentData">Recent market data</param>
        /// <returns>Current session activity analysis</returns>
        (TradingSession CurrentSession, decimal ActivityLevel, bool IsOptimal) AnalyzeRealtimeSessionActivity(DateTime currentTime, List<MarketDataPoint> recentData);

        /// <summary>
        /// Calculate market condition based on current activity
        /// </summary>
        /// <param name="currentTime">Current UTC time</param>
        /// <param name="recentData">Recent market data</param>
        /// <returns>Current market condition</returns>
        (string Condition, decimal Confidence) DetermineMarketCondition(DateTime currentTime, List<MarketDataPoint> recentData);
    }

    /// <summary>
    /// Real-time analysis engine that coordinates all Phase 1 analyzers for continuous market analysis
    /// </summary>
    public interface IRealTimeAnalysisEngine
    {
        /// <summary>
        /// Initialize the analysis engine with symbol profile and settings
        /// </summary>
        /// <param name="symbolProfile">Symbol profile from initial analysis</param>
        /// <param name="settings">Optimal settings from calibration</param>
        /// <param name="config">Real-time analysis configuration</param>
        void Initialize(SymbolProfile symbolProfile, OptimalSettings settings, RealTimeAnalysisConfig config = null);

        /// <summary>
        /// Update the engine with new market data
        /// </summary>
        /// <param name="newBar">New market data point</param>
        void UpdateMarketData(MarketDataPoint newBar);

        /// <summary>
        /// Get current market context with real-time analysis data
        /// </summary>
        /// <param name="currentBar">Current market data point</param>
        /// <returns>Enhanced market context with real analysis data</returns>
        MarketContext GetCurrentMarketContext(MarketDataPoint currentBar);

        /// <summary>
        /// Get current analysis state from all components
        /// </summary>
        /// <returns>Current real-time analysis state</returns>
        AnalysisState GetCurrentAnalysisState();

        /// <summary>
        /// Get analysis performance metrics
        /// </summary>
        /// <returns>Performance metrics for monitoring</returns>
        AnalysisPerformanceMetrics GetPerformanceMetrics();

        /// <summary>
        /// Reset the analysis engine state
        /// </summary>
        void Reset();

        /// <summary>
        /// Check if the engine is properly initialized
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// Get the current configuration
        /// </summary>
        RealTimeAnalysisConfig Configuration { get; }
    }

    /// <summary>
    /// Interface for adaptive calibration system that dynamically adjusts strategy parameters
    /// </summary>
    public interface IAdaptiveCalibrator
    {
        /// <summary>
        /// Initialize the adaptive calibrator with symbol profile and initial settings
        /// </summary>
        void Initialize(SymbolProfile symbolProfile, OptimalSettings initialSettings);

        /// <summary>
        /// Determine if recalibration is needed based on performance and market regime changes
        /// </summary>
        bool ShouldRecalibrate(PerformanceMetrics currentPerformance, MarketRegimeChange regimeChange);

        /// <summary>
        /// Perform adaptive calibration asynchronously
        /// </summary>
        Task<CalibrationResult> PerformAdaptiveCalibrationAsync(List<MarketDataPoint> recentData, PerformanceMetrics performance);

        /// <summary>
        /// Get current optimized settings
        /// </summary>
        OptimalSettings GetCurrentSettings();

        /// <summary>
        /// Get confidence in current calibration (decays over time)
        /// </summary>
        decimal GetCalibrationConfidence();

        /// <summary>
        /// Update performance metrics for calibration decision making
        /// </summary>
        void UpdatePerformanceMetrics(PerformanceMetrics metrics);

        /// <summary>
        /// Get calibration history for analysis
        /// </summary>
        CalibrationHistory GetCalibrationHistory();

        /// <summary>
        /// Reset calibrator to initial state
        /// </summary>
        void Reset();
    }

    #region Phase 4: Multi-Timeframe Analysis Interfaces

    /// <summary>
    /// Interface for multi-timeframe analysis across 1m, 5m, 15m, and 1h timeframes
    /// </summary>
    public interface IMultiTimeframeAnalyzer
    {
        /// <summary>
        /// Initialize the multi-timeframe analyzer
        /// </summary>
        void Initialize(SymbolProfile symbolProfile, OptimalSettings settings, MultiTimeframeConfig config = null);

        /// <summary>
        /// Update with new market data for all timeframes
        /// </summary>
        void UpdateMarketData(MarketDataPoint newBar);

        /// <summary>
        /// Get current multi-timeframe analysis state
        /// </summary>
        MultiTimeframeAnalysisState GetCurrentAnalysisState();

        /// <summary>
        /// Get analysis state for specific timeframe
        /// </summary>
        AnalysisState GetTimeframeAnalysisState(Timeframe timeframe);

        /// <summary>
        /// Analyze trend alignment across timeframes
        /// </summary>
        TrendAlignment AnalyzeTrendAlignment(string analysisType);

        /// <summary>
        /// Detect cross-timeframe patterns
        /// </summary>
        List<MultiTimeframePattern> DetectCrossTimeframePatterns();

        /// <summary>
        /// Get timeframe-weighted confidence
        /// </summary>
        decimal GetWeightedConfidence(string analysisType);

        /// <summary>
        /// Get performance metrics
        /// </summary>
        MultiTimeframePerformanceMetrics GetPerformanceMetrics();

        /// <summary>
        /// Reset analyzer state
        /// </summary>
        void Reset();

        /// <summary>
        /// Check if analyzer is initialized
        /// </summary>
        bool IsInitialized { get; }
    }

    /// <summary>
    /// Interface for advanced signal synthesis combining multiple signal sources
    /// </summary>
    public interface ISignalSynthesizer
    {
        /// <summary>
        /// Initialize the signal synthesizer
        /// </summary>
        void Initialize(SignalSynthesisConfig config = null);

        /// <summary>
        /// Synthesize signals from multi-timeframe analysis
        /// </summary>
        SynthesizedSignal SynthesizeSignal(MultiTimeframeAnalysisState multiTimeframeState, MarketContext marketContext);

        /// <summary>
        /// Analyze signal correlation across timeframes
        /// </summary>
        SignalCorrelationAnalysis AnalyzeSignalCorrelation(Dictionary<Timeframe, TradingSignal> timeframeSignals);

        /// <summary>
        /// Resolve signal conflicts when timeframes disagree
        /// </summary>
        SynthesizedSignal ResolveSignalConflicts(List<TradingSignal> conflictingSignals, MarketContext context);

        /// <summary>
        /// Track signal persistence and momentum
        /// </summary>
        SignalPersistenceAnalysis TrackSignalPersistence(SynthesizedSignal signal, List<SynthesizedSignal> historicalSignals);

        /// <summary>
        /// Filter signals based on market microstructure
        /// </summary>
        bool PassesMicrostructureFilter(SynthesizedSignal signal, MarketContext context, AnalysisState analysisState);

        /// <summary>
        /// Get synthesis performance metrics
        /// </summary>
        SignalSynthesisMetrics GetPerformanceMetrics();

        /// <summary>
        /// Reset synthesizer state
        /// </summary>
        void Reset();
    }

    /// <summary>
    /// Interface for enhanced pattern recognition across timeframes
    /// </summary>
    public interface IPatternRecognizer
    {
        /// <summary>
        /// Initialize pattern recognizer
        /// </summary>
        void Initialize(PatternRecognitionConfig config = null);

        /// <summary>
        /// Detect volume patterns (accumulation/distribution)
        /// </summary>
        List<VolumePattern> DetectVolumePatterns(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData);

        /// <summary>
        /// Detect institutional footprint across timeframes
        /// </summary>
        InstitutionalFootprint DetectInstitutionalFootprint(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData);

        /// <summary>
        /// Analyze market maker vs retail flow
        /// </summary>
        MarketParticipantAnalysis AnalyzeMarketParticipants(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData);

        /// <summary>
        /// Detect order flow imbalances with cross-timeframe validation
        /// </summary>
        OrderFlowImbalance DetectOrderFlowImbalance(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData);

        /// <summary>
        /// Validate patterns across timeframes
        /// </summary>
        PatternValidationResult ValidatePattern(MultiTimeframePattern pattern, Dictionary<Timeframe, List<MarketDataPoint>> timeframeData);

        /// <summary>
        /// Get pattern recognition performance metrics
        /// </summary>
        PatternRecognitionMetrics GetPerformanceMetrics();

        /// <summary>
        /// Reset recognizer state
        /// </summary>
        void Reset();
    }

    #endregion
}
