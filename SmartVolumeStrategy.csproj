<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>SmartVolumeStrategy</AssemblyName>
    <AssemblyTitle>Smart Volume Strategy with Intelligent Auto-Calibration</AssemblyTitle>
    <AssemblyDescription>ATAS strategy with intelligent symbol analysis and auto-calibrated settings for optimal performance</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Company>Smart Trading Solutions</Company>
    <Product>SmartVolumeStrategy</Product>
    <Copyright>Copyright © 2024</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="ATAS.DataFeedsCore">
      <HintPath>C:\Program Files (x86)\ATAS Platform\ATAS.DataFeedsCore.dll</HintPath>
    </Reference>
    <Reference Include="ATAS.Indicators">
      <HintPath>C:\Program Files (x86)\ATAS Platform\ATAS.Indicators.dll</HintPath>
    </Reference>
    <Reference Include="ATAS.Indicators.Technical">
      <HintPath>C:\Program Files (x86)\ATAS Platform\ATAS.Indicators.Technical.dll</HintPath>
    </Reference>
    <Reference Include="ATAS.Strategies">
      <HintPath>C:\Program Files (x86)\ATAS Platform\ATAS.Strategies.dll</HintPath>
    </Reference>
    <Reference Include="OFT.Attributes">
      <HintPath>C:\Program Files (x86)\ATAS Platform\OFT.Attributes.dll</HintPath>
    </Reference>
    <Reference Include="Utils.Common">
      <HintPath>C:\Program Files (x86)\ATAS Platform\Utils.Common.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore">
      <HintPath>C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.14\ref\net8.0\PresentationCore.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase">
      <HintPath>C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.14\ref\net8.0\WindowsBase.dll</HintPath>
    </Reference>
    <Reference Include="OFT.Rendering">
      <HintPath>C:\Program Files (x86)\ATAS Platform\OFT.Rendering.dll</HintPath>
    </Reference>
    <Reference Include="OFT.Rendering.OpenGL">
      <HintPath>C:\Program Files (x86)\ATAS Platform\OFT.Rendering.OpenGL.dll</HintPath>
    </Reference>
    <Reference Include="OFT.Rendering.Vortice">
      <HintPath>C:\Program Files (x86)\ATAS Platform\OFT.Rendering.Vortice.dll</HintPath>
    </Reference>
    <Reference Include="OFT.Rendering.Wpf">
      <HintPath>C:\Program Files (x86)\ATAS Platform\OFT.Rendering.Wpf.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.5" />
  </ItemGroup>

  <!-- Explicitly exclude the Documentation folder from compilation -->
  <ItemGroup>
    <None Include="Documentation\**\*.*">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="if exist &quot;C:\Users\<USER>\Documents\ATAS\Strategies\&quot; copy &quot;$(TargetPath)&quot; &quot;C:\Users\<USER>\Documents\ATAS\Strategies\SmartVolumeStrategy.dll&quot;" IgnoreExitCode="true" />
  </Target>

</Project>
