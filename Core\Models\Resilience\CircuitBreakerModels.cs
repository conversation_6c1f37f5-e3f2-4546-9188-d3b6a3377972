using System;
using System.Collections.Generic;

namespace SmartVolumeStrategy.Core.Models.Resilience
{
    /// <summary>
    /// Phase 2.3: Circuit breaker state enumeration
    /// </summary>
    public enum CircuitBreakerState
    {
        Closed,     // Normal operation, monitoring for failures
        Open,       // Component failed, blocking operations
        HalfOpen    // Testing recovery, limited operations allowed
    }

    /// <summary>
    /// Strategy component types for circuit breaker monitoring
    /// </summary>
    public enum StrategyComponent
    {
        SignalGeneration,
        MicrostructureFiltering,
        DataFeeds,
        AnalysisEngines,
        AdaptiveCalibration,
        MultiTimeframeAnalysis,
        PositionManagement,
        RiskControls,
        MemoryManager,           // Added for SlidingWindowMemoryManager
        AdaptiveCalibrator,      // Added for AdaptiveCalibrator
        InstitutionalAnalysis,   // Added for institutional components
        SignalQuality,           // Added for signal quality components
        MicrostructureFilter,    // Added for microstructure filter components
        PerformanceMonitor       // Added for PerformanceMonitor
    }

    /// <summary>
    /// System degradation levels
    /// </summary>
    public enum DegradationLevel
    {
        Normal,       // All features enabled
        Reduced,      // Non-essential features disabled
        Minimal,      // Only core functionality
        Emergency,    // Only position management and risk controls
        EmergencyStop // Complete system shutdown
    }

    /// <summary>
    /// Component health status
    /// </summary>
    public enum ComponentHealth
    {
        Unknown,
        Critical,
        Degraded,
        Recovering,
        Healthy
    }

    /// <summary>
    /// Health trend direction
    /// </summary>
    public enum HealthTrendDirection
    {
        Insufficient_Data,
        Declining,
        Stable,
        Improving
    }

    /// <summary>
    /// Microstructure health status for Phase 2.2 integration
    /// </summary>
    public class MicrostructureHealthStatus
    {
        public ComponentHealth VolumeHealth { get; set; } = ComponentHealth.Unknown;
        public ComponentHealth DeltaHealth { get; set; } = ComponentHealth.Unknown;
        public ComponentHealth CVDHealth { get; set; } = ComponentHealth.Unknown;
        public ComponentHealth OrderFlowHealth { get; set; } = ComponentHealth.Unknown;
        public ComponentHealth OverallHealth { get; set; } = ComponentHealth.Unknown;
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        public decimal OverallHealthScore { get; set; } = 0.5m;
    }

    /// <summary>
    /// Circuit breaker failure severity
    /// </summary>
    public enum FailureSeverity
    {
        Low,        // Minor issues, continue monitoring
        Medium,     // Significant issues, consider degradation
        High,       // Major issues, activate circuit breaker
        Critical    // System-threatening issues, emergency stop
    }

    /// <summary>
    /// Individual circuit breaker configuration and state
    /// </summary>
    public class CircuitBreaker
    {
        public StrategyComponent Component { get; set; }
        public CircuitBreakerState State { get; set; }
        public DateTime LastStateChange { get; set; }
        public int FailureCount { get; set; }
        public int ConsecutiveFailures { get; set; }
        public int SuccessCount { get; set; }
        public int ConsecutiveSuccesses { get; set; }
        public DateTime LastFailure { get; set; }
        public DateTime LastSuccess { get; set; }
        public CircuitBreakerConfig Config { get; set; }
        public List<string> RecentFailureReasons { get; set; } = new List<string>();
        public TimeSpan TotalDowntime { get; set; }
        public DateTime? OpenedAt { get; set; }
        public DateTime? LastHalfOpenAttempt { get; set; }
    }

    /// <summary>
    /// Circuit breaker configuration parameters
    /// </summary>
    public class CircuitBreakerConfig
    {
        public int FailureThreshold { get; set; } = 5;
        public int SuccessThreshold { get; set; } = 3;
        public TimeSpan OpenTimeout { get; set; } = TimeSpan.FromMinutes(2);
        public TimeSpan HalfOpenTimeout { get; set; } = TimeSpan.FromSeconds(30);
        public int MaxConsecutiveFailures { get; set; } = 10;
        public TimeSpan FailureWindow { get; set; } = TimeSpan.FromMinutes(5);
        public bool EnableAutoRecovery { get; set; } = true;
        public FailureSeverity TriggerSeverity { get; set; } = FailureSeverity.High;
    }

    /// <summary>
    /// Component health and performance metrics
    /// </summary>
    public class ComponentHealthMetrics
    {
        public StrategyComponent Component { get; set; }
        public DateTime Timestamp { get; set; }
        public decimal HealthScore { get; set; }
        public decimal PerformanceScore { get; set; }
        public decimal ErrorRate { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public int TotalOperations { get; set; }
        public int FailedOperations { get; set; }
        public int SuccessfulOperations { get; set; }
        public List<string> ActiveIssues { get; set; } = new List<string>();
        public Dictionary<string, decimal> CustomMetrics { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// System degradation status and configuration
    /// </summary>
    public class DegradationStatus
    {
        public DegradationLevel CurrentLevel { get; set; }
        public DegradationLevel PreviousLevel { get; set; }
        public DateTime LastLevelChange { get; set; }
        public List<StrategyComponent> FailedComponents { get; set; } = new List<StrategyComponent>();
        public List<string> DisabledFeatures { get; set; } = new List<string>();
        public List<string> DegradationReasons { get; set; } = new List<string>();
        public bool IsEmergencyMode { get; set; }
        public DateTime? EmergencyModeActivated { get; set; }
    }

    /// <summary>
    /// Circuit breaker event for logging and monitoring
    /// </summary>
    public class CircuitBreakerEvent
    {
        public DateTime Timestamp { get; set; }
        public StrategyComponent Component { get; set; }
        public CircuitBreakerState PreviousState { get; set; }
        public CircuitBreakerState NewState { get; set; }
        public string Reason { get; set; }
        public FailureSeverity Severity { get; set; }
        public ComponentHealthMetrics HealthMetrics { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// System resilience status overview
    /// </summary>
    public class SystemResilienceStatus
    {
        public DateTime Timestamp { get; set; }
        public DegradationLevel DegradationLevel { get; set; }
        public int ActiveCircuitBreakers { get; set; }
        public int HealthyComponents { get; set; }
        public int DegradedComponents { get; set; }
        public int FailedComponents { get; set; }
        public decimal OverallSystemHealth { get; set; }
        public List<StrategyComponent> CriticalComponents { get; set; } = new List<StrategyComponent>();
        public List<string> ActiveAlerts { get; set; } = new List<string>();
        public Dictionary<StrategyComponent, CircuitBreakerState> ComponentStates { get; set; } = new Dictionary<StrategyComponent, CircuitBreakerState>();
        public TimeSpan TotalSystemUptime { get; set; }
        public TimeSpan TotalSystemDowntime { get; set; }
    }

    /// <summary>
    /// Recovery attempt tracking
    /// </summary>
    public class RecoveryAttempt
    {
        public DateTime Timestamp { get; set; }
        public StrategyComponent Component { get; set; }
        public bool WasSuccessful { get; set; }
        public string AttemptReason { get; set; }
        public string FailureReason { get; set; }
        public TimeSpan AttemptDuration { get; set; }
        public ComponentHealthMetrics PreAttemptHealth { get; set; }
        public ComponentHealthMetrics PostAttemptHealth { get; set; }
    }

    /// <summary>
    /// Feature availability matrix for degradation modes
    /// </summary>
    public class FeatureAvailability
    {
        public Dictionary<DegradationLevel, List<string>> AvailableFeatures { get; set; } = new Dictionary<DegradationLevel, List<string>>();
        public Dictionary<DegradationLevel, List<string>> DisabledFeatures { get; set; } = new Dictionary<DegradationLevel, List<string>>();
        
        public FeatureAvailability()
        {
            InitializeDefaultFeatures();
        }
        
        private void InitializeDefaultFeatures()
        {
            // Normal mode - all features available
            AvailableFeatures[DegradationLevel.Normal] = new List<string>
            {
                "SignalGeneration", "MicrostructureFiltering", "MultiTimeframeAnalysis",
                "PatternDetection", "AdaptiveCalibration", "EnhancedSignalQuality",
                "AdvancedMicrostructureFilter", "CircuitBreakers", "PositionManagement",
                "RiskControls", "PerformanceMonitoring", "DetailedLogging"
            };
            
            // Reduced mode - disable advanced features
            AvailableFeatures[DegradationLevel.Reduced] = new List<string>
            {
                "SignalGeneration", "MicrostructureFiltering", "MultiTimeframeAnalysis",
                "AdaptiveCalibration", "EnhancedSignalQuality", "CircuitBreakers",
                "PositionManagement", "RiskControls", "BasicLogging"
            };
            
            // Minimal mode - only core functionality
            AvailableFeatures[DegradationLevel.Minimal] = new List<string>
            {
                "SignalGeneration", "BasicMicrostructureFilter", "AdaptiveCalibration",
                "CircuitBreakers", "PositionManagement", "RiskControls", "BasicLogging"
            };
            
            // Emergency mode - only position and risk management
            AvailableFeatures[DegradationLevel.Emergency] = new List<string>
            {
                "PositionManagement", "RiskControls", "EmergencyLogging"
            };
            
            // Calculate disabled features for each level
            foreach (var level in Enum.GetValues<DegradationLevel>())
            {
                var allFeatures = AvailableFeatures[DegradationLevel.Normal];
                var availableFeatures = AvailableFeatures[level];
                DisabledFeatures[level] = allFeatures.Except(availableFeatures).ToList();
            }
        }
        
        public bool IsFeatureAvailable(string feature, DegradationLevel level)
        {
            return AvailableFeatures.ContainsKey(level) && AvailableFeatures[level].Contains(feature);
        }
    }

    /// <summary>
    /// Circuit breaker statistics and performance metrics
    /// </summary>
    public class CircuitBreakerStatistics
    {
        public StrategyComponent Component { get; set; }
        public int TotalActivations { get; set; }
        public int TotalRecoveries { get; set; }
        public int FailedRecoveryAttempts { get; set; }
        public TimeSpan TotalDowntime { get; set; }
        public TimeSpan AverageDowntime { get; set; }
        public decimal AvailabilityPercentage { get; set; }
        public DateTime LastActivation { get; set; }
        public DateTime LastRecovery { get; set; }
        public List<RecoveryAttempt> RecentRecoveryAttempts { get; set; } = new List<RecoveryAttempt>();
        public Dictionary<FailureSeverity, int> FailuresBySeverity { get; set; } = new Dictionary<FailureSeverity, int>();
    }
}
