using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Core.Models.Resilience;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Resilience
{
    /// <summary>
    /// Enhanced circuit breaker manager with intelligent fallback strategies
    /// Provides graceful degradation instead of complete failure
    /// </summary>
    public class EnhancedCircuitBreakerManager : CircuitBreakerManager
    {
        private readonly Action<string> _logAction;
        private readonly Dictionary<StrategyComponent, FallbackStrategy> _fallbackStrategies;
        private readonly Dictionary<StrategyComponent, ComponentHealth> _componentHealth;
        
        public EnhancedCircuitBreakerManager(Action<string> logAction = null) : base(logAction)
        {
            _logAction = logAction ?? (_ => { });
            _fallbackStrategies = InitializeFallbackStrategies();
            _componentHealth = new Dictionary<StrategyComponent, ComponentHealth>();
            
            InitializeComponentHealth();
        }
        
        /// <summary>
        /// Enhanced operation recording with intelligent fallback activation
        /// </summary>
        public new void RecordOperation(StrategyComponent component, bool success, string details = "", FailureSeverity severity = FailureSeverity.Low)
        {
            // Call base implementation
            base.RecordOperation(component, success, details, severity);
            
            // Update component health
            UpdateComponentHealth(component, success, severity);
            
            // Check if fallback should be activated
            if (!success && ShouldActivateFallback(component, severity))
            {
                ActivateFallback(component, details);
            }
        }
        
        /// <summary>
        /// Check if component can operate or should use fallback
        /// </summary>
        public bool CanOperateOrFallback(StrategyComponent component, out bool useFallback)
        {
            useFallback = false;
            
            // Check if component is completely broken
            if (!IsOperationAllowed(component))
            {
                // Check if fallback is available
                if (_fallbackStrategies.ContainsKey(component) && _fallbackStrategies[component].IsActive)
                {
                    useFallback = true;
                    return true;
                }
                return false;
            }
            
            // Check if component health is degraded but fallback would be better
            if (_componentHealth.ContainsKey(component))
            {
                var health = _componentHealth[component];
                if (health.SuccessRate < 0.7m && _fallbackStrategies.ContainsKey(component))
                {
                    useFallback = true;
                    return true;
                }
            }
            
            return true;
        }
        
        /// <summary>
        /// Get fallback configuration for component
        /// </summary>
        public FallbackConfiguration GetFallbackConfiguration(StrategyComponent component)
        {
            if (_fallbackStrategies.ContainsKey(component))
            {
                return _fallbackStrategies[component].Configuration;
            }
            
            return new FallbackConfiguration
            {
                UseSimplifiedLogic = true,
                ReducedConfidenceMultiplier = 0.8m,
                EnableBasicValidation = true,
                FallbackTimeout = TimeSpan.FromMinutes(5)
            };
        }
        
        /// <summary>
        /// Initialize fallback strategies for each component
        /// </summary>
        private Dictionary<StrategyComponent, FallbackStrategy> InitializeFallbackStrategies()
        {
            return new Dictionary<StrategyComponent, FallbackStrategy>
            {
                [StrategyComponent.SignalGeneration] = new FallbackStrategy
                {
                    IsActive = false,
                    Configuration = new FallbackConfiguration
                    {
                        UseSimplifiedLogic = true,
                        ReducedConfidenceMultiplier = 0.9m,
                        EnableBasicValidation = true,
                        FallbackTimeout = TimeSpan.FromMinutes(10)
                    },
                    Description = "Use basic volume block detection only"
                },
                
                [StrategyComponent.MultiTimeframeAnalysis] = new FallbackStrategy
                {
                    IsActive = false,
                    Configuration = new FallbackConfiguration
                    {
                        UseSimplifiedLogic = true,
                        ReducedConfidenceMultiplier = 0.85m,
                        EnableBasicValidation = true,
                        FallbackTimeout = TimeSpan.FromMinutes(15)
                    },
                    Description = "Use single timeframe analysis with basic correlation"
                },
                
                [StrategyComponent.InstitutionalAnalysis] = new FallbackStrategy
                {
                    IsActive = false,
                    Configuration = new FallbackConfiguration
                    {
                        UseSimplifiedLogic = true,
                        ReducedConfidenceMultiplier = 0.8m,
                        EnableBasicValidation = true,
                        FallbackTimeout = TimeSpan.FromMinutes(20)
                    },
                    Description = "Use basic volume and confidence thresholds for institutional detection"
                },
                
                [StrategyComponent.MicrostructureFilter] = new FallbackStrategy
                {
                    IsActive = false,
                    Configuration = new FallbackConfiguration
                    {
                        UseSimplifiedLogic = true,
                        ReducedConfidenceMultiplier = 0.9m,
                        EnableBasicValidation = true,
                        FallbackTimeout = TimeSpan.FromMinutes(5)
                    },
                    Description = "Use basic volume and delta checks only"
                },
                
                [StrategyComponent.SignalQuality] = new FallbackStrategy
                {
                    IsActive = false,
                    Configuration = new FallbackConfiguration
                    {
                        UseSimplifiedLogic = true,
                        ReducedConfidenceMultiplier = 0.95m,
                        EnableBasicValidation = true,
                        FallbackTimeout = TimeSpan.FromMinutes(5)
                    },
                    Description = "Use confidence-based quality assessment only"
                }
            };
        }
        
        /// <summary>
        /// Initialize component health tracking
        /// </summary>
        private void InitializeComponentHealth()
        {
            foreach (StrategyComponent component in Enum.GetValues<StrategyComponent>())
            {
                _componentHealth[component] = new ComponentHealth
                {
                    Component = component,
                    SuccessRate = 1.0m,
                    LastSuccess = DateTime.UtcNow,
                    ConsecutiveFailures = 0,
                    TotalOperations = 0,
                    SuccessfulOperations = 0
                };
            }
        }
        
        /// <summary>
        /// Update component health metrics
        /// </summary>
        private void UpdateComponentHealth(StrategyComponent component, bool success, FailureSeverity severity)
        {
            if (!_componentHealth.ContainsKey(component))
            {
                _componentHealth[component] = new ComponentHealth { Component = component };
            }
            
            var health = _componentHealth[component];
            health.TotalOperations++;
            
            if (success)
            {
                health.SuccessfulOperations++;
                health.ConsecutiveFailures = 0;
                health.LastSuccess = DateTime.UtcNow;
            }
            else
            {
                health.ConsecutiveFailures++;
                health.LastFailure = DateTime.UtcNow;
                health.LastFailureSeverity = severity;
            }
            
            // Calculate success rate over last 100 operations
            health.SuccessRate = health.TotalOperations > 0 ? 
                (decimal)health.SuccessfulOperations / health.TotalOperations : 1.0m;
        }
        
        /// <summary>
        /// Determine if fallback should be activated
        /// </summary>
        private bool ShouldActivateFallback(StrategyComponent component, FailureSeverity severity)
        {
            if (!_componentHealth.ContainsKey(component))
                return false;
            
            var health = _componentHealth[component];
            
            // Activate fallback for severe failures
            if (severity >= FailureSeverity.High)
                return true;
            
            // Activate fallback for repeated medium failures
            if (severity >= FailureSeverity.Medium && health.ConsecutiveFailures >= 3)
                return true;
            
            // Activate fallback for low success rate
            if (health.SuccessRate < 0.5m && health.TotalOperations >= 10)
                return true;
            
            return false;
        }
        
        /// <summary>
        /// Activate fallback strategy for component
        /// </summary>
        private void ActivateFallback(StrategyComponent component, string reason)
        {
            if (_fallbackStrategies.ContainsKey(component))
            {
                var fallback = _fallbackStrategies[component];
                fallback.IsActive = true;
                fallback.ActivatedAt = DateTime.UtcNow;
                fallback.ActivationReason = reason;
                
                _logAction($"🔄 FALLBACK ACTIVATED for {component}: {fallback.Description}");
                _logAction($"   Reason: {reason}");
                _logAction($"   Confidence multiplier: {fallback.Configuration.ReducedConfidenceMultiplier:P0}");
            }
        }
        
        /// <summary>
        /// Deactivate fallback when component recovers
        /// </summary>
        public void DeactivateFallback(StrategyComponent component)
        {
            if (_fallbackStrategies.ContainsKey(component) && _fallbackStrategies[component].IsActive)
            {
                _fallbackStrategies[component].IsActive = false;
                _logAction($"✅ FALLBACK DEACTIVATED for {component} - Component recovered");
            }
        }
        
        /// <summary>
        /// Get component health status
        /// </summary>
        public ComponentHealth GetComponentHealth(StrategyComponent component)
        {
            return _componentHealth.ContainsKey(component) ? _componentHealth[component] : new ComponentHealth { Component = component };
        }
    }
    
    /// <summary>
    /// Fallback strategy configuration
    /// </summary>
    public class FallbackStrategy
    {
        public bool IsActive { get; set; }
        public DateTime ActivatedAt { get; set; }
        public string ActivationReason { get; set; }
        public string Description { get; set; }
        public FallbackConfiguration Configuration { get; set; }
    }
    
    /// <summary>
    /// Fallback configuration parameters
    /// </summary>
    public class FallbackConfiguration
    {
        public bool UseSimplifiedLogic { get; set; }
        public decimal ReducedConfidenceMultiplier { get; set; }
        public bool EnableBasicValidation { get; set; }
        public TimeSpan FallbackTimeout { get; set; }
    }
    
    /// <summary>
    /// Component health tracking
    /// </summary>
    public class ComponentHealth
    {
        public StrategyComponent Component { get; set; }
        public decimal SuccessRate { get; set; }
        public DateTime LastSuccess { get; set; }
        public DateTime LastFailure { get; set; }
        public FailureSeverity LastFailureSeverity { get; set; }
        public int ConsecutiveFailures { get; set; }
        public int TotalOperations { get; set; }
        public int SuccessfulOperations { get; set; }
    }
}
