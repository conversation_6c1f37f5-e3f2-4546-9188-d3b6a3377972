using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Analysis;

namespace SmartVolumeStrategy.Core.Strategy
{
    /// <summary>
    /// Clean signal generation using calibrated thresholds and intelligent market context analysis
    /// </summary>
    public class SignalGenerator : ISignalGenerator
    {
        private OptimalSettings _currentSettings;
        private SignalStatistics _statistics;
        private readonly Queue<TradingSignal> _recentSignals;
        private readonly object _lockObject = new object();
        private readonly Action<string> _logAction;

        // Phase 2: Enhanced Signal Quality Analysis
        private readonly EnhancedSignalQualityAnalyzer _qualityAnalyzer;

        public SignalGenerator(Action<string> logAction = null)
        {
            _statistics = new SignalStatistics();
            _recentSignals = new Queue<TradingSignal>();
            _currentSettings = new OptimalSettings(); // Default settings
            _logAction = logAction;

            // Phase 2: Initialize Enhanced Signal Quality Analyzer
            _qualityAnalyzer = new EnhancedSignalQualityAnalyzer(logAction);
        }

        /// <summary>
        /// Generate trading signal based on current market conditions using calibrated parameters
        /// </summary>
        public TradingSignal GenerateSignal(
            MarketDataPoint currentBar,
            VolumeBlockResult volumeBlock,
            MarketContext marketContext,
            OptimalSettings settings)
        {
            if (currentBar == null)
                throw new ArgumentNullException(nameof(currentBar));
            
            if (volumeBlock == null)
                throw new ArgumentNullException(nameof(volumeBlock));
            
            if (marketContext == null)
                throw new ArgumentNullException(nameof(marketContext));
            
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));

            lock (_lockObject)
            {
                _currentSettings = settings;
                
                var signal = new TradingSignal
                {
                    Timestamp = DateTime.UtcNow,
                    CorrelationId = Guid.NewGuid().ToString("N")[..8],
                    Type = SignalType.None,
                    Action = SignalAction.None,
                    CurrentPrice = currentBar.Close
                };

                // Update signal components with current market data
                UpdateSignalComponents(signal, currentBar, volumeBlock, marketContext);

                // Check if volume block meets signal threshold
                // CRITICAL FIX: Adjust threshold scale - CumulativeImpact is typically 0.001-0.01, not 0.30
                var adjustedThreshold = settings.SignalThreshold * 0.01m; // Convert 30% to 0.30 for proper comparison

                if (!volumeBlock.IsVolumeBlock)
                {
                    signal.PrimaryReason = "No volume block detected";
                    signal.Quality = SignalQuality.Poor;
                    UpdateStatistics(signal);
                    return signal;
                }

                if (Math.Abs(volumeBlock.CumulativeImpact) < adjustedThreshold)
                {
                    signal.PrimaryReason = $"Volume block impact {volumeBlock.CumulativeImpact:F6} below threshold {adjustedThreshold:F6}";
                    signal.Quality = SignalQuality.Poor;
                    UpdateStatistics(signal);
                    return signal;
                }

                // Determine signal direction based on market context
                var signalDirection = DetermineSignalDirection(currentBar, volumeBlock, marketContext, settings);
                
                if (signalDirection == SignalType.None)
                {
                    signal.PrimaryReason = "Market conditions not favorable for entry";
                    signal.Quality = SignalQuality.Fair;
                    UpdateStatistics(signal);
                    return signal;
                }

                // Generate entry signal
                signal.Type = signalDirection;
                signal.Action = SignalAction.Entry;
                signal.Strength = volumeBlock.CumulativeImpact;
                
                // Calculate signal confidence using multiple factors
                signal.Confidence = CalculateSignalConfidence(volumeBlock, marketContext, settings);

                // Phase 2: Enhanced signal quality analysis
                var enhancedQuality = _qualityAnalyzer.AnalyzeSignalQuality(
                    signal,
                    marketContext,
                    null, // multiTimeframeState - will be added in Phase 2.5
                    null  // recentHistory - will be added in Phase 2.5
                );

                signal.Quality = enhancedQuality.Level;

                // Log enhanced quality details
                _logAction?.Invoke($"📊 Enhanced Quality: {enhancedQuality.Level} (Score: {enhancedQuality.Score:F3}, Trend: {enhancedQuality.Trend})");
                if (enhancedQuality.MarketConditionAdjustment)
                {
                    _logAction?.Invoke($"  • Market condition adjustment applied");
                }
                
                // Generate reasoning and supporting factors
                GenerateSignalReasoning(signal, volumeBlock, marketContext);
                
                // Calculate recommended trade parameters
                CalculateTradeRecommendations(signal, currentBar, settings);

                // Add to recent signals history
                _recentSignals.Enqueue(signal);
                while (_recentSignals.Count > 50) // Keep last 50 signals
                {
                    _recentSignals.Dequeue();
                }

                UpdateStatistics(signal);
                return signal;
            }
        }

        /// <summary>
        /// Generate exit signal for existing position
        /// </summary>
        public TradingSignal GenerateExitSignal(
            MarketDataPoint currentBar,
            PositionInfo currentPosition,
            OptimalSettings settings)
        {
            if (currentBar == null)
                throw new ArgumentNullException(nameof(currentBar));
            
            if (currentPosition == null)
                throw new ArgumentNullException(nameof(currentPosition));
            
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));

            var exitSignal = new TradingSignal
            {
                Timestamp = DateTime.UtcNow,
                CorrelationId = currentPosition.CorrelationId,
                Type = SignalType.Exit,
                Action = SignalAction.Exit,
                CurrentPrice = currentBar.Close
            };

            // Check for exit conditions
            var exitReason = DetermineExitReason(currentBar, currentPosition, settings);
            
            if (string.IsNullOrEmpty(exitReason))
            {
                exitSignal.Action = SignalAction.Hold;
                exitSignal.PrimaryReason = "No exit conditions met - holding position";
                exitSignal.Confidence = 0.3m;
                exitSignal.Quality = SignalQuality.Fair;
                return exitSignal;
            }

            exitSignal.PrimaryReason = exitReason;
            exitSignal.Confidence = CalculateExitConfidence(exitReason, currentPosition);

            // Phase 2: Enhanced quality analysis for exit signals
            var enhancedQuality = _qualityAnalyzer.AnalyzeSignalQuality(
                exitSignal,
                new MarketContext(), // Basic context for exit signals
                null,
                null
            );

            exitSignal.Quality = enhancedQuality.Level;

            UpdateStatistics(exitSignal);
            return exitSignal;
        }

        /// <summary>
        /// Update signal generation parameters
        /// </summary>
        public void UpdateSettings(OptimalSettings newSettings)
        {
            lock (_lockObject)
            {
                _currentSettings = newSettings ?? throw new ArgumentNullException(nameof(newSettings));
            }
        }

        /// <summary>
        /// Get signal generation statistics
        /// </summary>
        public SignalStatistics GetSignalStatistics()
        {
            lock (_lockObject)
            {
                return new SignalStatistics
                {
                    TotalSignalsGenerated = _statistics.TotalSignalsGenerated,
                    SignalsExecuted = _statistics.SignalsExecuted,
                    AverageConfidence = _statistics.AverageConfidence,
                    AverageStrength = _statistics.AverageStrength,
                    SignalsByType = new Dictionary<SignalType, int>(_statistics.SignalsByType),
                    SignalsByQuality = new Dictionary<SignalQuality, int>(_statistics.SignalsByQuality),
                    LastSignalTime = _statistics.LastSignalTime,
                    AverageTimeBetweenSignals = _statistics.AverageTimeBetweenSignals
                };
            }
        }

        /// <summary>
        /// Reset signal generator state
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                _statistics = new SignalStatistics();
                _recentSignals.Clear();
            }
        }

        #region Private Methods

        private void UpdateSignalComponents(TradingSignal signal, MarketDataPoint currentBar, VolumeBlockResult volumeBlock, MarketContext marketContext)
        {
            // Update volume component
            signal.VolumeComponent = new VolumeSignalComponent
            {
                VolumeRatio = volumeBlock.VolumeRatio,
                VolumeThreshold = _currentSettings.VolumeThreshold,
                IsVolumeSpike = volumeBlock.IsVolumeBlock,
                VolumeConfidence = volumeBlock.Confidence,
                VolumePattern = volumeBlock.Description
            };

            // Update delta component
            signal.DeltaComponent = new DeltaSignalComponent
            {
                DeltaImbalance = Math.Abs(currentBar.Delta) / Math.Max(currentBar.Volume, 1),
                CVDTrend = marketContext.CVDTrend,
                Bias = DetermineDeltaBias(currentBar.Delta),
                DeltaConfidence = CalculateDeltaConfidence(currentBar, marketContext),
                IsInstitutionalActivity = signal.DeltaComponent.DeltaImbalance > _currentSettings.DeltaImbalanceThreshold,
                DeltaPattern = DetermineDeltaPattern(currentBar, marketContext)
            };

            // Update volatility component
            signal.VolatilityComponent = new VolatilitySignalComponent
            {
                CurrentVolatility = Math.Abs(currentBar.Close - currentBar.Open) / Math.Max(currentBar.Open, 0.01m),
                AverageVolatility = 0.01m, // This would come from market context in real implementation
                Regime = marketContext.VolatilityRegime,
                VolatilityConfidence = 0.8m, // Default confidence
                IsVolatilityExpansion = marketContext.VolatilityRegime == VolatilityRegime.High || marketContext.VolatilityRegime == VolatilityRegime.VeryHigh,
                VolatilityPattern = DetermineVolatilityPattern(marketContext.VolatilityRegime)
            };

            // Update market context fields
            signal.VolumeRatio = volumeBlock.VolumeRatio;
            signal.DeltaImbalance = signal.DeltaComponent.DeltaImbalance;
            signal.CumulativeImpact = volumeBlock.CumulativeImpact;
        }

        private SignalType DetermineSignalDirection(MarketDataPoint currentBar, VolumeBlockResult volumeBlock, MarketContext marketContext, OptimalSettings settings)
        {
            var priceChange = currentBar.Close - currentBar.Open;
            var deltaDirection = Math.Sign(currentBar.Delta);
            var priceDirection = Math.Sign(priceChange);

            // CRITICAL DIAGNOSTIC: Log the alignment check details
            _logAction?.Invoke($"🔍 SIGNAL DIRECTION ANALYSIS:");
            _logAction?.Invoke($"  • Price Change: {priceChange:F6} (Open: {currentBar.Open:F6}, Close: {currentBar.Close:F6})");
            _logAction?.Invoke($"  • Price Direction: {priceDirection} ({(priceDirection > 0 ? "Up" : priceDirection < 0 ? "Down" : "Flat")})");
            _logAction?.Invoke($"  • Delta: {currentBar.Delta:F2}");
            _logAction?.Invoke($"  • Delta Direction: {deltaDirection} ({(deltaDirection > 0 ? "Buying" : deltaDirection < 0 ? "Selling" : "Neutral")})");
            _logAction?.Invoke($"  • Delta/Price Aligned: {deltaDirection == priceDirection}");

            // Check for zero directions
            if (deltaDirection == 0 || priceDirection == 0)
            {
                _logAction?.Invoke($"  • REJECTED: Zero direction (Delta: {deltaDirection}, Price: {priceDirection})");
                return SignalType.None;
            }

            // Check delta significance first
            var deltaImbalance = Math.Abs(currentBar.Delta) / Math.Max(currentBar.Volume, 1);
            _logAction?.Invoke($"  • Delta Imbalance: {deltaImbalance:F4} (Threshold: {settings.DeltaImbalanceThreshold:F4})");

            if (deltaImbalance < settings.DeltaImbalanceThreshold)
            {
                _logAction?.Invoke($"  • REJECTED: Delta imbalance too low ({deltaImbalance:F4} < {settings.DeltaImbalanceThreshold:F4})");
                return SignalType.None;
            }

            // Check if we're in optimal trading time (if session adjustments are enabled)
            if (settings.EnableSessionAdjustments && !marketContext.IsOptimalTradingTime)
            {
                _logAction?.Invoke($"  • REJECTED: Not optimal trading time (Session adjustments enabled)");
                return SignalType.None;
            }

            // VOLUME BLOCK STRATEGY: Look for ABSORPTION patterns (delta/price misalignment)
            // This is the core concept - buyers absorbing selling pressure or sellers absorbing buying pressure
            if (deltaDirection != priceDirection)
            {
                // ABSORPTION DETECTED: Delta and price are misaligned
                if (deltaDirection > 0 && priceDirection < 0)
                {
                    // Buying pressure while price falls = Bullish absorption
                    _logAction?.Invoke($"  • 🎯 BULLISH ABSORPTION: Buying pressure ({deltaDirection}) while price falls ({priceDirection})");
                    _logAction?.Invoke($"  • ✅ SIGNAL APPROVED: Long (Absorption Pattern)");
                    return SignalType.Long;
                }
                else if (deltaDirection < 0 && priceDirection > 0)
                {
                    // Selling pressure while price rises = Bearish absorption
                    _logAction?.Invoke($"  • 🎯 BEARISH ABSORPTION: Selling pressure ({deltaDirection}) while price rises ({priceDirection})");
                    _logAction?.Invoke($"  • ✅ SIGNAL APPROVED: Short (Absorption Pattern)");
                    return SignalType.Short;
                }
            }
            else
            {
                // ALIGNED: Delta and price move in same direction (traditional momentum)
                _logAction?.Invoke($"  • 📈 MOMENTUM PATTERN: Delta and price aligned ({deltaDirection})");
                var signalType = deltaDirection > 0 ? SignalType.Long : SignalType.Short;
                _logAction?.Invoke($"  • ✅ SIGNAL APPROVED: {signalType} (Momentum Pattern)");
                return signalType;
            }

            // Fallback (should not reach here)
            _logAction?.Invoke($"  • ❌ UNEXPECTED: No pattern matched");
            return SignalType.None;
        }

        private decimal CalculateSignalConfidence(VolumeBlockResult volumeBlock, MarketContext marketContext, OptimalSettings settings)
        {
            var confidenceFactors = new List<decimal>();
            var factorWeights = new List<decimal>();

            // 1. Volume block confidence (Weight: 25%)
            confidenceFactors.Add(volumeBlock.Confidence);
            factorWeights.Add(0.25m);

            // 2. Signal strength factor (Weight: 20%)
            var adjustedThreshold = settings.SignalThreshold * 0.01m;
            var strengthFactor = Math.Min(1.0m, Math.Abs(volumeBlock.CumulativeImpact) / (adjustedThreshold * 2));
            confidenceFactors.Add(strengthFactor);
            factorWeights.Add(0.20m);

            // 3. Enhanced volatility regime factor (Weight: 15%)
            var volatilityFactor = CalculateVolatilityConfidenceFactor(marketContext.VolatilityRegime);
            confidenceFactors.Add(volatilityFactor);
            factorWeights.Add(0.15m);

            // 4. CVD trend confirmation factor (Weight: 15%)
            var cvdConfirmationFactor = CalculateCVDConfirmationFactor(marketContext.CVDTrend, volumeBlock);
            confidenceFactors.Add(cvdConfirmationFactor);
            factorWeights.Add(0.15m);

            // 5. Market profile timing factor (Weight: 10%)
            var timingFactor = CalculateMarketProfileTimingFactor(marketContext);
            confidenceFactors.Add(timingFactor);
            factorWeights.Add(0.10m);

            // 6. Delta alignment factor (Weight: 10%)
            var deltaAlignmentFactor = CalculateDeltaAlignmentFactor(marketContext, settings);
            confidenceFactors.Add(deltaAlignmentFactor);
            factorWeights.Add(0.10m);

            // 7. Market condition synthesis factor (Weight: 5%)
            var marketConditionFactor = CalculateMarketConditionSynthesisFactor(marketContext);
            confidenceFactors.Add(marketConditionFactor);
            factorWeights.Add(0.05m);

            // Calculate weighted average confidence
            var weightedConfidence = 0m;
            for (int i = 0; i < confidenceFactors.Count; i++)
            {
                weightedConfidence += confidenceFactors[i] * factorWeights[i];
            }

            // Log detailed confidence breakdown for debugging
            _logAction?.Invoke($"🎯 ENHANCED CONFIDENCE BREAKDOWN:");
            _logAction?.Invoke($"  • Volume Block: {volumeBlock.Confidence:F3} (Weight: 25%)");
            _logAction?.Invoke($"  • Signal Strength: {strengthFactor:F3} (Weight: 20%)");
            _logAction?.Invoke($"  • Volatility Regime: {volatilityFactor:F3} (Weight: 15%)");
            _logAction?.Invoke($"  • CVD Confirmation: {cvdConfirmationFactor:F3} (Weight: 15%)");
            _logAction?.Invoke($"  • Market Timing: {timingFactor:F3} (Weight: 10%)");
            _logAction?.Invoke($"  • Delta Alignment: {deltaAlignmentFactor:F3} (Weight: 10%)");
            _logAction?.Invoke($"  • Market Conditions: {marketConditionFactor:F3} (Weight: 5%)");
            _logAction?.Invoke($"  • FINAL WEIGHTED CONFIDENCE: {weightedConfidence:F3} ({weightedConfidence * 100:F1}%)");

            return Math.Max(0.1m, Math.Min(1.0m, weightedConfidence));
        }

        private decimal CalculateMarketConditionFactor(MarketContext marketContext)
        {
            // Higher confidence in normal to high volatility, lower in extreme volatility
            var volatilityFactor = marketContext.VolatilityRegime switch
            {
                VolatilityRegime.VeryLow => 0.6m,
                VolatilityRegime.Low => 0.7m,
                VolatilityRegime.Normal => 0.9m,
                VolatilityRegime.High => 0.8m,
                VolatilityRegime.VeryHigh => 0.6m,
                VolatilityRegime.Extreme => 0.4m,
                _ => 0.7m
            };

            return volatilityFactor;
        }

        private decimal CalculateVolatilityConfidenceFactor(VolatilityRegime regime)
        {
            // Enhanced volatility confidence based on regime suitability for volume-based signals
            return regime switch
            {
                VolatilityRegime.VeryLow => 0.5m,    // Low volatility may indicate weak signals
                VolatilityRegime.Low => 0.7m,       // Moderate confidence
                VolatilityRegime.Normal => 0.95m,   // Optimal for volume analysis
                VolatilityRegime.High => 0.85m,     // Good but slightly elevated risk
                VolatilityRegime.VeryHigh => 0.6m,  // Higher risk environment
                VolatilityRegime.Extreme => 0.3m,   // Very risky, signals may be unreliable
                _ => 0.7m
            };
        }

        private decimal CalculateCVDConfirmationFactor(decimal cvdTrend, VolumeBlockResult volumeBlock)
        {
            // CVD trend should align with volume block direction for higher confidence
            var volumeBlockDirection = Math.Sign(volumeBlock.CumulativeImpact);
            var cvdDirection = Math.Sign(cvdTrend);

            if (volumeBlockDirection == 0 || cvdDirection == 0)
                return 0.7m; // Neutral when no clear direction

            // Strong confirmation when CVD and volume block align
            if (volumeBlockDirection == cvdDirection)
            {
                var alignmentStrength = Math.Min(1.0m, Math.Abs(cvdTrend) / 100); // Scale CVD trend
                return 0.7m + (alignmentStrength * 0.3m); // 0.7 to 1.0 range
            }

            // Divergence reduces confidence
            var divergenceStrength = Math.Min(1.0m, Math.Abs(cvdTrend) / 100);
            return Math.Max(0.3m, 0.7m - (divergenceStrength * 0.4m)); // 0.3 to 0.7 range
        }

        private decimal CalculateMarketProfileTimingFactor(MarketContext marketContext)
        {
            var baseFactor = 0.6m; // Base confidence

            // Boost confidence during optimal trading times
            if (marketContext.IsOptimalTradingTime)
                baseFactor += 0.3m;

            // Additional boost for high-activity sessions
            var sessionBoost = marketContext.CurrentSession switch
            {
                TradingSession.Overlap_EuropeanUS => 0.1m,      // Highest activity overlap
                TradingSession.Overlap_AsianEuropean => 0.05m,  // Moderate activity overlap
                TradingSession.European => 0.05m,              // Major session
                TradingSession.US => 0.05m,                    // Major session
                TradingSession.Asian => 0.02m,                 // Lower activity
                TradingSession.Crypto24H => 0.0m,              // No specific boost
                _ => 0.0m
            };

            return Math.Min(1.0m, baseFactor + sessionBoost);
        }

        private decimal CalculateDeltaAlignmentFactor(MarketContext marketContext, OptimalSettings settings)
        {
            var deltaImbalance = marketContext.DeltaImbalance;

            // Base factor based on delta imbalance strength
            var baseFactor = Math.Min(1.0m, deltaImbalance * 2); // Scale 0-0.5 to 0-1

            // Threshold-based adjustment
            if (deltaImbalance > settings.DeltaImbalanceThreshold)
            {
                var excessRatio = deltaImbalance / settings.DeltaImbalanceThreshold;
                baseFactor = Math.Min(1.0m, baseFactor * excessRatio);
            }
            else
            {
                // Reduce confidence if below threshold
                baseFactor *= 0.7m;
            }

            return Math.Max(0.3m, baseFactor);
        }

        private decimal CalculateMarketConditionSynthesisFactor(MarketContext marketContext)
        {
            var baseFactor = 0.7m;

            // Analyze market conditions for additional insights
            if (marketContext.MarketConditions != null && marketContext.MarketConditions.Any())
            {
                var positiveConditions = 0;
                var negativeConditions = 0;

                foreach (var condition in marketContext.MarketConditions)
                {
                    var lowerCondition = condition.ToLower();

                    // Positive conditions
                    if (lowerCondition.Contains("active") ||
                        lowerCondition.Contains("optimal") ||
                        lowerCondition.Contains("strong") ||
                        lowerCondition.Contains("session overlap"))
                    {
                        positiveConditions++;
                    }

                    // Negative conditions
                    if (lowerCondition.Contains("quiet") ||
                        lowerCondition.Contains("low activity") ||
                        lowerCondition.Contains("transition") ||
                        lowerCondition.Contains("insufficient"))
                    {
                        negativeConditions++;
                    }
                }

                // Adjust factor based on condition balance
                var conditionBalance = positiveConditions - negativeConditions;
                baseFactor += conditionBalance * 0.1m; // ±0.1 per condition
            }

            return Math.Max(0.3m, Math.Min(1.0m, baseFactor));
        }

        private SignalQuality DetermineSignalQuality(decimal confidence, MarketContext marketContext)
        {
            if (confidence >= 0.9m)
                return SignalQuality.Exceptional;
            
            if (confidence >= 0.8m)
                return SignalQuality.Excellent;
            
            if (confidence >= 0.7m)
                return SignalQuality.Good;
            
            if (confidence >= 0.5m)
                return SignalQuality.Fair;
            
            return SignalQuality.Poor;
        }

        private void GenerateSignalReasoning(TradingSignal signal, VolumeBlockResult volumeBlock, MarketContext marketContext)
        {
            signal.PrimaryReason = $"Volume block detected: {volumeBlock.Description} with {signal.Confidence:P1} confidence";

            signal.SupportingFactors.Clear();
            var adjustedThreshold = _currentSettings.SignalThreshold * 0.01m;
            signal.SupportingFactors.Add($"Cumulative impact: {volumeBlock.CumulativeImpact:F6} (threshold: {adjustedThreshold:F6})");
            signal.SupportingFactors.Add($"Volume ratio: {volumeBlock.VolumeRatio:F1}x average");
            signal.SupportingFactors.Add($"Delta imbalance: {signal.DeltaComponent.DeltaImbalance:P1}");
            
            if (marketContext.IsOptimalTradingTime)
                signal.SupportingFactors.Add("Optimal trading session");

            signal.RiskFactors.Clear();
            if (marketContext.VolatilityRegime == VolatilityRegime.VeryHigh || marketContext.VolatilityRegime == VolatilityRegime.Extreme)
                signal.RiskFactors.Add("High volatility environment");
            
            if (!marketContext.IsOptimalTradingTime)
                signal.RiskFactors.Add("Outside optimal trading hours");
        }

        private void CalculateTradeRecommendations(TradingSignal signal, MarketDataPoint currentBar, OptimalSettings settings)
        {
            // Base position size adjusted for volatility
            signal.RecommendedPositionSize = settings.PositionSizeUSDT * settings.RiskAdjustmentFactor;

            // Calculate TP/SL levels based on direction
            if (signal.Type == SignalType.Long)
            {
                signal.RecommendedTakeProfit = currentBar.Close * (1 + settings.TakeProfitPercent / 100);
                signal.RecommendedStopLoss = currentBar.Close * (1 - settings.StopLossPercent / 100);
            }
            else if (signal.Type == SignalType.Short)
            {
                signal.RecommendedTakeProfit = currentBar.Close * (1 - settings.TakeProfitPercent / 100);
                signal.RecommendedStopLoss = currentBar.Close * (1 + settings.StopLossPercent / 100);
            }
        }

        private string DetermineExitReason(MarketDataPoint currentBar, PositionInfo position, OptimalSettings settings)
        {
            var currentPrice = currentBar.Close;

            // Check TP/SL levels
            if (position.Direction == SignalType.Long)
            {
                if (currentPrice >= position.TakeProfit)
                    return "Take Profit reached";
                
                if (currentPrice <= position.StopLoss)
                    return "Stop Loss triggered";
            }
            else if (position.Direction == SignalType.Short)
            {
                if (currentPrice <= position.TakeProfit)
                    return "Take Profit reached";
                
                if (currentPrice >= position.StopLoss)
                    return "Stop Loss triggered";
            }

            // Check for time-based exit (if position held too long)
            var holdTime = DateTime.UtcNow - position.EntryTime;
            if (holdTime.TotalHours > 24) // Max 24 hours per position
                return "Maximum hold time exceeded";

            return null; // No exit condition met
        }

        private decimal CalculateExitConfidence(string exitReason, PositionInfo position)
        {
            return exitReason switch
            {
                "Take Profit reached" => 0.95m,
                "Stop Loss triggered" => 0.90m,
                "Maximum hold time exceeded" => 0.70m,
                _ => 0.50m
            };
        }

        private void UpdateStatistics(TradingSignal signal)
        {
            _statistics.TotalSignalsGenerated++;
            _statistics.LastSignalTime = signal.Timestamp;

            // Update signal type counts
            if (!_statistics.SignalsByType.ContainsKey(signal.Type))
                _statistics.SignalsByType[signal.Type] = 0;
            _statistics.SignalsByType[signal.Type]++;

            // Update signal quality counts
            if (!_statistics.SignalsByQuality.ContainsKey(signal.Quality))
                _statistics.SignalsByQuality[signal.Quality] = 0;
            _statistics.SignalsByQuality[signal.Quality]++;

            // Update averages
            var totalSignals = _statistics.TotalSignalsGenerated;
            _statistics.AverageConfidence = (_statistics.AverageConfidence * (totalSignals - 1) + signal.Confidence) / totalSignals;
            _statistics.AverageStrength = (_statistics.AverageStrength * (totalSignals - 1) + signal.Strength) / totalSignals;

            // Calculate average time between signals
            if (_recentSignals.Count > 1)
            {
                var timeDifferences = new List<TimeSpan>();
                var signals = _recentSignals.ToArray();
                
                for (int i = 1; i < signals.Length; i++)
                {
                    timeDifferences.Add(signals[i].Timestamp - signals[i - 1].Timestamp);
                }
                
                _statistics.AverageTimeBetweenSignals = TimeSpan.FromTicks((long)timeDifferences.Average(t => t.Ticks));
            }
        }

        private DeltaBias DetermineDeltaBias(decimal delta)
        {
            if (Math.Abs(delta) < 100) // Threshold for neutral
                return DeltaBias.Neutral;
            
            return delta > 0 ? DeltaBias.Buy : DeltaBias.Sell;
        }

        private decimal CalculateDeltaConfidence(MarketDataPoint currentBar, MarketContext marketContext)
        {
            var deltaImbalance = Math.Abs(currentBar.Delta) / Math.Max(currentBar.Volume, 1);
            return Math.Min(1.0m, deltaImbalance * 2); // Scale to 0-1
        }

        private string DetermineDeltaPattern(MarketDataPoint currentBar, MarketContext marketContext)
        {
            var deltaImbalance = Math.Abs(currentBar.Delta) / Math.Max(currentBar.Volume, 1);
            
            if (deltaImbalance > 0.7m)
                return "Strong Pressure";
            
            if (deltaImbalance > 0.5m)
                return "Moderate Pressure";
            
            return "Normal Flow";
        }

        private string DetermineVolatilityPattern(VolatilityRegime regime)
        {
            return regime switch
            {
                VolatilityRegime.VeryLow => "Low Volatility",
                VolatilityRegime.Low => "Calm Market",
                VolatilityRegime.Normal => "Normal Volatility",
                VolatilityRegime.High => "Increased Volatility",
                VolatilityRegime.VeryHigh => "High Volatility",
                VolatilityRegime.Extreme => "Extreme Volatility",
                _ => "Unknown"
            };
        }

        #endregion
    }
}
