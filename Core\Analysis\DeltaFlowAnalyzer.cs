using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Analyzes delta flow patterns and buy/sell pressure to optimize delta-based signal generation
    /// </summary>
    public class DeltaFlowAnalyzer : IDeltaFlowAnalyzer
    {
        private const int DEFAULT_CVD_LOOKBACK = 30;
        private const decimal INSTITUTIONAL_ACTIVITY_THRESHOLD = 0.7m;
        private readonly decimal[] DEFAULT_DELTA_THRESHOLDS = { 0.5m, 1.0m, 1.5m, 2.0m, 2.5m, 3.0m, 4.0m, 5.0m };

        /// <summary>
        /// Analyze delta flow patterns and buy/sell pressure
        /// </summary>
        public DeltaFlowCharacteristics AnalyzeDeltaFlow(List<MarketDataPoint> marketData)
        {
            if (marketData == null || marketData.Count < 30)
                throw new ArgumentException("Need at least 30 bars for delta flow analysis", nameof(marketData));

            var characteristics = new DeltaFlowCharacteristics();

            // Calculate basic delta statistics
            CalculateDeltaStatistics(marketData, characteristics);

            // Analyze CVD trends
            var (trendStrength, reversalFrequency, cvdValues) = CalculateCVDTrends(marketData, DEFAULT_CVD_LOOKBACK);
            characteristics.CVDTrendStrength = trendStrength;
            characteristics.CVDReversalFrequency = reversalFrequency;

            // Detect institutional activity
            var (activityLevel, hasStrongActivity, patterns) = DetectInstitutionalActivity(marketData);
            characteristics.InstitutionalActivityLevel = activityLevel;
            characteristics.HasStrongInstitutionalActivity = hasStrongActivity;

            // Optimize delta thresholds
            var (optimalImbalance, optimalSignificance, _) = OptimizeDeltaThresholds(marketData, DEFAULT_DELTA_THRESHOLDS);
            characteristics.OptimalDeltaThreshold = optimalImbalance;

            // Classify delta flow regime
            characteristics.Regime = ClassifyDeltaFlowRegime(characteristics);

            // Calculate buy pressure dominance
            characteristics.BuyPressureDominance = CalculateBuyPressureDominance(marketData);

            return characteristics;
        }

        /// <summary>
        /// Calculate cumulative volume delta (CVD) trends
        /// </summary>
        public (decimal TrendStrength, decimal ReversalFrequency, List<decimal> CVDValues) CalculateCVDTrends(
            List<MarketDataPoint> marketData, 
            int lookbackPeriod = 30)
        {
            if (marketData.Count < lookbackPeriod)
                throw new ArgumentException($"Need at least {lookbackPeriod} bars for CVD analysis", nameof(marketData));

            var cvdValues = new List<decimal>();
            var cumulativeDelta = 0m;

            // Calculate CVD for each bar
            foreach (var bar in marketData)
            {
                cumulativeDelta += bar.Delta;
                cvdValues.Add(cumulativeDelta);
            }

            // Analyze trend strength using linear regression on CVD
            var trendStrength = CalculateCVDTrendStrength(cvdValues, lookbackPeriod);

            // Calculate reversal frequency
            var reversalFrequency = CalculateCVDReversalFrequency(cvdValues, lookbackPeriod);

            return (trendStrength, reversalFrequency, cvdValues);
        }

        /// <summary>
        /// Detect institutional activity patterns
        /// </summary>
        public (decimal ActivityLevel, bool HasStrongActivity, List<string> Patterns) DetectInstitutionalActivity(List<MarketDataPoint> marketData)
        {
            var patterns = new List<string>();
            var activityScores = new List<decimal>();

            // Pattern 1: Large delta imbalances with low volume (stealth trading)
            var stealthScore = DetectStealthTrading(marketData);
            activityScores.Add(stealthScore);
            if (stealthScore > 0.6m) patterns.Add("Stealth Trading");

            // Pattern 2: Sustained delta pressure over multiple bars
            var sustainedPressureScore = DetectSustainedDeltaPressure(marketData);
            activityScores.Add(sustainedPressureScore);
            if (sustainedPressureScore > 0.6m) patterns.Add("Sustained Pressure");

            // Pattern 3: Volume-price divergence with strong delta
            var divergenceScore = DetectVolumePriceDivergence(marketData);
            activityScores.Add(divergenceScore);
            if (divergenceScore > 0.6m) patterns.Add("Volume-Price Divergence");

            // Pattern 4: Absorption patterns (high volume, minimal price movement)
            var absorptionScore = DetectAbsorptionPatterns(marketData);
            activityScores.Add(absorptionScore);
            if (absorptionScore > 0.6m) patterns.Add("Absorption");

            var overallActivityLevel = activityScores.Average();
            var hasStrongActivity = overallActivityLevel > INSTITUTIONAL_ACTIVITY_THRESHOLD;

            return (overallActivityLevel, hasStrongActivity, patterns);
        }

        /// <summary>
        /// Optimize delta thresholds for signal generation
        /// </summary>
        public (decimal OptimalImbalanceThreshold, decimal OptimalSignificanceThreshold, List<ThresholdTestResult> TestResults) 
            OptimizeDeltaThresholds(List<MarketDataPoint> marketData, decimal[] testThresholds = null)
        {
            testThresholds ??= DEFAULT_DELTA_THRESHOLDS;
            var testResults = new List<ThresholdTestResult>();

            // Calculate average volume for significance threshold
            var averageVolume = marketData.Average(x => x.Volume);
            var significanceThresholds = new[] { averageVolume * 0.1m, averageVolume * 0.2m, averageVolume * 0.3m };

            decimal bestImbalanceThreshold = 2.0m;
            decimal bestSignificanceThreshold = averageVolume * 0.2m;
            decimal bestScore = 0;

            // Test combinations of imbalance and significance thresholds
            foreach (var imbalanceThreshold in testThresholds)
            {
                foreach (var significanceThreshold in significanceThresholds)
                {
                    var result = TestDeltaThresholdCombination(marketData, imbalanceThreshold, significanceThreshold);
                    testResults.Add(result);

                    if (result.Score > bestScore)
                    {
                        bestScore = result.Score;
                        bestImbalanceThreshold = imbalanceThreshold;
                        bestSignificanceThreshold = significanceThreshold;
                    }
                }
            }

            // Mark the optimal result
            var optimalResult = testResults.FirstOrDefault(r => 
                Math.Abs(r.ThresholdValue - bestImbalanceThreshold) < 0.01m);
            if (optimalResult != null)
                optimalResult.IsOptimal = true;

            return (bestImbalanceThreshold, bestSignificanceThreshold, testResults);
        }

        /// <summary>
        /// Analyze real-time delta flow
        /// </summary>
        public DeltaSignalComponent AnalyzeRealtimeDelta(MarketDataPoint currentBar, List<MarketDataPoint> recentBars)
        {
            if (recentBars == null || recentBars.Count < 10)
                throw new ArgumentException("Need at least 10 recent bars for real-time delta analysis", nameof(recentBars));

            var component = new DeltaSignalComponent();

            // Calculate current delta imbalance
            var averageVolume = recentBars.Average(x => x.Volume);
            component.DeltaImbalance = averageVolume > 0 ? Math.Abs(currentBar.Delta) / averageVolume : 0;

            // Calculate CVD trend from recent bars
            var recentCVD = CalculateRecentCVDTrend(recentBars.Concat(new[] { currentBar }).ToList());
            component.CVDTrend = recentCVD;

            // Determine delta bias
            component.Bias = DetermineDeltaBias(currentBar.Delta, component.DeltaImbalance);

            // Calculate confidence based on delta strength and consistency
            component.DeltaConfidence = CalculateDeltaConfidence(currentBar, recentBars);

            // Check for institutional activity
            component.IsInstitutionalActivity = component.DeltaImbalance > 2.0m && component.DeltaConfidence > 0.7m;

            // Determine delta pattern
            component.DeltaPattern = DetermineDeltaPattern(currentBar, recentBars);

            return component;
        }

        /// <summary>
        /// Get current real-time delta analysis state
        /// </summary>
        public DeltaAnalysisState GetCurrentDeltaState(List<MarketDataPoint> recentData)
        {
            if (recentData == null || recentData.Count < 10)
            {
                return new DeltaAnalysisState
                {
                    CurrentRegime = DeltaFlowRegime.Balanced,
                    DeltaConfidence = 0.3m,
                    CurrentDeltaPattern = "Insufficient Data"
                };
            }

            var currentBar = recentData.Last();
            var averageVolume = recentData.Average(x => x.Volume);
            var deltaImbalance = currentBar.Volume > 0 ? Math.Abs(currentBar.Delta) / currentBar.Volume : 0;

            // Calculate CVD trend
            var (cvdTrend, trendStrength, isDivergence) = CalculateRealtimeCVD(recentData, 20);

            // Detect institutional activity
            var (isInstitutional, activityLevel, pattern) = DetectRealtimeInstitutionalActivity(currentBar, recentData);

            // Determine current bias
            var bias = DetermineDeltaBias(currentBar.Delta, deltaImbalance);

            // Calculate buy pressure dominance
            var buyPressure = CalculateBuyPressureDominance(recentData);

            // Determine regime
            var regime = DetermineCurrentDeltaRegime(deltaImbalance, buyPressure, recentData);

            return new DeltaAnalysisState
            {
                LastUpdate = DateTime.UtcNow,
                CurrentRegime = regime,
                CurrentDeltaImbalance = deltaImbalance,
                CurrentCVDTrend = cvdTrend,
                CurrentBias = bias,
                IsInstitutionalActivityDetected = isInstitutional,
                InstitutionalActivityLevel = activityLevel,
                InstitutionalPattern = pattern,
                LastInstitutionalActivity = isInstitutional ? currentBar.Timestamp : DateTime.MinValue,
                CVDTrendStrength = trendStrength,
                CVDTrendDuration = CalculateCVDTrendDuration(recentData),
                IsCVDDivergence = isDivergence,
                CVDConfidence = Math.Min(1.0m, trendStrength * 2),
                CurrentDeltaPattern = DetermineDeltaPattern(currentBar, recentData),
                DeltaConfidence = CalculateDeltaConfidence(currentBar, recentData),
                BuyPressureDominance = buyPressure
            };
        }

        /// <summary>
        /// Calculate real-time CVD trend
        /// </summary>
        public (decimal CVDTrend, decimal TrendStrength, bool IsDivergence) CalculateRealtimeCVD(List<MarketDataPoint> recentData, int lookbackPeriod = 30)
        {
            if (recentData == null || recentData.Count < Math.Min(lookbackPeriod, 10))
                return (0, 0, false);

            var dataToUse = recentData.TakeLast(Math.Min(lookbackPeriod, recentData.Count)).ToList();

            // Calculate CVD values
            var cvdValues = new List<decimal>();
            var cumulativeDelta = 0m;

            foreach (var bar in dataToUse)
            {
                cumulativeDelta += bar.Delta;
                cvdValues.Add(cumulativeDelta);
            }

            // Calculate trend strength
            var trendStrength = CalculateCVDTrendStrength(cvdValues, cvdValues.Count);

            // Calculate current trend (recent change)
            var recentTrend = cvdValues.Count >= 5 ?
                cvdValues.TakeLast(3).Average() - cvdValues.Take(cvdValues.Count - 3).TakeLast(3).Average() : 0;

            // Detect divergence (CVD vs price)
            var isDivergence = DetectCVDPriceDivergence(dataToUse, cvdValues);

            return (recentTrend, trendStrength, isDivergence);
        }

        /// <summary>
        /// Detect real-time institutional activity
        /// </summary>
        public (bool IsDetected, decimal ActivityLevel, string Pattern) DetectRealtimeInstitutionalActivity(MarketDataPoint currentBar, List<MarketDataPoint> recentData)
        {
            if (recentData == null || recentData.Count < 5)
                return (false, 0, "None");

            var activityScores = new List<decimal>();
            var patterns = new List<string>();

            // Check current bar for stealth trading
            if (currentBar.Volume > 0)
            {
                var deltaPercent = Math.Abs(currentBar.Delta) / currentBar.Volume;
                var priceChangePercent = currentBar.Open > 0 ? Math.Abs(currentBar.Close - currentBar.Open) / currentBar.Open : 0;

                if (deltaPercent > 0.6m && priceChangePercent < 0.002m)
                {
                    activityScores.Add(0.8m);
                    patterns.Add("Stealth");
                }
            }

            // Check for sustained pressure in recent bars
            var sustainedScore = DetectSustainedDeltaPressure(recentData.TakeLast(10).ToList());
            if (sustainedScore > 0.5m)
            {
                activityScores.Add(sustainedScore);
                patterns.Add("Sustained Pressure");
            }

            // Check for absorption pattern
            var averageVolume = recentData.Average(x => x.Volume);
            if (currentBar.Volume > averageVolume * 2.0m && currentBar.Volume > 0)
            {
                var deltaImbalance = Math.Abs(currentBar.Delta) / currentBar.Volume;
                var priceChangePercent = currentBar.Open > 0 ? Math.Abs(currentBar.Close - currentBar.Open) / currentBar.Open : 0;

                if (deltaImbalance > 0.7m && priceChangePercent < 0.001m)
                {
                    activityScores.Add(0.9m);
                    patterns.Add("Absorption");
                }
            }

            var overallActivity = activityScores.Count > 0 ? activityScores.Average() : 0;
            var isDetected = overallActivity > 0.6m;
            var primaryPattern = patterns.Count > 0 ? patterns.First() : "None";

            return (isDetected, overallActivity, primaryPattern);
        }

        #region Private Methods

        private void CalculateDeltaStatistics(List<MarketDataPoint> marketData, DeltaFlowCharacteristics characteristics)
        {
            var deltas = marketData.Select(x => x.Delta).ToList();
            var deltaImbalances = marketData.Select(x => x.Volume > 0 ? Math.Abs(x.Delta) / x.Volume : 0).ToList();

            characteristics.AverageDeltaImbalance = deltaImbalances.Average();
            
            var variance = deltaImbalances.Sum(d => (d - characteristics.AverageDeltaImbalance) * (d - characteristics.AverageDeltaImbalance)) / deltaImbalances.Count;
            characteristics.DeltaImbalanceStdDev = (decimal)Math.Sqrt((double)variance);
        }

        private decimal CalculateCVDTrendStrength(List<decimal> cvdValues, int lookbackPeriod)
        {
            if (cvdValues.Count < lookbackPeriod)
                return 0;

            var recentCVD = cvdValues.TakeLast(lookbackPeriod).ToList();
            
            // Calculate linear regression slope to determine trend strength
            var n = recentCVD.Count;
            var sumX = n * (n - 1) / 2; // Sum of indices 0, 1, 2, ..., n-1
            var sumY = recentCVD.Sum();
            var sumXY = recentCVD.Select((value, index) => value * index).Sum();
            var sumX2 = n * (n - 1) * (2 * n - 1) / 6; // Sum of squares of indices

            var slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
            
            // Normalize slope to 0-1 scale
            var maxExpectedSlope = recentCVD.Max() - recentCVD.Min();
            return maxExpectedSlope > 0 ? Math.Min(1.0m, Math.Abs(slope) / maxExpectedSlope * 10) : 0;
        }

        private decimal CalculateCVDReversalFrequency(List<decimal> cvdValues, int lookbackPeriod)
        {
            if (cvdValues.Count < lookbackPeriod)
                return 0;

            var recentCVD = cvdValues.TakeLast(lookbackPeriod).ToList();
            var reversals = 0;

            for (int i = 2; i < recentCVD.Count; i++)
            {
                var prev2 = recentCVD[i - 2];
                var prev1 = recentCVD[i - 1];
                var current = recentCVD[i];

                // Check for trend reversal
                if ((prev1 > prev2 && current < prev1) || (prev1 < prev2 && current > prev1))
                {
                    reversals++;
                }
            }

            return (decimal)reversals / (recentCVD.Count - 2);
        }

        private decimal DetectStealthTrading(List<MarketDataPoint> marketData)
        {
            var stealthSignals = 0;
            var totalBars = 0;

            foreach (var bar in marketData)
            {
                if (bar.Volume > 0)
                {
                    totalBars++;
                    var deltaPercent = Math.Abs(bar.Delta) / bar.Volume;
                    var priceChangePercent = bar.Open > 0 ? Math.Abs(bar.Close - bar.Open) / bar.Open : 0;

                    // Stealth trading: high delta imbalance with low price movement
                    if (deltaPercent > 0.6m && priceChangePercent < 0.002m) // 0.2% price change
                    {
                        stealthSignals++;
                    }
                }
            }

            return totalBars > 0 ? (decimal)stealthSignals / totalBars : 0;
        }

        private decimal DetectSustainedDeltaPressure(List<MarketDataPoint> marketData)
        {
            var sustainedPeriods = 0;
            var currentStreak = 0;
            var lastDeltaSign = 0;

            foreach (var bar in marketData)
            {
                var deltaSign = Math.Sign(bar.Delta);
                
                if (deltaSign == lastDeltaSign && deltaSign != 0)
                {
                    currentStreak++;
                }
                else
                {
                    if (currentStreak >= 3) // 3+ bars of sustained pressure
                    {
                        sustainedPeriods++;
                    }
                    currentStreak = 1;
                    lastDeltaSign = deltaSign;
                }
            }

            // Final check for streak at end
            if (currentStreak >= 3)
            {
                sustainedPeriods++;
            }

            return Math.Min(1.0m, (decimal)sustainedPeriods / (marketData.Count / 10)); // Normalize
        }

        private decimal DetectVolumePriceDivergence(List<MarketDataPoint> marketData)
        {
            var divergenceSignals = 0;
            var totalBars = 0;

            for (int i = 1; i < marketData.Count; i++)
            {
                var current = marketData[i];
                var previous = marketData[i - 1];

                if (current.Volume > 0 && previous.Volume > 0)
                {
                    totalBars++;
                    
                    var volumeIncrease = current.Volume > previous.Volume * 1.5m;
                    var priceChangePercent = previous.Close > 0 ? Math.Abs(current.Close - previous.Close) / previous.Close : 0;
                    var deltaImbalance = Math.Abs(current.Delta) / current.Volume;

                    // Divergence: high volume and delta but low price movement
                    if (volumeIncrease && deltaImbalance > 0.5m && priceChangePercent < 0.003m)
                    {
                        divergenceSignals++;
                    }
                }
            }

            return totalBars > 0 ? (decimal)divergenceSignals / totalBars : 0;
        }

        private decimal DetectAbsorptionPatterns(List<MarketDataPoint> marketData)
        {
            var absorptionSignals = 0;
            var totalBars = 0;

            foreach (var bar in marketData)
            {
                if (bar.Volume > 0)
                {
                    totalBars++;
                    var averageVolume = marketData.Average(x => x.Volume);
                    var volumeRatio = bar.Volume / averageVolume;
                    var priceChangePercent = bar.Open > 0 ? Math.Abs(bar.Close - bar.Open) / bar.Open : 0;
                    var deltaImbalance = Math.Abs(bar.Delta) / bar.Volume;

                    // Absorption: high volume, high delta imbalance, minimal price movement
                    if (volumeRatio > 2.0m && deltaImbalance > 0.7m && priceChangePercent < 0.001m)
                    {
                        absorptionSignals++;
                    }
                }
            }

            return totalBars > 0 ? (decimal)absorptionSignals / totalBars : 0;
        }

        private DeltaFlowRegime ClassifyDeltaFlowRegime(DeltaFlowCharacteristics characteristics)
        {
            var buyPressure = characteristics.BuyPressureDominance;

            if (Math.Abs(buyPressure) < 0.1m)
                return DeltaFlowRegime.Balanced;
            
            if (characteristics.DeltaImbalanceStdDev > characteristics.AverageDeltaImbalance * 2)
                return DeltaFlowRegime.Erratic;

            if (buyPressure > 0.5m)
                return DeltaFlowRegime.StrongBuyPressure;
            
            if (buyPressure > 0.2m)
                return DeltaFlowRegime.BuyPressure;
            
            if (buyPressure < -0.5m)
                return DeltaFlowRegime.StrongSellPressure;
            
            if (buyPressure < -0.2m)
                return DeltaFlowRegime.SellPressure;

            return DeltaFlowRegime.Balanced;
        }

        private decimal CalculateBuyPressureDominance(List<MarketDataPoint> marketData)
        {
            var totalBuyVolume = marketData.Sum(x => x.BuyVolume);
            var totalSellVolume = marketData.Sum(x => x.SellVolume);
            var totalVolume = totalBuyVolume + totalSellVolume;

            if (totalVolume == 0)
                return 0;

            var buyRatio = totalBuyVolume / totalVolume;
            return (buyRatio - 0.5m) * 2; // Convert to -1 to 1 scale
        }

        private ThresholdTestResult TestDeltaThresholdCombination(List<MarketDataPoint> marketData, decimal imbalanceThreshold, decimal significanceThreshold)
        {
            var signals = 0;
            var profitableSignals = 0;
            var totalProfit = 0m;
            var totalLoss = 0m;

            for (int i = 1; i < marketData.Count - 1; i++)
            {
                var currentBar = marketData[i];
                var nextBar = marketData[i + 1];

                if (currentBar.Volume > significanceThreshold)
                {
                    var deltaImbalance = Math.Abs(currentBar.Delta) / currentBar.Volume;
                    
                    if (deltaImbalance >= imbalanceThreshold)
                    {
                        signals++;
                        
                        var priceChange = nextBar.Close - currentBar.Close;
                        var priceChangePercent = currentBar.Close > 0 ? priceChange / currentBar.Close : 0;
                        
                        // Assume we trade in direction of delta
                        var expectedDirection = Math.Sign(currentBar.Delta);
                        var actualDirection = Math.Sign(priceChangePercent);
                        
                        if (expectedDirection == actualDirection && Math.Abs(priceChangePercent) > 0.001m)
                        {
                            profitableSignals++;
                            totalProfit += Math.Abs(priceChangePercent);
                        }
                        else
                        {
                            totalLoss += Math.Abs(priceChangePercent);
                        }
                    }
                }
            }

            var winRate = signals > 0 ? (decimal)profitableSignals / signals : 0;
            var profitFactor = totalLoss > 0 ? totalProfit / totalLoss : (totalProfit > 0 ? 10 : 1);
            
            var frequencyScore = Math.Min(1.0m, signals / (decimal)marketData.Count * 20);
            var winRateScore = winRate;
            var profitScore = Math.Min(1.0m, profitFactor / 3.0m);
            
            var combinedScore = (frequencyScore * 0.3m + winRateScore * 0.4m + profitScore * 0.3m);

            return new ThresholdTestResult
            {
                ThresholdValue = imbalanceThreshold,
                SignalsGenerated = signals,
                WinRate = winRate,
                ProfitFactor = profitFactor,
                Score = combinedScore,
                IsOptimal = false
            };
        }

        private decimal CalculateRecentCVDTrend(List<MarketDataPoint> bars)
        {
            if (bars.Count < 5)
                return 0;

            var recentBars = bars.TakeLast(5).ToList();
            var firstCVD = recentBars.Take(2).Sum(x => x.Delta);
            var lastCVD = recentBars.TakeLast(2).Sum(x => x.Delta);

            return lastCVD - firstCVD;
        }

        private DeltaBias DetermineDeltaBias(decimal delta, decimal deltaImbalance)
        {
            if (deltaImbalance < 1.0m)
                return DeltaBias.Neutral;

            if (delta > 0)
            {
                return deltaImbalance > 3.0m ? DeltaBias.StrongBuy : DeltaBias.Buy;
            }
            else
            {
                return deltaImbalance > 3.0m ? DeltaBias.StrongSell : DeltaBias.Sell;
            }
        }

        private decimal CalculateDeltaConfidence(MarketDataPoint currentBar, List<MarketDataPoint> recentBars)
        {
            var averageVolume = recentBars.Average(x => x.Volume);
            var volumeRatio = averageVolume > 0 ? currentBar.Volume / averageVolume : 1;
            var deltaImbalance = currentBar.Volume > 0 ? Math.Abs(currentBar.Delta) / currentBar.Volume : 0;

            // Higher confidence for higher volume and delta imbalance
            var volumeConfidence = Math.Min(1.0m, volumeRatio / 2.0m);
            var deltaConfidence = Math.Min(1.0m, deltaImbalance * 2);

            return (volumeConfidence + deltaConfidence) / 2;
        }

        private string DetermineDeltaPattern(MarketDataPoint currentBar, List<MarketDataPoint> recentBars)
        {
            var deltaImbalance = currentBar.Volume > 0 ? Math.Abs(currentBar.Delta) / currentBar.Volume : 0;
            var averageVolume = recentBars.Average(x => x.Volume);
            var volumeRatio = averageVolume > 0 ? currentBar.Volume / averageVolume : 1;

            if (deltaImbalance > 0.8m && volumeRatio > 3.0m)
                return "Institutional Block";
            
            if (deltaImbalance > 0.6m && volumeRatio < 1.5m)
                return "Stealth Activity";
            
            if (deltaImbalance > 0.5m)
                return currentBar.Delta > 0 ? "Accumulation" : "Distribution";
            
            if (deltaImbalance > 0.3m)
                return "Moderate Pressure";

            return "Normal Flow";
        }

        private DeltaFlowRegime DetermineCurrentDeltaRegime(decimal deltaImbalance, decimal buyPressure, List<MarketDataPoint> recentData)
        {
            if (Math.Abs(buyPressure) < 0.1m)
                return DeltaFlowRegime.Balanced;

            // Calculate volatility of delta imbalances
            var deltaImbalances = recentData.Select(x => x.Volume > 0 ? Math.Abs(x.Delta) / x.Volume : 0).ToList();
            var avgImbalance = deltaImbalances.Average();
            var variance = deltaImbalances.Sum(d => (d - avgImbalance) * (d - avgImbalance)) / deltaImbalances.Count;
            var stdDev = (decimal)Math.Sqrt((double)variance);

            if (stdDev > avgImbalance * 2)
                return DeltaFlowRegime.Erratic;

            if (buyPressure > 0.5m)
                return DeltaFlowRegime.StrongBuyPressure;

            if (buyPressure > 0.2m)
                return DeltaFlowRegime.BuyPressure;

            if (buyPressure < -0.5m)
                return DeltaFlowRegime.StrongSellPressure;

            if (buyPressure < -0.2m)
                return DeltaFlowRegime.SellPressure;

            return DeltaFlowRegime.Balanced;
        }

        private int CalculateCVDTrendDuration(List<MarketDataPoint> recentData)
        {
            if (recentData.Count < 3)
                return 0;

            var cvdValues = new List<decimal>();
            var cumulativeDelta = 0m;

            foreach (var bar in recentData)
            {
                cumulativeDelta += bar.Delta;
                cvdValues.Add(cumulativeDelta);
            }

            // Determine current trend direction
            var recentSlope = cvdValues.Count >= 3 ?
                (cvdValues.Last() - cvdValues[cvdValues.Count - 3]) / 2 : 0;

            var currentTrendDirection = Math.Sign(recentSlope);
            if (currentTrendDirection == 0)
                return 1;

            // Count how many bars the trend has been consistent
            int duration = 1;
            for (int i = cvdValues.Count - 2; i >= 2; i--)
            {
                var slope = (cvdValues[i] - cvdValues[i - 2]) / 2;
                var direction = Math.Sign(slope);

                if (direction == currentTrendDirection)
                    duration++;
                else
                    break;
            }

            return duration;
        }

        private bool DetectCVDPriceDivergence(List<MarketDataPoint> dataToUse, List<decimal> cvdValues)
        {
            if (dataToUse.Count < 5 || cvdValues.Count < 5)
                return false;

            // Compare recent price trend vs CVD trend
            var recentPrices = dataToUse.TakeLast(5).Select(x => x.Close).ToList();
            var recentCVD = cvdValues.TakeLast(5).ToList();

            var priceChange = recentPrices.Last() - recentPrices.First();
            var cvdChange = recentCVD.Last() - recentCVD.First();

            // Divergence if price and CVD move in opposite directions significantly
            var priceDirection = Math.Sign(priceChange);
            var cvdDirection = Math.Sign(cvdChange);

            return priceDirection != cvdDirection &&
                   Math.Abs(priceChange) > 0.001m &&
                   Math.Abs(cvdChange) > 10; // Arbitrary CVD threshold
        }

        #endregion
    }
}
