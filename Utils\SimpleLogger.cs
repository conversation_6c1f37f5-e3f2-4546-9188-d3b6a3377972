using System;

namespace SmartVolumeStrategy.Utils
{
    /// <summary>
    /// Simple logger for strategy debugging
    /// </summary>
    public class SimpleLogger
    {
        private readonly string _name;

        public SimpleLogger(string name)
        {
            _name = name;
        }

        public void Info(string message)
        {
            Console.WriteLine($"[INFO] {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} [{_name}] {message}");
        }

        public void Warning(string message)
        {
            Console.WriteLine($"[WARN] {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} [{_name}] {message}");
        }

        public void Error(string message, Exception ex = null)
        {
            Console.WriteLine($"[ERROR] {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} [{_name}] {message}");
            if (ex != null)
            {
                Console.WriteLine($"[ERROR] Exception: {ex}");
            }
        }
    }
}
