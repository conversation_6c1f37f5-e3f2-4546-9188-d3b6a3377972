using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Strategy
{
    /// <summary>
    /// Simple, effective volume block detection using calibrated thresholds and intelligent impact calculation
    /// </summary>
    public class VolumeBlockDetector : IVolumeBlockDetector
    {
        private readonly Queue<MarketDataPoint> _historicalData;
        private readonly Queue<decimal> _volumeHistory;
        private readonly Queue<VolumeImpact> _impactHistory;
        
        private decimal _averageVolume;
        private decimal _volumeStandardDeviation;
        private decimal _currentThreshold;
        private int _lookbackPeriod;
        private readonly object _lockObject = new object();

        public VolumeBlockDetector()
        {
            _historicalData = new Queue<MarketDataPoint>();
            _volumeHistory = new Queue<decimal>();
            _impactHistory = new Queue<VolumeImpact>();
            _lookbackPeriod = 20; // Default lookback period
        }

        /// <summary>
        /// Detect volume blocks in real-time using calibrated settings
        /// </summary>
        public VolumeBlockResult DetectVolumeBlock(
            MarketDataPoint currentBar,
            List<MarketDataPoint> historicalData,
            OptimalSettings settings)
        {
            if (currentBar == null)
                throw new ArgumentNullException(nameof(currentBar));
            
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));

            lock (_lockObject)
            {
                // Update internal state with new bar
                UpdateVolumeStatistics(currentBar, settings.LookbackPeriod);
                
                var result = new VolumeBlockResult
                {
                    IsVolumeBlock = false,
                    VolumeRatio = 0,
                    Impact = 0,
                    CumulativeImpact = 0,
                    Type = VolumeBlockType.None,
                    Confidence = 0,
                    Description = "Normal Volume"
                };

                // Check if we have enough data
                if (_averageVolume <= 0 || _volumeHistory.Count < settings.LookbackPeriod / 2)
                {
                    result.Description = "Insufficient Data";
                    return result;
                }

                // Calculate volume ratio using calibrated threshold
                result.VolumeRatio = currentBar.Volume / _averageVolume;

                // CRITICAL FIX: Dramatically lower the volume threshold for crypto scalping conditions
                // ETHUSDT shows extremely low volume (0-14 range), need ultra-low threshold
                var adjustedVolumeThreshold = Math.Min(settings.VolumeThreshold, 0.01m); // Cap at 0.01x (1%) average for ultra-low volume instruments like ETHUSDT

                // Check if this qualifies as a volume block
                if (result.VolumeRatio >= adjustedVolumeThreshold)
                {
                    result.IsVolumeBlock = true;
                    
                    // Calculate impact using calibrated parameters
                    result.Impact = CalculateVolumeImpact(currentBar, settings);
                    
                    // Calculate cumulative impact with decay
                    result.CumulativeImpact = CalculateCumulativeImpact(result.Impact, settings);
                    
                    // Determine volume block type
                    result.Type = DetermineVolumeBlockType(currentBar, result.VolumeRatio);
                    
                    // Calculate confidence based on volume strength and market context
                    result.Confidence = CalculateVolumeBlockConfidence(currentBar, result, settings);
                    
                    // Generate descriptive text
                    result.Description = GenerateVolumeBlockDescription(result);
                }

                return result;
            }
        }

        /// <summary>
        /// Calculate cumulative impact from volume blocks with intelligent decay
        /// </summary>
        public decimal CalculateCumulativeImpact(List<MarketDataPoint> recentBars, OptimalSettings settings)
        {
            if (recentBars == null || recentBars.Count == 0)
                return 0;

            decimal cumulativeImpact = 0;
            var decayFactor = settings.ImpactDecay;

            // Process bars from most recent to oldest
            for (int i = recentBars.Count - 1; i >= 0; i--)
            {
                var bar = recentBars[i];
                var barsAgo = recentBars.Count - 1 - i;
                
                // Calculate impact for this bar
                var impact = CalculateVolumeImpact(bar, settings);
                
                // Apply decay based on age
                var decayedImpact = impact * (decimal)Math.Pow((double)decayFactor, barsAgo);
                
                cumulativeImpact += decayedImpact;
            }

            return cumulativeImpact;
        }

        /// <summary>
        /// Update volume statistics for threshold calculations
        /// </summary>
        public void UpdateVolumeStatistics(MarketDataPoint newBar, int lookbackPeriod)
        {
            if (newBar == null)
                return;

            lock (_lockObject)
            {
                _lookbackPeriod = lookbackPeriod;
                
                // Add new volume to history
                _volumeHistory.Enqueue(newBar.Volume);
                _historicalData.Enqueue(newBar);

                // Maintain lookback period
                while (_volumeHistory.Count > lookbackPeriod)
                {
                    _volumeHistory.Dequeue();
                    _historicalData.Dequeue();
                }

                // Recalculate statistics
                if (_volumeHistory.Count > 0)
                {
                    _averageVolume = _volumeHistory.Average();
                    _volumeStandardDeviation = CalculateStandardDeviation(_volumeHistory.ToList());
                }
            }
        }

        /// <summary>
        /// Get current volume statistics
        /// </summary>
        public (decimal Average, decimal StdDev, decimal Threshold) GetVolumeStatistics()
        {
            lock (_lockObject)
            {
                return (_averageVolume, _volumeStandardDeviation, _currentThreshold);
            }
        }

        /// <summary>
        /// Reset detector state
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                _historicalData.Clear();
                _volumeHistory.Clear();
                _impactHistory.Clear();
                _averageVolume = 0;
                _volumeStandardDeviation = 0;
                _currentThreshold = 0;
            }
        }

        #region Private Methods

        private decimal CalculateVolumeImpact(MarketDataPoint bar, OptimalSettings settings)
        {
            if (bar.Open <= 0)
                return 0;

            // Calculate price change impact
            var priceChange = bar.Close - bar.Open;
            var priceChangePercent = Math.Abs(priceChange / bar.Open);
            
            // Calculate volume impact
            var volumeRatio = _averageVolume > 0 ? bar.Volume / _averageVolume : 1;
            
            // Combine price and volume impact using calibrated multiplier
            var impact = priceChangePercent * settings.PriceChangeMultiplier * volumeRatio;
            
            // Apply directional bias
            var directionalImpact = Math.Sign(priceChange) * impact;
            
            return settings.UseAbsolutePrice ? Math.Abs(directionalImpact) : directionalImpact;
        }

        private decimal CalculateCumulativeImpact(decimal currentImpact, OptimalSettings settings)
        {
            // Add current impact to history
            var impactData = new VolumeImpact
            {
                Impact = currentImpact,
                Timestamp = DateTime.UtcNow,
                BarIndex = _historicalData.Count
            };
            
            _impactHistory.Enqueue(impactData);
            
            // Maintain impact history (keep last 50 impacts)
            while (_impactHistory.Count > 50)
            {
                _impactHistory.Dequeue();
            }

            // Calculate cumulative impact with decay
            decimal cumulativeImpact = 0;
            var impacts = _impactHistory.ToArray();
            
            for (int i = 0; i < impacts.Length; i++)
            {
                var age = impacts.Length - 1 - i; // Age in bars
                var decayedImpact = impacts[i].Impact * (decimal)Math.Pow((double)settings.ImpactDecay, age);
                cumulativeImpact += decayedImpact;
            }

            return cumulativeImpact;
        }

        private VolumeBlockType DetermineVolumeBlockType(MarketDataPoint bar, decimal volumeRatio)
        {
            var priceChange = bar.Close - bar.Open;
            var priceChangePercent = bar.Open > 0 ? priceChange / bar.Open : 0;
            
            // Determine type based on price movement and volume characteristics
            if (Math.Abs(priceChangePercent) < 0.001m) // Very small price movement
            {
                return volumeRatio > 3.0m ? VolumeBlockType.AbsorptionBlock : VolumeBlockType.None;
            }
            
            if (priceChangePercent > 0.002m) // Significant upward movement
            {
                return volumeRatio > 4.0m ? VolumeBlockType.BuyingBlock : VolumeBlockType.BuyingBlock;
            }
            
            if (priceChangePercent < -0.002m) // Significant downward movement
            {
                return volumeRatio > 4.0m ? VolumeBlockType.SellingBlock : VolumeBlockType.SellingBlock;
            }

            // Default case
            return VolumeBlockType.None;
        }

        private decimal CalculateVolumeBlockConfidence(MarketDataPoint bar, VolumeBlockResult result, OptimalSettings settings)
        {
            var confidenceFactors = new List<decimal>();

            // Volume strength factor (higher volume ratio = higher confidence)
            var volumeStrengthFactor = Math.Min(1.0m, result.VolumeRatio / (settings.VolumeThreshold * 2));
            confidenceFactors.Add(volumeStrengthFactor);

            // Price movement factor (significant price movement = higher confidence)
            var priceChangePercent = bar.Open > 0 ? Math.Abs(bar.Close - bar.Open) / bar.Open : 0;
            var priceMovementFactor = Math.Min(1.0m, priceChangePercent * 200); // Scale to 0-1
            confidenceFactors.Add(priceMovementFactor);

            // Volume consistency factor (how consistent recent volume has been)
            var volumeConsistencyFactor = CalculateVolumeConsistencyFactor();
            confidenceFactors.Add(volumeConsistencyFactor);

            // Data quality factor (more historical data = higher confidence)
            var dataQualityFactor = Math.Min(1.0m, (decimal)_volumeHistory.Count / settings.LookbackPeriod);
            confidenceFactors.Add(dataQualityFactor);

            // Calculate weighted average
            return confidenceFactors.Average();
        }

        private decimal CalculateVolumeConsistencyFactor()
        {
            if (_volumeHistory.Count < 5)
                return 0.5m; // Default moderate confidence

            var volumes = _volumeHistory.ToList();
            var coefficientOfVariation = _averageVolume > 0 ? _volumeStandardDeviation / _averageVolume : 0;
            
            // Lower CV = higher consistency = higher confidence
            return Math.Max(0.2m, Math.Min(1.0m, 1.0m - coefficientOfVariation));
        }

        private string GenerateVolumeBlockDescription(VolumeBlockResult result)
        {
            var intensity = result.VolumeRatio switch
            {
                >= 5.0m => "Massive",
                >= 3.0m => "Large",
                >= 2.0m => "Significant",
                _ => "Moderate"
            };

            var typeDescription = result.Type switch
            {
                VolumeBlockType.BuyingBlock => "Buying Pressure",
                VolumeBlockType.SellingBlock => "Selling Pressure", 
                VolumeBlockType.AbsorptionBlock => "Absorption",
                VolumeBlockType.DistributionBlock => "Distribution",
                _ => "Volume Block"
            };

            return $"{intensity} {typeDescription} ({result.VolumeRatio:F1}x avg volume)";
        }

        private decimal CalculateStandardDeviation(List<decimal> values)
        {
            if (values.Count == 0)
                return 0;

            var average = values.Average();
            var variance = values.Sum(v => (v - average) * (v - average)) / values.Count;
            return (decimal)Math.Sqrt((double)variance);
        }

        #endregion

        #region Helper Classes

        private class VolumeImpact
        {
            public decimal Impact { get; set; }
            public DateTime Timestamp { get; set; }
            public int BarIndex { get; set; }
        }

        #endregion
    }
}
