using System;
using System.Collections.Generic;
using SmartVolumeStrategy.Core.Analysis;
using SmartVolumeStrategy.Core.Models.Analysis;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Tests
{
    /// <summary>
    /// Phase 2.2 Test: Advanced Microstructure Filtering
    /// Tests the new weighted scoring system, component health monitoring, and adaptive thresholds
    /// </summary>
    public class Phase2_2_AdvancedMicrostructureFilterTest
    {
        private readonly List<string> _testLogs;
        private readonly AdaptiveMicrostructureFilter _microstructureFilter;
        private readonly MicrostructureHealthMonitor _healthMonitor;

        public Phase2_2_AdvancedMicrostructureFilterTest()
        {
            _testLogs = new List<string>();
            _microstructureFilter = new AdaptiveMicrostructureFilter(log => _testLogs.Add(log));
            _healthMonitor = new MicrostructureHealthMonitor(log => _testLogs.Add(log));
        }

        /// <summary>
        /// Run comprehensive Phase 2.2 tests
        /// </summary>
        public void RunAdvancedMicrostructureFilterTests()
        {
            Console.WriteLine("🧪 PHASE 2.2 TEST: Advanced Microstructure Filtering");
            Console.WriteLine(new string('=', 60));

            // Test 1: Weighted scoring system
            TestWeightedScoringSystem();

            // Test 2: Component health monitoring
            TestComponentHealthMonitoring();

            // Test 3: Adaptive threshold adjustment
            TestAdaptiveThresholdAdjustment();

            // Test 4: Health-based filter adjustments
            TestHealthBasedFilterAdjustments();

            // Test 5: Integration with Phase 2.1 Enhanced Signal Quality
            TestIntegrationWithEnhancedSignalQuality();

            // Test 6: Failure detection and recovery
            TestFailureDetectionAndRecovery();

            Console.WriteLine("\n✅ Phase 2.2 Advanced Microstructure Filter Tests Completed");
            Console.WriteLine($"📊 Total test logs generated: {_testLogs.Count}");
        }

        private void TestWeightedScoringSystem()
        {
            Console.WriteLine("\n🎯 Test 1: Weighted Scoring System");
            
            var signal = CreateTestSignal(SignalType.Long, 0.75m);
            var context = CreateOptimalMarketContext();
            var analysisState = CreateGoodAnalysisState();
            var config = CreateTestConfig();
            
            var filterResult = _microstructureFilter.ApplyFilter(signal, context, analysisState, config);
            
            Console.WriteLine($"  • Overall Filter Score: {filterResult.OverallScore:F3}");
            Console.WriteLine($"  • Filter Quality: {filterResult.FilterQuality}");
            Console.WriteLine($"  • Passes Filter: {filterResult.PassesFilter}");
            Console.WriteLine($"  • Component Scores:");
            Console.WriteLine($"    - Volume: {filterResult.ComponentScores.VolumeScore:F3}");
            Console.WriteLine($"    - Delta: {filterResult.ComponentScores.DeltaScore:F3}");
            Console.WriteLine($"    - Volatility: {filterResult.ComponentScores.VolatilityScore:F3}");
            Console.WriteLine($"    - CVD Alignment: {filterResult.ComponentScores.CVDAlignmentScore:F3}");
            Console.WriteLine($"    - Optimal Time: {filterResult.ComponentScores.OptimalTimeScore:F3}");
            
            // Verify weighted scoring works correctly
            if (filterResult.OverallScore > 0.6m && filterResult.PassesFilter)
            {
                Console.WriteLine("  ✅ Weighted scoring system working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Weighted scoring system not working as expected");
            }
        }

        private void TestComponentHealthMonitoring()
        {
            Console.WriteLine("\n🎯 Test 2: Component Health Monitoring");
            
            // Simulate multiple filter applications to build health history
            for (int i = 0; i < 25; i++)
            {
                var signal = CreateTestSignal(SignalType.Long, 0.6m + (i * 0.01m));
                var context = CreateOptimalMarketContext();
                var analysisState = CreateAnalysisStateWithVaryingQuality(i);
                var config = CreateTestConfig();
                
                var filterResult = _microstructureFilter.ApplyFilter(signal, context, analysisState, config);
                
                if (i == 24) // Last iteration
                {
                    var healthStatus = _microstructureFilter.GetHealthStatus();
                    
                    Console.WriteLine($"  • Overall Health: {healthStatus.OverallHealth}");
                    Console.WriteLine($"  • Volume Health: {healthStatus.VolumeHealth}");
                    Console.WriteLine($"  • Delta Health: {healthStatus.DeltaHealth}");
                    Console.WriteLine($"  • Volatility Health: {healthStatus.VolatilityHealth}");
                    Console.WriteLine($"  • CVD Alignment Health: {healthStatus.CVDAlignmentHealth}");
                    Console.WriteLine($"  • Optimal Time Health: {healthStatus.OptimalTimeHealth}");
                    
                    if (healthStatus.OverallHealth != ComponentHealth.Unknown)
                    {
                        Console.WriteLine("  ✅ Component health monitoring working correctly");
                    }
                    else
                    {
                        Console.WriteLine("  ❌ Component health monitoring not working as expected");
                    }
                }
            }
        }

        private void TestAdaptiveThresholdAdjustment()
        {
            Console.WriteLine("\n🎯 Test 3: Adaptive Threshold Adjustment");
            
            var initialStats = _microstructureFilter.GetStatistics();
            Console.WriteLine($"  • Initial Pass Rate: {initialStats.PassRate:P1}");
            
            // Generate many signals to trigger threshold adaptation
            for (int i = 0; i < 50; i++)
            {
                var signal = CreateTestSignal(SignalType.Long, 0.5m + (i * 0.008m));
                var context = CreateOptimalMarketContext();
                var analysisState = CreateGoodAnalysisState();
                var config = CreateTestConfig();
                
                _microstructureFilter.ApplyFilter(signal, context, analysisState, config);
            }
            
            var finalStats = _microstructureFilter.GetStatistics();
            Console.WriteLine($"  • Final Pass Rate: {finalStats.PassRate:P1}");
            Console.WriteLine($"  • Total Filters Applied: {finalStats.TotalFiltersApplied}");
            Console.WriteLine($"  • Average Filter Score: {finalStats.AverageFilterScore:F3}");
            
            if (finalStats.TotalFiltersApplied > initialStats.TotalFiltersApplied)
            {
                Console.WriteLine("  ✅ Adaptive threshold adjustment system working");
            }
            else
            {
                Console.WriteLine("  ❌ Adaptive threshold adjustment not working as expected");
            }
        }

        private void TestHealthBasedFilterAdjustments()
        {
            Console.WriteLine("\n🎯 Test 4: Health-Based Filter Adjustments");
            
            // Create a scenario with poor component health
            var signal = CreateTestSignal(SignalType.Long, 0.8m);
            var context = CreatePoorMarketContext();
            var analysisState = CreatePoorAnalysisState();
            var config = CreateTestConfig();
            
            var filterResult = _microstructureFilter.ApplyFilter(signal, context, analysisState, config);
            
            Console.WriteLine($"  • Filter Score: {filterResult.OverallScore:F3}");
            Console.WriteLine($"  • Has Health Warning: {filterResult.HasHealthWarning}");
            Console.WriteLine($"  • Filter Reason: {filterResult.FilterReason}");
            
            if (filterResult.HasHealthWarning || filterResult.OverallScore < 0.7m)
            {
                Console.WriteLine("  ✅ Health-based adjustments working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Health-based adjustments not working as expected");
            }
        }

        private void TestIntegrationWithEnhancedSignalQuality()
        {
            Console.WriteLine("\n🎯 Test 5: Integration with Enhanced Signal Quality");
            
            // This test verifies that the microstructure filter works well with Phase 2.1
            var signal = CreateTestSignal(SignalType.Long, 0.85m);
            var context = CreateOptimalMarketContext();
            var analysisState = CreateGoodAnalysisState();
            var config = CreateTestConfig();
            
            var filterResult = _microstructureFilter.ApplyFilter(signal, context, analysisState, config);
            
            Console.WriteLine($"  • High-quality signal filter result: {filterResult.PassesFilter}");
            Console.WriteLine($"  • Filter Score: {filterResult.OverallScore:F3}");
            Console.WriteLine($"  • Filter Quality: {filterResult.FilterQuality}");
            
            // High-quality signals should generally pass the filter
            if (filterResult.PassesFilter && filterResult.FilterQuality >= MicrostructureFilterQuality.Good)
            {
                Console.WriteLine("  ✅ Integration with Enhanced Signal Quality working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Integration issues detected");
            }
        }

        private void TestFailureDetectionAndRecovery()
        {
            Console.WriteLine("\n🎯 Test 6: Failure Detection and Recovery");
            
            // Simulate component failures
            for (int i = 0; i < 10; i++)
            {
                var signal = CreateTestSignal(SignalType.Long, 0.3m); // Low quality
                var context = CreatePoorMarketContext();
                var analysisState = CreateFailingAnalysisState();
                var config = CreateTestConfig();
                
                _microstructureFilter.ApplyFilter(signal, context, analysisState, config);
            }
            
            var healthStatus = _microstructureFilter.GetHealthStatus();
            Console.WriteLine($"  • Health after failures: {healthStatus.OverallHealth}");
            
            // Now simulate recovery
            for (int i = 0; i < 15; i++)
            {
                var signal = CreateTestSignal(SignalType.Long, 0.8m); // High quality
                var context = CreateOptimalMarketContext();
                var analysisState = CreateGoodAnalysisState();
                var config = CreateTestConfig();
                
                _microstructureFilter.ApplyFilter(signal, context, analysisState, config);
            }
            
            var recoveredHealthStatus = _microstructureFilter.GetHealthStatus();
            Console.WriteLine($"  • Health after recovery: {recoveredHealthStatus.OverallHealth}");
            
            if (recoveredHealthStatus.OverallHealth > healthStatus.OverallHealth)
            {
                Console.WriteLine("  ✅ Failure detection and recovery working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Recovery mechanism not working as expected");
            }
        }

        // Helper methods for creating test data
        private SynthesizedSignal CreateTestSignal(SignalType type, decimal confidence)
        {
            return new SynthesizedSignal
            {
                Timestamp = DateTime.UtcNow,
                Type = type,
                Confidence = confidence,
                Strength = 0.7m,
                Quality = confidence >= 0.8m ? SignalQuality.Excellent : 
                         confidence >= 0.6m ? SignalQuality.Good : SignalQuality.Fair
            };
        }

        private MarketContext CreateOptimalMarketContext()
        {
            return new MarketContext
            {
                VolatilityRegime = VolatilityRegime.Normal,
                CVDTrend = 25m,
                IsOptimalTradingTime = true,
                CurrentSession = TradingSession.Overlap_EuropeanUS,
                DeltaImbalance = 0.4m
            };
        }

        private MarketContext CreatePoorMarketContext()
        {
            return new MarketContext
            {
                VolatilityRegime = VolatilityRegime.VeryHigh,
                CVDTrend = -15m,
                IsOptimalTradingTime = false,
                CurrentSession = TradingSession.Asian,
                DeltaImbalance = 0.1m
            };
        }

        private AnalysisState CreateGoodAnalysisState()
        {
            return new AnalysisState
            {
                Volume = new VolumeAnalysisState { VolumeConfidence = 0.8m },
                DeltaFlow = new DeltaAnalysisState { DeltaConfidence = 0.75m },
                Volatility = new VolatilityAnalysisState { VolatilityConfidence = 0.7m }
            };
        }

        private AnalysisState CreatePoorAnalysisState()
        {
            return new AnalysisState
            {
                Volume = new VolumeAnalysisState { VolumeConfidence = 0.3m },
                DeltaFlow = new DeltaAnalysisState { DeltaConfidence = 0.25m },
                Volatility = new VolatilityAnalysisState { VolatilityConfidence = 0.2m }
            };
        }

        private AnalysisState CreateFailingAnalysisState()
        {
            return new AnalysisState
            {
                Volume = new VolumeAnalysisState { VolumeConfidence = 0.1m },
                DeltaFlow = new DeltaAnalysisState { DeltaConfidence = 0.15m },
                Volatility = new VolatilityAnalysisState { VolatilityConfidence = 0.1m }
            };
        }

        private AnalysisState CreateAnalysisStateWithVaryingQuality(int iteration)
        {
            var baseQuality = 0.4m + (iteration * 0.02m);
            return new AnalysisState
            {
                Volume = new VolumeAnalysisState { VolumeConfidence = Math.Min(1.0m, baseQuality + 0.1m) },
                DeltaFlow = new DeltaAnalysisState { DeltaConfidence = Math.Min(1.0m, baseQuality) },
                Volatility = new VolatilityAnalysisState { VolatilityConfidence = Math.Min(1.0m, baseQuality + 0.05m) }
            };
        }

        private SignalSynthesisConfig CreateTestConfig()
        {
            return new SignalSynthesisConfig
            {
                EnableMicrostructureFilter = true,
                MinVolumeConfidence = 0.5m,
                MinDeltaConfidence = 0.5m,
                MinVolatilityConfidence = 0.4m
            };
        }
    }
}
