using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models.Analysis;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;
using SignalConsistency = SmartVolumeStrategy.Core.Models.Analysis.SignalConsistency;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Phase 3.1: Multi-Timeframe Correlation Analyzer
    /// Advanced correlation analysis across multiple timeframes with circuit breaker integration
    /// </summary>
    public class MultiTimeframeCorrelationAnalyzer
    {
        private readonly Action<string> _logAction;
        private readonly MultiTimeframeAnalysisConfig _config;
        private readonly Queue<MultiTimeframeCorrelationAnalysis> _correlationHistory;
        private readonly Dictionary<Timeframe, Queue<TradingSignal>> _timeframeSignalHistory;
        private readonly object _lockObject = new object();
        
        // Circuit breaker integration
        private CircuitBreakerManager _circuitBreakerManager;
        
        // Configuration
        private const int MAX_CORRELATION_HISTORY = 200;
        private const int SIGNAL_HISTORY_SIZE = 100;
        private const decimal SIGNIFICANT_DIVERGENCE_THRESHOLD = 0.3m;

        public MultiTimeframeCorrelationAnalyzer(MultiTimeframeAnalysisConfig config = null, Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _config = config ?? new MultiTimeframeAnalysisConfig();
            _correlationHistory = new Queue<MultiTimeframeCorrelationAnalysis>();
            _timeframeSignalHistory = new Dictionary<Timeframe, Queue<TradingSignal>>();
            
            InitializeTimeframeHistory();
        }

        /// <summary>
        /// Set circuit breaker manager for fault tolerance
        /// </summary>
        public void SetCircuitBreakerManager(CircuitBreakerManager circuitBreakerManager)
        {
            _circuitBreakerManager = circuitBreakerManager;
        }

        /// <summary>
        /// Analyze correlation across multiple timeframes
        /// </summary>
        public MultiTimeframeCorrelationAnalysis AnalyzeCorrelation(Dictionary<Timeframe, TradingSignal> timeframeSignals, MarketContext marketContext)
        {
            lock (_lockObject)
            {
                var startTime = DateTime.UtcNow;
                
                try
                {
                    // Phase 3.1: Check circuit breaker status
                    if (_circuitBreakerManager != null && !_circuitBreakerManager.IsOperationAllowed(StrategyComponent.MultiTimeframeAnalysis))
                    {
                        _logAction("🔴 Multi-timeframe correlation analysis blocked by circuit breaker");
                        return CreateFallbackCorrelationAnalysis();
                    }

                    // Check degradation level
                    if (_circuitBreakerManager != null)
                    {
                        var degradationLevel = _circuitBreakerManager.GetDegradationManager().GetCurrentDegradationLevel();
                        if (degradationLevel >= DegradationLevel.Minimal)
                        {
                            _logAction("⚠️ Multi-timeframe correlation analysis running in degraded mode");
                            return AnalyzeBasicCorrelation(timeframeSignals);
                        }
                    }

                    _logAction("🔍 Starting advanced multi-timeframe correlation analysis...");

                    // Update signal history
                    UpdateSignalHistory(timeframeSignals);

                    // Perform correlation analysis
                    var analysis = new MultiTimeframeCorrelationAnalysis
                    {
                        Timestamp = DateTime.UtcNow
                    };

                    // Calculate different types of correlation
                    analysis.CorrelationScores[CorrelationType.DirectionalCorrelation] = CalculateDirectionalCorrelation(timeframeSignals);
                    analysis.CorrelationScores[CorrelationType.StrengthCorrelation] = CalculateStrengthCorrelation(timeframeSignals);
                    analysis.CorrelationScores[CorrelationType.TemporalCorrelation] = CalculateTemporalCorrelation(timeframeSignals);
                    analysis.CorrelationScores[CorrelationType.PatternCorrelation] = CalculatePatternCorrelation(timeframeSignals);

                    // Calculate overall correlation
                    analysis.OverallCorrelation = CalculateOverallCorrelation(analysis.CorrelationScores);

                    // Identify aligned and divergent timeframes
                    IdentifyTimeframeAlignment(timeframeSignals, analysis);

                    // Calculate timeframe reliability
                    analysis.TimeframeReliability = CalculateTimeframeReliability(timeframeSignals, marketContext);

                    // Determine consistency level
                    analysis.ConsistencyLevel = DetermineConsistencyLevel(analysis.OverallCorrelation);

                    // Check for significant divergence
                    analysis.HasSignificantDivergence = analysis.DivergentTimeframes.Count > timeframeSignals.Count * SIGNIFICANT_DIVERGENCE_THRESHOLD;
                    analysis.DivergenceStrength = CalculateDivergenceStrength(timeframeSignals, analysis);

                    // Add correlation factors
                    AddCorrelationFactors(analysis, timeframeSignals, marketContext);

                    // Update history
                    UpdateCorrelationHistory(analysis);

                    // Record successful operation
                    if (_circuitBreakerManager != null)
                    {
                        var responseTime = DateTime.UtcNow - startTime;
                        _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, true, "Correlation analysis completed");
                        _circuitBreakerManager.GetComponentHealthTracker().UpdateResponseTime(StrategyComponent.MultiTimeframeAnalysis, responseTime);
                    }

                    _logAction($"✅ Multi-timeframe correlation analysis completed - Overall: {analysis.OverallCorrelation:P1}, Consistency: {analysis.ConsistencyLevel}");

                    return analysis;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Multi-timeframe correlation analysis error: {ex.Message}");
                    
                    // Record failure with circuit breaker
                    if (_circuitBreakerManager != null)
                    {
                        _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, false, ex.Message, FailureSeverity.Medium);
                    }
                    
                    return CreateFallbackCorrelationAnalysis();
                }
            }
        }

        /// <summary>
        /// Get correlation analysis statistics
        /// </summary>
        public MultiTimeframeAnalysisStatistics GetCorrelationStatistics()
        {
            lock (_lockObject)
            {
                var stats = new MultiTimeframeAnalysisStatistics
                {
                    TotalAnalyses = _correlationHistory.Count,
                    LastAnalysis = _correlationHistory.Count > 0 ? _correlationHistory.Last().Timestamp : DateTime.MinValue
                };

                if (_correlationHistory.Count > 0)
                {
                    stats.AverageCorrelationScore = _correlationHistory.Average(c => c.OverallCorrelation);
                    stats.SuccessfulCorrelations = _correlationHistory.Count(c => c.OverallCorrelation >= _config.MinCorrelationThreshold);
                }

                return stats;
            }
        }

        /// <summary>
        /// Initialize timeframe signal history
        /// </summary>
        private void InitializeTimeframeHistory()
        {
            foreach (var timeframe in _config.EnabledTimeframes)
            {
                _timeframeSignalHistory[timeframe] = new Queue<TradingSignal>();
            }
        }

        /// <summary>
        /// Update signal history for each timeframe
        /// </summary>
        private void UpdateSignalHistory(Dictionary<Timeframe, TradingSignal> timeframeSignals)
        {
            foreach (var kvp in timeframeSignals)
            {
                if (_timeframeSignalHistory.ContainsKey(kvp.Key))
                {
                    var history = _timeframeSignalHistory[kvp.Key];
                    history.Enqueue(kvp.Value);
                    
                    while (history.Count > SIGNAL_HISTORY_SIZE)
                    {
                        history.Dequeue();
                    }
                }
            }
        }

        /// <summary>
        /// Calculate directional correlation across timeframes
        /// </summary>
        private decimal CalculateDirectionalCorrelation(Dictionary<Timeframe, TradingSignal> timeframeSignals)
        {
            if (timeframeSignals.Count < 2) return 1.0m;

            var signalTypes = timeframeSignals.Values.Select(s => s.Type).ToList();
            var dominantType = signalTypes.GroupBy(t => t).OrderByDescending(g => g.Count()).First().Key;
            var agreementCount = signalTypes.Count(t => t == dominantType);
            
            return (decimal)agreementCount / timeframeSignals.Count;
        }

        /// <summary>
        /// Calculate strength correlation across timeframes
        /// </summary>
        private decimal CalculateStrengthCorrelation(Dictionary<Timeframe, TradingSignal> timeframeSignals)
        {
            if (timeframeSignals.Count < 2) return 1.0m;

            var confidences = timeframeSignals.Values.Select(s => s.Confidence).ToList();
            var meanConfidence = confidences.Average();
            var variance = confidences.Sum(c => (c - meanConfidence) * (c - meanConfidence)) / confidences.Count;
            var standardDeviation = (decimal)Math.Sqrt((double)variance);
            
            // Lower standard deviation = higher correlation
            return Math.Max(0m, 1m - (standardDeviation * 2m));
        }

        /// <summary>
        /// Calculate temporal correlation (timing alignment)
        /// </summary>
        private decimal CalculateTemporalCorrelation(Dictionary<Timeframe, TradingSignal> timeframeSignals)
        {
            if (timeframeSignals.Count < 2) return 1.0m;

            var timestamps = timeframeSignals.Values.Select(s => s.Timestamp).ToList();
            var timeSpread = timestamps.Max() - timestamps.Min();
            
            // Signals within 5 minutes are considered temporally correlated
            var maxAllowedSpread = TimeSpan.FromMinutes(5);
            
            if (timeSpread <= maxAllowedSpread)
            {
                return 1.0m - (decimal)(timeSpread.TotalMinutes / maxAllowedSpread.TotalMinutes) * 0.5m;
            }
            
            return 0.5m;
        }

        /// <summary>
        /// Calculate pattern correlation across timeframes
        /// </summary>
        private decimal CalculatePatternCorrelation(Dictionary<Timeframe, TradingSignal> timeframeSignals)
        {
            if (timeframeSignals.Count < 2) return 1.0m;

            // Analyze signal quality patterns
            var qualityLevels = timeframeSignals.Values.Select(s => s.Quality).ToList();
            var dominantQuality = qualityLevels.GroupBy(q => q).OrderByDescending(g => g.Count()).First().Key;
            var qualityAgreement = (decimal)qualityLevels.Count(q => q == dominantQuality) / timeframeSignals.Count;

            // Analyze confidence patterns
            var confidences = timeframeSignals.Values.Select(s => s.Confidence).ToList();
            var confidenceRange = confidences.Max() - confidences.Min();
            var confidenceCorrelation = Math.Max(0m, 1m - confidenceRange);

            return (qualityAgreement + confidenceCorrelation) / 2m;
        }

        /// <summary>
        /// Calculate overall correlation from individual correlation types
        /// </summary>
        private decimal CalculateOverallCorrelation(Dictionary<CorrelationType, decimal> correlationScores)
        {
            if (correlationScores.Count == 0) return 0.5m;

            // Weight different correlation types
            var weights = new Dictionary<CorrelationType, decimal>
            {
                [CorrelationType.DirectionalCorrelation] = 0.4m,
                [CorrelationType.StrengthCorrelation] = 0.3m,
                [CorrelationType.TemporalCorrelation] = 0.2m,
                [CorrelationType.PatternCorrelation] = 0.1m
            };

            decimal weightedSum = 0m;
            decimal totalWeight = 0m;

            foreach (var kvp in correlationScores)
            {
                if (weights.ContainsKey(kvp.Key))
                {
                    var weight = weights[kvp.Key];
                    weightedSum += kvp.Value * weight;
                    totalWeight += weight;
                }
            }

            return totalWeight > 0 ? weightedSum / totalWeight : 0.5m;
        }

        /// <summary>
        /// Determine consistency level based on overall correlation
        /// </summary>
        private SignalConsistency DetermineConsistencyLevel(decimal overallCorrelation)
        {
            return overallCorrelation switch
            {
                >= 0.9m => SignalConsistency.VeryHigh,
                >= 0.8m => SignalConsistency.High,
                >= 0.6m => SignalConsistency.Medium,
                >= 0.4m => SignalConsistency.Low,
                _ => SignalConsistency.VeryLow
            };
        }

        /// <summary>
        /// Calculate divergence strength
        /// </summary>
        private decimal CalculateDivergenceStrength(Dictionary<Timeframe, TradingSignal> timeframeSignals, MultiTimeframeCorrelationAnalysis analysis)
        {
            if (analysis.DivergentTimeframes.Count == 0) return 0m;

            var divergentSignals = timeframeSignals
                .Where(kvp => analysis.DivergentTimeframes.Contains(kvp.Key))
                .Select(kvp => kvp.Value)
                .ToList();

            if (divergentSignals.Count == 0) return 0m;

            // Calculate average confidence of divergent signals
            var avgDivergentConfidence = divergentSignals.Average(s => s.Confidence);

            // Calculate proportion of divergent timeframes
            var divergentProportion = (decimal)analysis.DivergentTimeframes.Count / timeframeSignals.Count;

            return (avgDivergentConfidence + divergentProportion) / 2m;
        }

        /// <summary>
        /// Add correlation factors to analysis
        /// </summary>
        private void AddCorrelationFactors(MultiTimeframeCorrelationAnalysis analysis, Dictionary<Timeframe, TradingSignal> timeframeSignals, MarketContext marketContext)
        {
            // Market condition factors
            analysis.CorrelationFactors.Add($"Market Volatility: {marketContext.VolatilityRegime}");
            analysis.CorrelationFactors.Add($"Trading Session: {marketContext.CurrentSession}");

            // Signal factors
            var avgConfidence = timeframeSignals.Values.Average(s => s.Confidence);
            analysis.CorrelationFactors.Add($"Average Signal Confidence: {avgConfidence:P1}");

            var signalTypes = timeframeSignals.Values.Select(s => s.Type).Distinct().Count();
            analysis.CorrelationFactors.Add($"Signal Type Diversity: {signalTypes} types");

            // Timeframe factors
            analysis.CorrelationFactors.Add($"Active Timeframes: {timeframeSignals.Count}");
            analysis.CorrelationFactors.Add($"Aligned Timeframes: {analysis.AlignedTimeframes.Count}");
            analysis.CorrelationFactors.Add($"Divergent Timeframes: {analysis.DivergentTimeframes.Count}");
        }

        /// <summary>
        /// Update correlation history
        /// </summary>
        private void UpdateCorrelationHistory(MultiTimeframeCorrelationAnalysis analysis)
        {
            _correlationHistory.Enqueue(analysis);

            while (_correlationHistory.Count > MAX_CORRELATION_HISTORY)
            {
                _correlationHistory.Dequeue();
            }
        }

        /// <summary>
        /// Create fallback correlation analysis for error scenarios
        /// </summary>
        private MultiTimeframeCorrelationAnalysis CreateFallbackCorrelationAnalysis()
        {
            return new MultiTimeframeCorrelationAnalysis
            {
                Timestamp = DateTime.UtcNow,
                OverallCorrelation = 0.5m,
                ConsistencyLevel = SignalConsistency.Medium,
                HasSignificantDivergence = false,
                DivergenceStrength = 0m,
                CorrelationScores = new Dictionary<CorrelationType, decimal>
                {
                    [CorrelationType.DirectionalCorrelation] = 0.5m,
                    [CorrelationType.StrengthCorrelation] = 0.5m,
                    [CorrelationType.TemporalCorrelation] = 0.5m,
                    [CorrelationType.PatternCorrelation] = 0.5m
                },
                CorrelationFactors = new List<string> { "Fallback analysis due to error or circuit breaker activation" }
            };
        }

        /// <summary>
        /// Analyze basic correlation for degraded mode
        /// </summary>
        private MultiTimeframeCorrelationAnalysis AnalyzeBasicCorrelation(Dictionary<Timeframe, TradingSignal> timeframeSignals)
        {
            var analysis = new MultiTimeframeCorrelationAnalysis
            {
                Timestamp = DateTime.UtcNow
            };

            // Simple directional correlation only
            analysis.CorrelationScores[CorrelationType.DirectionalCorrelation] = CalculateDirectionalCorrelation(timeframeSignals);
            analysis.OverallCorrelation = analysis.CorrelationScores[CorrelationType.DirectionalCorrelation];

            // Basic alignment identification
            IdentifyTimeframeAlignment(timeframeSignals, analysis);

            analysis.ConsistencyLevel = DetermineConsistencyLevel(analysis.OverallCorrelation);
            analysis.CorrelationFactors.Add("Basic correlation analysis (degraded mode)");

            return analysis;
        }

        private void IdentifyTimeframeAlignment(Dictionary<Timeframe, TradingSignal> timeframeSignals, MultiTimeframeCorrelationAnalysis analysis)
        {
            var dominantSignalType = timeframeSignals.Values
                .GroupBy(s => s.Type)
                .OrderByDescending(g => g.Count())
                .First().Key;

            foreach (var kvp in timeframeSignals)
            {
                if (kvp.Value.Type == dominantSignalType)
                {
                    analysis.AlignedTimeframes.Add(kvp.Key);
                }
                else
                {
                    analysis.DivergentTimeframes.Add(kvp.Key);
                }
            }
        }

        private Dictionary<Timeframe, decimal> CalculateTimeframeReliability(Dictionary<Timeframe, TradingSignal> timeframeSignals, MarketContext marketContext)
        {
            var reliability = new Dictionary<Timeframe, decimal>();

            foreach (var kvp in timeframeSignals)
            {
                var timeframe = kvp.Key;
                var signal = kvp.Value;

                // Base reliability on signal confidence and quality
                var baseReliability = (signal.Confidence + GetQualityScore(signal.Quality)) / 2m;

                // Adjust based on market conditions
                var marketAdjustment = GetMarketConditionAdjustment(timeframe, marketContext);

                reliability[timeframe] = Math.Max(0.1m, Math.Min(1.0m, baseReliability + marketAdjustment));
            }

            return reliability;
        }

        private decimal GetQualityScore(SignalQuality quality)
        {
            return quality switch
            {
                SignalQuality.Excellent => 1.0m,
                SignalQuality.Good => 0.8m,
                SignalQuality.Fair => 0.6m,
                SignalQuality.Poor => 0.4m,
                _ => 0.5m
            };
        }

        private decimal GetMarketConditionAdjustment(Timeframe timeframe, MarketContext marketContext)
        {
            // Shorter timeframes are more reliable in high volatility
            // Longer timeframes are more reliable in low volatility
            return timeframe switch
            {
                Timeframe.M1 => marketContext.VolatilityRegime >= VolatilityRegime.High ? 0.1m : -0.1m,
                Timeframe.M5 => marketContext.VolatilityRegime == VolatilityRegime.Normal ? 0.1m : 0m,
                Timeframe.M15 => marketContext.VolatilityRegime <= VolatilityRegime.Normal ? 0.1m : 0m,
                Timeframe.H1 => marketContext.VolatilityRegime <= VolatilityRegime.Low ? 0.1m : -0.05m,
                _ => 0m
            };
        }
    }
}
