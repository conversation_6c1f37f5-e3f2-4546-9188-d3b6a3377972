using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Core.Models.Resilience;
using SmartVolumeStrategy.Core.Analysis;
using ComponentHealth = SmartVolumeStrategy.Core.Models.Analysis.ComponentHealth;

namespace SmartVolumeStrategy.Core.Resilience
{
    /// <summary>
    /// Phase 2.3: Component Health Tracker - Expanded health monitoring for all strategy components
    /// Integrates with Phase 2.2 MicrostructureHealthMonitor and extends to all system components
    /// </summary>
    public class ComponentHealthTracker
    {
        private readonly Action<string> _logAction;
        private readonly Dictionary<StrategyComponent, ComponentHealthMetrics> _componentMetrics;
        private readonly Queue<ComponentHealthMetrics> _healthHistory;
        private readonly object _lockObject = new object();
        
        // Integration with Phase 2.2 Microstructure Health Monitor
        private MicrostructureHealthMonitor _microstructureMonitor;
        
        // Configuration
        private const int MAX_HEALTH_HISTORY = 1000;
        private const int HEALTH_EVALUATION_WINDOW = 50;
        private const decimal CRITICAL_HEALTH_THRESHOLD = 0.2m;
        private const decimal DEGRADED_HEALTH_THRESHOLD = 0.5m;
        private const decimal HEALTHY_THRESHOLD = 0.8m;

        public ComponentHealthTracker(Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _componentMetrics = new Dictionary<StrategyComponent, ComponentHealthMetrics>();
            _healthHistory = new Queue<ComponentHealthMetrics>();
            
            InitializeComponentMetrics();
        }

        /// <summary>
        /// Set the microstructure health monitor for integration
        /// </summary>
        public void SetMicrostructureMonitor(MicrostructureHealthMonitor microstructureMonitor)
        {
            _microstructureMonitor = microstructureMonitor;
        }

        /// <summary>
        /// Update component health based on operation result
        /// </summary>
        public void UpdateComponentHealth(StrategyComponent component, bool wasSuccessful, string details = null, FailureSeverity severity = FailureSeverity.Low)
        {
            lock (_lockObject)
            {
                try
                {
                    if (!_componentMetrics.ContainsKey(component))
                        InitializeComponentMetric(component);

                    var metrics = _componentMetrics[component];
                    var timestamp = DateTime.UtcNow;

                    // Update operation counts
                    metrics.TotalOperations++;
                    if (wasSuccessful)
                    {
                        metrics.SuccessfulOperations++;
                    }
                    else
                    {
                        metrics.FailedOperations++;
                        if (!string.IsNullOrEmpty(details))
                        {
                            metrics.ActiveIssues.Add($"{timestamp:HH:mm:ss}: {details}");
                            while (metrics.ActiveIssues.Count > 10)
                            {
                                metrics.ActiveIssues.RemoveAt(0);
                            }
                        }
                    }

                    // Calculate error rate
                    metrics.ErrorRate = metrics.TotalOperations > 0 ? 
                        (decimal)metrics.FailedOperations / metrics.TotalOperations : 0m;

                    // Update health score based on recent performance
                    metrics.HealthScore = CalculateHealthScore(component, metrics);
                    
                    // Update performance score
                    metrics.PerformanceScore = CalculatePerformanceScore(component, metrics);

                    // Update timestamp
                    metrics.Timestamp = timestamp;

                    // Add to history
                    _healthHistory.Enqueue(CloneMetrics(metrics));
                    while (_healthHistory.Count > MAX_HEALTH_HISTORY)
                    {
                        _healthHistory.Dequeue();
                    }

                    // Log significant health changes
                    LogHealthChanges(component, metrics, wasSuccessful, severity);
                }
                catch (Exception ex)
                {
                    _logAction($"❌ ERROR in ComponentHealthTracker: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Update component response time
        /// </summary>
        public void UpdateResponseTime(StrategyComponent component, TimeSpan responseTime)
        {
            lock (_lockObject)
            {
                if (_componentMetrics.ContainsKey(component))
                {
                    var metrics = _componentMetrics[component];
                    
                    // Calculate rolling average response time
                    var currentAvg = metrics.AverageResponseTime.TotalMilliseconds;
                    var newAvg = (currentAvg * 0.9) + (responseTime.TotalMilliseconds * 0.1);
                    metrics.AverageResponseTime = TimeSpan.FromMilliseconds(newAvg);
                    
                    // Update performance score based on response time
                    metrics.PerformanceScore = CalculatePerformanceScore(component, metrics);
                }
            }
        }

        /// <summary>
        /// Add custom metric for a component
        /// </summary>
        public void AddCustomMetric(StrategyComponent component, string metricName, decimal value)
        {
            lock (_lockObject)
            {
                if (_componentMetrics.ContainsKey(component))
                {
                    _componentMetrics[component].CustomMetrics[metricName] = value;
                }
            }
        }

        /// <summary>
        /// Get current health metrics for a component
        /// </summary>
        public ComponentHealthMetrics GetComponentHealth(StrategyComponent component)
        {
            lock (_lockObject)
            {
                if (_componentMetrics.ContainsKey(component))
                {
                    return CloneMetrics(_componentMetrics[component]);
                }
                
                return new ComponentHealthMetrics
                {
                    Component = component,
                    Timestamp = DateTime.UtcNow,
                    HealthScore = 0.5m,
                    PerformanceScore = 0.5m
                };
            }
        }

        /// <summary>
        /// Get health metrics for all components
        /// </summary>
        public Dictionary<StrategyComponent, ComponentHealthMetrics> GetAllComponentHealth()
        {
            lock (_lockObject)
            {
                var result = new Dictionary<StrategyComponent, ComponentHealthMetrics>();
                foreach (var kvp in _componentMetrics)
                {
                    result[kvp.Key] = CloneMetrics(kvp.Value);
                }
                return result;
            }
        }

        /// <summary>
        /// Get health trend for a component over specified window
        /// </summary>
        public HealthTrend GetHealthTrend(StrategyComponent component, int windowSize = 20)
        {
            lock (_lockObject)
            {
                var componentHistory = _healthHistory
                    .Where(h => h.Component == component)
                    .TakeLast(windowSize)
                    .ToList();

                if (componentHistory.Count < 5)
                {
                    return new HealthTrend
                    {
                        Component = component,
                        Direction = HealthTrendDirection.Insufficient_Data,
                        Strength = 0m,
                        WindowSize = componentHistory.Count
                    };
                }

                var healthScores = componentHistory.Select(h => h.HealthScore).ToList();
                var trend = CalculateLinearTrend(healthScores);
                var strength = CalculateTrendStrength(healthScores);

                return new HealthTrend
                {
                    Component = component,
                    Direction = trend > 0.05m ? HealthTrendDirection.Improving :
                               trend < -0.05m ? HealthTrendDirection.Declining :
                               HealthTrendDirection.Stable,
                    Strength = strength,
                    RecentAverage = healthScores.Average(),
                    WindowSize = componentHistory.Count,
                    TrendValue = trend
                };
            }
        }

        /// <summary>
        /// Reset health metrics for a component
        /// </summary>
        public void ResetComponentHealth(StrategyComponent component)
        {
            lock (_lockObject)
            {
                if (_componentMetrics.ContainsKey(component))
                {
                    InitializeComponentMetric(component);
                    _logAction($"🔄 Component health reset: {component}");
                }
            }
        }

        /// <summary>
        /// Get overall system health score
        /// </summary>
        public decimal GetOverallSystemHealth()
        {
            lock (_lockObject)
            {
                if (_componentMetrics.Count == 0)
                    return 1.0m;

                // Weight critical components more heavily
                decimal weightedSum = 0m;
                decimal totalWeight = 0m;

                foreach (var kvp in _componentMetrics)
                {
                    var component = kvp.Key;
                    var metrics = kvp.Value;
                    var weight = GetComponentWeight(component);
                    
                    weightedSum += metrics.HealthScore * weight;
                    totalWeight += weight;
                }

                return totalWeight > 0 ? weightedSum / totalWeight : 0.5m;
            }
        }

        /// <summary>
        /// Initialize component metrics
        /// </summary>
        private void InitializeComponentMetrics()
        {
            foreach (StrategyComponent component in Enum.GetValues<StrategyComponent>())
            {
                InitializeComponentMetric(component);
            }
        }

        /// <summary>
        /// Initialize metrics for a specific component
        /// </summary>
        private void InitializeComponentMetric(StrategyComponent component)
        {
            _componentMetrics[component] = new ComponentHealthMetrics
            {
                Component = component,
                Timestamp = DateTime.UtcNow,
                HealthScore = 1.0m,
                PerformanceScore = 1.0m,
                ErrorRate = 0m,
                AverageResponseTime = TimeSpan.Zero,
                TotalOperations = 0,
                FailedOperations = 0,
                SuccessfulOperations = 0,
                ActiveIssues = new List<string>(),
                CustomMetrics = new Dictionary<string, decimal>()
            };
        }

        /// <summary>
        /// Calculate health score for a component
        /// </summary>
        private decimal CalculateHealthScore(StrategyComponent component, ComponentHealthMetrics metrics)
        {
            if (metrics.TotalOperations == 0)
                return 1.0m;

            // Base score from error rate
            var baseScore = 1.0m - metrics.ErrorRate;

            // Adjust based on component-specific factors
            var adjustedScore = component switch
            {
                StrategyComponent.MicrostructureFiltering => AdjustForMicrostructureHealth(baseScore),
                StrategyComponent.DataFeeds => AdjustForDataFeedHealth(baseScore, metrics),
                StrategyComponent.SignalGeneration => AdjustForSignalGenerationHealth(baseScore, metrics),
                _ => baseScore
            };

            // Apply response time penalty if applicable
            if (metrics.AverageResponseTime.TotalMilliseconds > GetExpectedResponseTime(component))
            {
                var penalty = Math.Min(0.3m, (decimal)(metrics.AverageResponseTime.TotalMilliseconds / GetExpectedResponseTime(component) - 1.0) * 0.1m);
                adjustedScore -= penalty;
            }

            return Math.Max(0m, Math.Min(1m, adjustedScore));
        }

        /// <summary>
        /// Calculate performance score for a component
        /// </summary>
        private decimal CalculatePerformanceScore(StrategyComponent component, ComponentHealthMetrics metrics)
        {
            var healthScore = metrics.HealthScore;
            var responseTimeScore = CalculateResponseTimeScore(component, metrics.AverageResponseTime);
            
            // Combine health and response time scores
            return (healthScore * 0.7m) + (responseTimeScore * 0.3m);
        }

        /// <summary>
        /// Calculate response time score
        /// </summary>
        private decimal CalculateResponseTimeScore(StrategyComponent component, TimeSpan responseTime)
        {
            var expectedTime = GetExpectedResponseTime(component);
            var actualTime = responseTime.TotalMilliseconds;
            
            if (actualTime <= expectedTime)
                return 1.0m;
            
            // Exponential decay for slower response times
            var ratio = actualTime / expectedTime;
            return Math.Max(0.1m, (decimal)Math.Exp(-ratio + 1));
        }

        /// <summary>
        /// Adjust health score based on microstructure health (Phase 2.2 integration)
        /// </summary>
        private decimal AdjustForMicrostructureHealth(decimal baseScore)
        {
            if (_microstructureMonitor == null)
                return baseScore;

            try
            {
                var microHealthStatus = _microstructureMonitor.GetCurrentHealthStatus();
                var microHealthScore = CalculateMicrostructureHealthScore(microHealthStatus);

                // Blend microstructure health with base score
                return (baseScore * 0.6m) + (microHealthScore * 0.4m);
            }
            catch
            {
                return baseScore;
            }
        }

        /// <summary>
        /// Calculate microstructure health score from Phase 2.2 health status
        /// </summary>
        private decimal CalculateMicrostructureHealthScore(SmartVolumeStrategy.Core.Models.Analysis.MicrostructureHealthStatus healthStatus)
        {
            var componentHealthScores = new[]
            {
                ConvertHealthToScore(healthStatus.VolumeHealth),
                ConvertHealthToScore(healthStatus.DeltaHealth),
                ConvertHealthToScore(healthStatus.VolatilityHealth),
                ConvertHealthToScore(healthStatus.CVDAlignmentHealth),
                ConvertHealthToScore(healthStatus.OptimalTimeHealth)
            };

            return componentHealthScores.Average();
        }

        /// <summary>
        /// Convert component health enum to numeric score
        /// </summary>
        private decimal ConvertHealthToScore(SmartVolumeStrategy.Core.Models.Analysis.ComponentHealth health)
        {
            return health switch
            {
                SmartVolumeStrategy.Core.Models.Analysis.ComponentHealth.Healthy => 1.0m,
                SmartVolumeStrategy.Core.Models.Analysis.ComponentHealth.Recovering => 0.7m,
                SmartVolumeStrategy.Core.Models.Analysis.ComponentHealth.Degraded => 0.4m,
                SmartVolumeStrategy.Core.Models.Analysis.ComponentHealth.Critical => 0.1m,
                SmartVolumeStrategy.Core.Models.Analysis.ComponentHealth.Unknown => 0.5m,
                _ => 0.5m
            };
        }

        /// <summary>
        /// Adjust health score for data feed specific factors
        /// </summary>
        private decimal AdjustForDataFeedHealth(decimal baseScore, ComponentHealthMetrics metrics)
        {
            // Check for data feed specific issues
            var latencyPenalty = 0m;
            if (metrics.CustomMetrics.ContainsKey("DataLatency"))
            {
                var latency = metrics.CustomMetrics["DataLatency"];
                if (latency > 1000) // More than 1 second latency
                {
                    latencyPenalty = Math.Min(0.5m, latency / 5000m);
                }
            }

            var connectivityBonus = 0m;
            if (metrics.CustomMetrics.ContainsKey("ConnectionStability"))
            {
                connectivityBonus = metrics.CustomMetrics["ConnectionStability"] * 0.1m;
            }

            return Math.Max(0m, Math.Min(1m, baseScore - latencyPenalty + connectivityBonus));
        }

        /// <summary>
        /// Adjust health score for signal generation specific factors
        /// </summary>
        private decimal AdjustForSignalGenerationHealth(decimal baseScore, ComponentHealthMetrics metrics)
        {
            // Check signal quality metrics
            var qualityBonus = 0m;
            if (metrics.CustomMetrics.ContainsKey("SignalQuality"))
            {
                var quality = metrics.CustomMetrics["SignalQuality"];
                qualityBonus = (quality - 0.5m) * 0.2m; // Bonus/penalty based on quality above/below 50%
            }

            var consistencyPenalty = 0m;
            if (metrics.CustomMetrics.ContainsKey("SignalConsistency"))
            {
                var consistency = metrics.CustomMetrics["SignalConsistency"];
                if (consistency < 0.6m)
                {
                    consistencyPenalty = (0.6m - consistency) * 0.3m;
                }
            }

            return Math.Max(0m, Math.Min(1m, baseScore + qualityBonus - consistencyPenalty));
        }

        /// <summary>
        /// Calculate linear trend from a series of values
        /// </summary>
        private decimal CalculateLinearTrend(List<decimal> values)
        {
            if (values.Count < 2)
                return 0m;

            var n = values.Count;
            var sumX = n * (n - 1) / 2; // Sum of indices 0,1,2...n-1
            var sumY = values.Sum();
            var sumXY = values.Select((y, x) => x * (double)y).Sum();
            var sumX2 = Enumerable.Range(0, n).Sum(x => x * x);

            var slope = (n * sumXY - sumX * (double)sumY) / (n * sumX2 - sumX * sumX);
            return (decimal)slope;
        }

        /// <summary>
        /// Calculate trend strength (volatility)
        /// </summary>
        private decimal CalculateTrendStrength(List<decimal> values)
        {
            if (values.Count < 3)
                return 0m;

            var mean = values.Average();
            var variance = values.Sum(v => (v - mean) * (v - mean)) / values.Count;
            return (decimal)Math.Sqrt((double)variance);
        }

        /// <summary>
        /// Clone component health metrics
        /// </summary>
        private ComponentHealthMetrics CloneMetrics(ComponentHealthMetrics original)
        {
            return new ComponentHealthMetrics
            {
                Component = original.Component,
                Timestamp = original.Timestamp,
                HealthScore = original.HealthScore,
                PerformanceScore = original.PerformanceScore,
                ErrorRate = original.ErrorRate,
                AverageResponseTime = original.AverageResponseTime,
                TotalOperations = original.TotalOperations,
                FailedOperations = original.FailedOperations,
                SuccessfulOperations = original.SuccessfulOperations,
                ActiveIssues = new List<string>(original.ActiveIssues),
                CustomMetrics = new Dictionary<string, decimal>(original.CustomMetrics)
            };
        }

        /// <summary>
        /// Log significant health changes
        /// </summary>
        private void LogHealthChanges(StrategyComponent component, ComponentHealthMetrics metrics, bool wasSuccessful, FailureSeverity severity)
        {
            var healthLevel = metrics.HealthScore switch
            {
                >= HEALTHY_THRESHOLD => "Healthy",
                >= DEGRADED_HEALTH_THRESHOLD => "Degraded",
                >= CRITICAL_HEALTH_THRESHOLD => "Critical",
                _ => "Failed"
            };

            // Log significant health changes or critical issues
            if (metrics.HealthScore < CRITICAL_HEALTH_THRESHOLD)
            {
                _logAction($"🚨 CRITICAL: {component} health critically low ({metrics.HealthScore:P1}) - Error rate: {metrics.ErrorRate:P1}");
            }
            else if (metrics.HealthScore < DEGRADED_HEALTH_THRESHOLD && metrics.TotalOperations % 10 == 0)
            {
                _logAction($"⚠️ WARNING: {component} health degraded ({metrics.HealthScore:P1}) - Error rate: {metrics.ErrorRate:P1}");
            }
            else if (!wasSuccessful && severity >= FailureSeverity.High)
            {
                _logAction($"❌ {component} operation failed with {severity} severity - Health: {metrics.HealthScore:P1}");
            }
        }

        private decimal GetComponentWeight(StrategyComponent component)
        {
            return component switch
            {
                StrategyComponent.PositionManagement => 2.0m,
                StrategyComponent.RiskControls => 2.0m,
                StrategyComponent.DataFeeds => 1.5m,
                StrategyComponent.SignalGeneration => 1.5m,
                StrategyComponent.MicrostructureFiltering => 1.0m,
                StrategyComponent.AdaptiveCalibration => 1.0m,
                StrategyComponent.MultiTimeframeAnalysis => 0.8m,
                StrategyComponent.AnalysisEngines => 0.8m,
                _ => 1.0m
            };
        }

        private double GetExpectedResponseTime(StrategyComponent component)
        {
            return component switch
            {
                StrategyComponent.DataFeeds => 100.0, // 100ms
                StrategyComponent.SignalGeneration => 500.0, // 500ms
                StrategyComponent.MicrostructureFiltering => 200.0, // 200ms
                StrategyComponent.PositionManagement => 50.0, // 50ms
                StrategyComponent.RiskControls => 50.0, // 50ms
                _ => 1000.0 // 1 second default
            };
        }
    }

    /// <summary>
    /// Health trend information
    /// </summary>
    public class HealthTrend
    {
        public StrategyComponent Component { get; set; }
        public HealthTrendDirection Direction { get; set; }
        public decimal Strength { get; set; }
        public decimal RecentAverage { get; set; }
        public int WindowSize { get; set; }
        public decimal TrendValue { get; set; }
    }
}
