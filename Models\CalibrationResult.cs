using System;
using System.Collections.Generic;

namespace SmartVolumeStrategy.Models
{
    /// <summary>
    /// Result of intelligent settings calibration based on symbol analysis
    /// </summary>
    public class CalibrationResult
    {
        public string Symbol { get; set; } = "";
        public DateTime CalibrationTimestamp { get; set; } = DateTime.UtcNow;
        public string CorrelationId { get; set; } = "";
        public bool IsSuccessful { get; set; }
        public decimal OverallConfidence { get; set; } // 0-1 confidence in calibration
        public decimal CalibrationScore { get; set; } // Overall calibration quality score

        // Calibrated Settings
        public OptimalSettings Settings { get; set; } = new();
        
        // Calibration Details
        public CalibrationDetails Details { get; set; } = new();
        
        // Validation Results
        public BacktestValidation Validation { get; set; } = new();

        // Analysis Results (for adaptive calibrator integration)
        public VolumeAnalysisResult VolumeAnalysisResult { get; set; } = new();
        public DeltaFlowAnalysisResult DeltaAnalysisResult { get; set; } = new();
        public VolatilityAnalysisResult VolatilityAnalysisResult { get; set; } = new();
        public MarketProfileAnalysisResult MarketProfileResult { get; set; } = new();

        // Calibration Notes
        public List<string> CalibrationNotes { get; set; } = new();

        // Warnings and Recommendations
        public List<string> Warnings { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public List<CalibrationAdjustment> Adjustments { get; set; } = new();
    }

    /// <summary>
    /// Optimal settings calculated for the symbol
    /// </summary>
    public class OptimalSettings
    {
        // Core Volume Settings
        public decimal VolumeThreshold { get; set; } = 2.0m;
        public decimal SignalThreshold { get; set; } = 0.8m;
        public int LookbackPeriod { get; set; } = 20;
        
        // Delta Flow Settings - CRYPTO OPTIMIZED
        public decimal DeltaImbalanceThreshold { get; set; } = 0.15m; // 15% - realistic for crypto markets
        public decimal DeltaSignificanceThreshold { get; set; } = 1000m;
        public int CVDLookbackPeriod { get; set; } = 30;
        
        // Risk Management Settings
        public decimal TakeProfitPercent { get; set; } = 0.6m;
        public decimal StopLossPercent { get; set; } = 0.3m;
        public decimal PositionSizeUSDT { get; set; } = 1000m;
        public decimal RiskAdjustmentFactor { get; set; } = 1.0m;
        
        // Advanced Settings
        public decimal ImpactDecay { get; set; } = 0.95m;
        public decimal PriceChangeMultiplier { get; set; } = 10.0m;
        public bool UseAbsolutePrice { get; set; } = false;
        
        // Session Adjustments
        public bool EnableSessionAdjustments { get; set; } = false;
        public decimal AsianSessionMultiplier { get; set; } = 1.0m;
        public decimal EuropeanSessionMultiplier { get; set; } = 1.0m;
        public decimal USSessionMultiplier { get; set; } = 1.0m;
    }

    /// <summary>
    /// Detailed information about the calibration process
    /// </summary>
    public class CalibrationDetails
    {
        public int BarsAnalyzed { get; set; }
        public TimeSpan AnalysisDuration { get; set; }
        public DateTime DataStartTime { get; set; }
        public DateTime DataEndTime { get; set; }
        
        // Analysis Scores
        public decimal VolumeAnalysisConfidence { get; set; }
        public decimal DeltaAnalysisConfidence { get; set; }
        public decimal VolatilityAnalysisConfidence { get; set; }
        public decimal MarketProfileConfidence { get; set; }
        
        // Optimization Results
        public List<ThresholdTestResult> VolumeThresholdTests { get; set; } = new();
        public List<ThresholdTestResult> SignalThresholdTests { get; set; } = new();
        public List<RiskTestResult> RiskParameterTests { get; set; } = new();
        
        // Symbol Classification
        public string SymbolCategory { get; set; } = ""; // "HighVolume", "Volatile", "Stable", etc.
        public List<string> SymbolTags { get; set; } = new(); // "Institutional", "Retail", "Trending", etc.
    }

    /// <summary>
    /// Backtest validation of calibrated settings
    /// </summary>
    public class BacktestValidation
    {
        public DateTime ValidationTimestamp { get; set; } = DateTime.UtcNow;
        public OptimalSettings SettingsValidated { get; set; } = new();
        public int BarsAnalyzed { get; set; }
        public bool ValidationPerformed { get; set; }
        public int TradesGenerated { get; set; }
        public int TotalTrades { get; set; }
        public decimal WinRate { get; set; }
        public decimal AverageWin { get; set; }
        public decimal AverageLoss { get; set; }
        public decimal ProfitFactor { get; set; }
        public decimal MaxDrawdown { get; set; }
        public decimal SharpeRatio { get; set; }
        public decimal ValidationScore { get; set; }
        public bool IsValid { get; set; }
        public List<string> ValidationErrors { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public decimal MaxConsecutiveLosses { get; set; }
        public decimal TotalPnL { get; set; }
        public decimal AverageTradeReturn { get; set; }

        // Signal Quality Metrics
        public decimal SignalFrequency { get; set; } // Signals per day
        public decimal SignalQualityScore { get; set; } // 0-1 overall quality
        public List<string> ValidationWarnings { get; set; } = new();

        // Performance by Time Period
        public Dictionary<string, decimal> PerformanceBySession { get; set; } = new();
        public Dictionary<string, decimal> PerformanceByHour { get; set; } = new();
    }

    /// <summary>
    /// Individual threshold test result
    /// </summary>
    public class ThresholdTestResult
    {
        public decimal Threshold { get; set; }
        public decimal ThresholdValue { get; set; }
        public int SignalsGenerated { get; set; }
        public decimal WinRate { get; set; }
        public decimal ProfitFactor { get; set; }
        public decimal SignalFrequency { get; set; }
        public decimal Score { get; set; } // Combined score for ranking
        public bool IsOptimal { get; set; }
    }

    /// <summary>
    /// Risk parameter test result
    /// </summary>
    public class RiskTestResult
    {
        public decimal TakeProfitPercent { get; set; }
        public decimal StopLossPercent { get; set; }
        public decimal WinRate { get; set; }
        public decimal AverageRR { get; set; } // Risk/Reward ratio
        public decimal ProfitFactor { get; set; }
        public decimal MaxDrawdown { get; set; }
        public decimal Score { get; set; }
        public bool IsOptimal { get; set; }
    }

    /// <summary>
    /// Calibration adjustment made during optimization
    /// </summary>
    public class CalibrationAdjustment
    {
        public string Parameter { get; set; } = "";
        public string OldValue { get; set; } = "";
        public string OriginalValue { get; set; } = "";
        public string AdjustedValue { get; set; } = "";
        public string Reason { get; set; } = "";
        public decimal ConfidenceInAdjustment { get; set; }
        public AdjustmentType Type { get; set; } = AdjustmentType.Optimization;
    }

    /// <summary>
    /// Type of calibration adjustment
    /// </summary>
    public enum AdjustmentType
    {
        Performance,       // Performance-based adjustment
        Optimization,      // Based on backtest results
        RiskManagement,    // Safety-based adjustment
        MarketCondition,   // Based on current market regime
        DataQuality,       // Based on data quality issues
        UserPreference,    // Based on user constraints
        Emergency          // CRITICAL FIX: Emergency adjustment when no signals are generated
    }

    /// <summary>
    /// Calibration status and progress
    /// </summary>
    public class CalibrationStatus
    {
        public CalibrationPhase CurrentPhase { get; set; } = CalibrationPhase.NotStarted;
        public decimal ProgressPercent { get; set; }
        public string CurrentTask { get; set; } = "";
        public DateTime StartTime { get; set; }
        public TimeSpan EstimatedTimeRemaining { get; set; }
        public List<string> CompletedTasks { get; set; } = new();
        public List<string> PendingTasks { get; set; } = new();
        public bool HasErrors { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    /// <summary>
    /// Calibration phases
    /// </summary>
    public enum CalibrationPhase
    {
        NotStarted,
        DataCollection,
        VolumeAnalysis,
        DeltaAnalysis,
        VolatilityAnalysis,
        MarketProfileAnalysis,
        ThresholdOptimization,
        RiskCalibration,
        BacktestValidation,
        SettingsFinalization,
        Completed,
        Failed
    }



}
