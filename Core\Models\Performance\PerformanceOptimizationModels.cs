using System;
using System.Collections.Generic;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Models.Performance
{
    /// <summary>
    /// Phase 3.3: Analysis interval configuration
    /// </summary>
    public enum AnalysisInterval
    {
        OneSecond = 1,      // High-frequency analysis
        FiveSeconds = 5,    // Normal market conditions
        FifteenSeconds = 15 // Low volatility/resource conservation
    }

    /// <summary>
    /// Cache invalidation strategies
    /// </summary>
    public enum CacheInvalidationStrategy
    {
        TimeBasedTTL,           // Time-to-live based invalidation
        EventBased,             // Market event based invalidation
        PerformanceBased,       // Performance metrics based invalidation
        MemoryPressure,         // Memory usage based invalidation
        Hybrid                  // Combination of multiple strategies
    }

    /// <summary>
    /// Performance optimization component types
    /// </summary>
    public enum PerformanceComponent
    {
        AnalysisInterval,
        CachingSystem,
        MemoryManager,
        AdaptiveCalibrator,
        PerformanceMonitor,
        MultiTimeframeAnalysis,
        InstitutionalAnalysis,
        SignalSynthesis
    }

    /// <summary>
    /// Adaptive calibrator interface for dynamic parameter adjustment
    /// </summary>
    public interface IAdaptiveCalibrator
    {
        /// <summary>
        /// Calibrate parameters based on performance metrics
        /// </summary>
        void CalibrateParameters(PerformanceMetrics metrics, MarketContext marketContext);

        /// <summary>
        /// Get current calibration state
        /// </summary>
        CalibrationState GetCalibrationState();

        /// <summary>
        /// Reset calibration to default values
        /// </summary>
        void ResetCalibration();

        /// <summary>
        /// Apply calibration adjustments
        /// </summary>
        void ApplyCalibration(Dictionary<string, decimal> adjustments);

        /// <summary>
        /// Get optimization recommendations
        /// </summary>
        List<OptimizationRecommendation> GetOptimizationRecommendations();
    }

    /// <summary>
    /// Analysis interval configuration
    /// </summary>
    public class AnalysisIntervalConfig
    {
        public AnalysisInterval DefaultInterval { get; set; } = AnalysisInterval.FiveSeconds;
        public bool EnableIntelligentSwitching { get; set; } = true;
        public bool EnableMarketConditionAdaptation { get; set; } = true;
        public bool EnablePerformanceBasedSwitching { get; set; } = true;
        
        // Switching thresholds
        public decimal HighVolatilityThreshold { get; set; } = 0.8m;
        public decimal LowVolatilityThreshold { get; set; } = 0.3m;
        public decimal HighVolumeThreshold { get; set; } = 1.5m;
        public decimal LowVolumeThreshold { get; set; } = 0.7m;
        public TimeSpan MaxResponseTimeThreshold { get; set; } = TimeSpan.FromMilliseconds(500);
        public TimeSpan MinResponseTimeThreshold { get; set; } = TimeSpan.FromMilliseconds(50);
        
        // Market condition factors
        public Dictionary<TradingSession, decimal> SessionMultipliers { get; set; } = new Dictionary<TradingSession, decimal>();
        public Dictionary<VolatilityRegime, AnalysisInterval> VolatilityIntervals { get; set; } = new Dictionary<VolatilityRegime, AnalysisInterval>();
        
        public AnalysisIntervalConfig()
        {
            InitializeDefaults();
        }
        
        private void InitializeDefaults()
        {
            // Session multipliers for interval adjustment
            SessionMultipliers[TradingSession.Asian] = 1.2m;
            SessionMultipliers[TradingSession.European] = 1.0m;
            SessionMultipliers[TradingSession.US] = 0.9m;
            SessionMultipliers[TradingSession.Overlap_EuropeanUS] = 0.8m;
            SessionMultipliers[TradingSession.Overlap_AsianEuropean] = 1.1m;
            
            // Volatility-based interval mapping
            VolatilityIntervals[VolatilityRegime.VeryLow] = AnalysisInterval.FifteenSeconds;
            VolatilityIntervals[VolatilityRegime.Low] = AnalysisInterval.FifteenSeconds;
            VolatilityIntervals[VolatilityRegime.Normal] = AnalysisInterval.FiveSeconds;
            VolatilityIntervals[VolatilityRegime.High] = AnalysisInterval.FiveSeconds;
            VolatilityIntervals[VolatilityRegime.VeryHigh] = AnalysisInterval.OneSecond;
        }
    }

    /// <summary>
    /// Intelligent caching configuration
    /// </summary>
    public class IntelligentCachingConfig
    {
        public bool EnableCaching { get; set; } = true;
        public bool EnableMultiLevelCaching { get; set; } = true;
        public bool EnableIntelligentInvalidation { get; set; } = true;
        public CacheInvalidationStrategy DefaultInvalidationStrategy { get; set; } = CacheInvalidationStrategy.Hybrid;
        
        // Cache levels and TTL
        public TimeSpan L1CacheTTL { get; set; } = TimeSpan.FromSeconds(30);  // Recent data
        public TimeSpan L2CacheTTL { get; set; } = TimeSpan.FromMinutes(5);   // Historical patterns
        public TimeSpan L3CacheTTL { get; set; } = TimeSpan.FromMinutes(15);  // Statistical data
        
        // Cache size limits
        public int MaxL1CacheSize { get; set; } = 1000;
        public int MaxL2CacheSize { get; set; } = 500;
        public int MaxL3CacheSize { get; set; } = 200;
        
        // Performance thresholds
        public decimal MinCacheHitRatio { get; set; } = 0.7m;
        public TimeSpan MaxCacheResponseTime { get; set; } = TimeSpan.FromMilliseconds(10);
        public long MaxMemoryUsageBytes { get; set; } = 100 * 1024 * 1024; // 100MB
        
        // Invalidation triggers
        public Dictionary<string, TimeSpan> DataTypeTTL { get; set; } = new Dictionary<string, TimeSpan>();
        public List<string> EventBasedInvalidationTriggers { get; set; } = new List<string>();
        
        public IntelligentCachingConfig()
        {
            InitializeDefaults();
        }
        
        private void InitializeDefaults()
        {
            // Data type specific TTL
            DataTypeTTL["TradingSignal"] = TimeSpan.FromSeconds(30);
            DataTypeTTL["InstitutionalFootprint"] = TimeSpan.FromMinutes(2);
            DataTypeTTL["AdvancedPattern"] = TimeSpan.FromMinutes(5);
            DataTypeTTL["CorrelationAnalysis"] = TimeSpan.FromMinutes(1);
            DataTypeTTL["PerformanceMetrics"] = TimeSpan.FromSeconds(15);
            
            // Event-based invalidation triggers
            EventBasedInvalidationTriggers.Add("MarketOpen");
            EventBasedInvalidationTriggers.Add("MarketClose");
            EventBasedInvalidationTriggers.Add("VolatilitySpike");
            EventBasedInvalidationTriggers.Add("VolumeSpike");
            EventBasedInvalidationTriggers.Add("CircuitBreakerTriggered");
        }
    }

    /// <summary>
    /// Sliding window memory management configuration
    /// </summary>
    public class SlidingWindowConfig
    {
        public bool EnableSlidingWindow { get; set; } = true;
        public bool EnableAutomaticCleanup { get; set; } = true;
        public bool EnableMemoryOptimization { get; set; } = true;
        
        // Window sizes for different data types
        public Dictionary<string, int> WindowSizes { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, TimeSpan> RetentionPeriods { get; set; } = new Dictionary<string, TimeSpan>();
        
        // Memory management
        public long MaxMemoryUsageBytes { get; set; } = 200 * 1024 * 1024; // 200MB
        public decimal MemoryPressureThreshold { get; set; } = 0.8m;
        public TimeSpan CleanupInterval { get; set; } = TimeSpan.FromMinutes(5);
        
        // Performance thresholds
        public TimeSpan MaxDataAccessTime { get; set; } = TimeSpan.FromMilliseconds(50);
        public int MaxConcurrentOperations { get; set; } = 10;
        
        public SlidingWindowConfig()
        {
            InitializeDefaults();
        }
        
        private void InitializeDefaults()
        {
            // Window sizes (number of items)
            WindowSizes["TradingSignal"] = 1000;
            WindowSizes["InstitutionalFootprint"] = 500;
            WindowSizes["AdvancedPattern"] = 300;
            WindowSizes["CorrelationAnalysis"] = 200;
            WindowSizes["PerformanceMetrics"] = 2000;
            WindowSizes["ConflictAnalysis"] = 100;
            
            // Retention periods
            RetentionPeriods["TradingSignal"] = TimeSpan.FromHours(2);
            RetentionPeriods["InstitutionalFootprint"] = TimeSpan.FromHours(4);
            RetentionPeriods["AdvancedPattern"] = TimeSpan.FromHours(6);
            RetentionPeriods["CorrelationAnalysis"] = TimeSpan.FromHours(1);
            RetentionPeriods["PerformanceMetrics"] = TimeSpan.FromHours(8);
            RetentionPeriods["ConflictAnalysis"] = TimeSpan.FromHours(3);
        }
    }

    /// <summary>
    /// Performance metrics for monitoring and optimization
    /// </summary>
    public class PerformanceMetrics
    {
        public DateTime Timestamp { get; set; }
        public PerformanceComponent Component { get; set; }
        public string OperationName { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public bool Success { get; set; }
        public long MemoryUsage { get; set; }
        public int CacheHits { get; set; }
        public int CacheMisses { get; set; }
        public decimal CacheHitRatio => CacheHits + CacheMisses > 0 ? (decimal)CacheHits / (CacheHits + CacheMisses) : 0m;
        public Dictionary<string, object> AdditionalMetrics { get; set; } = new Dictionary<string, object>();
        public List<string> PerformanceFactors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Calibration state for adaptive parameter adjustment
    /// </summary>
    public class CalibrationState
    {
        public DateTime Timestamp { get; set; }
        public Dictionary<string, decimal> CurrentParameters { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, decimal> BaselineParameters { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, decimal> ParameterAdjustments { get; set; } = new Dictionary<string, decimal>();
        public List<string> CalibrationReasons { get; set; } = new List<string>();
        public decimal OverallCalibrationScore { get; set; }
        public bool IsCalibrated { get; set; }
        public TimeSpan CalibrationDuration { get; set; }
        public int CalibrationIterations { get; set; }
    }

    /// <summary>
    /// Optimization recommendation
    /// </summary>
    public class OptimizationRecommendation
    {
        public DateTime Timestamp { get; set; }
        public PerformanceComponent Component { get; set; }
        public string RecommendationType { get; set; }
        public string Description { get; set; }
        public decimal Priority { get; set; }
        public decimal ExpectedImprovement { get; set; }
        public Dictionary<string, object> RecommendationData { get; set; } = new Dictionary<string, object>();
        public List<string> ImplementationSteps { get; set; } = new List<string>();
        public bool IsImplemented { get; set; }
        public DateTime? ImplementationDate { get; set; }
    }

    /// <summary>
    /// Cache entry for intelligent caching system
    /// </summary>
    public class CacheEntry<T>
    {
        public string Key { get; set; }
        public T Value { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastAccessed { get; set; }
        public DateTime ExpiresAt { get; set; }
        public int AccessCount { get; set; }
        public long SizeBytes { get; set; }
        public CacheInvalidationStrategy InvalidationStrategy { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
        
        public bool IsExpired => DateTime.UtcNow > ExpiresAt;
        public TimeSpan Age => DateTime.UtcNow - CreatedAt;
        public TimeSpan TimeSinceLastAccess => DateTime.UtcNow - LastAccessed;
    }

    /// <summary>
    /// Sliding window data container
    /// </summary>
    public class SlidingWindowData<T>
    {
        public Queue<T> Data { get; set; } = new Queue<T>();
        public int MaxSize { get; set; }
        public TimeSpan RetentionPeriod { get; set; }
        public DateTime LastCleanup { get; set; }
        public long TotalMemoryUsage { get; set; }
        public int TotalOperations { get; set; }
        public TimeSpan AverageAccessTime { get; set; }
        
        public int Count => Data.Count;
        public bool IsFull => Data.Count >= MaxSize;
        public decimal MemoryUsageMB => TotalMemoryUsage / (1024m * 1024m);
    }

    /// <summary>
    /// Performance monitoring statistics
    /// </summary>
    public class PerformanceStatistics
    {
        public DateTime StartTime { get; set; }
        public DateTime LastUpdate { get; set; }
        public Dictionary<PerformanceComponent, ComponentPerformanceStats> ComponentStats { get; set; } = new Dictionary<PerformanceComponent, ComponentPerformanceStats>();
        public OverallPerformanceStats OverallStats { get; set; } = new OverallPerformanceStats();
        public List<PerformanceBottleneck> IdentifiedBottlenecks { get; set; } = new List<PerformanceBottleneck>();
        public List<OptimizationRecommendation> ActiveRecommendations { get; set; } = new List<OptimizationRecommendation>();
    }

    /// <summary>
    /// Component-specific performance statistics
    /// </summary>
    public class ComponentPerformanceStats
    {
        public PerformanceComponent Component { get; set; }
        public int TotalOperations { get; set; }
        public int SuccessfulOperations { get; set; }
        public int FailedOperations { get; set; }
        public TimeSpan TotalResponseTime { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public TimeSpan MinResponseTime { get; set; }
        public TimeSpan MaxResponseTime { get; set; }
        public long TotalMemoryUsage { get; set; }
        public long AverageMemoryUsage { get; set; }
        public decimal SuccessRate => TotalOperations > 0 ? (decimal)SuccessfulOperations / TotalOperations : 0m;
        public decimal OperationsPerSecond { get; set; }
    }

    /// <summary>
    /// Overall performance statistics
    /// </summary>
    public class OverallPerformanceStats
    {
        public TimeSpan TotalUptime { get; set; }
        public long TotalMemoryUsage { get; set; }
        public decimal AverageMemoryUsage { get; set; }
        public decimal PeakMemoryUsage { get; set; }
        public int TotalCacheHits { get; set; }
        public int TotalCacheMisses { get; set; }
        public decimal OverallCacheHitRatio => TotalCacheHits + TotalCacheMisses > 0 ? (decimal)TotalCacheHits / (TotalCacheHits + TotalCacheMisses) : 0m;
        public decimal SystemEfficiencyScore { get; set; }
        public List<string> PerformanceHighlights { get; set; } = new List<string>();
    }

    /// <summary>
    /// Performance bottleneck identification
    /// </summary>
    public class PerformanceBottleneck
    {
        public DateTime DetectedAt { get; set; }
        public PerformanceComponent Component { get; set; }
        public string BottleneckType { get; set; }
        public string Description { get; set; }
        public decimal Severity { get; set; }
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> BottleneckData { get; set; } = new Dictionary<string, object>();
        public List<OptimizationRecommendation> Recommendations { get; set; } = new List<OptimizationRecommendation>();
        public bool IsResolved { get; set; }
        public DateTime? ResolvedAt { get; set; }
    }
}
