using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models.Performance;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;

namespace SmartVolumeStrategy.Core.Performance
{
    /// <summary>
    /// Phase 3.3: Adaptive Calibrator
    /// Dynamic parameter adjustment and optimization based on performance metrics
    /// </summary>
    public class AdaptiveCalibrator : IAdaptiveCalibrator
    {
        private readonly Action<string> _logAction;
        private readonly Dictionary<string, decimal> _baselineParameters;
        private readonly Dictionary<string, decimal> _currentParameters;
        private readonly Dictionary<string, decimal> _parameterBounds;
        private readonly Queue<SmartVolumeStrategy.Core.Models.Performance.PerformanceMetrics> _performanceHistory;
        private readonly Queue<CalibrationState> _calibrationHistory;
        private readonly object _lockObject = new object();
        
        // Circuit breaker integration
        private CircuitBreakerManager _circuitBreakerManager;
        
        // Calibration state
        private DateTime _lastCalibration;
        private int _calibrationIterations;
        private bool _isCalibrated;
        private decimal _overallCalibrationScore;
        
        // Configuration
        private const int MAX_PERFORMANCE_HISTORY = 200;
        private const int MAX_CALIBRATION_HISTORY = 50;
        private static readonly TimeSpan MIN_CALIBRATION_INTERVAL = TimeSpan.FromMinutes(5);
        private const decimal MIN_PERFORMANCE_IMPROVEMENT = 0.05m; // 5% minimum improvement
        private const decimal MAX_PARAMETER_ADJUSTMENT = 0.2m; // 20% maximum adjustment per iteration

        public AdaptiveCalibrator(Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _baselineParameters = new Dictionary<string, decimal>();
            _currentParameters = new Dictionary<string, decimal>();
            _parameterBounds = new Dictionary<string, decimal>();
            _performanceHistory = new Queue<SmartVolumeStrategy.Core.Models.Performance.PerformanceMetrics>();
            _calibrationHistory = new Queue<CalibrationState>();
            
            _lastCalibration = DateTime.UtcNow;
            _isCalibrated = false;
            
            InitializeDefaultParameters();
        }

        /// <summary>
        /// Set circuit breaker manager for fault tolerance
        /// </summary>
        public void SetCircuitBreakerManager(CircuitBreakerManager circuitBreakerManager)
        {
            _circuitBreakerManager = circuitBreakerManager;
        }

        /// <summary>
        /// Calibrate parameters based on performance metrics
        /// </summary>
        public void CalibrateParameters(SmartVolumeStrategy.Core.Models.Performance.PerformanceMetrics metrics, MarketContext marketContext)
        {
            lock (_lockObject)
            {
                var startTime = DateTime.UtcNow;
                
                try
                {
                    // Phase 3.3: Check circuit breaker status
                    if (_circuitBreakerManager != null && !_circuitBreakerManager.IsOperationAllowed(StrategyComponent.AdaptiveCalibrator))
                    {
                        _logAction("🔴 Adaptive calibration blocked by circuit breaker");
                        return;
                    }

                    // Check degradation level
                    if (_circuitBreakerManager != null)
                    {
                        var degradationLevel = _circuitBreakerManager.GetDegradationManager().GetCurrentDegradationLevel();
                        if (degradationLevel >= DegradationLevel.Minimal)
                        {
                            _logAction("⚠️ Adaptive calibration running in degraded mode");
                            CalibrateParametersDegraded(metrics);
                            return;
                        }
                    }

                    // Add metrics to history
                    _performanceHistory.Enqueue(metrics);
                    while (_performanceHistory.Count > MAX_PERFORMANCE_HISTORY)
                    {
                        _performanceHistory.Dequeue();
                    }

                    // Check if calibration should be performed
                    if (!ShouldPerformCalibration())
                    {
                        return;
                    }

                    _logAction("🎯 Starting adaptive parameter calibration...");

                    // Analyze performance trends
                    var performanceAnalysis = AnalyzePerformanceTrends();
                    
                    // Determine parameter adjustments
                    var adjustments = DetermineParameterAdjustments(performanceAnalysis, marketContext);
                    
                    // Apply adjustments
                    ApplyCalibration(adjustments);
                    
                    // Update calibration state
                    UpdateCalibrationState(adjustments, performanceAnalysis);
                    
                    _lastCalibration = DateTime.UtcNow;
                    _calibrationIterations++;

                    // Record successful operation
                    if (_circuitBreakerManager != null)
                    {
                        var responseTime = DateTime.UtcNow - startTime;
                        _circuitBreakerManager.RecordOperation(StrategyComponent.AdaptiveCalibrator, true, $"Calibration completed with {adjustments.Count} adjustments");
                        _circuitBreakerManager.GetComponentHealthTracker().UpdateResponseTime(StrategyComponent.AdaptiveCalibrator, responseTime);
                    }

                    _logAction($"✅ Adaptive calibration completed - Applied {adjustments.Count} parameter adjustments, Score: {_overallCalibrationScore:P1}");
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Adaptive calibration error: {ex.Message}");
                    
                    // Record failure with circuit breaker
                    if (_circuitBreakerManager != null)
                    {
                        _circuitBreakerManager.RecordOperation(StrategyComponent.AdaptiveCalibrator, false, ex.Message, FailureSeverity.Medium);
                    }
                }
            }
        }

        /// <summary>
        /// Get current calibration state
        /// </summary>
        public CalibrationState GetCalibrationState()
        {
            lock (_lockObject)
            {
                return new CalibrationState
                {
                    Timestamp = DateTime.UtcNow,
                    CurrentParameters = new Dictionary<string, decimal>(_currentParameters),
                    BaselineParameters = new Dictionary<string, decimal>(_baselineParameters),
                    ParameterAdjustments = CalculateParameterAdjustments(),
                    OverallCalibrationScore = _overallCalibrationScore,
                    IsCalibrated = _isCalibrated,
                    CalibrationDuration = DateTime.UtcNow - _lastCalibration,
                    CalibrationIterations = _calibrationIterations,
                    CalibrationReasons = GetCalibrationReasons()
                };
            }
        }

        /// <summary>
        /// Reset calibration to default values
        /// </summary>
        public void ResetCalibration()
        {
            lock (_lockObject)
            {
                _currentParameters.Clear();
                foreach (var kvp in _baselineParameters)
                {
                    _currentParameters[kvp.Key] = kvp.Value;
                }
                
                _isCalibrated = false;
                _overallCalibrationScore = 0m;
                _calibrationIterations = 0;
                _lastCalibration = DateTime.UtcNow;
                
                _logAction("🔄 Adaptive calibration reset to baseline parameters");
            }
        }

        /// <summary>
        /// Apply calibration adjustments
        /// </summary>
        public void ApplyCalibration(Dictionary<string, decimal> adjustments)
        {
            lock (_lockObject)
            {
                foreach (var kvp in adjustments)
                {
                    var parameterName = kvp.Key;
                    var adjustment = kvp.Value;
                    
                    if (_currentParameters.ContainsKey(parameterName))
                    {
                        var currentValue = _currentParameters[parameterName];
                        var newValue = currentValue + adjustment;
                        
                        // Apply parameter bounds
                        if (_parameterBounds.ContainsKey(parameterName))
                        {
                            var bound = _parameterBounds[parameterName];
                            newValue = Math.Max(-bound, Math.Min(bound, newValue));
                        }
                        
                        _currentParameters[parameterName] = newValue;
                        
                        _logAction($"📊 Parameter adjusted: {parameterName} = {currentValue:F3} → {newValue:F3} (Δ{adjustment:+F3;-F3})");
                    }
                }
            }
        }

        /// <summary>
        /// Get optimization recommendations
        /// </summary>
        public List<OptimizationRecommendation> GetOptimizationRecommendations()
        {
            lock (_lockObject)
            {
                var recommendations = new List<OptimizationRecommendation>();
                
                if (_performanceHistory.Count < 10)
                {
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Timestamp = DateTime.UtcNow,
                        Component = PerformanceComponent.AdaptiveCalibrator,
                        RecommendationType = "DataCollection",
                        Description = "Collect more performance data before making optimization recommendations",
                        Priority = 0.3m,
                        ExpectedImprovement = 0m
                    });
                    
                    return recommendations;
                }

                var performanceAnalysis = AnalyzePerformanceTrends();
                
                // Response time optimization
                if (performanceAnalysis.AverageResponseTime > TimeSpan.FromMilliseconds(200))
                {
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Timestamp = DateTime.UtcNow,
                        Component = PerformanceComponent.AdaptiveCalibrator,
                        RecommendationType = "ResponseTimeOptimization",
                        Description = "Reduce analysis intervals or increase caching to improve response times",
                        Priority = 0.8m,
                        ExpectedImprovement = 0.3m,
                        ImplementationSteps = new List<string>
                        {
                            "Increase analysis interval from current setting",
                            "Enable more aggressive caching",
                            "Reduce complexity of analysis algorithms"
                        }
                    });
                }

                // Memory optimization
                if (performanceAnalysis.AverageMemoryUsage > 150 * 1024 * 1024) // 150MB
                {
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Timestamp = DateTime.UtcNow,
                        Component = PerformanceComponent.AdaptiveCalibrator,
                        RecommendationType = "MemoryOptimization",
                        Description = "Reduce memory usage through better data management",
                        Priority = 0.7m,
                        ExpectedImprovement = 0.25m,
                        ImplementationSteps = new List<string>
                        {
                            "Reduce sliding window sizes",
                            "Increase cache cleanup frequency",
                            "Optimize data structures"
                        }
                    });
                }

                // Success rate optimization
                if (performanceAnalysis.SuccessRate < 0.9m)
                {
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Timestamp = DateTime.UtcNow,
                        Component = PerformanceComponent.AdaptiveCalibrator,
                        RecommendationType = "ReliabilityOptimization",
                        Description = "Improve system reliability and reduce failure rates",
                        Priority = 0.9m,
                        ExpectedImprovement = 0.4m,
                        ImplementationSteps = new List<string>
                        {
                            "Increase error handling robustness",
                            "Add more circuit breaker protection",
                            "Implement better fallback mechanisms"
                        }
                    });
                }

                return recommendations.OrderByDescending(r => r.Priority).ToList();
            }
        }

        /// <summary>
        /// Initialize default parameters
        /// </summary>
        private void InitializeDefaultParameters()
        {
            // Analysis interval parameters
            _baselineParameters["AnalysisInterval"] = 5.0m; // 5 seconds default
            _baselineParameters["VolatilityThreshold"] = 0.7m;
            _baselineParameters["VolumeThreshold"] = 1.2m;
            
            // Caching parameters
            _baselineParameters["CacheHitRatioTarget"] = 0.8m;
            _baselineParameters["CacheTTLMultiplier"] = 1.0m;
            _baselineParameters["CacheCleanupFrequency"] = 2.0m; // minutes
            
            // Memory management parameters
            _baselineParameters["MemoryPressureThreshold"] = 0.8m;
            _baselineParameters["SlidingWindowSizeMultiplier"] = 1.0m;
            _baselineParameters["CleanupFrequencyMultiplier"] = 1.0m;
            
            // Performance thresholds
            _baselineParameters["ResponseTimeTarget"] = 100.0m; // milliseconds
            _baselineParameters["SuccessRateTarget"] = 0.95m;
            _baselineParameters["MemoryUsageTarget"] = 100.0m; // MB
            
            // Copy baseline to current
            foreach (var kvp in _baselineParameters)
            {
                _currentParameters[kvp.Key] = kvp.Value;
            }
            
            // Set parameter bounds (maximum absolute values)
            _parameterBounds["AnalysisInterval"] = 10.0m; // 1-15 seconds
            _parameterBounds["VolatilityThreshold"] = 0.5m; // 0.2-1.0
            _parameterBounds["VolumeThreshold"] = 1.0m; // 0.5-2.5
            _parameterBounds["CacheHitRatioTarget"] = 0.3m; // 0.5-1.0
            _parameterBounds["CacheTTLMultiplier"] = 2.0m; // 0.1-3.0
            _parameterBounds["MemoryPressureThreshold"] = 0.3m; // 0.5-1.0
            _parameterBounds["ResponseTimeTarget"] = 400.0m; // 50-500ms
            _parameterBounds["SuccessRateTarget"] = 0.2m; // 0.75-1.0
            _parameterBounds["MemoryUsageTarget"] = 150.0m; // 50-250MB
        }

        /// <summary>
        /// Check if calibration should be performed
        /// </summary>
        private bool ShouldPerformCalibration()
        {
            var timeSinceLastCalibration = DateTime.UtcNow - _lastCalibration;
            var hasEnoughData = _performanceHistory.Count >= 20;
            var intervalPassed = timeSinceLastCalibration >= MIN_CALIBRATION_INTERVAL;
            
            return hasEnoughData && intervalPassed;
        }

        /// <summary>
        /// Analyze performance trends
        /// </summary>
        private PerformanceAnalysis AnalyzePerformanceTrends()
        {
            var recentMetrics = _performanceHistory.TakeLast(50).ToList();
            
            return new PerformanceAnalysis
            {
                AverageResponseTime = TimeSpan.FromMilliseconds(recentMetrics.Average(m => m.ResponseTime.TotalMilliseconds)),
                SuccessRate = (decimal)recentMetrics.Count(m => m.Success) / recentMetrics.Count,
                AverageMemoryUsage = (long)recentMetrics.Average(m => m.MemoryUsage),
                CacheHitRatio = recentMetrics.Where(m => m.CacheHits + m.CacheMisses > 0)
                    .Average(m => m.CacheHitRatio),
                TrendDirection = CalculateTrendDirection(recentMetrics),
                PerformanceScore = CalculatePerformanceScore(recentMetrics)
            };
        }

        /// <summary>
        /// Determine parameter adjustments based on performance analysis
        /// </summary>
        private Dictionary<string, decimal> DetermineParameterAdjustments(PerformanceAnalysis analysis, MarketContext marketContext)
        {
            var adjustments = new Dictionary<string, decimal>();
            
            // Response time adjustments
            if (analysis.AverageResponseTime > TimeSpan.FromMilliseconds((double)_currentParameters["ResponseTimeTarget"]))
            {
                // Increase analysis interval to reduce load
                adjustments["AnalysisInterval"] = Math.Min(MAX_PARAMETER_ADJUSTMENT, 0.1m);
                adjustments["CacheTTLMultiplier"] = Math.Min(MAX_PARAMETER_ADJUSTMENT, 0.1m);
            }
            else if (analysis.AverageResponseTime < TimeSpan.FromMilliseconds((double)(_currentParameters["ResponseTimeTarget"] * 0.5m)))
            {
                // Decrease analysis interval for better responsiveness
                adjustments["AnalysisInterval"] = Math.Max(-MAX_PARAMETER_ADJUSTMENT, -0.05m);
            }
            
            // Cache performance adjustments
            if (analysis.CacheHitRatio < _currentParameters["CacheHitRatioTarget"])
            {
                adjustments["CacheTTLMultiplier"] = Math.Min(MAX_PARAMETER_ADJUSTMENT, 0.1m);
                adjustments["CacheCleanupFrequency"] = Math.Max(-MAX_PARAMETER_ADJUSTMENT, -0.2m);
            }
            
            // Memory usage adjustments
            if (analysis.AverageMemoryUsage > _currentParameters["MemoryUsageTarget"] * 1024 * 1024)
            {
                adjustments["SlidingWindowSizeMultiplier"] = Math.Max(-MAX_PARAMETER_ADJUSTMENT, -0.1m);
                adjustments["CleanupFrequencyMultiplier"] = Math.Min(MAX_PARAMETER_ADJUSTMENT, 0.1m);
            }
            
            // Market condition adjustments
            if (marketContext.VolatilityRegime >= VolatilityRegime.High)
            {
                adjustments["VolatilityThreshold"] = Math.Max(-MAX_PARAMETER_ADJUSTMENT, -0.05m);
                adjustments["AnalysisInterval"] = Math.Max(-MAX_PARAMETER_ADJUSTMENT, -0.1m);
            }
            else if (marketContext.VolatilityRegime <= VolatilityRegime.Low)
            {
                adjustments["VolatilityThreshold"] = Math.Min(MAX_PARAMETER_ADJUSTMENT, 0.05m);
                adjustments["AnalysisInterval"] = Math.Min(MAX_PARAMETER_ADJUSTMENT, 0.1m);
            }
            
            return adjustments;
        }

        /// <summary>
        /// Update calibration state after adjustments
        /// </summary>
        private void UpdateCalibrationState(Dictionary<string, decimal> adjustments, PerformanceAnalysis analysis)
        {
            var calibrationState = new CalibrationState
            {
                Timestamp = DateTime.UtcNow,
                CurrentParameters = new Dictionary<string, decimal>(_currentParameters),
                BaselineParameters = new Dictionary<string, decimal>(_baselineParameters),
                ParameterAdjustments = adjustments,
                OverallCalibrationScore = analysis.PerformanceScore,
                IsCalibrated = adjustments.Count > 0,
                CalibrationDuration = DateTime.UtcNow - _lastCalibration,
                CalibrationIterations = _calibrationIterations + 1
            };

            // Add calibration reasons
            if (analysis.AverageResponseTime > TimeSpan.FromMilliseconds((double)_currentParameters["ResponseTimeTarget"]))
            {
                calibrationState.CalibrationReasons.Add("High response time detected");
            }
            if (analysis.CacheHitRatio < _currentParameters["CacheHitRatioTarget"])
            {
                calibrationState.CalibrationReasons.Add("Low cache hit ratio detected");
            }
            if (analysis.AverageMemoryUsage > _currentParameters["MemoryUsageTarget"] * 1024 * 1024)
            {
                calibrationState.CalibrationReasons.Add("High memory usage detected");
            }
            if (analysis.SuccessRate < _currentParameters["SuccessRateTarget"])
            {
                calibrationState.CalibrationReasons.Add("Low success rate detected");
            }

            _calibrationHistory.Enqueue(calibrationState);
            while (_calibrationHistory.Count > MAX_CALIBRATION_HISTORY)
            {
                _calibrationHistory.Dequeue();
            }

            _overallCalibrationScore = analysis.PerformanceScore;
            _isCalibrated = adjustments.Count > 0;
        }

        /// <summary>
        /// Calculate parameter adjustments from baseline
        /// </summary>
        private Dictionary<string, decimal> CalculateParameterAdjustments()
        {
            var adjustments = new Dictionary<string, decimal>();

            foreach (var kvp in _currentParameters)
            {
                var parameterName = kvp.Key;
                var currentValue = kvp.Value;

                if (_baselineParameters.ContainsKey(parameterName))
                {
                    var baselineValue = _baselineParameters[parameterName];
                    adjustments[parameterName] = currentValue - baselineValue;
                }
            }

            return adjustments;
        }

        /// <summary>
        /// Get calibration reasons
        /// </summary>
        private List<string> GetCalibrationReasons()
        {
            var reasons = new List<string>();

            if (_calibrationHistory.Count > 0)
            {
                var latestCalibration = _calibrationHistory.Last();
                reasons.AddRange(latestCalibration.CalibrationReasons);
            }

            if (_performanceHistory.Count > 0)
            {
                var recentMetrics = _performanceHistory.TakeLast(10).ToList();
                var avgResponseTime = recentMetrics.Average(m => m.ResponseTime.TotalMilliseconds);
                var successRate = (decimal)recentMetrics.Count(m => m.Success) / recentMetrics.Count;

                if ((decimal)avgResponseTime > _currentParameters["ResponseTimeTarget"])
                {
                    reasons.Add($"Response time ({avgResponseTime:F0}ms) above target ({_currentParameters["ResponseTimeTarget"]:F0}ms)");
                }

                if (successRate < _currentParameters["SuccessRateTarget"])
                {
                    reasons.Add($"Success rate ({successRate:P1}) below target ({_currentParameters["SuccessRateTarget"]:P1})");
                }
            }

            return reasons.Distinct().ToList();
        }

        /// <summary>
        /// Calculate trend direction from metrics
        /// </summary>
        private decimal CalculateTrendDirection(List<SmartVolumeStrategy.Core.Models.Performance.PerformanceMetrics> metrics)
        {
            if (metrics.Count < 5) return 0m;

            var responseTimes = metrics.Select(m => m.ResponseTime.TotalMilliseconds).ToList();
            var successRates = metrics.Select(m => m.Success ? 1.0 : 0.0).ToList();

            // Simple linear trend calculation
            var responseTimeTrend = CalculateLinearTrend(responseTimes);
            var successRateTrend = CalculateLinearTrend(successRates);

            // Combine trends (negative response time trend is good, positive success rate trend is good)
            return (decimal)(successRateTrend - responseTimeTrend);
        }

        /// <summary>
        /// Calculate performance score
        /// </summary>
        private decimal CalculatePerformanceScore(List<SmartVolumeStrategy.Core.Models.Performance.PerformanceMetrics> metrics)
        {
            if (metrics.Count == 0) return 0m;

            var avgResponseTime = metrics.Average(m => m.ResponseTime.TotalMilliseconds);
            var successRate = (decimal)metrics.Count(m => m.Success) / metrics.Count;
            var avgMemoryUsage = metrics.Average(m => m.MemoryUsage);
            var cacheHitRatio = metrics.Where(m => m.CacheHits + m.CacheMisses > 0)
                .DefaultIfEmpty()
                .Average(m => m?.CacheHitRatio ?? 0m);

            // Normalize scores (0-1 scale)
            var responseTimeScore = Math.Max(0m, 1m - (decimal)avgResponseTime / 1000m); // 1000ms = 0 score
            var successRateScore = successRate;
            var memoryScore = Math.Max(0m, 1m - (decimal)avgMemoryUsage / (200 * 1024 * 1024)); // 200MB = 0 score
            var cacheScore = cacheHitRatio;

            // Weighted average
            return (responseTimeScore * 0.3m) + (successRateScore * 0.4m) + (memoryScore * 0.2m) + (cacheScore * 0.1m);
        }

        /// <summary>
        /// Calculate linear trend
        /// </summary>
        private double CalculateLinearTrend(List<double> values)
        {
            if (values.Count < 2) return 0.0;

            var n = values.Count;
            var sumX = 0.0;
            var sumY = values.Sum();
            var sumXY = 0.0;
            var sumX2 = 0.0;

            for (int i = 0; i < n; i++)
            {
                sumX += i;
                sumXY += i * values[i];
                sumX2 += i * i;
            }

            var slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
            return slope;
        }

        private void CalibrateParametersDegraded(SmartVolumeStrategy.Core.Models.Performance.PerformanceMetrics metrics)
        {
            // Simple calibration in degraded mode
            if (metrics.ResponseTime > TimeSpan.FromMilliseconds(500))
            {
                _currentParameters["AnalysisInterval"] = Math.Min(15.0m, _currentParameters["AnalysisInterval"] + 1.0m);
            }
        }
    }

    /// <summary>
    /// Performance analysis result
    /// </summary>
    public class PerformanceAnalysis
    {
        public TimeSpan AverageResponseTime { get; set; }
        public decimal SuccessRate { get; set; }
        public long AverageMemoryUsage { get; set; }
        public decimal CacheHitRatio { get; set; }
        public decimal TrendDirection { get; set; }
        public decimal PerformanceScore { get; set; }
    }
}
