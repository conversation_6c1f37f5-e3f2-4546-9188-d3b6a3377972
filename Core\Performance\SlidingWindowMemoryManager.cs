using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections.Concurrent;
using SmartVolumeStrategy.Core.Models.Performance;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;

namespace SmartVolumeStrategy.Core.Performance
{
    /// <summary>
    /// Phase 3.3: Sliding Window Memory Manager
    /// Efficient data retention and cleanup with configurable sliding windows
    /// </summary>
    public class SlidingWindowMemoryManager
    {
        private readonly Action<string> _logAction;
        private readonly SlidingWindowConfig _config;
        private readonly ConcurrentDictionary<string, SlidingWindowData<object>> _slidingWindows;
        private readonly Queue<PerformanceMetrics> _performanceHistory;
        private readonly object _lockObject = new object();
        
        // Circuit breaker integration
        private CircuitBreakerManager _circuitBreakerManager;
        
        // Memory management state
        private long _totalMemoryUsage;
        private DateTime _lastCleanup;
        private DateTime _lastMemoryCheck;
        private int _totalOperations;
        
        // Configuration
        private const int MAX_PERFORMANCE_HISTORY = 100;
        private static readonly TimeSpan MEMORY_CHECK_INTERVAL = TimeSpan.FromMinutes(1);
        private const long BYTES_PER_MB = 1024 * 1024;

        public SlidingWindowMemoryManager(SlidingWindowConfig config = null, Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _config = config ?? new SlidingWindowConfig();
            _slidingWindows = new ConcurrentDictionary<string, SlidingWindowData<object>>();
            _performanceHistory = new Queue<PerformanceMetrics>();
            
            _lastCleanup = DateTime.UtcNow;
            _lastMemoryCheck = DateTime.UtcNow;
        }

        /// <summary>
        /// Set circuit breaker manager for fault tolerance
        /// </summary>
        public void SetCircuitBreakerManager(CircuitBreakerManager circuitBreakerManager)
        {
            _circuitBreakerManager = circuitBreakerManager;
        }

        /// <summary>
        /// Add data to sliding window
        /// </summary>
        public void AddData<T>(string dataType, T data) where T : class
        {
            if (data == null) return;

            var startTime = DateTime.UtcNow;
            
            try
            {
                // Phase 3.3: Check circuit breaker status
                if (_circuitBreakerManager != null && !_circuitBreakerManager.IsOperationAllowed(StrategyComponent.MemoryManager))
                {
                    _logAction("🔴 Sliding window memory management blocked by circuit breaker");
                    return;
                }

                // Check degradation level
                if (_circuitBreakerManager != null)
                {
                    var degradationLevel = _circuitBreakerManager.GetDegradationManager().GetCurrentDegradationLevel();
                    if (degradationLevel >= DegradationLevel.Minimal)
                    {
                        _logAction("⚠️ Sliding window memory management running in degraded mode");
                        AddDataDegraded(dataType, data);
                        return;
                    }
                }

                if (!_config.EnableSlidingWindow) return;

                // Get or create sliding window for data type
                var window = _slidingWindows.GetOrAdd(dataType, _ => CreateSlidingWindow(dataType));

                lock (window)
                {
                    // Add data to window
                    window.Data.Enqueue(data);
                    window.TotalOperations++;

                    // Update memory usage
                    var dataSize = EstimateObjectSize(data);
                    window.TotalMemoryUsage += dataSize;
                    
                    lock (_lockObject)
                    {
                        _totalMemoryUsage += dataSize;
                        _totalOperations++;
                    }

                    // Enforce window size limit
                    if (window.Data.Count > window.MaxSize)
                    {
                        var removedData = window.Data.Dequeue();
                        var removedSize = EstimateObjectSize(removedData);
                        window.TotalMemoryUsage -= removedSize;
                        
                        lock (_lockObject)
                        {
                            _totalMemoryUsage -= removedSize;
                        }
                    }

                    // Update access time
                    window.AverageAccessTime = CalculateAverageAccessTime(window, DateTime.UtcNow - startTime);
                }

                // Trigger cleanup if needed
                if (ShouldTriggerCleanup())
                {
                    TriggerCleanup();
                }

                // Record successful operation
                if (_circuitBreakerManager != null)
                {
                    var responseTime = DateTime.UtcNow - startTime;
                    _circuitBreakerManager.RecordOperation(StrategyComponent.MemoryManager, true, $"Added data to {dataType} window");
                    _circuitBreakerManager.GetComponentHealthTracker().UpdateResponseTime(StrategyComponent.MemoryManager, responseTime);
                }
            }
            catch (Exception ex)
            {
                _logAction($"❌ Sliding window add data error for {dataType}: {ex.Message}");
                
                // Record failure with circuit breaker
                if (_circuitBreakerManager != null)
                {
                    _circuitBreakerManager.RecordOperation(StrategyComponent.MemoryManager, false, ex.Message, FailureSeverity.Medium);
                }
            }
        }

        /// <summary>
        /// Get recent data from sliding window
        /// </summary>
        public List<T> GetRecentData<T>(string dataType, int count = -1, TimeSpan? maxAge = null) where T : class
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                // Phase 3.3: Check circuit breaker status
                if (_circuitBreakerManager != null && !_circuitBreakerManager.IsOperationAllowed(StrategyComponent.MemoryManager))
                {
                    return new List<T>();
                }

                if (!_slidingWindows.TryGetValue(dataType, out var window))
                {
                    return new List<T>();
                }

                lock (window)
                {
                    var data = window.Data.OfType<T>().ToList();
                    
                    // Apply age filter if specified
                    if (maxAge.HasValue)
                    {
                        var cutoffTime = DateTime.UtcNow - maxAge.Value;
                        data = data.Where(item => GetDataTimestamp(item) >= cutoffTime).ToList();
                    }
                    
                    // Apply count limit if specified
                    if (count > 0 && data.Count > count)
                    {
                        data = data.TakeLast(count).ToList();
                    }

                    // Update access time
                    window.AverageAccessTime = CalculateAverageAccessTime(window, DateTime.UtcNow - startTime);
                    
                    return data;
                }
            }
            catch (Exception ex)
            {
                _logAction($"❌ Sliding window get data error for {dataType}: {ex.Message}");
                return new List<T>();
            }
        }

        /// <summary>
        /// Get sliding window statistics
        /// </summary>
        public SlidingWindowStatistics GetStatistics()
        {
            lock (_lockObject)
            {
                var stats = new SlidingWindowStatistics
                {
                    TotalWindows = _slidingWindows.Count,
                    TotalMemoryUsageMB = _totalMemoryUsage / (decimal)BYTES_PER_MB,
                    TotalOperations = _totalOperations,
                    LastCleanup = _lastCleanup,
                    LastMemoryCheck = _lastMemoryCheck
                };

                // Window-specific statistics
                foreach (var kvp in _slidingWindows)
                {
                    var windowStats = new WindowStatistics
                    {
                        DataType = kvp.Key,
                        Count = kvp.Value.Count,
                        MaxSize = kvp.Value.MaxSize,
                        MemoryUsageMB = kvp.Value.MemoryUsageMB,
                        TotalOperations = kvp.Value.TotalOperations,
                        AverageAccessTime = kvp.Value.AverageAccessTime,
                        IsFull = kvp.Value.IsFull,
                        LastCleanup = kvp.Value.LastCleanup
                    };
                    
                    stats.WindowStatistics[kvp.Key] = windowStats;
                }

                if (_performanceHistory.Count > 0)
                {
                    stats.AverageResponseTime = TimeSpan.FromMilliseconds(_performanceHistory.Average(p => p.ResponseTime.TotalMilliseconds));
                    stats.SuccessRate = (decimal)_performanceHistory.Count(p => p.Success) / _performanceHistory.Count;
                }

                return stats;
            }
        }

        /// <summary>
        /// Force cleanup of all sliding windows
        /// </summary>
        public void ForceCleanup(string reason = null)
        {
            _logAction($"🧹 Forcing sliding window cleanup: {reason ?? "Manual trigger"}");
            TriggerCleanup();
        }

        /// <summary>
        /// Clear all sliding windows
        /// </summary>
        public void ClearAll()
        {
            lock (_lockObject)
            {
                foreach (var window in _slidingWindows.Values)
                {
                    lock (window)
                    {
                        window.Data.Clear();
                        window.TotalMemoryUsage = 0;
                    }
                }
                
                _slidingWindows.Clear();
                _totalMemoryUsage = 0;
                _totalOperations = 0;
                
                _logAction("🧹 All sliding windows cleared");
            }
        }

        /// <summary>
        /// Create sliding window for data type
        /// </summary>
        private SlidingWindowData<object> CreateSlidingWindow(string dataType)
        {
            var maxSize = _config.WindowSizes.ContainsKey(dataType) ? _config.WindowSizes[dataType] : 1000;
            var retentionPeriod = _config.RetentionPeriods.ContainsKey(dataType) ? _config.RetentionPeriods[dataType] : TimeSpan.FromHours(2);

            return new SlidingWindowData<object>
            {
                MaxSize = maxSize,
                RetentionPeriod = retentionPeriod,
                LastCleanup = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Add data in degraded mode (simplified)
        /// </summary>
        private void AddDataDegraded<T>(string dataType, T data) where T : class
        {
            var window = _slidingWindows.GetOrAdd(dataType, _ => CreateSlidingWindow(dataType));

            lock (window)
            {
                // Simple add with basic size enforcement
                window.Data.Enqueue(data);
                
                if (window.Data.Count > window.MaxSize / 2) // Use half capacity in degraded mode
                {
                    window.Data.Dequeue();
                }
            }
        }

        /// <summary>
        /// Check if cleanup should be triggered
        /// </summary>
        private bool ShouldTriggerCleanup()
        {
            var timeSinceLastCleanup = DateTime.UtcNow - _lastCleanup;
            var memoryPressure = _totalMemoryUsage > _config.MaxMemoryUsageBytes * _config.MemoryPressureThreshold;
            var intervalPassed = timeSinceLastCleanup >= _config.CleanupInterval;

            return (_config.EnableAutomaticCleanup && intervalPassed) || memoryPressure;
        }

        /// <summary>
        /// Trigger cleanup of sliding windows
        /// </summary>
        private void TriggerCleanup()
        {
            var cleanupStartTime = DateTime.UtcNow;
            var initialMemoryUsage = _totalMemoryUsage;
            var cleanedWindows = 0;
            var removedItems = 0;

            try
            {
                foreach (var kvp in _slidingWindows.ToList())
                {
                    var dataType = kvp.Key;
                    var window = kvp.Value;

                    lock (window)
                    {
                        var initialCount = window.Data.Count;
                        
                        // Remove expired data based on retention period
                        if (_config.RetentionPeriods.ContainsKey(dataType))
                        {
                            var retentionPeriod = _config.RetentionPeriods[dataType];
                            var cutoffTime = DateTime.UtcNow - retentionPeriod;
                            
                            var itemsToRemove = new List<object>();
                            foreach (var item in window.Data)
                            {
                                if (GetDataTimestamp(item) < cutoffTime)
                                {
                                    itemsToRemove.Add(item);
                                }
                            }

                            foreach (var item in itemsToRemove)
                            {
                                var itemSize = EstimateObjectSize(item);
                                window.TotalMemoryUsage -= itemSize;
                                
                                lock (_lockObject)
                                {
                                    _totalMemoryUsage -= itemSize;
                                }
                            }

                            // Recreate queue without expired items
                            var validItems = window.Data.Except(itemsToRemove);
                            window.Data.Clear();
                            foreach (var item in validItems)
                            {
                                window.Data.Enqueue(item);
                            }
                        }

                        var finalCount = window.Data.Count;
                        var itemsRemoved = initialCount - finalCount;
                        
                        if (itemsRemoved > 0)
                        {
                            cleanedWindows++;
                            removedItems += itemsRemoved;
                        }

                        window.LastCleanup = DateTime.UtcNow;
                    }
                }

                _lastCleanup = DateTime.UtcNow;
                _lastMemoryCheck = DateTime.UtcNow;
                
                var cleanupDuration = DateTime.UtcNow - cleanupStartTime;
                var memoryFreed = initialMemoryUsage - _totalMemoryUsage;
                
                if (cleanedWindows > 0)
                {
                    _logAction($"🧹 Sliding window cleanup completed - Cleaned {cleanedWindows} windows, removed {removedItems} items, freed {memoryFreed / BYTES_PER_MB:F1}MB in {cleanupDuration.TotalMilliseconds:F0}ms");
                }

                // Record performance metric
                RecordPerformanceMetric(true, cleanupDuration, cleanedWindows);
            }
            catch (Exception ex)
            {
                _logAction($"❌ Sliding window cleanup error: {ex.Message}");
                RecordPerformanceMetric(false, DateTime.UtcNow - cleanupStartTime, 0);
            }
        }

        /// <summary>
        /// Calculate average access time for window
        /// </summary>
        private TimeSpan CalculateAverageAccessTime(SlidingWindowData<object> window, TimeSpan currentAccessTime)
        {
            if (window.TotalOperations <= 1)
            {
                return currentAccessTime;
            }

            // Simple moving average
            var currentAverage = window.AverageAccessTime.TotalMilliseconds;
            var newAverage = (currentAverage * (window.TotalOperations - 1) + currentAccessTime.TotalMilliseconds) / window.TotalOperations;
            
            return TimeSpan.FromMilliseconds(newAverage);
        }

        /// <summary>
        /// Get timestamp from data object (if available)
        /// </summary>
        private DateTime GetDataTimestamp(object data)
        {
            // Try to extract timestamp from common properties
            var type = data.GetType();
            var timestampProperty = type.GetProperty("Timestamp") ?? type.GetProperty("CreatedAt") ?? type.GetProperty("Time");
            
            if (timestampProperty != null && timestampProperty.PropertyType == typeof(DateTime))
            {
                return (DateTime)timestampProperty.GetValue(data);
            }
            
            // Default to current time if no timestamp found
            return DateTime.UtcNow;
        }

        /// <summary>
        /// Estimate object size in bytes
        /// </summary>
        private long EstimateObjectSize(object obj)
        {
            if (obj == null) return 0;
            
            // Simple size estimation - in production, use more sophisticated methods
            return obj.ToString().Length * 2; // Rough estimate: 2 bytes per character
        }

        /// <summary>
        /// Record performance metric
        /// </summary>
        private void RecordPerformanceMetric(bool success, TimeSpan responseTime, int operationCount)
        {
            var metric = new PerformanceMetrics
            {
                Timestamp = DateTime.UtcNow,
                Component = PerformanceComponent.MemoryManager,
                OperationName = success ? "SlidingWindowCleanup" : "SlidingWindowCleanupFailed",
                ResponseTime = responseTime,
                Success = success,
                MemoryUsage = _totalMemoryUsage
            };
            
            metric.AdditionalMetrics["WindowsCleaned"] = operationCount;
            metric.AdditionalMetrics["TotalWindows"] = _slidingWindows.Count;
            
            lock (_lockObject)
            {
                _performanceHistory.Enqueue(metric);
                
                while (_performanceHistory.Count > MAX_PERFORMANCE_HISTORY)
                {
                    _performanceHistory.Dequeue();
                }
            }
        }
    }

    /// <summary>
    /// Sliding window statistics
    /// </summary>
    public class SlidingWindowStatistics
    {
        public int TotalWindows { get; set; }
        public decimal TotalMemoryUsageMB { get; set; }
        public int TotalOperations { get; set; }
        public DateTime LastCleanup { get; set; }
        public DateTime LastMemoryCheck { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public decimal SuccessRate { get; set; }
        public Dictionary<string, WindowStatistics> WindowStatistics { get; set; } = new Dictionary<string, WindowStatistics>();
    }

    /// <summary>
    /// Individual window statistics
    /// </summary>
    public class WindowStatistics
    {
        public string DataType { get; set; }
        public int Count { get; set; }
        public int MaxSize { get; set; }
        public decimal MemoryUsageMB { get; set; }
        public int TotalOperations { get; set; }
        public TimeSpan AverageAccessTime { get; set; }
        public bool IsFull { get; set; }
        public DateTime LastCleanup { get; set; }
    }
}
