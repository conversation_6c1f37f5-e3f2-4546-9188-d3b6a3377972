using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Media;
using ATAS.Indicators;
using ATAS.Strategies.Chart;
using ATAS.DataFeedsCore;
using OFT.Attributes;
using Utils.Common.Logging;
using SmartVolumeStrategy.Core.Analysis;
using SmartVolumeStrategy.Core.Calibration;
using SmartVolumeStrategy.Core.Strategy;
using SmartVolumeStrategy.Core.Monitoring;
using SmartVolumeStrategy.ATAS.UI;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Utils;
using SmartVolumeStrategy.Core.Models.Analysis;

namespace ATAS.Strategies.Chart
{
    /// <summary>
    /// Phase 1.1: Multi-Timeframe Validation Result
    /// Contains the result of multi-timeframe consensus validation
    /// </summary>
    public class MultiTimeframeValidationResult
    {
        public bool IsValid { get; set; }
        public int AgreeingTimeframes { get; set; }
        public decimal TrendAlignment { get; set; }
        public string Details { get; set; }
        public string SupportingTimeframes { get; set; }
    }

    /// <summary>
    /// Phase 1.3: Volume Profile Validation Result
    /// Contains the result of institutional volume level validation
    /// </summary>
    public class VolumeProfileValidationResult
    {
        public bool IsValid { get; set; }
        public int InstitutionalLevelsFound { get; set; }
        public string NearbyVolumeNodes { get; set; }
        public string ValidationReason { get; set; }
        public List<decimal> SupportingLevels { get; set; }
    }

    /// <summary>
    /// Phase 2.1: Institutional Footprint Validation Result
    /// Contains the result of institutional trading activity detection
    /// </summary>
    public class InstitutionalFootprintValidationResult
    {
        public bool IsValid { get; set; }
        public int InstitutionalFootprintsFound { get; set; }
        public string FootprintTypes { get; set; }
        public string ValidationReason { get; set; }
        public List<string> SupportingFootprints { get; set; }
    }

    /// <summary>
    /// Phase 2.2: Pattern Recognition Validation Result
    /// Contains the result of advanced pattern recognition analysis
    /// </summary>
    public class PatternRecognitionValidationResult
    {
        public bool IsValid { get; set; }
        public int PatternsFound { get; set; }
        public string PatternTypes { get; set; }
        public string ValidationReason { get; set; }
        public List<string> SupportingPatterns { get; set; }
    }

    /// <summary>
    /// Smart Volume Strategy with Intelligent Auto-Calibration for ATAS Platform
    /// Combines simple volume block analysis with sophisticated symbol-specific parameter optimization
    /// </summary>
    [DisplayName("Smart Volume Strategy")]
    [Category("Volume Analysis")]
    [HelpLink("https://github.com/smartvolumestrategy/docs")]
    public class SmartVolumeChartStrategy : ChartStrategy
    {
        #region Fields

        private string _correlationId;

        // CRITICAL FIX: Track last known position to detect changes manually
        private decimal _lastKnownPosition = 0m;

        // PNL FIX: Track actual entry price for PnL calculation
        private decimal _actualEntryPrice = 0m;
        private DateTime _positionEntryTime = DateTime.MinValue;

        // Order Management Fields - CRITICAL for preventing orphaned orders
        private Order _currentTakeProfitOrder;
        private Order _currentStopLossOrder;
        private Order _currentEntryOrder;
        private readonly object _orderLock = new object();
        private bool _hasActiveOrders = false;

        // Logging control fields
        private int _lastLoggedBar = -1;
        private DateTime _lastStatusLog = DateTime.MinValue;
        private DateTime _lastPerformanceLog = DateTime.MinValue;
        private int _processedBarsCount = 0;

        // Phase 1: Analysis & Calibration Components
        private ISymbolAnalyzer _symbolAnalyzer;
        private ISettingsCalibrator _settingsCalibrator;
        private IRealTimeAnalysisEngine _realTimeAnalysisEngine;
        private IAdaptiveCalibrator _adaptiveCalibrator;

        // Phase 2: Strategy Components
        private IVolumeBlockDetector _volumeBlockDetector;
        private ISignalGenerator _signalGenerator;
        private IPositionManager _positionManager;
        private IPerformanceTracker _performanceTracker;
        private IAdaptiveAdjuster _adaptiveAdjuster;

        // Phase 4: Multi-Timeframe Analysis Components
        private IMultiTimeframeAnalyzer _multiTimeframeAnalyzer;
        private ISignalSynthesizer _signalSynthesizer;
        private ISignalGenerator _enhancedSignalGenerator;

        // Phase 1.3: Volume Profile Analysis Component
        private IVolumePatternAnalyzer _volumeProfileAnalyzer;

        // Phase 2: Advanced Signal Synthesis & Institutional Detection Components
        private InstitutionalFootprintDetector _institutionalFootprintDetector;
        private AdvancedPatternRecognizer _advancedPatternRecognizer;

        // Strategy State
        private OptimalSettings _currentSettings;
        private SymbolProfile _symbolProfile;
        private CalibrationResult _calibrationResult;
        private bool _isCalibrated;
        private bool _isInitialized;
        private DateTime _lastCalibrationTime;
        private DateTime _lastPerformanceCheck;
        
        // Market Data
        private readonly Queue<MarketDataPoint> _marketDataHistory;
        private readonly List<TradingSignal> _recentSignals;
        
        // UI State
        private string _statusMessage = "Initializing...";
        private System.Windows.Media.Color _statusColor = Colors.Orange;
        private decimal _currentConfidence;
        private StrategyInfoPanel _infoPanel;

        #endregion

        #region Constructor

        public SmartVolumeChartStrategy()
        {
            _correlationId = Guid.NewGuid().ToString("N")[..8];

            // IMMEDIATE TEST LOG - This should appear in ATAS immediately
            LogInfo($"🚀 SMART VOLUME STRATEGY STARTUP - ID: {_correlationId}");
            LogInfo($"⏰ Timestamp: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");

            _marketDataHistory = new Queue<MarketDataPoint>();
            _recentSignals = new List<TradingSignal>();

            // Initialize order management state
            _currentTakeProfitOrder = null;
            _currentStopLossOrder = null;
            _currentEntryOrder = null;
            _hasActiveOrders = false;

            // Initialize with default settings
            _currentSettings = new OptimalSettings();
            _isCalibrated = false;
            _isInitialized = false;
            _lastCalibrationTime = DateTime.MinValue;
            _lastPerformanceCheck = DateTime.UtcNow;

            // Set default UI parameters
            InitializeDefaultParameters();
            LogInfo($"⚙️ Default parameters initialized - CalibrationBars: {CalibrationBars}, MaxPositionSize: {MaxPositionSizeUSDT} USDT, Risk: {RiskPercentPerTrade}%");

            // Initialize UI components
            _infoPanel = new StrategyInfoPanel();

            LogInfo($"✅ Strategy constructor completed successfully");
        }

        #endregion

        #region User Settings

        [Display(Name = "Enable Strategy", Description = "Enable/disable the trading strategy", GroupName = "Strategy Control", Order = 1)]
        public bool EnableStrategy { get; set; } = false;

        [Display(Name = "Enable Auto-Calibration", Description = "Automatically calibrate settings based on symbol analysis", GroupName = "Strategy Control", Order = 2)]
        public bool EnableAutoCalibration { get; set; } = true;

        [Display(Name = "Calibration Bars", Description = "Number of historical bars to analyze for calibration (200-500)", GroupName = "Calibration", Order = 10)]
        [Range(200, 500)]
        public int CalibrationBars { get; set; } = 300;

        [Display(Name = "Max Position Size (USDT)", Description = "Maximum position size in USDT", GroupName = "Risk Management", Order = 20)]
        [Range(100, 10000)]
        public decimal MaxPositionSizeUSDT { get; set; } = 2000;

        [Display(Name = "Risk Per Trade (%)", Description = "Maximum risk percentage per trade", GroupName = "Risk Management", Order = 21)]
        [Range(0.5, 5.0)]
        public decimal RiskPercentPerTrade { get; set; } = 2.0m;

        [Display(Name = "Conservative Mode", Description = "Use conservative parameter adjustments", GroupName = "Risk Management", Order = 22)]
        public bool ConservativeMode { get; set; } = false;

        [Display(Name = "Manual Volume Threshold", Description = "Manual volume threshold (0 = auto-calibrated)", GroupName = "Manual Overrides", Order = 30)]
        [Range(0, 5.0)]
        public decimal ManualVolumeThreshold { get; set; } = 0;

        [Display(Name = "Manual Signal Threshold", Description = "Manual signal threshold (0 = auto-calibrated)", GroupName = "Manual Overrides", Order = 31)]
        [Range(0, 3.0)]
        public decimal ManualSignalThreshold { get; set; } = 0;

        [Display(Name = "Take Profit (%)", Description = "Take profit percentage (e.g., 1.2 for 1.2%)", GroupName = "Position Management", Order = 40)]
        [Range(0.1, 10.0)]
        public decimal TakeProfitPercent { get; set; } = 1.2m;

        [Display(Name = "Stop Loss (%)", Description = "Stop loss percentage (e.g., 0.8 for 0.8%)", GroupName = "Position Management", Order = 41)]
        [Range(0.1, 5.0)]
        public decimal StopLossPercent { get; set; } = 0.8m;

        [Display(Name = "Minimum Signal Quality", Description = "Minimum required signal quality", GroupName = "Signal Filtering", Order = 50)]
        public SignalQuality MinimumSignalQuality { get; set; } = SignalQuality.Good;

        [Display(Name = "Minimum Signal Confidence (%)", Description = "Minimum signal confidence percentage (e.g., 70 for 70%)", GroupName = "Signal Filtering", Order = 51)]
        [Range(50, 95)]
        public decimal MinimumSignalConfidence { get; set; } = 70m;

        [Display(Name = "Position Cooldown (seconds)", Description = "Minimum time between position openings (10s recommended for crypto scalping)", GroupName = "Position Management", Order = 42)]
        [Range(5, 300)]
        public int PositionCooldownSeconds { get; set; } = 10;

        #endregion

        #region Indicator Overrides

        protected override void OnInitialize()
        {
            try
            {
                LogInfo($"🔧 === STRATEGY INITIALIZATION STARTING ===");
                LogInfo($"📊 Symbol: {InstrumentInfo?.Instrument ?? "Unknown"}");
                LogInfo($"⚙️ Conservative Mode: {ConservativeMode}");
                LogInfo($"🎯 Manual Signal Threshold: {ManualSignalThreshold} (0 = auto-calibrated)");
                LogInfo($"🤖 Auto Calibration: {EnableAutoCalibration}");
                LogInfo($"▶️ Strategy Enabled: {EnableStrategy}");

                LogInfo("🔨 Initializing core components...");
                InitializeComponents();
                LogInfo("✅ Core components initialized successfully");

                _statusMessage = "Ready for calibration";
                _statusColor = Colors.Yellow;

                // CRITICAL FIX: Initialize position tracking
                _lastKnownPosition = CurrentPosition;
                LogInfo($"🔄 Initial position tracking set: {_lastKnownPosition:F6}");

                _isInitialized = true;

                LogInfo("🎉 === STRATEGY INITIALIZATION COMPLETED ===");
                LogInfo($"🚀 Strategy is ready for market data processing");
            }
            catch (Exception ex)
            {
                LogError($"❌ === INITIALIZATION FAILED ===");
                LogError($"💥 Error: {ex.Message}");
                _statusMessage = $"Initialization failed: {ex.Message}";
                _statusColor = Colors.Red;
            }
        }

        protected override void OnCalculate(int bar, decimal value)
        {
            try
            {
                if (!_isInitialized || bar < 1)
                    return;

                // CRITICAL FIX: Manual position change detection
                // Check if ATAS position has changed without OnCurrentPositionChanged being called
                var currentATASPosition = CurrentPosition;
                if (Math.Abs(currentATASPosition - _lastKnownPosition) > 0.000001m)
                {
                    LogInfo($"🔄 MANUAL POSITION CHANGE DETECTED: {_lastKnownPosition:F6} → {currentATASPosition:F6}");
                    _lastKnownPosition = currentATASPosition;

                    // Manually trigger position synchronization
                    if (_positionManager != null)
                    {
                        var positionInfo = ConvertToPositionInfo(currentATASPosition);
                        LogInfo($"📊 Manual Position Sync: ATAS={currentATASPosition:F6} | HasPosition={positionInfo.HasPosition}");
                        _positionManager.UpdatePosition(positionInfo);

                        // If position was closed, cancel any remaining orders
                        if (!positionInfo.HasPosition)
                        {
                            LogInfo($"📭 POSITION CLOSED DETECTED - Canceling remaining orders");
                            CancelAllActiveOrders("Position closed - manual detection");
                        }
                    }
                }

                _processedBarsCount++;

                // Convert ATAS bar data to our MarketDataPoint format
                var marketData = ConvertToMarketDataPoint(bar);
                if (marketData == null)
                    return;

                // Log market data processing every 50 bars or on significant events
                if (_processedBarsCount % 50 == 0 || bar != _lastLoggedBar)
                {
                    LogInfo($"📊 Processing bar {bar}: {marketData.Close:F2} | Vol: {marketData.Volume:F0} | Time: {marketData.Timestamp:HH:mm:ss}");
                    _lastLoggedBar = bar;
                }

                // Add to history
                _marketDataHistory.Enqueue(marketData);
                while (_marketDataHistory.Count > CalibrationBars + 50) // Keep extra for analysis
                {
                    _marketDataHistory.Dequeue();
                }

                // Update analysis engines with new market data if calibrated
                if (_isCalibrated && _realTimeAnalysisEngine != null)
                {
                    _realTimeAnalysisEngine.UpdateMarketData(marketData);

                    // Update Phase 4 multi-timeframe analyzer
                    if (_multiTimeframeAnalyzer != null)
                    {
                        _multiTimeframeAnalyzer.UpdateMarketData(marketData);
                    }
                }

                // Perform initial calibration if needed
                if (!_isCalibrated && EnableAutoCalibration && _marketDataHistory.Count >= CalibrationBars)
                {
                    LogInfo($"🎯 === STARTING INITIAL CALIBRATION ===");
                    LogInfo($"📈 Market data collected: {_marketDataHistory.Count} bars (required: {CalibrationBars})");
                    _ = Task.Run(async () => await PerformInitialCalibrationAsync());
                }

                // Process trading logic if strategy is enabled and calibrated
                if (EnableStrategy && _isCalibrated && _currentSettings != null)
                {
                    LogInfo($"🎯 PROCESSING TRADING LOGIC: Strategy enabled and calibrated");
                    ProcessTradingLogic(marketData);
                }
                else if (!EnableStrategy)
                {
                    // Only log this occasionally to avoid spam
                    if (_processedBarsCount % 100 == 0)
                    {
                        LogInfo($"⏸️ STRATEGY DISABLED: EnableStrategy = {EnableStrategy}");
                    }
                }
                else if (!_isCalibrated)
                {
                    // Only log this occasionally to avoid spam
                    if (_processedBarsCount % 100 == 0)
                    {
                        LogInfo($"⏳ WAITING FOR CALIBRATION: _isCalibrated = {_isCalibrated}");
                    }
                }

                // Periodic performance check and adjustment - FREQUENT CHECKS for zero signal detection
                var minutesSinceLastCheck = (DateTime.UtcNow - _lastPerformanceCheck).TotalMinutes;
                var shouldCheckPerformance = minutesSinceLastCheck >= 5 ||  // Reduced from 15 to 5 minutes
                                           (minutesSinceLastCheck >= 2 && _recentSignals.Count == 0) || // Reduced from 3 to 2 minutes
                                           (minutesSinceLastCheck >= 3 && IsZeroExecutionDetected()); // NEW: Emergency check for zero execution

                if (shouldCheckPerformance)
                {
                    var checkReason = minutesSinceLastCheck >= 5 ? "Scheduled 5-min check" :
                                     _recentSignals.Count == 0 ? $"Emergency check - {_recentSignals.Count} signals in {minutesSinceLastCheck:F1} minutes" :
                                     "Emergency check - zero execution detected";
                    LogInfo($"🔍 === STARTING PERFORMANCE CHECK === Reason: {checkReason}");
                    _ = Task.Run(async () => await CheckPerformanceAndAdjustAsync());
                }

                // Log status updates every 5 minutes
                if ((DateTime.UtcNow - _lastStatusLog).TotalMinutes >= 5)
                {
                    LogStatusUpdate();
                    _lastStatusLog = DateTime.UtcNow;
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ Error in OnCalculate (bar {bar}): {ex.Message}");
                _statusMessage = $"Calculation error: {ex.Message}";
                _statusColor = Colors.Red;
            }
        }

        /// <summary>
        /// CRITICAL FIX: Detect zero execution scenario for emergency performance checks
        /// </summary>
        private bool IsZeroExecutionDetected()
        {
            var performance = _performanceTracker?.GetPerformanceMetrics();
            if (performance == null) return false;

            var runtimeMinutes = (DateTime.UtcNow - performance.StartTime).TotalMinutes;
            return _recentSignals.Count >= 5 &&
                   performance.SignalsExecuted == 0 &&
                   runtimeMinutes > 8; // Trigger emergency check after 8 minutes of zero execution
        }

        protected override void OnOrderChanged(Order order)
        {
            try
            {
                if (order == null)
                    return;

                LogInfo($"📋 ORDER STATUS CHANGE: {order.Comment} | Status: {order.Status()} | State: {order.State}");

                lock (_orderLock)
                {
                    // Check if this is one of our tracked orders
                    bool isOurOrder = (order == _currentEntryOrder) ||
                                     (order == _currentTakeProfitOrder) ||
                                     (order == _currentStopLossOrder);

                    if (!isOurOrder)
                        return; // Not our order, ignore

                    // Handle order status changes
                    switch (order.Status())
                    {
                        case OrderStatus.Filled:
                            HandleOrderFilled(order);
                            break;
                        case OrderStatus.Canceled:
                            HandleOrderCanceled(order);
                            break;
                        case OrderStatus.Placed:
                            LogInfo($"✅ ORDER PLACED: {order.Comment}");
                            break;
                        case OrderStatus.PartlyFilled:
                            LogInfo($"⚠️ ORDER PARTIALLY FILLED: {order.Comment} | Unfilled: {order.Unfilled}");
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ ERROR IN ORDER CHANGE EVENT: {ex.Message}");
            }
        }

        protected override void OnCurrentPositionChanged()
        {
            try
            {
                if (_positionManager == null)
                    return;

                LogInfo($"📍 POSITION CHANGE EVENT DETECTED");

                // CRITICAL FIX: Get actual ATAS position and convert properly
                var actualATASPosition = CurrentPosition;
                var positionInfo = ConvertToPositionInfo(actualATASPosition);

                LogInfo($"📊 ATAS Position State: {actualATASPosition:F6} | HasPosition: {positionInfo.HasPosition}");

                // CRITICAL FIX: Update position tracking
                _lastKnownPosition = actualATASPosition;

                _positionManager.UpdatePosition(positionInfo);

                // Update unrealized PnL if position exists
                if (positionInfo.HasPosition && _marketDataHistory.Count > 0)
                {
                    var currentPrice = _marketDataHistory.Last().Close;
                    LogInfo($"📊 Position PnL Update: Current Price={currentPrice:F2}");
                    // Fix: UpdateUnrealizedPnL doesn't exist, use UpdatePosition instead
                    // _positionManager.UpdateUnrealizedPnL(currentPrice);
                }

                if (positionInfo.HasPosition)
                {
                    var currentPrice = GetCurrentPrice();
                    LogInfo($"📈 ACTIVE POSITION: {positionInfo.Direction} | Qty: {positionInfo.Quantity:F2} | Entry: {positionInfo.EntryPrice:F2} | Current: {currentPrice:F2} | PnL: {positionInfo.UnrealizedPnL:F2} USDT ({positionInfo.UnrealizedPnLPercent:F2}%)");
                }
                else
                {
                    LogInfo($"📭 NO ACTIVE POSITION");

                    // PNL FIX: Reset entry price tracking when position is closed
                    if (_actualEntryPrice > 0)
                    {
                        LogInfo($"📊 PnL: Resetting entry price tracking (was {_actualEntryPrice:F2})");
                        _actualEntryPrice = 0m;
                        _positionEntryTime = DateTime.MinValue;
                    }

                    // CRITICAL: When position is closed, cancel any remaining orders
                    CancelAllActiveOrders("Position closed");
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ ERROR IN POSITION CHANGE EVENT: {ex.Message}");
            }
        }

        #endregion

        #region Order Management Methods

        /// <summary>
        /// Handle order filled event - cancel remaining orders when TP or SL is hit
        /// </summary>
        private void HandleOrderFilled(Order filledOrder)
        {
            try
            {
                LogInfo($"🎯 ORDER FILLED: {filledOrder.Comment} | Quantity: {filledOrder.QuantityToFill}");

                lock (_orderLock)
                {
                    if (filledOrder == _currentTakeProfitOrder)
                    {
                        LogInfo($"💰 TAKE PROFIT HIT - Canceling Stop Loss order");
                        CancelStopLossOrder("Take Profit filled");
                        _currentTakeProfitOrder = null;
                    }
                    else if (filledOrder == _currentStopLossOrder)
                    {
                        LogInfo($"🛑 STOP LOSS HIT - Canceling Take Profit order");
                        CancelTakeProfitOrder("Stop Loss filled");
                        _currentStopLossOrder = null;
                    }
                    else if (filledOrder == _currentEntryOrder)
                    {
                        // PNL FIX: Track entry price and time when entry order is filled
                        _actualEntryPrice = filledOrder.Price > 0 ? filledOrder.Price : GetCurrentPrice();
                        _positionEntryTime = DateTime.UtcNow;

                        LogInfo($"🚀 ENTRY ORDER FILLED - Position opened at {_actualEntryPrice:F2}");
                        LogInfo($"📊 PnL: Entry price tracked: {_actualEntryPrice:F2} at {_positionEntryTime:HH:mm:ss}");
                        _currentEntryOrder = null;
                    }

                    UpdateActiveOrdersStatus();
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ Error handling filled order: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle order canceled event
        /// </summary>
        private void HandleOrderCanceled(Order canceledOrder)
        {
            try
            {
                LogInfo($"❌ ORDER CANCELED: {canceledOrder.Comment}");

                lock (_orderLock)
                {
                    if (canceledOrder == _currentTakeProfitOrder)
                    {
                        _currentTakeProfitOrder = null;
                    }
                    else if (canceledOrder == _currentStopLossOrder)
                    {
                        _currentStopLossOrder = null;
                    }
                    else if (canceledOrder == _currentEntryOrder)
                    {
                        _currentEntryOrder = null;
                    }

                    UpdateActiveOrdersStatus();
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ Error handling canceled order: {ex.Message}");
            }
        }

        /// <summary>
        /// Cancel all active orders - CRITICAL for preventing orphaned orders
        /// </summary>
        private void CancelAllActiveOrders(string reason)
        {
            try
            {
                LogInfo($"🧹 CANCELING ALL ACTIVE ORDERS - Reason: {reason}");

                lock (_orderLock)
                {
                    CancelTakeProfitOrder(reason);
                    CancelStopLossOrder(reason);
                    CancelEntryOrder(reason);

                    UpdateActiveOrdersStatus();
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ Error canceling all orders: {ex.Message}");
            }
        }

        /// <summary>
        /// Cancel Take Profit order if active
        /// </summary>
        private void CancelTakeProfitOrder(string reason)
        {
            if (_currentTakeProfitOrder != null && _currentTakeProfitOrder.State == OrderStates.Active)
            {
                try
                {
                    LogInfo($"❌ Canceling Take Profit order - Reason: {reason}");
                    CancelOrder(_currentTakeProfitOrder);
                }
                catch (Exception ex)
                {
                    LogError($"❌ Failed to cancel Take Profit order: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Cancel Stop Loss order if active
        /// </summary>
        private void CancelStopLossOrder(string reason)
        {
            if (_currentStopLossOrder != null && _currentStopLossOrder.State == OrderStates.Active)
            {
                try
                {
                    LogInfo($"❌ Canceling Stop Loss order - Reason: {reason}");
                    CancelOrder(_currentStopLossOrder);
                }
                catch (Exception ex)
                {
                    LogError($"❌ Failed to cancel Stop Loss order: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Cancel Entry order if active
        /// </summary>
        private void CancelEntryOrder(string reason)
        {
            if (_currentEntryOrder != null && _currentEntryOrder.State == OrderStates.Active)
            {
                try
                {
                    LogInfo($"❌ Canceling Entry order - Reason: {reason}");
                    CancelOrder(_currentEntryOrder);
                }
                catch (Exception ex)
                {
                    LogError($"❌ Failed to cancel Entry order: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Update active orders status
        /// </summary>
        private void UpdateActiveOrdersStatus()
        {
            var activeCount = 0;
            if (_currentEntryOrder != null && _currentEntryOrder.State == OrderStates.Active) activeCount++;
            if (_currentTakeProfitOrder != null && _currentTakeProfitOrder.State == OrderStates.Active) activeCount++;
            if (_currentStopLossOrder != null && _currentStopLossOrder.State == OrderStates.Active) activeCount++;

            _hasActiveOrders = activeCount > 0;
            LogInfo($"📊 ACTIVE ORDERS COUNT: {activeCount} | HasActiveOrders: {_hasActiveOrders}");
        }

        #endregion

        #region ETHUSDT Component Health Recovery

        private DateTime _lastComponentHealthReset = DateTime.MinValue;
        private readonly TimeSpan _componentHealthResetCooldown = TimeSpan.FromMinutes(5);

        /// <summary>
        /// Try to reset component health for ETHUSDT recovery
        /// </summary>
        private void TryResetComponentHealth()
        {
            try
            {
                // Prevent too frequent resets
                if (DateTime.UtcNow - _lastComponentHealthReset < _componentHealthResetCooldown)
                    return;

                // TODO: Reset microstructure health monitor components (when implemented)
                // Note: _microstructureHealthMonitor and _circuitBreakerManager not yet implemented
                LogInfo($"🔄 ETHUSDT: Component health reset (placeholder - advanced features not yet implemented)");

                _lastComponentHealthReset = DateTime.UtcNow;
                LogInfo($"🔄 ETHUSDT: Component health reset completed");
            }
            catch (Exception ex)
            {
                LogError($"❌ ETHUSDT: Error resetting component health: {ex.Message}");
            }
        }

        #endregion

        #region Private Methods

        private void InitializeDefaultParameters()
        {
            // Set reasonable defaults based on our strategy design
            CalibrationBars = 300;
            MaxPositionSizeUSDT = 2000;
            RiskPercentPerTrade = 2.0m;
            ConservativeMode = false;
        }

        private void InitializeComponents()
        {
            try
            {
                LogInfo("🔧 Initializing Phase 1 components (Analysis & Calibration)...");

                // Initialize Phase 1 components (Analysis & Calibration)
                var volumeAnalyzer = new VolumePatternAnalyzer();
                LogInfo("✅ VolumePatternAnalyzer initialized");

                var deltaAnalyzer = new DeltaFlowAnalyzer();
                LogInfo("✅ DeltaFlowAnalyzer initialized");

                var volatilityAnalyzer = new VolatilityAnalyzer();
                LogInfo("✅ VolatilityAnalyzer initialized");

                var marketProfileAnalyzer = new MarketProfileAnalyzer();
                LogInfo("✅ MarketProfileAnalyzer initialized");

                // Phase 1.3: Initialize Volume Profile Analyzer (using VolumePatternAnalyzer)
                _volumeProfileAnalyzer = new VolumePatternAnalyzer();
                LogInfo("✅ VolumeProfileAnalyzer initialized for institutional level validation");

                _symbolAnalyzer = new SymbolAnalyzer(volumeAnalyzer, deltaAnalyzer, volatilityAnalyzer, marketProfileAnalyzer);
                LogInfo("✅ SymbolAnalyzer initialized with all analyzers");

                // Initialize Real-Time Analysis Engine
                LogInfo("🔧 Initializing Real-Time Analysis Engine...");
                _realTimeAnalysisEngine = new RealTimeAnalysisEngine(
                    volumeAnalyzer,
                    deltaAnalyzer,
                    volatilityAnalyzer,
                    marketProfileAnalyzer);
                LogInfo("✅ RealTimeAnalysisEngine initialized with all Phase 1 analyzers");

                // Initialize calibration components
                LogInfo("🔧 Initializing calibration components...");
                _settingsCalibrator = CreateSettingsCalibrator();
                LogInfo("✅ SettingsCalibrator initialized");

                // Phase 3: Initialize AdaptiveCalibrator
                LogInfo("🔧 Initializing AdaptiveCalibrator...");
                _adaptiveCalibrator = new AdaptiveCalibrator(_settingsCalibrator, LogInfo);
                LogInfo("✅ AdaptiveCalibrator initialized with base calibrator");

                LogInfo("🔧 Initializing Phase 2 components (Strategy)...");

                // Initialize Phase 2 components (Strategy)
                _volumeBlockDetector = new VolumeBlockDetector();
                LogInfo("✅ VolumeBlockDetector initialized");

                _signalGenerator = new SignalGenerator(LogInfo);
                LogInfo("✅ SignalGenerator initialized with diagnostic logging");

                _positionManager = new PositionManager();
                LogInfo("✅ PositionManager initialized");

                _performanceTracker = new PerformanceTracker();
                LogInfo("✅ PerformanceTracker initialized");

                _adaptiveAdjuster = new AdaptiveAdjuster(_settingsCalibrator);
                LogInfo("✅ AdaptiveAdjuster initialized");

                LogInfo("🔧 Initializing Phase 4 components (Multi-Timeframe Analysis)...");

                // Initialize Phase 4 components (Multi-Timeframe Analysis)
                _multiTimeframeAnalyzer = new MultiTimeframeAnalyzer(
                    volumeAnalyzer,
                    deltaAnalyzer,
                    volatilityAnalyzer,
                    marketProfileAnalyzer);
                LogInfo("✅ MultiTimeframeAnalyzer initialized with all Phase 1 analyzers");

                _signalSynthesizer = new SignalSynthesizer(LogInfo);
                _signalSynthesizer.Initialize();
                LogInfo("✅ SignalSynthesizer initialized with advanced synthesis capabilities");

                _enhancedSignalGenerator = new EnhancedSignalGenerator(
                    _signalGenerator,
                    _multiTimeframeAnalyzer,
                    _signalSynthesizer,
                    LogInfo);
                LogInfo("✅ EnhancedSignalGenerator initialized with multi-timeframe integration");

                LogInfo("🔧 Initializing Phase 2 components (Advanced Signal Synthesis & Institutional Detection)...");

                // Phase 2.1: Initialize Institutional Footprint Detector
                var advancedSynthesisConfig = new AdvancedSignalSynthesisConfig();
                _institutionalFootprintDetector = new InstitutionalFootprintDetector(advancedSynthesisConfig, LogInfo);
                LogInfo("✅ InstitutionalFootprintDetector initialized for smart money detection");

                // Phase 2.2: Initialize Advanced Pattern Recognizer
                _advancedPatternRecognizer = new AdvancedPatternRecognizer(advancedSynthesisConfig, LogInfo);
                LogInfo("✅ AdvancedPatternRecognizer initialized for institutional pattern detection");

                LogInfo("🎉 === ALL STRATEGY COMPONENTS INITIALIZED SUCCESSFULLY (INCLUDING PHASE 2) ===");
            }
            catch (Exception ex)
            {
                LogError($"❌ Failed to initialize components: {ex.Message}");
                throw;
            }
        }

        private ISettingsCalibrator CreateSettingsCalibrator()
        {
            // Initialize calibration dependencies
            var thresholdOptimizer = new ThresholdOptimizer();
            var riskCalibrator = new RiskCalibrator();
            var backtestValidator = new BacktestValidator();

            return new SettingsCalibrator(thresholdOptimizer, riskCalibrator, backtestValidator);
        }

        private MarketDataPoint ConvertToMarketDataPoint(int bar)
        {
            try
            {
                if (bar >= CurrentBar)
                    return null;

                var candle = GetCandle(bar);
                if (candle == null)
                    return null;

                return new MarketDataPoint
                {
                    Timestamp = candle.Time,
                    Open = candle.Open,
                    High = candle.High,
                    Low = candle.Low,
                    Close = candle.Close,
                    Volume = candle.Volume,
                    BuyVolume = candle.Volume * 0.6m, // Estimate - would use actual data if available
                    SellVolume = candle.Volume * 0.4m, // Estimate - would use actual data if available
                    BarIndex = bar
                };
            }
            catch (Exception ex)
            {
                LogError($"Error converting bar {bar} to MarketDataPoint: {ex.Message}");
                return null;
            }
        }

        private PositionInfo ConvertToPositionInfo(decimal atasPosition)
        {
            // CRITICAL FIX: Properly determine position state from ATAS CurrentPosition
            var hasPosition = Math.Abs(atasPosition) > 0.000001m; // Consider positions > 0.000001 as active

            if (!hasPosition)
            {
                return new PositionInfo
                {
                    HasPosition = false,
                    State = TradingState.Ready,
                    Direction = SignalType.Long, // Default
                    EntryPrice = 0,
                    Quantity = 0,
                    EntryTime = DateTime.UtcNow,
                    UnrealizedPnL = 0,
                    UnrealizedPnLPercent = 0,
                    CorrelationId = _correlationId
                };
            }

            // CRITICAL FIX: Create proper position info from actual ATAS position
            var direction = atasPosition > 0 ? SignalType.Long : SignalType.Short;
            var quantity = Math.Abs(atasPosition);

            // PNL FIX: Get actual entry price and calculate PnL
            var entryPrice = GetATASEntryPrice(); // Get real entry price from ATAS
            var currentPrice = GetCurrentPrice(); // Get current market price
            var unrealizedPnL = CalculateUnrealizedPnL(direction, quantity, entryPrice, currentPrice);
            var unrealizedPnLPercent = entryPrice > 0 ? (unrealizedPnL / (quantity * entryPrice)) * 100 : 0;

            return new PositionInfo
            {
                HasPosition = true,
                Direction = direction,
                EntryPrice = entryPrice,
                Quantity = quantity,
                EntryTime = _positionEntryTime != DateTime.MinValue ? _positionEntryTime : DateTime.UtcNow,
                UnrealizedPnL = unrealizedPnL,
                UnrealizedPnLPercent = unrealizedPnLPercent,
                CorrelationId = _correlationId,
                State = direction == SignalType.Long ? TradingState.InLongPosition : TradingState.InShortPosition
            };
        }

        /// <summary>
        /// PNL FIX: Get actual entry price from ATAS
        /// </summary>
        private decimal GetATASEntryPrice()
        {
            try
            {
                // Use tracked entry price if available
                if (_actualEntryPrice > 0 && Math.Abs(CurrentPosition) > 0.000001m)
                {
                    return _actualEntryPrice;
                }

                // Fallback: Try to get average entry price from ATAS
                if (Math.Abs(CurrentPosition) > 0.000001m && _marketDataHistory.Count > 0)
                {
                    // Use recent average price as approximation
                    var recentBars = _marketDataHistory.TakeLast(3).ToList();
                    var approximateEntry = recentBars.Average(x => (x.High + x.Low + x.Close) / 3);
                    LogInfo($"📊 PnL: Using approximate entry price {approximateEntry:F2} (no tracked entry)");
                    return approximateEntry;
                }

                return 0;
            }
            catch (Exception ex)
            {
                LogError($"❌ Error getting ATAS entry price: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// PNL FIX: Get current market price
        /// </summary>
        private decimal GetCurrentPrice()
        {
            try
            {
                if (_marketDataHistory.Count > 0)
                {
                    return _marketDataHistory.Last().Close;
                }
                return 0;
            }
            catch (Exception ex)
            {
                LogError($"❌ Error getting current price: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// PNL FIX: Calculate unrealized PnL
        /// </summary>
        private decimal CalculateUnrealizedPnL(SignalType direction, decimal quantity, decimal entryPrice, decimal currentPrice)
        {
            try
            {
                if (entryPrice <= 0 || currentPrice <= 0 || quantity <= 0)
                    return 0;

                decimal pnl = 0;
                if (direction == SignalType.Long)
                {
                    // Long position: PnL = (Current Price - Entry Price) * Quantity
                    pnl = (currentPrice - entryPrice) * quantity;
                }
                else if (direction == SignalType.Short)
                {
                    // Short position: PnL = (Entry Price - Current Price) * Quantity
                    pnl = (entryPrice - currentPrice) * quantity;
                }

                return pnl;
            }
            catch (Exception ex)
            {
                LogError($"❌ Error calculating unrealized PnL: {ex.Message}");
                return 0;
            }
        }

        private async Task PerformInitialCalibrationAsync()
        {
            try
            {
                if (_isCalibrated)
                    return;

                LogInfo($"🔬 === STARTING SYMBOL ANALYSIS ===");
                _statusMessage = "Performing symbol analysis...";
                _statusColor = Colors.Orange;

                var marketDataList = _marketDataHistory.ToList();
                LogInfo($"📊 Analyzing {marketDataList.Count} bars of market data");

                // Phase 1: Analyze symbol characteristics
                _symbolProfile = await _symbolAnalyzer.AnalyzeSymbolAsync(InstrumentInfo?.Instrument ?? "Unknown", marketDataList, CalibrationBars);
                LogInfo($"✅ Symbol analysis completed - Profile created successfully");

                LogInfo($"⚙️ === STARTING SETTINGS CALIBRATION ===");
                _statusMessage = "Calibrating optimal settings...";

                // Phase 2: Calibrate settings based on analysis
                var userConstraints = CreateUserConstraints();
                LogInfo($"📋 User constraints: MaxSize={userConstraints.MaxPositionSizeUSDT} USDT, Risk={userConstraints.MaxRiskPercentPerTrade:P1}, Conservative={userConstraints.ConservativeMode}");

                _calibrationResult = await _settingsCalibrator.CalibrateSettingsAsync(_symbolProfile, marketDataList, userConstraints);

                if (_calibrationResult.IsSuccessful)
                {
                    _currentSettings = _calibrationResult.Settings;
                    LogInfo($"✅ Calibration successful - VolumeThreshold: {_currentSettings.VolumeThreshold:F2}, SignalThreshold: {_currentSettings.SignalThreshold:F2}");

                    ApplyManualOverrides();

                    // Initialize RealTimeAnalysisEngine with calibrated settings
                    LogInfo("🔧 Initializing RealTimeAnalysisEngine with calibrated settings...");
                    _realTimeAnalysisEngine.Initialize(_symbolProfile, _currentSettings);
                    LogInfo("✅ RealTimeAnalysisEngine initialized with symbol profile and optimal settings");

                    // Initialize Phase 4 components with calibrated settings
                    LogInfo("🔧 Initializing Phase 4 components with calibrated settings...");
                    _multiTimeframeAnalyzer.Initialize(_symbolProfile, _currentSettings);
                    LogInfo("✅ MultiTimeframeAnalyzer initialized with calibrated settings");

                    _enhancedSignalGenerator.UpdateSettings(_currentSettings);
                    LogInfo("✅ EnhancedSignalGenerator updated with calibrated settings");

                    // Phase 3: Initialize AdaptiveCalibrator with calibrated settings
                    LogInfo("🔧 Initializing AdaptiveCalibrator with calibrated settings...");
                    _adaptiveCalibrator.Initialize(_symbolProfile, _currentSettings);
                    LogInfo("✅ AdaptiveCalibrator initialized and ready for adaptive recalibration");

                    _isCalibrated = true;
                    _lastCalibrationTime = DateTime.UtcNow;
                    _currentConfidence = _calibrationResult.OverallConfidence;

                    _statusMessage = $"Calibrated successfully ({_currentConfidence:P1} confidence)";
                    _statusColor = Colors.Green;

                    LogInfo($"🎉 === CALIBRATION COMPLETED SUCCESSFULLY ===");
                    LogInfo($"🚀 Overall Confidence: {_currentConfidence:P1} | Strategy ready for trading");
                }
                else
                {
                    _statusMessage = "Calibration failed - using default settings";
                    _statusColor = Colors.Red;
                    _currentSettings = new OptimalSettings(); // Use defaults

                    LogWarning("❌ CALIBRATION FAILED - Using default settings");
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ ERROR DURING CALIBRATION: {ex.Message}");
                _statusMessage = $"Calibration error: {ex.Message}";
                _statusColor = Colors.Red;
            }
        }

        private UserConstraints CreateUserConstraints()
        {
            return new UserConstraints
            {
                MaxPositionSizeUSDT = MaxPositionSizeUSDT,
                MaxRiskPercentPerTrade = RiskPercentPerTrade / 100m,
                ConservativeMode = ConservativeMode
            };
        }

        private void ApplyManualOverrides()
        {
            if (ManualVolumeThreshold > 0)
            {
                _currentSettings.VolumeThreshold = ManualVolumeThreshold;
                LogInfo($"⚙️ Applied manual volume threshold override: {ManualVolumeThreshold}");
            }

            if (ManualSignalThreshold > 0)
            {
                _currentSettings.SignalThreshold = ManualSignalThreshold;
                LogInfo($"⚙️ Applied manual signal threshold override: {ManualSignalThreshold}");
            }

            // Apply user-configured TP/SL settings
            _currentSettings.TakeProfitPercent = TakeProfitPercent;
            _currentSettings.StopLossPercent = StopLossPercent;
            LogInfo($"⚙️ Applied TP/SL settings: TP={TakeProfitPercent}%, SL={StopLossPercent}%");
        }

        private void ProcessTradingLogic(MarketDataPoint currentBar)
        {
            try
            {
                // Step 1: Detect volume blocks
                var volumeBlock = _volumeBlockDetector.DetectVolumeBlock(
                    currentBar,
                    _marketDataHistory.ToList(),
                    _currentSettings);

                // Log volume block detection with detailed diagnostics
                if (volumeBlock != null)
                {
                    LogInfo($"📊 VOLUME BLOCK DETECTED:");
                    LogInfo($"  • IsVolumeBlock: {volumeBlock.IsVolumeBlock}");
                    LogInfo($"  • CumulativeImpact: {volumeBlock.CumulativeImpact:F6}");
                    LogInfo($"  • VolumeRatio: {volumeBlock.VolumeRatio:F2}");
                    LogInfo($"  • VolumeThreshold (original): {_currentSettings.VolumeThreshold:F2}");
                    LogInfo($"  • VolumeThreshold (adjusted): {Math.Min(_currentSettings.VolumeThreshold, 0.05m):F2}");
                    LogInfo($"  • Volume Check: {volumeBlock.VolumeRatio:F2} >= {Math.Min(_currentSettings.VolumeThreshold, 0.05m):F2} = {volumeBlock.VolumeRatio >= Math.Min(_currentSettings.VolumeThreshold, 0.05m)}");
                    LogInfo($"  • Confidence: {volumeBlock.Confidence:F2}");
                    LogInfo($"  • Type: {volumeBlock.Type}");
                    LogInfo($"  • Description: {volumeBlock.Description}");
                }
                else
                {
                    LogInfo($"📊 NO VOLUME BLOCK: Detection returned null");
                }

                // Step 2: Create market context
                var marketContext = CreateMarketContext(currentBar);

                // Step 3: Generate enhanced trading signal using Phase 4 multi-timeframe analysis
                var signal = _enhancedSignalGenerator != null ?
                    _enhancedSignalGenerator.GenerateSignal(currentBar, volumeBlock, marketContext, _currentSettings) :
                    _signalGenerator.GenerateSignal(currentBar, volumeBlock, marketContext, _currentSettings);

                // CRITICAL DIAGNOSTIC: Log detailed signal generation analysis
                var signalSource = _enhancedSignalGenerator != null ? "ENHANCED (Phase 4)" : "BASIC (Phase 1)";
                LogInfo($"🎯 SIGNAL ANALYSIS ({signalSource}):");
                LogInfo($"  • Signal Type: {signal.Type}");
                LogInfo($"  • Signal Action: {signal.Action}");
                LogInfo($"  • Signal Confidence: {signal.Confidence:F2}");
                LogInfo($"  • Signal Quality: {signal.Quality}");
                LogInfo($"  • Primary Reason: {signal.PrimaryReason}");

                // Log multi-timeframe analysis if available
                if (_enhancedSignalGenerator != null && _multiTimeframeAnalyzer != null)
                {
                    var mtfState = _multiTimeframeAnalyzer.GetCurrentAnalysisState();
                    LogInfo($"  • Multi-Timeframe Consistency: {mtfState.IsTimeframeConsistent}");
                    LogInfo($"  • Trend Alignment: {mtfState.TrendAlignment:P1}");
                    LogInfo($"  • Active Timeframes: {mtfState.TimeframeStates.Count}");
                }

                if (volumeBlock != null)
                {
                    // CRITICAL FIX: Use the same adjusted threshold logic as SignalGenerator
                    var adjustedThreshold = _currentSettings.SignalThreshold * 0.01m;
                    LogInfo($"  • Volume Block CumulativeImpact: {volumeBlock.CumulativeImpact:F6}");
                    LogInfo($"  • Settings SignalThreshold (original): {_currentSettings.SignalThreshold:F6}");
                    LogInfo($"  • Settings SignalThreshold (adjusted): {adjustedThreshold:F6}");
                    LogInfo($"  • Threshold Check: {Math.Abs(volumeBlock.CumulativeImpact):F6} >= {adjustedThreshold:F6} = {Math.Abs(volumeBlock.CumulativeImpact) >= adjustedThreshold}");
                    LogInfo($"  • IsVolumeBlock: {volumeBlock.IsVolumeBlock}");
                }

                if (signal.Action == SignalAction.Entry)
                {
                    LogInfo($"🎯 ENTRY SIGNAL GENERATED: {signal.Type} | Confidence: {signal.Confidence:P1} | Price: {currentBar.Close:F2}");
                }
                else
                {
                    LogInfo($"⏸️ NO ENTRY SIGNAL: Action={signal.Action}, Type={signal.Type}");
                }

                // Record signal for performance tracking
                _performanceTracker.RecordSignal(signal, false); // Will be updated if executed

                // Step 4: Check if we can open a position - CRITICAL FIX: Use ATAS actual position
                if (signal.Action == SignalAction.Entry)
                {
                    LogInfo($"🔍 POSITION CHECK: Entry signal detected, checking if position can be opened");

                    // CRITICAL FIX: Check ATAS actual position and synchronize PositionManager state
                    var actualATASPosition = CurrentPosition;
                    var hasActualPosition = Math.Abs(actualATASPosition) > 0.000001m; // Consider positions > 0.000001 as active

                    // Synchronize PositionManager state with ATAS before checking
                    _positionManager.SynchronizeWithATAS(actualATASPosition);

                    // ADDITIONAL FIX: Force update position info to ensure synchronization
                    var currentPositionInfo = ConvertToPositionInfo(actualATASPosition);
                    _positionManager.UpdatePosition(currentPositionInfo);

                    LogInfo($"  • ATAS Actual Position: {actualATASPosition:F6}");
                    LogInfo($"  • Has Active Position: {hasActualPosition}");
                    LogInfo($"  • PositionManager State: {_positionManager.GetCurrentPosition().HasPosition}");
                    LogInfo($"  • Position Info HasPosition: {currentPositionInfo.HasPosition}");

                    if (hasActualPosition)
                    {
                        LogInfo($"❌ SIGNAL BLOCKED: Cannot open position - ATAS already has active position ({actualATASPosition:F6})");
                    }
                    else
                    {
                        // DETAILED CONSTRAINT ANALYSIS - Check each constraint individually
                        LogInfo($"🔍 DETAILED POSITION MANAGER CONSTRAINT ANALYSIS:");
                        LogInfo($"  📊 Current State Analysis:");
                        LogInfo($"    • HasPosition: {_positionManager.GetCurrentPosition().HasPosition}");
                        LogInfo($"    • State: {_positionManager.GetCurrentPosition().State}");
                        LogInfo($"    • Signal Quality: {signal.Quality} (Required: >= {MinimumSignalQuality})");
                        LogInfo($"    • Signal Confidence: {signal.Confidence:F3} ({signal.Confidence * 100:F1}%) (Required: >= {MinimumSignalConfidence}%)");
                        LogInfo($"    • Signal Action: {signal.Action}");
                        LogInfo($"    • Signal Type: {signal.Type}");
                        LogInfo($"    • Cooldown Setting: {PositionCooldownSeconds}s");

                        // Test each constraint individually
                        var currentPosition = _positionManager.GetCurrentPosition();

                        // Constraint 1: Position limit
                        if (currentPosition.HasPosition)
                        {
                            LogInfo($"  ❌ CONSTRAINT 1 FAILED: Already has position");
                            LogInfo($"❌ SIGNAL BLOCKED: Already has active position");
                        }
                        else
                        {
                            LogInfo($"  ✅ CONSTRAINT 1 PASSED: No existing position");

                            // Constraint 2: Strategy state
                            if (currentPosition.State != TradingState.Ready)
                            {
                                LogInfo($"  ❌ CONSTRAINT 2 FAILED: Strategy not in Ready state");
                                LogInfo($"    • Current state: {currentPosition.State} (Required: Ready)");
                                LogInfo($"❌ SIGNAL BLOCKED: Strategy not ready");
                            }
                            else
                            {
                                LogInfo($"  ✅ CONSTRAINT 2 PASSED: Strategy state is Ready");

                                // Constraint 3: Signal quality
                                if (signal.Quality < MinimumSignalQuality)
                                {
                                    LogInfo($"  ❌ CONSTRAINT 3 FAILED: Signal quality too low");
                                    LogInfo($"    • Signal quality: {signal.Quality} (Required: >= {MinimumSignalQuality})");
                                    LogInfo($"❌ SIGNAL BLOCKED: Signal quality insufficient");
                                }
                                else
                                {
                                    LogInfo($"  ✅ CONSTRAINT 3 PASSED: Signal quality sufficient ({signal.Quality} >= {MinimumSignalQuality})");

                                    // Constraint 3.5: Multi-Timeframe Confirmation (Phase 1.1 Enhancement)
                                    var mtfValidation = ValidateMultiTimeframeConsensus(signal);
                                    if (!mtfValidation.IsValid)
                                    {
                                        LogInfo($"  ❌ CONSTRAINT 3.5 FAILED: Multi-timeframe confirmation insufficient");
                                        LogInfo($"    • Agreeing timeframes: {mtfValidation.AgreeingTimeframes} (Required: >= 2)");
                                        LogInfo($"    • Trend alignment: {mtfValidation.TrendAlignment:P1} (Required: >= 60%)");
                                        LogInfo($"    • Details: {mtfValidation.Details}");
                                        LogInfo($"❌ SIGNAL BLOCKED: Multi-timeframe consensus failed");
                                    }
                                    else
                                    {
                                        LogInfo($"  ✅ CONSTRAINT 3.5 PASSED: Multi-timeframe confirmation sufficient");
                                        LogInfo($"    • Agreeing timeframes: {mtfValidation.AgreeingTimeframes}/3 (Required: >= 2)");
                                        LogInfo($"    • Trend alignment: {mtfValidation.TrendAlignment:P1} (Required: >= 60%)");
                                        LogInfo($"    • Supporting timeframes: {mtfValidation.SupportingTimeframes}");

                                        // Constraint 3.6: Volume Profile Validation (Phase 1.3 Enhancement)
                                        var volumeProfileValidation = ValidateInstitutionalLevels(signal, currentBar);
                                        if (!volumeProfileValidation.IsValid)
                                        {
                                            LogInfo($"  ❌ CONSTRAINT 3.6 FAILED: Volume profile validation insufficient");
                                            LogInfo($"    • Institutional levels found: {volumeProfileValidation.InstitutionalLevelsFound}");
                                            LogInfo($"    • Nearby volume nodes: {volumeProfileValidation.NearbyVolumeNodes}");
                                            LogInfo($"    • Validation reason: {volumeProfileValidation.ValidationReason}");
                                            LogInfo($"❌ SIGNAL BLOCKED: No institutional volume support");
                                        }
                                        else
                                        {
                                            LogInfo($"  ✅ CONSTRAINT 3.6 PASSED: Volume profile validation sufficient");
                                            LogInfo($"    • Institutional levels found: {volumeProfileValidation.InstitutionalLevelsFound}");
                                            LogInfo($"    • Supporting levels: {string.Join(", ", volumeProfileValidation.SupportingLevels.Select(l => l.ToString("F2")))}");
                                            LogInfo($"    • Validation reason: {volumeProfileValidation.ValidationReason}");

                                            // Constraint 3.7: Institutional Footprint Validation (Phase 2.1 Enhancement)
                                            var institutionalFootprintValidation = ValidateInstitutionalFootprint(signal, currentBar);
                                            if (!institutionalFootprintValidation.IsValid)
                                            {
                                                LogInfo($"  ❌ CONSTRAINT 3.7 FAILED: Institutional footprint validation insufficient");
                                                LogInfo($"    • Institutional footprints found: {institutionalFootprintValidation.InstitutionalFootprintsFound}");
                                                LogInfo($"    • Footprint types: {institutionalFootprintValidation.FootprintTypes}");
                                                LogInfo($"    • Validation reason: {institutionalFootprintValidation.ValidationReason}");
                                                LogInfo($"❌ SIGNAL BLOCKED: No institutional footprint support");
                                            }
                                            else
                                            {
                                                LogInfo($"  ✅ CONSTRAINT 3.7 PASSED: Institutional footprint validation sufficient");
                                                LogInfo($"    • Institutional footprints found: {institutionalFootprintValidation.InstitutionalFootprintsFound}");
                                                LogInfo($"    • Supporting footprints: {string.Join(", ", institutionalFootprintValidation.SupportingFootprints)}");
                                                LogInfo($"    • Validation reason: {institutionalFootprintValidation.ValidationReason}");

                                                // Constraint 3.8: Advanced Pattern Recognition (Phase 2.2 Enhancement)
                                                var patternRecognitionValidation = ValidateAdvancedPatterns(signal, currentBar);
                                                if (!patternRecognitionValidation.IsValid)
                                                {
                                                    LogInfo($"  ❌ CONSTRAINT 3.8 FAILED: Pattern recognition validation insufficient");
                                                    LogInfo($"    • Patterns found: {patternRecognitionValidation.PatternsFound}");
                                                    LogInfo($"    • Pattern types: {patternRecognitionValidation.PatternTypes}");
                                                    LogInfo($"    • Validation reason: {patternRecognitionValidation.ValidationReason}");
                                                    LogInfo($"❌ SIGNAL BLOCKED: No supporting pattern recognition");
                                                }
                                                else
                                                {
                                                    LogInfo($"  ✅ CONSTRAINT 3.8 PASSED: Pattern recognition validation sufficient");
                                                    LogInfo($"    • Patterns found: {patternRecognitionValidation.PatternsFound}");
                                                    LogInfo($"    • Supporting patterns: {string.Join(", ", patternRecognitionValidation.SupportingPatterns)}");
                                                    LogInfo($"    • Validation reason: {patternRecognitionValidation.ValidationReason}");

                                                    // Constraint 4: Signal confidence (Phase 1.2: Adaptive Thresholds)
                                                    var currentMarketContext = CreateMarketContext(currentBar);
                                                    var adaptiveConfidenceThreshold = CalculateAdaptiveConfidenceThreshold(currentMarketContext);

                                                    // ETHUSDT FIX: Apply additional adjustment for ETHUSDT if needed
                                                    var effectiveConfidenceThreshold = adaptiveConfidenceThreshold;
                                                    if (Instrument?.Contains("ETHUSDT") == true)
                                                    {
                                                        var ethUsdtAdjustment = Math.Max(50m, adaptiveConfidenceThreshold - 15m);
                                                        LogInfo($"🔧 ETHUSDT: Additional adjustment from {adaptiveConfidenceThreshold:F1}% to {ethUsdtAdjustment:F1}% due to component health issues");
                                                        effectiveConfidenceThreshold = ethUsdtAdjustment;
                                                    }

                                                    var requiredConfidenceDecimal = effectiveConfidenceThreshold / 100m;
                                                    if (signal.Confidence < requiredConfidenceDecimal)
                                                    {
                                                        LogInfo($"  ❌ CONSTRAINT 4 FAILED: Signal confidence too low");
                                                        LogInfo($"    • Signal confidence: {signal.Confidence:F3} ({signal.Confidence * 100:F1}%) (Required: >= {effectiveConfidenceThreshold}%)");
                                                        LogInfo($"❌ SIGNAL BLOCKED: Signal confidence insufficient");
                                                    }
                                                    else
                                                    {
                                                        LogInfo($"  ✅ CONSTRAINT 4 PASSED: Signal confidence sufficient ({signal.Confidence * 100:F1}% >= {effectiveConfidenceThreshold}%)");

                                                        // Constraint 5: Cooldown period - This is likely the issue!
                                                        var canOpen = _positionManager.CanOpenPosition(signal, currentBar,
                                                            MinimumSignalQuality, effectiveConfidenceThreshold, PositionCooldownSeconds);

                                                        if (!canOpen)
                                                        {
                                                            LogInfo($"  ❌ CONSTRAINT 5 FAILED: Cooldown period active or other constraint");
                                                            LogInfo($"    • Cooldown setting: {PositionCooldownSeconds}s");
                                                            LogInfo($"❌ SIGNAL BLOCKED: Cooldown period or other constraint failed");
                                                        }
                                                        else
                                                        {
                                                            LogInfo($"  ✅ ALL CONSTRAINTS PASSED: Position can be opened!");
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // Phase 1.2: Use adaptive threshold for final position manager check
                        var finalMarketContext = CreateMarketContext(currentBar);
                        var finalAdaptiveThreshold = CalculateAdaptiveConfidenceThreshold(finalMarketContext);

                        // ETHUSDT FIX: Apply additional adjustment for ETHUSDT if needed
                        var adjustedConfidenceThreshold = finalAdaptiveThreshold;
                        if (Instrument?.Contains("ETHUSDT") == true)
                        {
                            adjustedConfidenceThreshold = Math.Max(50m, finalAdaptiveThreshold - 15m); // Lower by 15% for ETHUSDT
                            LogInfo($"🔧 ETHUSDT: Final adjusted confidence threshold from {finalAdaptiveThreshold:F1}% to {adjustedConfidenceThreshold:F1}% due to component health issues");

                            // Reset component health to help recovery from failing components
                            TryResetComponentHealth();
                        }

                        var finalCanOpen = _positionManager.CanOpenPosition(signal, currentBar,
                            MinimumSignalQuality, adjustedConfidenceThreshold, PositionCooldownSeconds);
                        LogInfo($"  • Final PositionManager Result: {finalCanOpen}");

                        if (finalCanOpen)
                        {
                            LogInfo($"✅ EXECUTING SIGNAL: {signal.Type} at {currentBar.Close:F2}");
                            ExecuteSignal(signal, currentBar);
                        }
                        else
                        {
                            LogInfo($"❌ SIGNAL BLOCKED: PositionManager constraints failed (signal quality, cooldown, etc.)");
                        }
                    }
                }
                else
                {
                    LogInfo($"⏭️ SKIPPING POSITION CHECK: Signal action is {signal.Action}, not Entry");
                }

                // Step 5: Check for exit conditions if we have a position - CRITICAL FIX: Use ATAS actual position
                var currentATASPosition = CurrentPosition;
                var hasActivePosition = Math.Abs(currentATASPosition) > 0.000001m; // Use same threshold as other checks

                if (hasActivePosition)
                {
                    // Ensure PositionManager is synchronized before generating exit signals
                    _positionManager.SynchronizeWithATAS(currentATASPosition);
                    var currentPositionInfo = ConvertToPositionInfo(currentATASPosition);
                    _positionManager.UpdatePosition(currentPositionInfo);

                    var currentPosition = _positionManager.GetCurrentPosition();
                    var exitSignal = _signalGenerator.GenerateExitSignal(currentBar, currentPosition, _currentSettings);
                    if (exitSignal.Action == SignalAction.Exit)
                    {
                        LogInfo($"🚪 EXIT SIGNAL: {exitSignal.PrimaryReason} | ATAS Position: {currentATASPosition:F6} | Current PnL: {currentPosition.UnrealizedPnL:F2} USDT");
                        ExecuteExitSignal(exitSignal, currentBar);
                    }
                }

                // Add signal to recent signals for logging
                _recentSignals.Add(signal);
                while (_recentSignals.Count > 20) // Keep last 20 signals
                {
                    _recentSignals.RemoveAt(0);
                }
            }
            catch (Exception ex)
            {
                LogError($"❌ Error in trading logic: {ex.Message}");
            }
        }

        /// <summary>
        /// Phase 1.2: Adaptive Signal Threshold Calculation
        /// Dynamically adjusts confidence thresholds based on volatility regime and trading session
        /// </summary>
        private decimal CalculateAdaptiveConfidenceThreshold(MarketContext marketContext)
        {
            try
            {
                var baseThreshold = MinimumSignalConfidence;
                LogInfo($"🎯 ADAPTIVE THRESHOLD CALCULATION:");
                LogInfo($"  • Base threshold: {baseThreshold}%");

                // Phase 1.2: Volatility-based adjustment
                var volatilityMultiplier = marketContext.VolatilityRegime switch
                {
                    VolatilityRegime.VeryLow => 0.8m,    // Lower threshold in calm markets
                    VolatilityRegime.Low => 0.9m,
                    VolatilityRegime.Normal => 1.0m,     // Baseline
                    VolatilityRegime.High => 1.2m,       // Higher threshold in volatile markets
                    VolatilityRegime.VeryHigh => 1.4m,
                    _ => 1.0m // Fallback to baseline
                };

                LogInfo($"  • Volatility regime: {marketContext.VolatilityRegime} → Multiplier: {volatilityMultiplier:F2}x");

                // Phase 1.2: Session-based adjustment
                var sessionMultiplier = marketContext.CurrentSession switch
                {
                    TradingSession.Asian => 1.1m,     // Higher threshold in low-volume sessions
                    TradingSession.European => 0.95m,
                    TradingSession.US => 0.9m,        // Lower threshold in high-volume sessions
                    TradingSession.Crypto24H => 1.0m,
                    _ => 1.0m // Fallback to baseline
                };

                LogInfo($"  • Trading session: {marketContext.CurrentSession} → Multiplier: {sessionMultiplier:F2}x");

                // Calculate adaptive threshold with bounds checking
                var adaptiveThreshold = baseThreshold * volatilityMultiplier * sessionMultiplier;

                // Phase 1.2: Ensure threshold never exceeds 95% to prevent overly restrictive filtering
                var finalThreshold = Math.Min(95m, adaptiveThreshold);

                LogInfo($"  • Calculated threshold: {baseThreshold}% × {volatilityMultiplier:F2} × {sessionMultiplier:F2} = {adaptiveThreshold:F1}%");
                LogInfo($"  • Final adaptive threshold: {finalThreshold:F1}% (max 95%)");

                return finalThreshold;
            }
            catch (Exception ex)
            {
                LogError($"❌ Adaptive threshold calculation error: {ex.Message}");
                LogInfo($"🔄 Falling back to base threshold: {MinimumSignalConfidence}%");
                return MinimumSignalConfidence; // Fallback to static threshold
            }
        }

        /// <summary>
        /// Phase 1.3: Volume Profile Analysis Integration
        /// Validates that signals align with significant institutional volume levels within 0.2% of current price
        /// </summary>
        private VolumeProfileValidationResult ValidateInstitutionalLevels(TradingSignal signal, MarketDataPoint currentBar)
        {
            try
            {
                var currentPrice = currentBar.Close;
                LogInfo($"🏛️ VOLUME PROFILE VALIDATION:");
                LogInfo($"  • Current price: {currentPrice:F2}");
                LogInfo($"  • Signal type: {signal.Type}");

                // Check if volume profile analyzer is available
                if (_volumeProfileAnalyzer == null)
                {
                    LogInfo($"⚠️ Volume profile analyzer not available - using fallback validation");
                    return new VolumeProfileValidationResult
                    {
                        IsValid = true, // Allow signal if volume profile not available (backward compatibility)
                        InstitutionalLevelsFound = 0,
                        NearbyVolumeNodes = "Volume profile analyzer not initialized",
                        ValidationReason = "Fallback validation - volume profile analyzer not available",
                        SupportingLevels = new List<decimal>()
                    };
                }

                // Get current volume analysis state (using available VolumePatternAnalyzer)
                var recentData = _marketDataHistory.TakeLast(20).ToList();
                var volumeState = _volumeProfileAnalyzer.GetCurrentVolumeState(recentData);

                if (volumeState == null || recentData.Count < 10)
                {
                    LogInfo($"⚠️ Volume analysis data not available - using fallback validation");
                    return new VolumeProfileValidationResult
                    {
                        IsValid = true, // Allow signal if volume analysis data not available
                        InstitutionalLevelsFound = 0,
                        NearbyVolumeNodes = "Volume analysis data not available",
                        ValidationReason = "Fallback validation - volume analysis data not available",
                        SupportingLevels = new List<decimal>()
                    };
                }

                // Phase 1.3: Analyze volume patterns for institutional activity validation
                LogInfo($"  • Volume analysis state: {volumeState.CurrentVolumePattern}");
                LogInfo($"  • Current volume ratio: {volumeState.CurrentVolumeRatio:F2}x average");
                LogInfo($"  • Average volume: {volumeState.AverageVolume:F0}");
                LogInfo($"  • Volume regime: {volumeState.CurrentRegime}");
                LogInfo($"  • Volume spike active: {volumeState.IsVolumeSpikeActive}");

                // Phase 1.3: Check for institutional-level volume activity
                // Criteria: High volume ratio (>2x), volume spike active, or high volume regime
                var institutionalThreshold = 2.0m; // 2x average volume indicates institutional activity
                var hasInstitutionalVolume = volumeState.CurrentVolumeRatio >= institutionalThreshold ||
                                           volumeState.IsVolumeSpikeActive ||
                                           volumeState.CurrentRegime == VolumeRegime.High ||
                                           volumeState.CurrentRegime == VolumeRegime.VeryHigh;

                // Additional validation: Check recent volume consistency for institutional footprint
                var recentVolumeRatios = recentData.TakeLast(5)
                    .Select(bar => volumeState.AverageVolume > 0 ? bar.Volume / volumeState.AverageVolume : 1m)
                    .ToList();

                var sustainedHighVolume = recentVolumeRatios.Count(ratio => ratio >= 1.5m) >= 3; // 3 out of 5 bars with 1.5x+ volume
                var institutionalLevelsFound = hasInstitutionalVolume ? 1 : 0;
                if (sustainedHighVolume) institutionalLevelsFound++;

                LogInfo($"  • Institutional threshold: {institutionalThreshold:F1}x average");
                LogInfo($"  • Has institutional volume: {hasInstitutionalVolume}");
                LogInfo($"  • Sustained high volume (3/5 bars >1.5x): {sustainedHighVolume}");
                LogInfo($"  • Recent volume ratios: [{string.Join(", ", recentVolumeRatios.Select(r => r.ToString("F1")))}]");

                // Phase 1.3: Validation criteria - institutional volume activity detected
                var isValid = hasInstitutionalVolume || sustainedHighVolume;
                var supportingLevels = isValid ? new List<decimal> { currentPrice } : new List<decimal>();

                var result = new VolumeProfileValidationResult
                {
                    IsValid = isValid,
                    InstitutionalLevelsFound = institutionalLevelsFound,
                    NearbyVolumeNodes = $"Volume ratio: {volumeState.CurrentVolumeRatio:F2}x, Regime: {volumeState.CurrentRegime}",
                    ValidationReason = isValid
                        ? $"Institutional volume activity detected: {(hasInstitutionalVolume ? "High volume/spike" : "")} {(sustainedHighVolume ? "Sustained activity" : "")}"
                        : "No institutional volume activity detected",
                    SupportingLevels = supportingLevels
                };

                LogInfo($"🏛️ Volume profile validation: {(isValid ? "PASSED" : "FAILED")} - {result.ValidationReason}");
                return result;
            }
            catch (Exception ex)
            {
                LogError($"❌ Volume profile validation error: {ex.Message}");
                // Fallback to allow signal on error (backward compatibility)
                return new VolumeProfileValidationResult
                {
                    IsValid = true,
                    InstitutionalLevelsFound = 0,
                    NearbyVolumeNodes = $"Validation error: {ex.Message}",
                    ValidationReason = "Error fallback - allowing signal",
                    SupportingLevels = new List<decimal>()
                };
            }
        }

        /// <summary>
        /// Phase 2.1: Institutional Footprint Detection Validation
        /// Validates that signals align with detected institutional trading activity
        /// </summary>
        private InstitutionalFootprintValidationResult ValidateInstitutionalFootprint(TradingSignal signal, MarketDataPoint currentBar)
        {
            try
            {
                LogInfo($"🏛️ INSTITUTIONAL FOOTPRINT VALIDATION:");
                LogInfo($"  • Current price: {currentBar.Close:F2}");
                LogInfo($"  • Signal type: {signal.Type}");

                // Check if institutional footprint detector is available
                if (_institutionalFootprintDetector == null)
                {
                    LogInfo($"⚠️ Institutional footprint detector not available - using fallback validation");
                    return new InstitutionalFootprintValidationResult
                    {
                        IsValid = true, // Allow signal if detector not available (backward compatibility)
                        InstitutionalFootprintsFound = 0,
                        FootprintTypes = "Detector not initialized",
                        ValidationReason = "Fallback validation - institutional detector not available",
                        SupportingFootprints = new List<string>()
                    };
                }

                // Get multi-timeframe analysis state for institutional detection
                var multiTimeframeState = _multiTimeframeAnalyzer?.GetCurrentAnalysisState();
                if (multiTimeframeState == null)
                {
                    LogInfo($"⚠️ Multi-timeframe analysis not available - using fallback validation");
                    return new InstitutionalFootprintValidationResult
                    {
                        IsValid = true, // Allow signal if analysis not available
                        InstitutionalFootprintsFound = 0,
                        FootprintTypes = "Multi-timeframe analysis not available",
                        ValidationReason = "Fallback validation - multi-timeframe analysis not available",
                        SupportingFootprints = new List<string>()
                    };
                }

                // Create timeframe signals for institutional detection
                var timeframeSignals = new Dictionary<Timeframe, TradingSignal>
                {
                    { Timeframe.M1, signal },
                    { Timeframe.M5, signal },
                    { Timeframe.M15, signal }
                };

                // Create timeframe analysis states
                var timeframeAnalysisStates = new Dictionary<Timeframe, AnalysisState>();
                foreach (var timeframe in timeframeSignals.Keys)
                {
                    var analysisState = multiTimeframeState.TimeframeStates.ContainsKey(timeframe)
                        ? multiTimeframeState.TimeframeStates[timeframe]
                        : new AnalysisState();
                    timeframeAnalysisStates[timeframe] = analysisState;
                }

                // Create market context
                var marketContext = CreateMarketContext(currentBar);

                // Phase 2.1: Detect institutional footprints
                var institutionalFootprints = _institutionalFootprintDetector.DetectInstitutionalFootprints(
                    timeframeSignals, timeframeAnalysisStates, marketContext);

                LogInfo($"  • Institutional footprints detected: {institutionalFootprints.Count}");

                if (institutionalFootprints.Any())
                {
                    foreach (var footprint in institutionalFootprints.Take(3)) // Show top 3
                    {
                        LogInfo($"    🏛️ {footprint.ActivityType}: Confidence {footprint.Confidence:P1}, Strength {footprint.Strength:F2}");
                    }
                }

                // Phase 2.1: Validation criteria - institutional activity detected with sufficient confidence
                var significantFootprints = institutionalFootprints
                    .Where(f => f.Confidence >= 0.6m && f.Strength >= 0.5m)
                    .ToList();

                var isValid = significantFootprints.Any();
                var supportingFootprints = significantFootprints
                    .Select(f => $"{f.ActivityType} ({f.Confidence:P0})")
                    .ToList();

                var result = new InstitutionalFootprintValidationResult
                {
                    IsValid = isValid,
                    InstitutionalFootprintsFound = significantFootprints.Count,
                    FootprintTypes = string.Join(", ", significantFootprints.Select(f => f.ActivityType.ToString())),
                    ValidationReason = isValid
                        ? $"Institutional activity detected: {string.Join(", ", supportingFootprints)}"
                        : "No significant institutional footprints detected",
                    SupportingFootprints = supportingFootprints
                };

                LogInfo($"🏛️ Institutional footprint validation: {(isValid ? "PASSED" : "FAILED")} - {result.ValidationReason}");
                return result;
            }
            catch (Exception ex)
            {
                LogError($"❌ Institutional footprint validation error: {ex.Message}");
                // Fallback to allow signal on error (backward compatibility)
                return new InstitutionalFootprintValidationResult
                {
                    IsValid = true,
                    InstitutionalFootprintsFound = 0,
                    FootprintTypes = $"Validation error: {ex.Message}",
                    ValidationReason = "Error fallback - allowing signal",
                    SupportingFootprints = new List<string>()
                };
            }
        }

        /// <summary>
        /// Phase 2.2: Advanced Pattern Recognition Validation
        /// Validates that signals align with detected institutional and algorithmic patterns
        /// </summary>
        private PatternRecognitionValidationResult ValidateAdvancedPatterns(TradingSignal signal, MarketDataPoint currentBar)
        {
            try
            {
                LogInfo($"🔍 ADVANCED PATTERN RECOGNITION VALIDATION:");
                LogInfo($"  • Current price: {currentBar.Close:F2}");
                LogInfo($"  • Signal type: {signal.Type}");

                // Check if advanced pattern recognizer is available
                if (_advancedPatternRecognizer == null)
                {
                    LogInfo($"⚠️ Advanced pattern recognizer not available - using fallback validation");
                    return new PatternRecognitionValidationResult
                    {
                        IsValid = true, // Allow signal if recognizer not available (backward compatibility)
                        PatternsFound = 0,
                        PatternTypes = "Recognizer not initialized",
                        ValidationReason = "Fallback validation - pattern recognizer not available",
                        SupportingPatterns = new List<string>()
                    };
                }

                // Get recent market data for pattern analysis
                var recentData = _marketDataHistory.TakeLast(50).ToList();
                if (recentData.Count < 20)
                {
                    LogInfo($"⚠️ Insufficient market data for pattern analysis - using fallback validation");
                    return new PatternRecognitionValidationResult
                    {
                        IsValid = true, // Allow signal if insufficient data
                        PatternsFound = 0,
                        PatternTypes = "Insufficient market data",
                        ValidationReason = "Fallback validation - insufficient market data",
                        SupportingPatterns = new List<string>()
                    };
                }

                // Create market context for pattern recognition
                var marketContext = CreateMarketContext(currentBar);

                // Phase 2.2: Recognize institutional and algorithmic patterns
                // Create timeframe signals for pattern recognition
                var timeframeSignals = new Dictionary<Timeframe, TradingSignal>
                {
                    { Timeframe.M1, signal },
                    { Timeframe.M5, signal },
                    { Timeframe.M15, signal }
                };

                // Get cross-timeframe patterns (empty for now, can be enhanced later)
                var crossTimeframePatterns = new List<CrossTimeframePattern>();

                // Get institutional footprints (empty for now, will be populated by institutional detector)
                var institutionalFootprints = new List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint>();

                var recognizedPatterns = _advancedPatternRecognizer.RecognizeAdvancedPatterns(
                    timeframeSignals, crossTimeframePatterns, institutionalFootprints, marketContext);

                LogInfo($"  • Patterns recognized: {recognizedPatterns.Count}");

                if (recognizedPatterns.Any())
                {
                    foreach (var pattern in recognizedPatterns.Take(3)) // Show top 3
                    {
                        LogInfo($"    🔍 {pattern.PatternName}: Confidence {pattern.Confidence:P1}, Strength {pattern.Strength:F2}");
                    }
                }

                // Phase 2.2: Validation criteria - patterns support the signal direction
                var supportingPatterns = recognizedPatterns
                    .Where(p => p.Confidence >= 0.6m &&
                               p.Strength >= 0.5m &&
                               IsPatternSupportingSignal(p, signal))
                    .ToList();

                var isValid = supportingPatterns.Any();
                var supportingPatternNames = supportingPatterns
                    .Select(p => $"{p.PatternName} ({p.Confidence:P0})")
                    .ToList();

                var result = new PatternRecognitionValidationResult
                {
                    IsValid = isValid,
                    PatternsFound = supportingPatterns.Count,
                    PatternTypes = string.Join(", ", supportingPatterns.Select(p => p.PatternName)),
                    ValidationReason = isValid
                        ? $"Supporting patterns detected: {string.Join(", ", supportingPatternNames)}"
                        : "No supporting patterns detected for signal direction",
                    SupportingPatterns = supportingPatternNames
                };

                LogInfo($"🔍 Pattern recognition validation: {(isValid ? "PASSED" : "FAILED")} - {result.ValidationReason}");
                return result;
            }
            catch (Exception ex)
            {
                LogError($"❌ Pattern recognition validation error: {ex.Message}");
                // Fallback to allow signal on error (backward compatibility)
                return new PatternRecognitionValidationResult
                {
                    IsValid = true,
                    PatternsFound = 0,
                    PatternTypes = $"Validation error: {ex.Message}",
                    ValidationReason = "Error fallback - allowing signal",
                    SupportingPatterns = new List<string>()
                };
            }
        }

        /// <summary>
        /// Helper method to determine if a pattern supports the signal direction
        /// </summary>
        private bool IsPatternSupportingSignal(AdvancedPattern pattern, TradingSignal signal)
        {
            // Check if pattern direction aligns with signal direction
            // For AdvancedPattern, use ExpectedDirection property
            if (pattern.ExpectedDirection == SignalType.Long && signal.Type == SignalType.Long)
                return true;
            if (pattern.ExpectedDirection == SignalType.Short && signal.Type == SignalType.Short)
                return true;

            // Check pattern category for additional validation
            switch (pattern.Category)
            {
                case AdvancedPatternCategory.Institutional:
                    // Institutional patterns require higher confidence
                    return pattern.Confidence >= 0.7m && pattern.Strength >= 0.6m;

                case AdvancedPatternCategory.Algorithmic:
                    // Algorithmic patterns are generally reliable
                    return pattern.Confidence >= 0.6m;

                case AdvancedPatternCategory.VolumePrice:
                    // Volume-price patterns support direction based on expected direction
                    return pattern.ExpectedDirection == signal.Type || pattern.ExpectedDirection == SignalType.None;

                case AdvancedPatternCategory.OrderFlow:
                    // Order flow patterns are directional
                    return pattern.ExpectedDirection == signal.Type;

                default:
                    // For unknown categories, allow if confidence is high
                    return pattern.Confidence >= 0.7m;
            }
        }

        /// <summary>
        /// Phase 1.1: Multi-Timeframe Confirmation Validation
        /// Validates that at least 2 timeframes agree on signal direction with trend alignment > 60%
        /// </summary>
        private MultiTimeframeValidationResult ValidateMultiTimeframeConsensus(TradingSignal signal)
        {
            try
            {
                // Check if multi-timeframe analyzer is available
                if (_multiTimeframeAnalyzer == null)
                {
                    LogInfo($"⚠️ Multi-timeframe analyzer not available - using fallback validation");
                    return new MultiTimeframeValidationResult
                    {
                        IsValid = true, // Allow signal if MTF not available (backward compatibility)
                        AgreeingTimeframes = 1,
                        TrendAlignment = 0.7m, // Assume reasonable alignment
                        Details = "Multi-timeframe analyzer not initialized - fallback validation",
                        SupportingTimeframes = "Fallback"
                    };
                }

                // Get current multi-timeframe analysis state
                var mtfState = _multiTimeframeAnalyzer.GetCurrentAnalysisState();

                if (mtfState == null || mtfState.TimeframeStates == null || !mtfState.TimeframeStates.Any())
                {
                    LogInfo($"⚠️ Multi-timeframe state not available - using fallback validation");
                    return new MultiTimeframeValidationResult
                    {
                        IsValid = true, // Allow signal if MTF state not available
                        AgreeingTimeframes = 1,
                        TrendAlignment = 0.7m,
                        Details = "Multi-timeframe state not available - fallback validation",
                        SupportingTimeframes = "Fallback"
                    };
                }

                // Count timeframes that agree with the signal direction
                var agreeingTimeframes = 0;
                var supportingTimeframesList = new List<string>();
                var totalTimeframes = mtfState.TimeframeStates.Count;

                // Check if the synthesized primary signal matches our signal
                if (mtfState.Synthesis?.PrimarySignal?.Type == signal.Type)
                {
                    agreeingTimeframes++;
                    supportingTimeframesList.Add("Synthesized");
                }

                // Check individual timeframe states for volume/delta bias alignment
                foreach (var kvp in mtfState.TimeframeStates)
                {
                    var timeframe = kvp.Key;
                    var timeframeState = kvp.Value;

                    // Check if this timeframe's volume and delta analysis supports our signal
                    var volumeSupportsSignal = false;
                    var deltaSupportsSignal = false;

                    if (signal.Type == SignalType.Long)
                    {
                        volumeSupportsSignal = timeframeState.Volume?.IsVolumeSpikeActive == true;
                        deltaSupportsSignal = timeframeState.DeltaFlow?.CurrentBias == DeltaBias.BuyPressure;
                    }
                    else if (signal.Type == SignalType.Short)
                    {
                        volumeSupportsSignal = timeframeState.Volume?.IsVolumeSpikeActive == true;
                        deltaSupportsSignal = timeframeState.DeltaFlow?.CurrentBias == DeltaBias.SellPressure;
                    }

                    // If both volume and delta support the signal, count this timeframe
                    if (volumeSupportsSignal && deltaSupportsSignal)
                    {
                        agreeingTimeframes++;
                        supportingTimeframesList.Add(timeframe.ToString());
                    }
                }

                // Get trend alignment from multi-timeframe state
                var trendAlignment = mtfState.TrendAlignment;

                // Validation criteria: At least 2 timeframes agree AND trend alignment > 60%
                var isValid = agreeingTimeframes >= 2 && trendAlignment > 0.6m;

                var result = new MultiTimeframeValidationResult
                {
                    IsValid = isValid,
                    AgreeingTimeframes = agreeingTimeframes,
                    TrendAlignment = trendAlignment,
                    Details = $"{agreeingTimeframes}/{totalTimeframes} timeframes agree, alignment: {trendAlignment:P1}",
                    SupportingTimeframes = string.Join(", ", supportingTimeframesList)
                };

                LogInfo($"🔍 Multi-timeframe validation: {(isValid ? "PASSED" : "FAILED")} - {result.Details}");
                return result;
            }
            catch (Exception ex)
            {
                LogError($"❌ Multi-timeframe validation error: {ex.Message}");
                // Fallback to allow signal on error (backward compatibility)
                return new MultiTimeframeValidationResult
                {
                    IsValid = true,
                    AgreeingTimeframes = 1,
                    TrendAlignment = 0.7m,
                    Details = $"Validation error: {ex.Message} - allowing signal",
                    SupportingTimeframes = "Error fallback"
                };
            }
        }

        private MarketContext CreateMarketContext(MarketDataPoint currentBar)
        {
            // Use RealTimeAnalysisEngine if available and calibrated, otherwise fallback to basic context
            if (_isCalibrated && _realTimeAnalysisEngine != null)
            {
                try
                {
                    LogInfo("📊 Creating MarketContext using RealTimeAnalysisEngine...");
                    var realTimeContext = _realTimeAnalysisEngine.GetCurrentMarketContext(currentBar);
                    LogInfo($"✅ Real-time MarketContext created - CVD: {realTimeContext.CVDTrend:F2}, Volatility: {realTimeContext.VolatilityRegime}, Session: {realTimeContext.CurrentSession}, Optimal: {realTimeContext.IsOptimalTradingTime}");
                    return realTimeContext;
                }
                catch (Exception ex)
                {
                    LogError($"❌ Error creating real-time MarketContext: {ex.Message}");
                    LogWarning("⚠️ Falling back to basic MarketContext creation");
                }
            }

            // Fallback to basic MarketContext creation
            LogInfo("📊 Creating basic MarketContext (RealTimeAnalysisEngine not available)");
            return new MarketContext
            {
                CurrentPrice = currentBar.Close,
                PriceChange = currentBar.Close - currentBar.Open,
                PriceChangePercent = currentBar.Open > 0 ? (currentBar.Close - currentBar.Open) / currentBar.Open * 100 : 0,
                DeltaImbalance = Math.Abs(currentBar.Delta) / Math.Max(currentBar.Volume, 1),
                CVDTrend = 0, // Basic fallback
                VolatilityRegime = VolatilityRegime.Normal, // Basic fallback
                CurrentSession = TradingSession.Crypto24H, // Basic fallback
                IsOptimalTradingTime = true, // Basic fallback
                MarketConditions = new List<string> { "Basic Context - Real-time analysis not available" }
            };
        }

        private void ExecuteSignal(TradingSignal signal, MarketDataPoint currentBar)
        {
            try
            {
                // SIMPLE POSITION SIZING: Use MaxPositionSizeUSDT setting directly
                var positionSizeUSDT = MaxPositionSizeUSDT; // Simple: Use the USDT amount from settings

                // Convert USDT to contracts for order placement
                var positionSizeContracts = positionSizeUSDT / currentBar.Close; // Convert USDT to contracts

                // BYBIT FIX: Round to exchange minimum step size (0.01 for ETHUSDT)
                var exchangeStepSize = 0.01m; // Bybit ETHUSDT minimum step
                positionSizeContracts = Math.Round(positionSizeContracts / exchangeStepSize) * exchangeStepSize;

                // VALIDATION: Ensure reasonable position size
                if (positionSizeContracts <= 0 || positionSizeContracts > 1000)
                {
                    LogError($"❌ INVALID POSITION SIZE: {positionSizeContracts:F6} contracts ({positionSizeUSDT:F2} USDT) - Aborting trade");
                    return;
                }

                LogInfo($"💰 SIMPLE POSITION: {signal.Type} | {positionSizeUSDT:F0} USDT = {positionSizeContracts:F2} contracts (Bybit step: {exchangeStepSize:F2}) @ {currentBar.Close:F2}");

                // Calculate TP/SL levels
                var takeProfitPercent = TakeProfitPercent / 100m; // Convert percentage to decimal
                var stopLossPercent = StopLossPercent / 100m;

                decimal takeProfitPrice, stopLossPrice;
                if (signal.Type == SignalType.Long)
                {
                    takeProfitPrice = currentBar.Close * (1 + takeProfitPercent);
                    stopLossPrice = currentBar.Close * (1 - stopLossPercent);
                }
                else // Short
                {
                    takeProfitPrice = currentBar.Close * (1 - takeProfitPercent);
                    stopLossPrice = currentBar.Close * (1 + stopLossPercent);
                }

                LogInfo($"  • TP Level: {takeProfitPrice:F6} ({TakeProfitPercent}%)");
                LogInfo($"  • SL Level: {stopLossPrice:F6} ({StopLossPercent}%)");

                // CRITICAL: Cancel any existing orders before placing new ones
                CancelAllActiveOrders("New position opening");

                // ACTUAL ATAS ORDER PLACEMENT using proper ATAS API
                if (signal.Type == SignalType.Long)
                {
                    // Create Long Entry Order
                    LogInfo($"🔄 BUYING: {positionSizeUSDT:F0} USDT worth ({positionSizeContracts:F2} contracts) at Market Price");

                    var entryOrder = new Order
                    {
                        Security = Security,
                        Portfolio = Portfolio,
                        Direction = OrderDirections.Buy,
                        Type = OrderTypes.Market,
                        QuantityToFill = positionSizeContracts,
                        Comment = "Smart Volume Long Entry"
                    };

                    // Create Take Profit Order
                    var takeProfitOrder = new Order
                    {
                        Security = Security,
                        Portfolio = Portfolio,
                        Direction = OrderDirections.Sell,
                        Type = OrderTypes.Limit,
                        Price = takeProfitPrice,
                        QuantityToFill = positionSizeContracts,
                        Comment = "Take Profit"
                    };

                    // Create Stop Loss Order with proper TriggerPrice for ATAS/Bybit compatibility
                    // CRITICAL FIX: Use TriggerPrice property for Stop orders
                    var formattedStopLossPrice = Math.Round(stopLossPrice, 8); // Round to 8 decimal places
                    var stopLossOrder = new Order
                    {
                        Security = Security,
                        Portfolio = Portfolio,
                        Direction = OrderDirections.Sell,
                        Type = OrderTypes.Stop,
                        TriggerPrice = formattedStopLossPrice,  // Use TriggerPrice for Stop orders
                        TriggerPriceType = TriggerPriceType.Last,  // Use Last price as trigger
                        QuantityToFill = positionSizeContracts,
                        Comment = "Stop Loss"
                    };

                    // CRITICAL: Store order references BEFORE placing them
                    lock (_orderLock)
                    {
                        _currentEntryOrder = entryOrder;
                        _currentTakeProfitOrder = takeProfitOrder;
                        _currentStopLossOrder = stopLossOrder;
                        _hasActiveOrders = true;
                    }

                    // Place the orders
                    OpenOrder(entryOrder);
                    LogInfo($"✅ LONG ORDER PLACED: Size={positionSizeContracts:F6} contracts ({positionSizeUSDT:F2} USDT) | Comment: {entryOrder.Comment}");

                    OpenOrder(takeProfitOrder);
                    LogInfo($"✅ TAKE PROFIT ORDER PLACED: {takeProfitPrice:F6}");

                    OpenOrder(stopLossOrder);
                    LogInfo($"✅ STOP LOSS ORDER PLACED: {formattedStopLossPrice:F8} (TriggerPrice)");
                }
                else if (signal.Type == SignalType.Short)
                {
                    // Create Short Entry Order
                    LogInfo($"🔄 SELLING: {positionSizeUSDT:F0} USDT worth ({positionSizeContracts:F2} contracts) at Market Price");

                    var entryOrder = new Order
                    {
                        Security = Security,
                        Portfolio = Portfolio,
                        Direction = OrderDirections.Sell,
                        Type = OrderTypes.Market,
                        QuantityToFill = positionSizeContracts,
                        Comment = "Smart Volume Short Entry"
                    };

                    // Create Take Profit Order
                    var takeProfitOrder = new Order
                    {
                        Security = Security,
                        Portfolio = Portfolio,
                        Direction = OrderDirections.Buy,
                        Type = OrderTypes.Limit,
                        Price = takeProfitPrice,
                        QuantityToFill = positionSizeContracts,
                        Comment = "Take Profit"
                    };

                    // Create Stop Loss Order with proper TriggerPrice for ATAS/Bybit compatibility
                    // CRITICAL FIX: Use TriggerPrice property for Stop orders
                    var formattedStopLossPrice = Math.Round(stopLossPrice, 8); // Round to 8 decimal places
                    var stopLossOrder = new Order
                    {
                        Security = Security,
                        Portfolio = Portfolio,
                        Direction = OrderDirections.Buy,
                        Type = OrderTypes.Stop,
                        TriggerPrice = formattedStopLossPrice,  // Use TriggerPrice for Stop orders
                        TriggerPriceType = TriggerPriceType.Last,  // Use Last price as trigger
                        QuantityToFill = positionSizeContracts,
                        Comment = "Stop Loss"
                    };

                    // CRITICAL: Store order references BEFORE placing them
                    lock (_orderLock)
                    {
                        _currentEntryOrder = entryOrder;
                        _currentTakeProfitOrder = takeProfitOrder;
                        _currentStopLossOrder = stopLossOrder;
                        _hasActiveOrders = true;
                    }

                    // Place the orders
                    OpenOrder(entryOrder);
                    LogInfo($"✅ SHORT ORDER PLACED: Size={positionSizeContracts:F6} contracts ({positionSizeUSDT:F2} USDT) | Comment: {entryOrder.Comment}");

                    OpenOrder(takeProfitOrder);
                    LogInfo($"✅ TAKE PROFIT ORDER PLACED: {takeProfitPrice:F6}");

                    OpenOrder(stopLossOrder);
                    LogInfo($"✅ STOP LOSS ORDER PLACED: {formattedStopLossPrice:F8} (TriggerPrice)");
                }

                // Update internal position manager state AFTER successful order placement (use contract-based size)
                _positionManager.OpenPosition(signal, currentBar.Close, positionSizeContracts, _currentSettings);

                // Update performance tracker
                _performanceTracker.RecordSignal(signal, true);

                LogInfo($"✅ POSITION OPENED: {signal.Type} | {positionSizeUSDT:F0} USDT ({positionSizeContracts:F6} contracts) @ {currentBar.Close:F2}");

                // Update status
                _statusMessage = $"Position: {signal.Type} @ {currentBar.Close:F2}";
                _statusColor = signal.Type == SignalType.Long ? Colors.Green : Colors.Red;
            }
            catch (Exception ex)
            {
                LogError($"❌ Signal execution failed: {ex.Message}");
                _statusMessage = "Signal execution failed";
                _statusColor = Colors.Red;
            }
        }

        private void ExecuteExitSignal(TradingSignal exitSignal, MarketDataPoint currentBar)
        {
            try
            {
                // This would integrate with ATAS order execution to close position
                // For now, we'll simulate the position closing
                var currentPosition = _positionManager.GetCurrentPosition();

                LogInfo($"🚪 CLOSING POSITION: {currentPosition.Direction} | Entry: {currentPosition.EntryPrice:F2} | Exit: {currentBar.Close:F2} | Reason: {exitSignal.PrimaryReason}");

                // Create trade result for performance tracking
                var tradeResult = new TradeResult
                {
                    EntryTime = currentPosition.EntryTime,
                    ExitTime = DateTime.UtcNow,
                    EntryPrice = currentPosition.EntryPrice,
                    ExitPrice = currentBar.Close,
                    Quantity = currentPosition.Quantity,
                    Direction = currentPosition.Direction,
                    PnL = currentPosition.UnrealizedPnL,
                    PnLPercent = currentPosition.UnrealizedPnLPercent,
                    ExitReason = exitSignal.PrimaryReason,
                    SignalConfidence = exitSignal.Confidence,
                    HoldTime = DateTime.UtcNow - currentPosition.EntryTime,
                    CorrelationId = currentPosition.CorrelationId
                };

                _performanceTracker.RecordTrade(tradeResult);

                // Update position manager
                var closedPosition = currentPosition;
                closedPosition.HasPosition = false;
                _positionManager.UpdatePosition(closedPosition);

                var pnlEmoji = tradeResult.PnL >= 0 ? "💚" : "❤️";
                LogInfo($"{pnlEmoji} POSITION CLOSED: {exitSignal.PrimaryReason} | PnL: {tradeResult.PnL:F2} USDT ({tradeResult.PnLPercent:F2}%) | Hold: {tradeResult.HoldTime:hh\\:mm\\:ss}");

                // Update status
                _statusMessage = $"Closed: {tradeResult.PnL:F2} USDT ({tradeResult.PnLPercent:F1}%)";
                _statusColor = tradeResult.PnL >= 0 ? Colors.Green : Colors.Red;
            }
            catch (Exception ex)
            {
                LogError($"❌ Exit execution failed: {ex.Message}");
                _statusMessage = "Exit execution failed";
                _statusColor = Colors.Red;
            }
        }

        private async Task CheckPerformanceAndAdjustAsync()
        {
            try
            {
                _lastPerformanceCheck = DateTime.UtcNow;

                LogInfo($"=== PERFORMANCE CHECK STARTING ===");

                var performance = _performanceTracker.GetPerformanceMetrics();
                var marketData = _marketDataHistory.ToList();

                LogInfo($"📊 Current Performance: Trades={performance.TotalTrades}, WinRate={performance.WinRate:F1}%, PnL={performance.TotalPnL:F2} USDT");

                // Check if adjustment is needed
                if (_adaptiveAdjuster.NeedsAdjustment(_currentSettings, performance, marketData))
                {
                    LogInfo($"🔧 Performance adjustment needed - analyzing market conditions");

                    var adjustments = await _adaptiveAdjuster.SuggestAdjustmentsAsync(_currentSettings, performance, marketData);

                    if (adjustments.Any())
                    {
                        LogInfo($"📈 Applying {adjustments.Count} performance-based adjustments:");
                        foreach (var adjustment in adjustments)
                        {
                            LogInfo($"  • {adjustment.Parameter}: {adjustment.OldValue} → {adjustment.AdjustedValue} ({adjustment.Reason})");
                        }

                        _currentSettings = _adaptiveAdjuster.ApplyAdjustments(_currentSettings, adjustments);

                        LogInfo($"✅ ADJUSTMENTS APPLIED SUCCESSFULLY");

                        // Update status
                        _statusMessage = $"Settings adjusted ({adjustments.Count} changes)";
                        _statusColor = Colors.Blue;
                    }
                    else
                    {
                        LogInfo($"ℹ️ No adjustments needed at this time");
                    }
                }
                else
                {
                    LogInfo($"✅ Performance within acceptable range - no adjustments needed");
                }

                LogInfo($"=== PERFORMANCE CHECK COMPLETED ===");
            }
            catch (Exception ex)
            {
                LogError($"❌ Performance check failed: {ex.Message}");
            }
        }

        private void LogStatusUpdate()
        {
            try
            {
                LogInfo($"=== STATUS UPDATE ===");
                LogInfo($"Strategy State: {(_isCalibrated ? "CALIBRATED" : "WAITING FOR CALIBRATION")} | Enabled: {EnableStrategy}");
                LogInfo($"Processed Bars: {_processedBarsCount} | Market Data History: {_marketDataHistory.Count} bars");

                if (_isCalibrated && _currentSettings != null)
                {
                    LogInfo($"Current Settings: VolumeThreshold={_currentSettings.VolumeThreshold:F2}, SignalThreshold={_currentSettings.SignalThreshold:F2}");
                    LogInfo($"Confidence Level: {_currentConfidence:P1}");
                }

                if (_performanceTracker != null)
                {
                    var performance = _performanceTracker.GetPerformanceMetrics();
                    LogInfo($"Performance: {performance.TotalTrades} trades, {performance.WinRate:F1}% win rate, {performance.TotalPnL:F2} USDT PnL");
                }

                // CRITICAL FIX: Show ATAS actual position instead of internal PositionManager state
                var statusATASPosition = CurrentPosition;
                var hasStatusPosition = Math.Abs(statusATASPosition) > 0.001m;

                if (hasStatusPosition)
                {
                    var currentPosition = _positionManager?.GetCurrentPosition();
                    LogInfo($"ATAS Position: {statusATASPosition:F6} | Internal: {currentPosition?.Direction} | Entry: {currentPosition?.EntryPrice:F2} | PnL: {currentPosition?.UnrealizedPnL:F2} USDT");
                }
                else
                {
                    LogInfo($"Position Status: No active position (ATAS: {statusATASPosition:F6})");
                }

                LogInfo($"Recent Signals: {_recentSignals.Count} in buffer");
                LogInfo($"Status: {_statusMessage}");
            }
            catch (Exception ex)
            {
                LogError($"❌ Status update failed: {ex.Message}");
            }
        }



        #endregion

        #region ATAS Logging Methods

        /// <summary>
        /// Log information message to ATAS
        /// </summary>
        public void LogInfo(string message)
        {
            RaiseShowNotification(message, null, LoggingLevel.Info);
        }

        /// <summary>
        /// Log warning message to ATAS
        /// </summary>
        public void LogWarning(string message)
        {
            RaiseShowNotification(message, null, LoggingLevel.Warning);
        }

        /// <summary>
        /// Log error message to ATAS
        /// </summary>
        public void LogError(string message)
        {
            RaiseShowNotification(message, null, LoggingLevel.Error);
        }

        /// <summary>
        /// Log debug message to ATAS
        /// </summary>
        public void LogDebug(string message)
        {
            RaiseShowNotification(message, null, LoggingLevel.Info);
        }

        #endregion
    }
}
