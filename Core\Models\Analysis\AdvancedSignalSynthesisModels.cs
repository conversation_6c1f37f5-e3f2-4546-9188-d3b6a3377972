using System;
using System.Collections.Generic;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Models.Analysis
{
    /// <summary>
    /// Phase 3.2: Institutional activity types
    /// </summary>
    public enum InstitutionalActivityType
    {
        Accumulation,        // Institutional buying/accumulation
        Distribution,        // Institutional selling/distribution
        Manipulation,        // Price manipulation patterns
        IcebergOrder,        // Hidden large order execution
        BlockTrade,          // Large block trade execution
        SmartMoneyFlow,      // Professional money movement
        RetailTrapping,      // Retail trader trapping patterns
        Neutral              // No significant institutional activity
    }

    /// <summary>
    /// Advanced pattern categories
    /// </summary>
    public enum AdvancedPatternCategory
    {
        Institutional,       // Institutional behavior patterns
        Algorithmic,         // Algorithmic trading patterns
        VolumePrice,         // Volume-price relationship patterns
        OrderFlow,           // Order flow patterns
        Microstructure,      // Market microstructure patterns
        CrossMarket,         // Cross-market patterns
        Sentiment,           // Market sentiment patterns
        Momentum            // Momentum patterns
    }

    /// <summary>
    /// Signal conflict resolution strategies for advanced synthesis
    /// </summary>
    public enum AdvancedConflictResolutionStrategy
    {
        InstitutionalBias,           // Institutional activity trumps retail signals
        PatternStrengthWeighted,     // Stronger patterns win conflicts
        MultiFactor,                 // Multiple factors considered
        ConfidenceBased,            // Highest confidence combination wins
        HistoricalPerformance,      // Past accuracy influences resolution
        VolumeConfirmation,         // Volume confirmation required
        CrossTimeframeConsensus,    // Cross-timeframe agreement required
        InstitutionalConsensus      // Institutional activity consensus required
    }

    /// <summary>
    /// Institutional footprint detection result
    /// </summary>
    public class InstitutionalFootprint
    {
        public DateTime Timestamp { get; set; }
        public InstitutionalActivityType ActivityType { get; set; }
        public decimal Confidence { get; set; }
        public decimal Strength { get; set; }
        public decimal VolumeSignificance { get; set; }
        public List<Timeframe> SupportingTimeframes { get; set; } = new List<Timeframe>();
        public Dictionary<string, decimal> FootprintIndicators { get; set; } = new Dictionary<string, decimal>();
        public List<string> DetectionReasons { get; set; } = new List<string>();
        public TimeSpan EstimatedDuration { get; set; }
        public decimal PriceImpact { get; set; }
        public bool IsValidated { get; set; }
        public decimal ValidationScore { get; set; }
    }

    /// <summary>
    /// Advanced pattern recognition result
    /// </summary>
    public class AdvancedPattern
    {
        public DateTime Timestamp { get; set; }
        public string PatternName { get; set; }
        public AdvancedPatternCategory Category { get; set; }
        public decimal Confidence { get; set; }
        public decimal Strength { get; set; }
        public decimal Reliability { get; set; }
        public List<Timeframe> SupportingTimeframes { get; set; } = new List<Timeframe>();
        public Dictionary<string, decimal> PatternMetrics { get; set; } = new Dictionary<string, decimal>();
        public List<string> PatternFactors { get; set; } = new List<string>();
        public TimeSpan EstimatedDuration { get; set; }
        public SignalType ExpectedDirection { get; set; }
        public decimal ExpectedMagnitude { get; set; }
        public bool RequiresConfirmation { get; set; }
        public List<InstitutionalFootprint> SupportingFootprints { get; set; } = new List<InstitutionalFootprint>();
    }

    /// <summary>
    /// Signal conflict analysis result
    /// </summary>
    public class SignalConflictAnalysis
    {
        public DateTime Timestamp { get; set; }
        public List<TradingSignal> ConflictingSignals { get; set; } = new List<TradingSignal>();
        public List<string> ConflictReasons { get; set; } = new List<string>();
        public decimal ConflictSeverity { get; set; }
        public AdvancedConflictResolutionStrategy RecommendedStrategy { get; set; }
        public TradingSignal ResolvedSignal { get; set; }
        public List<string> ResolutionFactors { get; set; } = new List<string>();
        public decimal ResolutionConfidence { get; set; }
        public bool WasResolved { get; set; }
        public Dictionary<string, decimal> ConflictMetrics { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// Institutional signal analysis result
    /// </summary>
    public class InstitutionalSignalAnalysis
    {
        public DateTime Timestamp { get; set; }
        public SignalType InstitutionalDirection { get; set; }
        public decimal InstitutionalStrength { get; set; }
        public decimal InstitutionalConfidence { get; set; }
        public decimal OrderFlowImbalance { get; set; }
        public decimal SmartMoneyRatio { get; set; }
        public List<InstitutionalFootprint> ActiveFootprints { get; set; } = new List<InstitutionalFootprint>();
        public Dictionary<string, decimal> OrderFlowMetrics { get; set; } = new Dictionary<string, decimal>();
        public List<string> InstitutionalFactors { get; set; } = new List<string>();
        public bool HasInstitutionalSupport { get; set; }
        public decimal InstitutionalAlignment { get; set; }
        public TimeSpan InstitutionalMomentum { get; set; }
    }

    /// <summary>
    /// Advanced synthesized signal with institutional and pattern data
    /// </summary>
    public class AdvancedSynthesizedSignal
    {
        public DateTime Timestamp { get; set; }
        public SignalType Type { get; set; }
        public decimal Confidence { get; set; }
        public decimal Strength { get; set; }
        public SignalQuality Quality { get; set; }
        
        // Multi-timeframe data from Phase 3.1
        public TimeframeWeightedSignal TimeframeWeightedData { get; set; }
        
        // Advanced synthesis data
        public InstitutionalSignalAnalysis InstitutionalAnalysis { get; set; }
        public List<AdvancedPattern> SupportingPatterns { get; set; } = new List<AdvancedPattern>();
        public List<InstitutionalFootprint> InstitutionalFootprints { get; set; } = new List<InstitutionalFootprint>();
        public SignalConflictAnalysis ConflictAnalysis { get; set; }
        
        // Enhancement factors
        public decimal InstitutionalEnhancement { get; set; }
        public decimal PatternEnhancement { get; set; }
        public decimal OverallEnhancement { get; set; }
        
        // Synthesis metadata
        public List<string> SynthesisFactors { get; set; } = new List<string>();
        public Dictionary<string, decimal> SynthesisMetrics { get; set; } = new Dictionary<string, decimal>();
        public bool HasAdvancedConfirmation { get; set; }
        public decimal AdvancedConfirmationScore { get; set; }
    }

    /// <summary>
    /// Advanced signal synthesis configuration
    /// </summary>
    public class AdvancedSignalSynthesisConfig
    {
        public bool EnableInstitutionalDetection { get; set; } = true;
        public bool EnableAdvancedPatterns { get; set; } = true;
        public bool EnableConflictResolution { get; set; } = true;
        public bool EnableOrderFlowAnalysis { get; set; } = true;
        
        public decimal MinInstitutionalConfidence { get; set; } = 0.6m;
        public decimal MinPatternConfidence { get; set; } = 0.7m;
        public decimal MinConflictResolutionConfidence { get; set; } = 0.65m;
        
        public AdvancedConflictResolutionStrategy DefaultConflictStrategy { get; set; } = AdvancedConflictResolutionStrategy.MultiFactor;
        
        public int MaxInstitutionalFootprints { get; set; } = 10;
        public int MaxAdvancedPatterns { get; set; } = 15;
        public int ConflictAnalysisWindow { get; set; } = 30;
        
        public Dictionary<AdvancedPatternCategory, decimal> PatternCategoryWeights { get; set; } = new Dictionary<AdvancedPatternCategory, decimal>();
        public Dictionary<InstitutionalActivityType, decimal> InstitutionalActivityWeights { get; set; } = new Dictionary<InstitutionalActivityType, decimal>();
        
        public AdvancedSignalSynthesisConfig()
        {
            InitializeDefaultWeights();
        }
        
        private void InitializeDefaultWeights()
        {
            // Pattern category weights
            PatternCategoryWeights[AdvancedPatternCategory.Institutional] = 0.25m;
            PatternCategoryWeights[AdvancedPatternCategory.Algorithmic] = 0.15m;
            PatternCategoryWeights[AdvancedPatternCategory.VolumePrice] = 0.20m;
            PatternCategoryWeights[AdvancedPatternCategory.OrderFlow] = 0.15m;
            PatternCategoryWeights[AdvancedPatternCategory.Microstructure] = 0.10m;
            PatternCategoryWeights[AdvancedPatternCategory.CrossMarket] = 0.05m;
            PatternCategoryWeights[AdvancedPatternCategory.Sentiment] = 0.05m;
            PatternCategoryWeights[AdvancedPatternCategory.Momentum] = 0.05m;
            
            // Institutional activity weights
            InstitutionalActivityWeights[InstitutionalActivityType.Accumulation] = 0.20m;
            InstitutionalActivityWeights[InstitutionalActivityType.Distribution] = 0.20m;
            InstitutionalActivityWeights[InstitutionalActivityType.SmartMoneyFlow] = 0.18m;
            InstitutionalActivityWeights[InstitutionalActivityType.BlockTrade] = 0.15m;
            InstitutionalActivityWeights[InstitutionalActivityType.IcebergOrder] = 0.12m;
            InstitutionalActivityWeights[InstitutionalActivityType.Manipulation] = 0.08m;
            InstitutionalActivityWeights[InstitutionalActivityType.RetailTrapping] = 0.05m;
            InstitutionalActivityWeights[InstitutionalActivityType.Neutral] = 0.02m;
        }
    }

    /// <summary>
    /// Advanced signal synthesis statistics
    /// </summary>
    public class AdvancedSignalSynthesisStatistics
    {
        public int TotalAdvancedSyntheses { get; set; }
        public int InstitutionalDetections { get; set; }
        public int AdvancedPatternsDetected { get; set; }
        public int ConflictsResolved { get; set; }
        public int EnhancedSignals { get; set; }
        
        public decimal AverageInstitutionalConfidence { get; set; }
        public decimal AveragePatternConfidence { get; set; }
        public decimal AverageConflictResolutionConfidence { get; set; }
        public decimal AverageEnhancementFactor { get; set; }
        
        public Dictionary<InstitutionalActivityType, int> InstitutionalActivityDistribution { get; set; } = new Dictionary<InstitutionalActivityType, int>();
        public Dictionary<AdvancedPatternCategory, int> PatternCategoryDistribution { get; set; } = new Dictionary<AdvancedPatternCategory, int>();
        public Dictionary<AdvancedConflictResolutionStrategy, int> ConflictResolutionStrategyUsage { get; set; } = new Dictionary<AdvancedConflictResolutionStrategy, int>();
        
        public DateTime LastSynthesis { get; set; }
        public TimeSpan AverageSynthesisTime { get; set; }
        public decimal SuccessRate { get; set; }
    }

    /// <summary>
    /// Order flow analysis data
    /// </summary>
    public class OrderFlowAnalysis
    {
        public DateTime Timestamp { get; set; }
        public decimal BuyPressure { get; set; }
        public decimal SellPressure { get; set; }
        public decimal OrderFlowImbalance { get; set; }
        public decimal LargeOrderRatio { get; set; }
        public decimal SmallOrderRatio { get; set; }
        public decimal AverageOrderSize { get; set; }
        public decimal OrderFlowMomentum { get; set; }
        public List<string> OrderFlowFactors { get; set; } = new List<string>();
        public Dictionary<string, decimal> OrderFlowMetrics { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// Pattern validation result
    /// </summary>
    public class PatternValidationResult
    {
        public bool IsValid { get; set; }
        public decimal ValidationScore { get; set; }
        public List<string> ValidationFactors { get; set; } = new List<string>();
        public List<string> ValidationFailures { get; set; } = new List<string>();
        public decimal ConfidenceAdjustment { get; set; }
        public bool RequiresAdditionalConfirmation { get; set; }
    }
}
