using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Calibration
{
    /// <summary>
    /// Calibrates risk management parameters based on volatility analysis and user risk profile
    /// </summary>
    public class RiskCalibrator : IRiskCalibrator
    {
        private readonly decimal[] DEFAULT_TP_PERCENTAGES = { 0.3m, 0.4m, 0.5m, 0.6m, 0.8m, 1.0m, 1.2m, 1.5m, 2.0m };
        private readonly decimal[] DEFAULT_RISK_REWARD_RATIOS = { 1.5m, 2.0m, 2.5m, 3.0m };

        /// <summary>
        /// Calibrate risk parameters based on volatility characteristics and user preferences
        /// </summary>
        public async Task<RiskCalibrationResult> CalibrateRiskParametersAsync(
            List<MarketDataPoint> marketData,
            VolatilityCharacteristics volatilityCharacteristics,
            UserRiskProfile userRiskProfile)
        {
            if (marketData == null || marketData.Count < 50)
                throw new ArgumentException("Need at least 50 bars for risk calibration", nameof(marketData));

            var result = new RiskCalibrationResult
            {
                CalibrationTimestamp = DateTime.UtcNow,
                BarsAnalyzed = marketData.Count
            };

            // Calibrate TP/SL percentages based on volatility
            var tpSlOptimization = await OptimizeTakeProfitStopLossAsync(marketData, volatilityCharacteristics, userRiskProfile);
            result.OptimalTakeProfitPercent = tpSlOptimization.OptimalTakeProfit;
            result.OptimalStopLossPercent = tpSlOptimization.OptimalStopLoss;
            result.TPSLConfidence = tpSlOptimization.Confidence;

            // Calibrate position sizing
            var positionSizing = await CalibratePositionSizingAsync(marketData, volatilityCharacteristics, userRiskProfile);
            result.OptimalPositionSizeUSDT = positionSizing.OptimalPositionSize;
            result.RiskAdjustmentFactor = positionSizing.RiskAdjustmentFactor;
            result.PositionSizingConfidence = positionSizing.Confidence;

            // Calculate overall calibration confidence
            result.CalibrationConfidence = (result.TPSLConfidence + result.PositionSizingConfidence) / 2;

            return result;
        }

        /// <summary>
        /// Quick risk parameter adjustment for recalibration
        /// </summary>
        public async Task<RiskCalibrationResult> QuickCalibrateAsync(
            List<MarketDataPoint> recentData,
            VolatilityCharacteristics currentVolatility,
            OptimalSettings currentSettings)
        {
            if (recentData == null || recentData.Count < 20)
                throw new ArgumentException("Need at least 20 bars for quick calibration", nameof(recentData));

            var result = new RiskCalibrationResult
            {
                CalibrationTimestamp = DateTime.UtcNow,
                BarsAnalyzed = recentData.Count
            };

            // Quick volatility assessment
            var recentVolatility = CalculateRecentVolatility(recentData);
            var volatilityChange = currentVolatility.AverageVolatility > 0 ? 
                recentVolatility / currentVolatility.AverageVolatility : 1.0m;

            // Adjust TP/SL based on volatility change
            if (volatilityChange > 1.5m) // Volatility increased significantly
            {
                result.OptimalTakeProfitPercent = currentSettings.TakeProfitPercent * 1.2m;
                result.OptimalStopLossPercent = currentSettings.StopLossPercent * 1.1m;
                result.RiskAdjustmentFactor = currentSettings.RiskAdjustmentFactor * 0.8m; // Reduce position size
            }
            else if (volatilityChange < 0.7m) // Volatility decreased significantly
            {
                result.OptimalTakeProfitPercent = currentSettings.TakeProfitPercent * 0.9m;
                result.OptimalStopLossPercent = currentSettings.StopLossPercent * 0.95m;
                result.RiskAdjustmentFactor = currentSettings.RiskAdjustmentFactor * 1.1m; // Increase position size
            }
            else
            {
                // No significant change
                result.OptimalTakeProfitPercent = currentSettings.TakeProfitPercent;
                result.OptimalStopLossPercent = currentSettings.StopLossPercent;
                result.RiskAdjustmentFactor = currentSettings.RiskAdjustmentFactor;
            }

            result.OptimalPositionSizeUSDT = currentSettings.PositionSizeUSDT;
            result.TPSLConfidence = 0.7m; // Lower confidence for quick calibration
            result.PositionSizingConfidence = 0.7m;
            result.CalibrationConfidence = 0.7m;

            return result;
        }

        #region Private Methods

        private async Task<(decimal OptimalTakeProfit, decimal OptimalStopLoss, decimal Confidence)> 
            OptimizeTakeProfitStopLossAsync(
                List<MarketDataPoint> marketData,
                VolatilityCharacteristics volatilityCharacteristics,
                UserRiskProfile userRiskProfile)
        {
            var testResults = new List<TPSLTestResult>();

            // Test different TP/SL combinations
            foreach (var tpPercent in DEFAULT_TP_PERCENTAGES)
            {
                foreach (var rrRatio in DEFAULT_RISK_REWARD_RATIOS)
                {
                    var slPercent = tpPercent / rrRatio;
                    var result = await TestTPSLCombination(marketData, tpPercent, slPercent);
                    testResults.Add(result);
                }
            }

            // Find the best combination based on user preferences
            var bestResult = SelectBestTPSLResult(testResults, userRiskProfile, volatilityCharacteristics);

            // Adjust for volatility regime
            var (adjustedTP, adjustedSL) = AdjustForVolatilityRegime(
                bestResult.TakeProfitPercent, 
                bestResult.StopLossPercent, 
                volatilityCharacteristics);

            // Apply user constraints
            var (finalTP, finalSL) = ApplyUserConstraints(adjustedTP, adjustedSL, userRiskProfile);

            return (finalTP, finalSL, bestResult.Score);
        }

        private async Task<(decimal OptimalPositionSize, decimal RiskAdjustmentFactor, decimal Confidence)> 
            CalibratePositionSizingAsync(
                List<MarketDataPoint> marketData,
                VolatilityCharacteristics volatilityCharacteristics,
                UserRiskProfile userRiskProfile)
        {
            // Base position size from user profile
            var basePositionSize = CalculateBasePositionSize(userRiskProfile);

            // Calculate risk adjustment factor based on volatility
            var riskAdjustmentFactor = CalculateVolatilityRiskAdjustment(volatilityCharacteristics);

            // Apply user risk tolerance
            riskAdjustmentFactor = ApplyRiskToleranceAdjustment(riskAdjustmentFactor, userRiskProfile);

            // Calculate final position size
            var optimalPositionSize = basePositionSize * riskAdjustmentFactor;

            // Ensure within user constraints
            optimalPositionSize = Math.Max(optimalPositionSize, 100m); // Minimum $100
            optimalPositionSize = Math.Min(optimalPositionSize, userRiskProfile.AccountBalance * 0.1m); // Max 10% of account

            var confidence = CalculatePositionSizingConfidence(volatilityCharacteristics, userRiskProfile);

            return (optimalPositionSize, riskAdjustmentFactor, confidence);
        }

        private async Task<TPSLTestResult> TestTPSLCombination(
            List<MarketDataPoint> marketData,
            decimal tpPercent,
            decimal slPercent)
        {
            var trades = 0;
            var wins = 0;
            var totalWinAmount = 0m;
            var totalLossAmount = 0m;
            var maxDrawdown = 0m;
            var currentDrawdown = 0m;
            var equity = 1000m;

            // Simulate trades with these TP/SL levels
            for (int i = 1; i < marketData.Count - 10; i++)
            {
                var entryBar = marketData[i];
                var entryPrice = entryBar.Close;

                // Test both long and short scenarios
                foreach (var direction in new[] { 1, -1 })
                {
                    trades++;
                    var tpPrice = entryPrice * (1 + direction * tpPercent / 100);
                    var slPrice = entryPrice * (1 - direction * slPercent / 100);

                    var tradeResult = SimulateTrade(marketData, i + 1, entryPrice, tpPrice, slPrice, direction);

                    if (tradeResult > 0)
                    {
                        wins++;
                        totalWinAmount += tradeResult;
                        equity += tradeResult;
                        currentDrawdown = 0;
                    }
                    else
                    {
                        totalLossAmount += Math.Abs(tradeResult);
                        equity += tradeResult;
                        currentDrawdown += Math.Abs(tradeResult);
                        maxDrawdown = Math.Max(maxDrawdown, currentDrawdown);
                    }
                }
            }

            var winRate = trades > 0 ? (decimal)wins / trades : 0;
            var profitFactor = totalLossAmount > 0 ? totalWinAmount / totalLossAmount : (totalWinAmount > 0 ? 10 : 1);
            var riskRewardRatio = tpPercent / slPercent;

            // Score based on multiple factors
            var winRateScore = winRate;
            var profitFactorScore = Math.Min(1.0m, profitFactor / 2.0m);
            var drawdownScore = maxDrawdown < 100 ? 1.0m - (maxDrawdown / 100) : 0;
            var rrScore = Math.Min(1.0m, riskRewardRatio / 3.0m);

            var combinedScore = (winRateScore * 0.3m + profitFactorScore * 0.3m + drawdownScore * 0.2m + rrScore * 0.2m);

            return new TPSLTestResult
            {
                TakeProfitPercent = tpPercent,
                StopLossPercent = slPercent,
                WinRate = winRate,
                ProfitFactor = profitFactor,
                MaxDrawdown = maxDrawdown,
                RiskRewardRatio = riskRewardRatio,
                Score = combinedScore
            };
        }

        private decimal SimulateTrade(
            List<MarketDataPoint> marketData,
            int startIndex,
            decimal entryPrice,
            decimal tpPrice,
            decimal slPrice,
            int direction)
        {
            for (int i = startIndex; i < Math.Min(startIndex + 20, marketData.Count); i++)
            {
                var bar = marketData[i];

                if (direction == 1) // Long
                {
                    if (bar.High >= tpPrice)
                        return tpPrice - entryPrice;
                    if (bar.Low <= slPrice)
                        return slPrice - entryPrice;
                }
                else // Short
                {
                    if (bar.Low <= tpPrice)
                        return entryPrice - tpPrice;
                    if (bar.High >= slPrice)
                        return entryPrice - slPrice;
                }
            }

            // Trade didn't hit TP or SL
            var exitPrice = marketData[Math.Min(startIndex + 19, marketData.Count - 1)].Close;
            return direction * (exitPrice - entryPrice);
        }

        private TPSLTestResult SelectBestTPSLResult(
            List<TPSLTestResult> results,
            UserRiskProfile userRiskProfile,
            VolatilityCharacteristics volatilityCharacteristics)
        {
            // Weight results based on user preferences
            foreach (var result in results)
            {
                var adjustedScore = result.Score;

                // Prefer higher win rate if user prefers it
                if (userRiskProfile.PreferHigherWinRate)
                {
                    adjustedScore = adjustedScore * 0.7m + result.WinRate * 0.3m;
                }

                // Prefer specific risk-reward ratio
                var rrDifference = Math.Abs(result.RiskRewardRatio - userRiskProfile.PreferredRiskReward);
                var rrPenalty = Math.Min(0.3m, rrDifference * 0.1m);
                adjustedScore -= rrPenalty;

                result.Score = adjustedScore;
            }

            return results.OrderByDescending(r => r.Score).First();
        }

        private (decimal AdjustedTP, decimal AdjustedSL) AdjustForVolatilityRegime(
            decimal baseTP,
            decimal baseSL,
            VolatilityCharacteristics volatilityCharacteristics)
        {
            var volatilityMultiplier = volatilityCharacteristics.Regime switch
            {
                VolatilityRegime.VeryLow => 0.8m,
                VolatilityRegime.Low => 0.9m,
                VolatilityRegime.Normal => 1.0m,
                VolatilityRegime.High => 1.2m,
                VolatilityRegime.VeryHigh => 1.4m,
                VolatilityRegime.Extreme => 1.6m,
                _ => 1.0m
            };

            return (baseTP * volatilityMultiplier, baseSL * volatilityMultiplier);
        }

        private (decimal FinalTP, decimal FinalSL) ApplyUserConstraints(
            decimal adjustedTP,
            decimal adjustedSL,
            UserRiskProfile userRiskProfile)
        {
            // Ensure minimum risk-reward ratio
            var currentRR = adjustedTP / adjustedSL;
            if (currentRR < userRiskProfile.PreferredRiskReward)
            {
                adjustedSL = adjustedTP / userRiskProfile.PreferredRiskReward;
            }

            // Apply maximum drawdown constraint
            if (userRiskProfile.MaxDrawdownTolerance < 0.1m) // Very conservative
            {
                adjustedSL = Math.Min(adjustedSL, 0.5m); // Max 0.5% stop loss
            }

            return (adjustedTP, adjustedSL);
        }

        private decimal CalculateBasePositionSize(UserRiskProfile userRiskProfile)
        {
            // Calculate position size based on account balance and risk per trade
            var riskAmount = userRiskProfile.AccountBalance * userRiskProfile.RiskPercentPerTrade;
            
            // Assume 1% stop loss for base calculation
            var basePositionSize = riskAmount / 0.01m;
            
            return Math.Min(basePositionSize, userRiskProfile.AccountBalance * 0.1m); // Max 10% of account
        }

        private decimal CalculateVolatilityRiskAdjustment(VolatilityCharacteristics volatilityCharacteristics)
        {
            return volatilityCharacteristics.Regime switch
            {
                VolatilityRegime.VeryLow => 1.2m,
                VolatilityRegime.Low => 1.1m,
                VolatilityRegime.Normal => 1.0m,
                VolatilityRegime.High => 0.8m,
                VolatilityRegime.VeryHigh => 0.6m,
                VolatilityRegime.Extreme => 0.4m,
                _ => 1.0m
            };
        }

        private decimal ApplyRiskToleranceAdjustment(decimal baseAdjustment, UserRiskProfile userRiskProfile)
        {
            return userRiskProfile.Tolerance switch
            {
                RiskTolerance.Conservative => baseAdjustment * 0.7m,
                RiskTolerance.Moderate => baseAdjustment,
                RiskTolerance.Aggressive => baseAdjustment * 1.3m,
                _ => baseAdjustment
            };
        }

        private decimal CalculatePositionSizingConfidence(
            VolatilityCharacteristics volatilityCharacteristics,
            UserRiskProfile userRiskProfile)
        {
            var baseConfidence = 0.8m;

            // Higher confidence for stable volatility
            if (volatilityCharacteristics.Regime == VolatilityRegime.Normal)
                baseConfidence += 0.1m;

            // Higher confidence for clear user preferences
            if (userRiskProfile.Tolerance != RiskTolerance.Moderate)
                baseConfidence += 0.05m;

            return Math.Min(1.0m, baseConfidence);
        }

        private decimal CalculateRecentVolatility(List<MarketDataPoint> recentData)
        {
            return recentData.Average(bar => bar.Open > 0 ? Math.Abs(bar.Close - bar.Open) / bar.Open : 0);
        }

        #endregion

        #region Helper Classes

        private class TPSLTestResult
        {
            public decimal TakeProfitPercent { get; set; }
            public decimal StopLossPercent { get; set; }
            public decimal WinRate { get; set; }
            public decimal ProfitFactor { get; set; }
            public decimal MaxDrawdown { get; set; }
            public decimal RiskRewardRatio { get; set; }
            public decimal Score { get; set; }
        }

        #endregion
    }
}
