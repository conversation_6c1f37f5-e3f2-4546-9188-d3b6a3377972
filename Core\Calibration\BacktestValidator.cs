using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Calibration
{
    /// <summary>
    /// Validates calibrated settings through comprehensive backtesting analysis
    /// </summary>
    public class BacktestValidator : IBacktestValidator
    {
        /// <summary>
        /// Perform comprehensive backtest validation of calibrated settings
        /// </summary>
        public async Task<BacktestValidation> ValidateAsync(OptimalSettings settings, List<MarketDataPoint> marketData)
        {
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));
            
            if (marketData == null || marketData.Count < 50)
                throw new ArgumentException("Need at least 50 bars for backtest validation", nameof(marketData));

            var validation = new BacktestValidation
            {
                ValidationTimestamp = DateTime.UtcNow,
                SettingsValidated = settings,
                BarsAnalyzed = marketData.Count,
                ValidationPerformed = true
            };

            try
            {
                // Run comprehensive backtest simulation
                var backtestResults = await RunBacktestSimulation(settings, marketData);
                
                // Populate validation results
                PopulateValidationResults(validation, backtestResults);
                
                // Calculate validation score
                validation.ValidationScore = CalculateValidationScore(validation);
                
                // Determine if settings are acceptable
                validation.IsValid = validation.ValidationScore >= 0.6m && validation.WinRate >= 0.4m;
                
                // Generate recommendations
                GenerateValidationRecommendations(validation, backtestResults);
                
                return validation;
            }
            catch (Exception ex)
            {
                validation.ValidationPerformed = false;
                validation.ValidationErrors.Add($"Validation failed: {ex.Message}");
                validation.IsValid = false;
                return validation;
            }
        }

        /// <summary>
        /// Perform quick validation for recalibration scenarios
        /// </summary>
        public async Task<BacktestValidation> QuickValidateAsync(OptimalSettings settings, List<MarketDataPoint> recentData)
        {
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));
            
            if (recentData == null || recentData.Count < 20)
                throw new ArgumentException("Need at least 20 bars for quick validation", nameof(recentData));

            var validation = new BacktestValidation
            {
                ValidationTimestamp = DateTime.UtcNow,
                SettingsValidated = settings,
                BarsAnalyzed = recentData.Count,
                ValidationPerformed = true
            };

            try
            {
                // Run simplified backtest on recent data
                var quickResults = await RunQuickBacktest(settings, recentData);
                
                // Populate basic validation results
                validation.WinRate = quickResults.WinRate;
                validation.ProfitFactor = quickResults.ProfitFactor;
                validation.MaxDrawdown = quickResults.MaxDrawdown;
                validation.TotalTrades = quickResults.TotalTrades;
                validation.SignalFrequency = quickResults.SignalFrequency;
                
                // Quick validation score (less comprehensive)
                validation.ValidationScore = (validation.WinRate + Math.Min(1.0m, validation.ProfitFactor / 2.0m)) / 2;
                validation.IsValid = validation.ValidationScore >= 0.5m;
                
                return validation;
            }
            catch (Exception ex)
            {
                validation.ValidationPerformed = false;
                validation.ValidationErrors.Add($"Quick validation failed: {ex.Message}");
                validation.IsValid = false;
                return validation;
            }
        }

        #region Private Methods

        private async Task<BacktestResults> RunBacktestSimulation(OptimalSettings settings, List<MarketDataPoint> marketData)
        {
            var results = new BacktestResults();
            var trades = new List<BacktestTrade>();
            var signals = new List<BacktestSignal>();
            
            var equity = 10000m; // Starting equity
            var peakEquity = equity;
            var maxDrawdown = 0m;
            var cumulativeImpact = 0m;
            var averageVolume = marketData.Average(x => x.Volume);
            
            Position currentPosition = null;

            for (int i = 1; i < marketData.Count - 1; i++)
            {
                var currentBar = marketData[i];
                var nextBar = marketData[i + 1];

                // Update cumulative impact
                cumulativeImpact = UpdateCumulativeImpact(cumulativeImpact, currentBar, averageVolume, settings);

                // Check for signal generation
                var signal = GenerateBacktestSignal(currentBar, cumulativeImpact, settings, i);
                if (signal != null)
                {
                    signals.Add(signal);

                    // Check if we can open a position
                    if (currentPosition == null && signal.IsEntry)
                    {
                        currentPosition = OpenBacktestPosition(signal, currentBar, settings);
                    }
                }

                // Check for position exit
                if (currentPosition != null)
                {
                    var exitResult = CheckPositionExit(currentPosition, currentBar, nextBar, settings);
                    if (exitResult.ShouldExit)
                    {
                        var trade = CloseBacktestPosition(currentPosition, exitResult.ExitPrice, exitResult.ExitReason);
                        trades.Add(trade);
                        
                        // Update equity and drawdown
                        equity += trade.PnL;
                        if (equity > peakEquity)
                        {
                            peakEquity = equity;
                        }
                        else
                        {
                            var currentDrawdown = (peakEquity - equity) / peakEquity * 100;
                            maxDrawdown = Math.Max(maxDrawdown, currentDrawdown);
                        }

                        currentPosition = null;
                        cumulativeImpact = 0; // Reset after trade
                    }
                }
            }

            // Close any remaining position
            if (currentPosition != null)
            {
                var finalBar = marketData.Last();
                var finalTrade = CloseBacktestPosition(currentPosition, finalBar.Close, "End of data");
                trades.Add(finalTrade);
                equity += finalTrade.PnL;
            }

            // Calculate results
            results.TotalTrades = trades.Count;
            results.WinningTrades = trades.Count(t => t.PnL > 0);
            results.LosingTrades = trades.Count(t => t.PnL <= 0);
            results.WinRate = trades.Count > 0 ? (decimal)results.WinningTrades / trades.Count : 0;
            
            var totalWins = trades.Where(t => t.PnL > 0).Sum(t => t.PnL);
            var totalLosses = Math.Abs(trades.Where(t => t.PnL <= 0).Sum(t => t.PnL));
            results.ProfitFactor = totalLosses > 0 ? totalWins / totalLosses : (totalWins > 0 ? 10 : 1);
            
            results.TotalPnL = equity - 10000m;
            results.MaxDrawdown = maxDrawdown;
            results.SignalFrequency = (decimal)signals.Count / marketData.Count * 100;
            results.SignalsGenerated = signals.Count;
            results.Trades = trades;
            results.Signals = signals;

            return results;
        }

        private async Task<BacktestResults> RunQuickBacktest(OptimalSettings settings, List<MarketDataPoint> recentData)
        {
            // Simplified version of full backtest for quick validation
            var results = new BacktestResults();
            var trades = new List<BacktestTrade>();
            var signals = 0;
            var wins = 0;
            var totalPnL = 0m;
            var maxDrawdown = 0m;
            var equity = 1000m;
            var peakEquity = equity;

            var averageVolume = recentData.Average(x => x.Volume);
            var cumulativeImpact = 0m;

            for (int i = 1; i < recentData.Count - 1; i++)
            {
                var currentBar = recentData[i];
                var nextBar = recentData[i + 1];

                // Update cumulative impact
                cumulativeImpact = UpdateCumulativeImpact(cumulativeImpact, currentBar, averageVolume, settings);

                // Simple signal check
                if (cumulativeImpact >= settings.SignalThreshold)
                {
                    signals++;
                    
                    // Simulate trade
                    var entryPrice = currentBar.Close;
                    var direction = Math.Sign(currentBar.Delta);
                    
                    if (direction != 0)
                    {
                        var tpPrice = entryPrice * (1 + direction * settings.TakeProfitPercent / 100);
                        var slPrice = entryPrice * (1 - direction * settings.StopLossPercent / 100);
                        
                        // Check next few bars for TP/SL hit
                        for (int j = i + 1; j < Math.Min(i + 10, recentData.Count); j++)
                        {
                            var testBar = recentData[j];
                            var pnl = 0m;
                            
                            if (direction == 1) // Long
                            {
                                if (testBar.High >= tpPrice)
                                    pnl = tpPrice - entryPrice;
                                else if (testBar.Low <= slPrice)
                                    pnl = slPrice - entryPrice;
                            }
                            else // Short
                            {
                                if (testBar.Low <= tpPrice)
                                    pnl = entryPrice - tpPrice;
                                else if (testBar.High >= slPrice)
                                    pnl = entryPrice - slPrice;
                            }
                            
                            if (pnl != 0)
                            {
                                if (pnl > 0) wins++;
                                totalPnL += pnl;
                                equity += pnl;
                                
                                if (equity > peakEquity)
                                    peakEquity = equity;
                                else
                                    maxDrawdown = Math.Max(maxDrawdown, (peakEquity - equity) / peakEquity * 100);
                                
                                break;
                            }
                        }
                    }
                    
                    cumulativeImpact = 0; // Reset after signal
                }
            }

            results.TotalTrades = signals;
            results.WinningTrades = wins;
            results.WinRate = signals > 0 ? (decimal)wins / signals : 0;
            results.ProfitFactor = totalPnL > 0 ? Math.Abs(totalPnL) / Math.Max(0.01m, Math.Abs(totalPnL - totalPnL)) : 1;
            results.TotalPnL = totalPnL;
            results.MaxDrawdown = maxDrawdown;
            results.SignalFrequency = (decimal)signals / recentData.Count * 100;
            results.SignalsGenerated = signals;

            return results;
        }

        private decimal UpdateCumulativeImpact(decimal currentImpact, MarketDataPoint bar, decimal averageVolume, OptimalSettings settings)
        {
            var volumeRatio = bar.Volume / averageVolume;
            
            if (volumeRatio >= settings.VolumeThreshold)
            {
                var priceChangePercent = bar.Open > 0 ? Math.Abs(bar.Close - bar.Open) / bar.Open : 0;
                var impact = priceChangePercent * volumeRatio * settings.PriceChangeMultiplier;
                return currentImpact * settings.ImpactDecay + impact;
            }
            
            return currentImpact * settings.ImpactDecay;
        }

        private BacktestSignal GenerateBacktestSignal(MarketDataPoint bar, decimal cumulativeImpact, OptimalSettings settings, int barIndex)
        {
            if (cumulativeImpact >= settings.SignalThreshold)
            {
                var deltaImbalance = Math.Abs(bar.Delta) / Math.Max(bar.Volume, 1);
                
                if (deltaImbalance >= settings.DeltaImbalanceThreshold)
                {
                    return new BacktestSignal
                    {
                        BarIndex = barIndex,
                        Timestamp = bar.Timestamp,
                        Price = bar.Close,
                        Direction = Math.Sign(bar.Delta),
                        Strength = cumulativeImpact,
                        IsEntry = true,
                        Confidence = Math.Min(1.0m, cumulativeImpact / (settings.SignalThreshold * 2))
                    };
                }
            }
            
            return null;
        }

        private Position OpenBacktestPosition(BacktestSignal signal, MarketDataPoint bar, OptimalSettings settings)
        {
            return new Position
            {
                EntryPrice = bar.Close,
                EntryTime = bar.Timestamp,
                Direction = signal.Direction,
                TakeProfit = bar.Close * (1 + signal.Direction * settings.TakeProfitPercent / 100),
                StopLoss = bar.Close * (1 - signal.Direction * settings.StopLossPercent / 100),
                Quantity = settings.PositionSizeUSDT / bar.Close
            };
        }

        private (bool ShouldExit, decimal ExitPrice, string ExitReason) CheckPositionExit(Position position, MarketDataPoint currentBar, MarketDataPoint nextBar, OptimalSettings settings)
        {
            // Check TP/SL levels
            if (position.Direction == 1) // Long
            {
                if (currentBar.High >= position.TakeProfit)
                    return (true, position.TakeProfit, "Take Profit");
                if (currentBar.Low <= position.StopLoss)
                    return (true, position.StopLoss, "Stop Loss");
            }
            else // Short
            {
                if (currentBar.Low <= position.TakeProfit)
                    return (true, position.TakeProfit, "Take Profit");
                if (currentBar.High >= position.StopLoss)
                    return (true, position.StopLoss, "Stop Loss");
            }

            // Check maximum hold time (24 hours = 1440 minutes)
            if ((currentBar.Timestamp - position.EntryTime).TotalMinutes > 1440)
                return (true, currentBar.Close, "Max Hold Time");

            return (false, 0, "");
        }

        private BacktestTrade CloseBacktestPosition(Position position, decimal exitPrice, string exitReason)
        {
            var pnl = position.Direction * (exitPrice - position.EntryPrice) * position.Quantity;
            var pnlPercent = position.EntryPrice > 0 ? (pnl / (position.EntryPrice * position.Quantity)) * 100 : 0;

            return new BacktestTrade
            {
                EntryPrice = position.EntryPrice,
                ExitPrice = exitPrice,
                EntryTime = position.EntryTime,
                ExitTime = DateTime.UtcNow, // Simplified
                Direction = position.Direction,
                Quantity = position.Quantity,
                PnL = pnl,
                PnLPercent = pnlPercent,
                ExitReason = exitReason
            };
        }

        private void PopulateValidationResults(BacktestValidation validation, BacktestResults results)
        {
            validation.WinRate = results.WinRate;
            validation.ProfitFactor = results.ProfitFactor;
            validation.MaxDrawdown = results.MaxDrawdown;
            validation.TotalTrades = results.TotalTrades;
            validation.SignalFrequency = results.SignalFrequency;
            validation.TotalPnL = results.TotalPnL;
            validation.AverageTradeReturn = results.TotalTrades > 0 ? results.TotalPnL / results.TotalTrades : 0;
            validation.SharpeRatio = CalculateSharpeRatio(results.Trades);
            validation.MaxConsecutiveLosses = CalculateMaxConsecutiveLosses(results.Trades);
        }

        private decimal CalculateValidationScore(BacktestValidation validation)
        {
            var winRateScore = validation.WinRate;
            var profitFactorScore = Math.Min(1.0m, validation.ProfitFactor / 2.0m);
            var drawdownScore = validation.MaxDrawdown < 20 ? 1.0m - (validation.MaxDrawdown / 20) : 0;
            var frequencyScore = Math.Min(1.0m, validation.SignalFrequency / 10); // Target ~10% signal frequency
            var sharpeScore = Math.Min(1.0m, Math.Max(0, validation.SharpeRatio) / 2.0m);

            return (winRateScore * 0.25m + profitFactorScore * 0.25m + drawdownScore * 0.2m + frequencyScore * 0.15m + sharpeScore * 0.15m);
        }

        private void GenerateValidationRecommendations(BacktestValidation validation, BacktestResults results)
        {
            validation.Recommendations.Clear();

            if (validation.WinRate < 0.5m)
                validation.Recommendations.Add("Consider increasing signal threshold for better win rate");

            if (validation.ProfitFactor < 1.2m)
                validation.Recommendations.Add("Review TP/SL ratios - profit factor is low");

            if (validation.MaxDrawdown > 15)
                validation.Recommendations.Add("Reduce position size - drawdown is too high");

            if (validation.SignalFrequency < 2)
                validation.Recommendations.Add("Consider decreasing signal threshold for more trading opportunities");

            if (validation.SignalFrequency > 20)
                validation.Recommendations.Add("Consider increasing signal threshold to reduce overtrading");

            if (validation.MaxConsecutiveLosses > 5)
                validation.Recommendations.Add("Implement additional filters to reduce consecutive losses");
        }

        private decimal CalculateSharpeRatio(List<BacktestTrade> trades)
        {
            if (trades.Count < 2)
                return 0;

            var returns = trades.Select(t => t.PnLPercent).ToList();
            var averageReturn = returns.Average();
            var stdDev = CalculateStandardDeviation(returns);

            return stdDev > 0 ? averageReturn / stdDev : 0;
        }

        private int CalculateMaxConsecutiveLosses(List<BacktestTrade> trades)
        {
            var maxConsecutive = 0;
            var currentConsecutive = 0;

            foreach (var trade in trades)
            {
                if (trade.PnL <= 0)
                {
                    currentConsecutive++;
                    maxConsecutive = Math.Max(maxConsecutive, currentConsecutive);
                }
                else
                {
                    currentConsecutive = 0;
                }
            }

            return maxConsecutive;
        }

        private decimal CalculateStandardDeviation(List<decimal> values)
        {
            if (values.Count == 0)
                return 0;

            var average = values.Average();
            var variance = values.Sum(v => (v - average) * (v - average)) / values.Count;
            return (decimal)Math.Sqrt((double)variance);
        }

        #endregion

        #region Helper Classes

        private class BacktestResults
        {
            public int TotalTrades { get; set; }
            public int WinningTrades { get; set; }
            public int LosingTrades { get; set; }
            public decimal WinRate { get; set; }
            public decimal ProfitFactor { get; set; }
            public decimal TotalPnL { get; set; }
            public decimal MaxDrawdown { get; set; }
            public decimal SignalFrequency { get; set; }
            public int SignalsGenerated { get; set; }
            public List<BacktestTrade> Trades { get; set; } = new List<BacktestTrade>();
            public List<BacktestSignal> Signals { get; set; } = new List<BacktestSignal>();
        }

        private class BacktestSignal
        {
            public int BarIndex { get; set; }
            public DateTime Timestamp { get; set; }
            public decimal Price { get; set; }
            public int Direction { get; set; }
            public decimal Strength { get; set; }
            public bool IsEntry { get; set; }
            public decimal Confidence { get; set; }
        }

        private class BacktestTrade
        {
            public decimal EntryPrice { get; set; }
            public decimal ExitPrice { get; set; }
            public DateTime EntryTime { get; set; }
            public DateTime ExitTime { get; set; }
            public int Direction { get; set; }
            public decimal Quantity { get; set; }
            public decimal PnL { get; set; }
            public decimal PnLPercent { get; set; }
            public string ExitReason { get; set; }
        }

        private class Position
        {
            public decimal EntryPrice { get; set; }
            public DateTime EntryTime { get; set; }
            public int Direction { get; set; }
            public decimal TakeProfit { get; set; }
            public decimal StopLoss { get; set; }
            public decimal Quantity { get; set; }
        }

        #endregion
    }
}
