using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models.Analysis;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Enhanced confidence calculation with market-aware weighting and adaptive scoring
    /// Addresses the confidence calculation imbalances identified in log analysis
    /// </summary>
    public class EnhancedConfidenceCalculator
    {
        private readonly Action<string> _logAction;
        private readonly object _lockObject = new object();
        
        // Market-aware weight configurations
        private readonly Dictionary<VolatilityRegime, ConfidenceWeights> _volatilityWeights;
        private readonly Dictionary<TradingSession, ConfidenceWeights> _sessionWeights;
        
        public EnhancedConfidenceCalculator(Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _volatilityWeights = InitializeVolatilityWeights();
            _sessionWeights = InitializeSessionWeights();
        }
        
        /// <summary>
        /// Calculate enhanced confidence with market-aware weighting
        /// </summary>
        public decimal CalculateEnhancedConfidence(
            TradingSignal baseSignal,
            MarketContext marketContext,
            MultiTimeframeAnalysisState multiTimeframeState = null)
        {
            lock (_lockObject)
            {
                try
                {
                    // Get market-appropriate weights
                    var weights = GetMarketAwareWeights(marketContext);
                    
                    // Calculate component scores with enhanced logic
                    var componentScores = CalculateComponentScores(baseSignal, marketContext, multiTimeframeState);
                    
                    // Apply weighted calculation
                    var weightedConfidence = CalculateWeightedConfidence(componentScores, weights);
                    
                    // Apply market condition multipliers
                    var finalConfidence = ApplyMarketConditionMultipliers(weightedConfidence, marketContext, baseSignal);
                    
                    // Log detailed breakdown for debugging
                    LogConfidenceBreakdown(componentScores, weights, weightedConfidence, finalConfidence);
                    
                    return Math.Max(0.1m, Math.Min(1.0m, finalConfidence));
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Enhanced confidence calculation error: {ex.Message}");
                    return baseSignal.Confidence; // Fallback to base confidence
                }
            }
        }
        
        /// <summary>
        /// Get market-appropriate weights based on current conditions
        /// </summary>
        private ConfidenceWeights GetMarketAwareWeights(MarketContext marketContext)
        {
            var baseWeights = new ConfidenceWeights
            {
                VolumeBlock = 0.30m,        // Increased from 0.25m - volume is reliable
                SignalStrength = 0.25m,     // Increased from 0.20m - signal strength is key
                DeltaAlignment = 0.15m,     // Increased from 0.10m - delta is important
                MarketTiming = 0.10m,       // Same as before
                CVDConfirmation = 0.10m,    // Reduced from 0.15m - CVD is problematic
                VolatilityRegime = 0.10m    // Reduced from 0.15m - less critical
            };
            
            // Adjust weights based on volatility regime
            if (_volatilityWeights.ContainsKey(marketContext.VolatilityRegime))
            {
                var volatilityAdjustment = _volatilityWeights[marketContext.VolatilityRegime];
                baseWeights = AdjustWeights(baseWeights, volatilityAdjustment, 0.2m);
            }
            
            // Adjust weights based on trading session
            if (_sessionWeights.ContainsKey(marketContext.CurrentSession))
            {
                var sessionAdjustment = _sessionWeights[marketContext.CurrentSession];
                baseWeights = AdjustWeights(baseWeights, sessionAdjustment, 0.1m);
            }
            
            return baseWeights;
        }
        
        /// <summary>
        /// Calculate enhanced component scores
        /// </summary>
        private ComponentScores CalculateComponentScores(
            TradingSignal baseSignal,
            MarketContext marketContext,
            MultiTimeframeAnalysisState multiTimeframeState)
        {
            var scores = new ComponentScores();
            
            // Volume Block Score - Enhanced with volatility awareness
            scores.VolumeBlock = CalculateEnhancedVolumeScore(baseSignal, marketContext);
            
            // Signal Strength Score - Direct from base signal
            scores.SignalStrength = baseSignal.Strength;
            
            // Delta Alignment Score - Enhanced with momentum analysis
            scores.DeltaAlignment = CalculateEnhancedDeltaScore(baseSignal, marketContext);
            
            // Market Timing Score - Enhanced with session analysis
            scores.MarketTiming = CalculateEnhancedTimingScore(marketContext);
            
            // CVD Confirmation Score - Enhanced with tolerance
            scores.CVDConfirmation = CalculateEnhancedCVDScore(baseSignal, marketContext);
            
            // Volatility Regime Score - Enhanced with regime analysis
            scores.VolatilityRegime = CalculateEnhancedVolatilityScore(marketContext);
            
            return scores;
        }
        
        /// <summary>
        /// Calculate enhanced volume score with volatility awareness
        /// </summary>
        private decimal CalculateEnhancedVolumeScore(TradingSignal baseSignal, MarketContext marketContext)
        {
            var baseScore = baseSignal.VolumeComponent?.VolumeConfidence ?? 0.5m;
            
            // Boost score for high volume ratios
            if (baseSignal.VolumeRatio > 2.0m)
            {
                baseScore = Math.Min(1.0m, baseScore * 1.15m);
            }
            
            // Adjust for volatility regime
            var volatilityMultiplier = marketContext.VolatilityRegime switch
            {
                VolatilityRegime.VeryLow => 1.1m,  // Volume more significant in low volatility
                VolatilityRegime.Low => 1.05m,
                VolatilityRegime.Medium => 1.0m,
                VolatilityRegime.High => 0.95m,
                VolatilityRegime.VeryHigh => 0.9m,
                _ => 1.0m
            };
            
            return Math.Min(1.0m, baseScore * volatilityMultiplier);
        }
        
        /// <summary>
        /// Calculate enhanced delta score with momentum analysis
        /// </summary>
        private decimal CalculateEnhancedDeltaScore(TradingSignal baseSignal, MarketContext marketContext)
        {
            var baseScore = baseSignal.DeltaComponent?.DeltaConfidence ?? 0.5m;
            
            // Boost for strong delta imbalance
            var deltaImbalance = Math.Abs(baseSignal.DeltaImbalance);
            if (deltaImbalance > 0.3m)
            {
                baseScore = Math.Min(1.0m, baseScore * 1.2m);
            }
            else if (deltaImbalance > 0.2m)
            {
                baseScore = Math.Min(1.0m, baseScore * 1.1m);
            }
            
            return baseScore;
        }
        
        /// <summary>
        /// Calculate enhanced timing score with session analysis
        /// </summary>
        private decimal CalculateEnhancedTimingScore(MarketContext marketContext)
        {
            var baseScore = 0.65m; // Default neutral score
            
            // Enhance based on trading session
            baseScore = marketContext.CurrentSession switch
            {
                TradingSession.Asian => 0.6m,      // Lower activity
                TradingSession.European => 0.7m,   // Moderate activity - more lenient than before
                TradingSession.US => 0.8m,         // High activity
                TradingSession.Overlap_EuropeanUS => 0.9m,    // Highest activity
                TradingSession.Overlap_AsianEuropean => 0.85m, // High activity
                _ => 0.65m
            };
            
            // Adjust for optimal trading conditions
            if (marketContext.IsOptimalTradingTime)
            {
                baseScore = Math.Min(1.0m, baseScore * 1.2m);
            }
            
            return baseScore;
        }
        
        /// <summary>
        /// Calculate enhanced CVD score with improved tolerance
        /// </summary>
        private decimal CalculateEnhancedCVDScore(TradingSignal baseSignal, MarketContext marketContext)
        {
            var cvdTrend = marketContext.CVDTrend;
            var signalDirection = baseSignal.Type == SignalType.Long ? 1 : baseSignal.Type == SignalType.Short ? -1 : 0;
            
            // Enhanced CVD analysis with better scaling
            var cvdMagnitude = Math.Abs(cvdTrend);
            var cvdDirection = Math.Sign(cvdTrend);
            
            // Perfect alignment
            if (cvdDirection == signalDirection && cvdDirection != 0)
            {
                var strength = Math.Min(1.0m, cvdMagnitude / 200000m);
                return 0.7m + (strength * 0.3m); // 0.7 to 1.0 range
            }
            
            // Neutral CVD
            if (cvdMagnitude < 50000m)
            {
                return 0.6m; // Neutral - more lenient
            }
            
            // Divergence - but more tolerant
            return 0.4m; // Reduced penalty for divergence
        }
        
        /// <summary>
        /// Calculate enhanced volatility score
        /// </summary>
        private decimal CalculateEnhancedVolatilityScore(MarketContext marketContext)
        {
            return marketContext.VolatilityRegime switch
            {
                VolatilityRegime.VeryLow => 0.8m,   // Good for precise signals
                VolatilityRegime.Low => 0.9m,       // Optimal
                VolatilityRegime.Medium => 1.0m,    // Perfect
                VolatilityRegime.High => 0.8m,      // Acceptable
                VolatilityRegime.VeryHigh => 0.6m,  // Challenging
                _ => 0.7m
            };
        }
        
        /// <summary>
        /// Calculate weighted confidence from component scores
        /// </summary>
        private decimal CalculateWeightedConfidence(ComponentScores scores, ConfidenceWeights weights)
        {
            var weightedSum = 
                (scores.VolumeBlock * weights.VolumeBlock) +
                (scores.SignalStrength * weights.SignalStrength) +
                (scores.DeltaAlignment * weights.DeltaAlignment) +
                (scores.MarketTiming * weights.MarketTiming) +
                (scores.CVDConfirmation * weights.CVDConfirmation) +
                (scores.VolatilityRegime * weights.VolatilityRegime);
            
            var totalWeight = 
                weights.VolumeBlock + weights.SignalStrength + weights.DeltaAlignment +
                weights.MarketTiming + weights.CVDConfirmation + weights.VolatilityRegime;
            
            return totalWeight > 0 ? weightedSum / totalWeight : 0.5m;
        }
        
        /// <summary>
        /// Apply market condition multipliers for final confidence
        /// </summary>
        private decimal ApplyMarketConditionMultipliers(
            decimal baseConfidence, 
            MarketContext marketContext, 
            TradingSignal baseSignal)
        {
            var multiplier = 1.0m;
            
            // Strong volume boost
            if (baseSignal.VolumeRatio > 2.5m)
            {
                multiplier *= 1.1m;
            }
            
            // Strong delta boost
            if (Math.Abs(baseSignal.DeltaImbalance) > 0.4m)
            {
                multiplier *= 1.05m;
            }
            
            // Market condition boost
            if (marketContext.IsOptimalTradingTime && marketContext.VolatilityRegime == VolatilityRegime.Medium)
            {
                multiplier *= 1.05m;
            }
            
            return baseConfidence * multiplier;
        }
        
        /// <summary>
        /// Log detailed confidence breakdown for debugging
        /// </summary>
        private void LogConfidenceBreakdown(
            ComponentScores scores, 
            ConfidenceWeights weights, 
            decimal weightedConfidence, 
            decimal finalConfidence)
        {
            _logAction($"🎯 ENHANCED CONFIDENCE BREAKDOWN:");
            _logAction($"  • Volume Block: {scores.VolumeBlock:F3} (Weight: {weights.VolumeBlock:P0})");
            _logAction($"  • Signal Strength: {scores.SignalStrength:F3} (Weight: {weights.SignalStrength:P0})");
            _logAction($"  • Delta Alignment: {scores.DeltaAlignment:F3} (Weight: {weights.DeltaAlignment:P0})");
            _logAction($"  • Market Timing: {scores.MarketTiming:F3} (Weight: {weights.MarketTiming:P0})");
            _logAction($"  • CVD Confirmation: {scores.CVDConfirmation:F3} (Weight: {weights.CVDConfirmation:P0})");
            _logAction($"  • Volatility Regime: {scores.VolatilityRegime:F3} (Weight: {weights.VolatilityRegime:P0})");
            _logAction($"  • WEIGHTED CONFIDENCE: {weightedConfidence:F3} ({weightedConfidence:P1})");
            _logAction($"  • FINAL ENHANCED CONFIDENCE: {finalConfidence:F3} ({finalConfidence:P1})");
        }

        /// <summary>
        /// Initialize volatility-based weight adjustments
        /// </summary>
        private Dictionary<VolatilityRegime, ConfidenceWeights> InitializeVolatilityWeights()
        {
            return new Dictionary<VolatilityRegime, ConfidenceWeights>
            {
                [VolatilityRegime.VeryLow] = new ConfidenceWeights
                {
                    VolumeBlock = 0.35m,     // Volume more important in low volatility
                    SignalStrength = 0.25m,
                    DeltaAlignment = 0.20m,  // Delta more reliable
                    MarketTiming = 0.05m,    // Less important
                    CVDConfirmation = 0.10m,
                    VolatilityRegime = 0.05m
                },
                [VolatilityRegime.High] = new ConfidenceWeights
                {
                    VolumeBlock = 0.25m,     // Volume less reliable in high volatility
                    SignalStrength = 0.30m,  // Signal strength more important
                    DeltaAlignment = 0.15m,
                    MarketTiming = 0.15m,    // Timing more critical
                    CVDConfirmation = 0.05m, // CVD less reliable
                    VolatilityRegime = 0.10m
                }
            };
        }

        /// <summary>
        /// Initialize session-based weight adjustments
        /// </summary>
        private Dictionary<TradingSession, ConfidenceWeights> InitializeSessionWeights()
        {
            return new Dictionary<TradingSession, ConfidenceWeights>
            {
                [TradingSession.European] = new ConfidenceWeights
                {
                    VolumeBlock = 0.30m,
                    SignalStrength = 0.25m,
                    DeltaAlignment = 0.15m,
                    MarketTiming = 0.15m,    // More important during European session
                    CVDConfirmation = 0.10m,
                    VolatilityRegime = 0.05m
                },
                [TradingSession.US] = new ConfidenceWeights
                {
                    VolumeBlock = 0.35m,     // Volume very important during high activity
                    SignalStrength = 0.25m,
                    DeltaAlignment = 0.20m,
                    MarketTiming = 0.10m,
                    CVDConfirmation = 0.05m,
                    VolatilityRegime = 0.05m
                }
            };
        }

        /// <summary>
        /// Adjust weights with specified influence factor
        /// </summary>
        private ConfidenceWeights AdjustWeights(ConfidenceWeights baseWeights, ConfidenceWeights adjustment, decimal influence)
        {
            return new ConfidenceWeights
            {
                VolumeBlock = baseWeights.VolumeBlock + ((adjustment.VolumeBlock - baseWeights.VolumeBlock) * influence),
                SignalStrength = baseWeights.SignalStrength + ((adjustment.SignalStrength - baseWeights.SignalStrength) * influence),
                DeltaAlignment = baseWeights.DeltaAlignment + ((adjustment.DeltaAlignment - baseWeights.DeltaAlignment) * influence),
                MarketTiming = baseWeights.MarketTiming + ((adjustment.MarketTiming - baseWeights.MarketTiming) * influence),
                CVDConfirmation = baseWeights.CVDConfirmation + ((adjustment.CVDConfirmation - baseWeights.CVDConfirmation) * influence),
                VolatilityRegime = baseWeights.VolatilityRegime + ((adjustment.VolatilityRegime - baseWeights.VolatilityRegime) * influence)
            };
        }
    }

    /// <summary>
    /// Component scores for confidence calculation
    /// </summary>
    public class ComponentScores
    {
        public decimal VolumeBlock { get; set; }
        public decimal SignalStrength { get; set; }
        public decimal DeltaAlignment { get; set; }
        public decimal MarketTiming { get; set; }
        public decimal CVDConfirmation { get; set; }
        public decimal VolatilityRegime { get; set; }
    }

    /// <summary>
    /// Confidence calculation weights
    /// </summary>
    public class ConfidenceWeights
    {
        public decimal VolumeBlock { get; set; }
        public decimal SignalStrength { get; set; }
        public decimal DeltaAlignment { get; set; }
        public decimal MarketTiming { get; set; }
        public decimal CVDConfirmation { get; set; }
        public decimal VolatilityRegime { get; set; }
    }
}
