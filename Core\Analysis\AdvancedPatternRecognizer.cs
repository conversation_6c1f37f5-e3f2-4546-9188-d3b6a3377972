using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models.Analysis;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;
using InstitutionalActivityType = SmartVolumeStrategy.Core.Models.Analysis.InstitutionalActivityType;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Phase 3.2: Advanced Pattern Recognizer
    /// Complex pattern recognition with confidence scoring and institutional integration
    /// </summary>
    public class AdvancedPatternRecognizer
    {
        private readonly Action<string> _logAction;
        private readonly AdvancedSignalSynthesisConfig _config;
        private readonly Queue<AdvancedPattern> _patternHistory;
        private readonly Dictionary<AdvancedPatternCategory, Queue<decimal>> _categoryConfidenceHistory;
        private readonly object _lockObject = new object();
        
        // Circuit breaker integration
        private CircuitBreakerManager _circuitBreakerManager;
        
        // Configuration
        private const int MAX_PATTERN_HISTORY = 150;
        private const int CONFIDENCE_HISTORY_SIZE = 30;
        private const decimal PATTERN_VALIDATION_THRESHOLD = 0.65m;
        private const decimal INSTITUTIONAL_CONFIRMATION_BOOST = 0.15m;

        public AdvancedPatternRecognizer(AdvancedSignalSynthesisConfig config = null, Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _config = config ?? new AdvancedSignalSynthesisConfig();
            _patternHistory = new Queue<AdvancedPattern>();
            _categoryConfidenceHistory = new Dictionary<AdvancedPatternCategory, Queue<decimal>>();
            
            InitializeCategoryHistory();
        }

        /// <summary>
        /// Set circuit breaker manager for fault tolerance
        /// </summary>
        public void SetCircuitBreakerManager(CircuitBreakerManager circuitBreakerManager)
        {
            _circuitBreakerManager = circuitBreakerManager;
        }

        /// <summary>
        /// Recognize advanced patterns with institutional footprint integration
        /// </summary>
        public List<AdvancedPattern> RecognizeAdvancedPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            List<CrossTimeframePattern> crossTimeframePatterns,
            List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> institutionalFootprints,
            MarketContext marketContext)
        {
            lock (_lockObject)
            {
                var startTime = DateTime.UtcNow;
                var recognizedPatterns = new List<AdvancedPattern>();
                
                try
                {
                    // Phase 3.2: Check circuit breaker status
                    if (_circuitBreakerManager != null && !_circuitBreakerManager.IsOperationAllowed(StrategyComponent.MultiTimeframeAnalysis))
                    {
                        _logAction("🔴 Advanced pattern recognition blocked by circuit breaker");
                        return recognizedPatterns;
                    }

                    // Check degradation level
                    if (_circuitBreakerManager != null)
                    {
                        var degradationLevel = _circuitBreakerManager.GetDegradationManager().GetCurrentDegradationLevel();
                        if (degradationLevel >= DegradationLevel.Minimal)
                        {
                            _logAction("⚠️ Advanced pattern recognition running in degraded mode");
                            return RecognizeBasicPatterns(timeframeSignals, crossTimeframePatterns);
                        }
                    }

                    if (!_config.EnableAdvancedPatterns)
                    {
                        return recognizedPatterns;
                    }

                    _logAction("🎯 Starting advanced pattern recognition...");

                    // Recognize different pattern categories
                    recognizedPatterns.AddRange(RecognizeInstitutionalPatterns(timeframeSignals, institutionalFootprints, marketContext));
                    recognizedPatterns.AddRange(RecognizeAlgorithmicPatterns(timeframeSignals, crossTimeframePatterns, marketContext));
                    recognizedPatterns.AddRange(RecognizeVolumePricePatterns(timeframeSignals, institutionalFootprints, marketContext));
                    recognizedPatterns.AddRange(RecognizeOrderFlowPatterns(timeframeSignals, institutionalFootprints, marketContext));
                    recognizedPatterns.AddRange(RecognizeMicrostructurePatterns(timeframeSignals, crossTimeframePatterns, marketContext));
                    recognizedPatterns.AddRange(RecognizeMomentumPatterns(timeframeSignals, crossTimeframePatterns, marketContext));

                    // Validate and score patterns
                    foreach (var pattern in recognizedPatterns)
                    {
                        ValidateAdvancedPattern(pattern, timeframeSignals, institutionalFootprints, marketContext);
                    }

                    // Filter patterns by confidence threshold
                    var validPatterns = recognizedPatterns
                        .Where(p => p.Confidence >= _config.MinPatternConfidence)
                        .OrderByDescending(p => p.Confidence)
                        .Take(_config.MaxAdvancedPatterns)
                        .ToList();

                    // Update pattern history
                    foreach (var pattern in validPatterns)
                    {
                        UpdatePatternHistory(pattern);
                    }

                    // Record successful operation
                    if (_circuitBreakerManager != null)
                    {
                        var responseTime = DateTime.UtcNow - startTime;
                        _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, true, $"Recognized {validPatterns.Count} advanced patterns");
                        _circuitBreakerManager.GetComponentHealthTracker().UpdateResponseTime(StrategyComponent.MultiTimeframeAnalysis, responseTime);
                    }

                    _logAction($"✅ Advanced pattern recognition completed - Found {validPatterns.Count} valid patterns");

                    return validPatterns;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Advanced pattern recognition error: {ex.Message}");
                    
                    // Record failure with circuit breaker
                    if (_circuitBreakerManager != null)
                    {
                        _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, false, ex.Message, FailureSeverity.Medium);
                    }
                    
                    return recognizedPatterns;
                }
            }
        }

        /// <summary>
        /// Get pattern recognition statistics
        /// </summary>
        public AdvancedSignalSynthesisStatistics GetPatternStatistics()
        {
            lock (_lockObject)
            {
                var stats = new AdvancedSignalSynthesisStatistics
                {
                    AdvancedPatternsDetected = _patternHistory.Count,
                    LastSynthesis = _patternHistory.Count > 0 ? _patternHistory.Last().Timestamp : DateTime.MinValue
                };

                if (_patternHistory.Count > 0)
                {
                    stats.AveragePatternConfidence = _patternHistory.Average(p => p.Confidence);
                    
                    // Pattern category distribution
                    foreach (var pattern in _patternHistory)
                    {
                        if (!stats.PatternCategoryDistribution.ContainsKey(pattern.Category))
                            stats.PatternCategoryDistribution[pattern.Category] = 0;
                        stats.PatternCategoryDistribution[pattern.Category]++;
                    }
                }

                return stats;
            }
        }

        /// <summary>
        /// Recognize institutional patterns
        /// </summary>
        private List<AdvancedPattern> RecognizeInstitutionalPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> institutionalFootprints,
            MarketContext marketContext)
        {
            var patterns = new List<AdvancedPattern>();

            // Institutional accumulation/distribution patterns
            var accumulationFootprints = institutionalFootprints
                .Where(f => f.ActivityType == InstitutionalActivityType.Accumulation && f.Confidence >= 0.7m)
                .ToList();

            if (accumulationFootprints.Count > 0)
            {
                var avgConfidence = accumulationFootprints.Average(f => f.Confidence);
                var supportingTimeframes = accumulationFootprints.SelectMany(f => f.SupportingTimeframes).Distinct().ToList();

                var pattern = new AdvancedPattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternName = "Institutional Accumulation Pattern",
                    Category = AdvancedPatternCategory.Institutional,
                    Confidence = avgConfidence * 0.9m,
                    Strength = avgConfidence,
                    Reliability = CalculateInstitutionalReliability(accumulationFootprints),
                    SupportingTimeframes = supportingTimeframes,
                    EstimatedDuration = TimeSpan.FromMinutes(60),
                    ExpectedDirection = SignalType.Long,
                    ExpectedMagnitude = avgConfidence,
                    SupportingFootprints = accumulationFootprints
                };

                pattern.PatternMetrics["AccumulationFootprints"] = accumulationFootprints.Count;
                pattern.PatternMetrics["AverageConfidence"] = avgConfidence;
                pattern.PatternMetrics["VolumeSignificance"] = accumulationFootprints.Average(f => f.VolumeSignificance);

                pattern.PatternFactors.Add($"Institutional accumulation detected across {supportingTimeframes.Count} timeframes");
                pattern.PatternFactors.Add($"Average footprint confidence: {avgConfidence:P1}");

                patterns.Add(pattern);
            }

            // Smart money flow patterns
            var smartMoneyFootprints = institutionalFootprints
                .Where(f => f.ActivityType == InstitutionalActivityType.SmartMoneyFlow && f.Confidence >= 0.8m)
                .ToList();

            if (smartMoneyFootprints.Count > 0)
            {
                var avgConfidence = smartMoneyFootprints.Average(f => f.Confidence);
                var supportingTimeframes = smartMoneyFootprints.SelectMany(f => f.SupportingTimeframes).Distinct().ToList();

                var pattern = new AdvancedPattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternName = "Smart Money Flow Pattern",
                    Category = AdvancedPatternCategory.Institutional,
                    Confidence = avgConfidence * 0.95m,
                    Strength = avgConfidence,
                    Reliability = CalculateInstitutionalReliability(smartMoneyFootprints),
                    SupportingTimeframes = supportingTimeframes,
                    EstimatedDuration = TimeSpan.FromMinutes(45),
                    ExpectedDirection = DetermineExpectedDirection(timeframeSignals, supportingTimeframes),
                    ExpectedMagnitude = avgConfidence * 1.2m,
                    SupportingFootprints = smartMoneyFootprints
                };

                pattern.PatternMetrics["SmartMoneyFootprints"] = smartMoneyFootprints.Count;
                pattern.PatternMetrics["AverageConfidence"] = avgConfidence;
                pattern.PatternMetrics["CrossTimeframeAlignment"] = (decimal)supportingTimeframes.Count / timeframeSignals.Count;

                pattern.PatternFactors.Add($"Smart money flow detected across {supportingTimeframes.Count} timeframes");
                pattern.PatternFactors.Add($"High institutional confidence: {avgConfidence:P1}");

                patterns.Add(pattern);
            }

            return patterns;
        }

        /// <summary>
        /// Recognize algorithmic patterns
        /// </summary>
        private List<AdvancedPattern> RecognizeAlgorithmicPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            List<CrossTimeframePattern> crossTimeframePatterns,
            MarketContext marketContext)
        {
            var patterns = new List<AdvancedPattern>();

            // Algorithmic trend following patterns
            var trendPatterns = crossTimeframePatterns
                .Where(p => p.PatternType == MultiTimeframePatternType.TrendAlignment && p.Confidence >= 0.8m)
                .ToList();

            if (trendPatterns.Count > 0)
            {
                var strongestPattern = trendPatterns.OrderByDescending(p => p.Confidence).First();
                
                var pattern = new AdvancedPattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternName = "Algorithmic Trend Following",
                    Category = AdvancedPatternCategory.Algorithmic,
                    Confidence = strongestPattern.Confidence * 0.85m,
                    Strength = strongestPattern.PatternStrength,
                    Reliability = CalculateAlgorithmicReliability(trendPatterns),
                    SupportingTimeframes = strongestPattern.SupportingTimeframes,
                    EstimatedDuration = strongestPattern.EstimatedDuration,
                    ExpectedDirection = DetermineExpectedDirection(timeframeSignals, strongestPattern.SupportingTimeframes),
                    ExpectedMagnitude = strongestPattern.Confidence
                };

                pattern.PatternMetrics["TrendPatterns"] = trendPatterns.Count;
                pattern.PatternMetrics["StrongestConfidence"] = strongestPattern.Confidence;
                pattern.PatternMetrics["AlgorithmicScore"] = CalculateAlgorithmicScore(timeframeSignals);

                pattern.PatternFactors.Add($"Strong trend alignment pattern detected");
                pattern.PatternFactors.Add($"Algorithmic trend following behavior identified");

                patterns.Add(pattern);
            }

            return patterns;
        }

        /// <summary>
        /// Recognize volume-price patterns
        /// </summary>
        private List<AdvancedPattern> RecognizeVolumePricePatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> institutionalFootprints,
            MarketContext marketContext)
        {
            var patterns = new List<AdvancedPattern>();

            // Volume confirmation patterns
            var highVolumeFootprints = institutionalFootprints
                .Where(f => f.VolumeSignificance >= 0.8m)
                .ToList();

            if (highVolumeFootprints.Count >= 2)
            {
                var avgVolumeSignificance = highVolumeFootprints.Average(f => f.VolumeSignificance);
                var supportingTimeframes = highVolumeFootprints.SelectMany(f => f.SupportingTimeframes).Distinct().ToList();

                var pattern = new AdvancedPattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternName = "Volume-Price Confirmation",
                    Category = AdvancedPatternCategory.VolumePrice,
                    Confidence = avgVolumeSignificance * 0.8m,
                    Strength = avgVolumeSignificance,
                    Reliability = CalculateVolumePriceReliability(highVolumeFootprints, timeframeSignals),
                    SupportingTimeframes = supportingTimeframes,
                    EstimatedDuration = TimeSpan.FromMinutes(30),
                    ExpectedDirection = DetermineExpectedDirection(timeframeSignals, supportingTimeframes),
                    ExpectedMagnitude = avgVolumeSignificance,
                    SupportingFootprints = highVolumeFootprints
                };

                pattern.PatternMetrics["VolumeFootprints"] = highVolumeFootprints.Count;
                pattern.PatternMetrics["AverageVolumeSignificance"] = avgVolumeSignificance;
                pattern.PatternMetrics["VolumePriceCorrelation"] = CalculateVolumePriceCorrelation(timeframeSignals, highVolumeFootprints);

                pattern.PatternFactors.Add($"High volume significance across {supportingTimeframes.Count} timeframes");
                pattern.PatternFactors.Add($"Volume-price confirmation pattern");

                patterns.Add(pattern);
            }

            return patterns;
        }

        /// <summary>
        /// Recognize order flow patterns
        /// </summary>
        private List<AdvancedPattern> RecognizeOrderFlowPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> institutionalFootprints,
            MarketContext marketContext)
        {
            var patterns = new List<AdvancedPattern>();

            // Order flow imbalance patterns
            var blockTradeFootprints = institutionalFootprints
                .Where(f => f.ActivityType == InstitutionalActivityType.BlockTrade && f.Confidence >= 0.8m)
                .ToList();

            if (blockTradeFootprints.Count > 0)
            {
                var avgConfidence = blockTradeFootprints.Average(f => f.Confidence);
                var supportingTimeframes = blockTradeFootprints.SelectMany(f => f.SupportingTimeframes).Distinct().ToList();

                var pattern = new AdvancedPattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternName = "Order Flow Imbalance",
                    Category = AdvancedPatternCategory.OrderFlow,
                    Confidence = avgConfidence * 0.9m,
                    Strength = avgConfidence,
                    Reliability = CalculateOrderFlowReliability(blockTradeFootprints),
                    SupportingTimeframes = supportingTimeframes,
                    EstimatedDuration = TimeSpan.FromMinutes(15),
                    ExpectedDirection = DetermineExpectedDirection(timeframeSignals, supportingTimeframes),
                    ExpectedMagnitude = avgConfidence * 1.1m,
                    SupportingFootprints = blockTradeFootprints
                };

                pattern.PatternMetrics["BlockTrades"] = blockTradeFootprints.Count;
                pattern.PatternMetrics["OrderFlowStrength"] = avgConfidence;
                pattern.PatternMetrics["PriceImpact"] = blockTradeFootprints.Average(f => f.PriceImpact);

                pattern.PatternFactors.Add($"Block trade order flow detected");
                pattern.PatternFactors.Add($"Strong order flow imbalance pattern");

                patterns.Add(pattern);
            }

            return patterns;
        }

        /// <summary>
        /// Recognize microstructure patterns
        /// </summary>
        private List<AdvancedPattern> RecognizeMicrostructurePatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            List<CrossTimeframePattern> crossTimeframePatterns,
            MarketContext marketContext)
        {
            var patterns = new List<AdvancedPattern>();

            // Microstructure efficiency patterns
            var highQualitySignals = timeframeSignals
                .Where(kvp => kvp.Value.Quality >= SignalQuality.Good && kvp.Value.Confidence >= 0.8m)
                .ToList();

            if (highQualitySignals.Count >= 3)
            {
                var avgConfidence = highQualitySignals.Average(kvp => kvp.Value.Confidence);
                var avgStrength = highQualitySignals.Average(kvp => kvp.Value.Strength);

                var pattern = new AdvancedPattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternName = "Microstructure Efficiency",
                    Category = AdvancedPatternCategory.Microstructure,
                    Confidence = avgConfidence * 0.85m,
                    Strength = avgStrength,
                    Reliability = CalculateMicrostructureReliability(highQualitySignals),
                    SupportingTimeframes = highQualitySignals.Select(kvp => kvp.Key).ToList(),
                    EstimatedDuration = TimeSpan.FromMinutes(25),
                    ExpectedDirection = DetermineExpectedDirection(timeframeSignals, highQualitySignals.Select(kvp => kvp.Key).ToList()),
                    ExpectedMagnitude = avgConfidence
                };

                pattern.PatternMetrics["HighQualitySignals"] = highQualitySignals.Count;
                pattern.PatternMetrics["MicrostructureEfficiency"] = avgConfidence;
                pattern.PatternMetrics["SignalQuality"] = (decimal)highQualitySignals.Average(kvp => (int)kvp.Value.Quality);

                pattern.PatternFactors.Add($"High microstructure efficiency detected");
                pattern.PatternFactors.Add($"Quality signals across {highQualitySignals.Count} timeframes");

                patterns.Add(pattern);
            }

            return patterns;
        }

        /// <summary>
        /// Recognize momentum patterns
        /// </summary>
        private List<AdvancedPattern> RecognizeMomentumPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            List<CrossTimeframePattern> crossTimeframePatterns,
            MarketContext marketContext)
        {
            var patterns = new List<AdvancedPattern>();

            // Momentum acceleration patterns
            var continuationPatterns = crossTimeframePatterns
                .Where(p => p.PatternType == MultiTimeframePatternType.ContinuationPattern && p.Confidence >= 0.8m)
                .ToList();

            if (continuationPatterns.Count > 0)
            {
                var strongestPattern = continuationPatterns.OrderByDescending(p => p.Confidence).First();

                var pattern = new AdvancedPattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternName = "Momentum Acceleration",
                    Category = AdvancedPatternCategory.Momentum,
                    Confidence = strongestPattern.Confidence * 0.9m,
                    Strength = strongestPattern.PatternStrength,
                    Reliability = CalculateMomentumReliability(continuationPatterns),
                    SupportingTimeframes = strongestPattern.SupportingTimeframes,
                    EstimatedDuration = strongestPattern.EstimatedDuration,
                    ExpectedDirection = DetermineExpectedDirection(timeframeSignals, strongestPattern.SupportingTimeframes),
                    ExpectedMagnitude = strongestPattern.Confidence * 1.3m
                };

                pattern.PatternMetrics["ContinuationPatterns"] = continuationPatterns.Count;
                pattern.PatternMetrics["MomentumStrength"] = strongestPattern.PatternStrength;
                pattern.PatternMetrics["AccelerationFactor"] = CalculateAccelerationFactor(timeframeSignals, strongestPattern.SupportingTimeframes);

                pattern.PatternFactors.Add($"Strong momentum acceleration detected");
                pattern.PatternFactors.Add($"Continuation pattern support");

                patterns.Add(pattern);
            }

            return patterns;
        }

        /// <summary>
        /// Validate advanced pattern
        /// </summary>
        private void ValidateAdvancedPattern(
            AdvancedPattern pattern,
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> institutionalFootprints,
            MarketContext marketContext)
        {
            var validationResult = new SmartVolumeStrategy.Core.Models.Analysis.PatternValidationResult();
            var validationScore = 0m;

            // Validate based on timeframe support
            var timeframeSupport = (decimal)pattern.SupportingTimeframes.Count / timeframeSignals.Count;
            validationScore += timeframeSupport * 0.25m;
            validationResult.ValidationFactors.Add($"Timeframe support: {timeframeSupport:P0}");

            // Validate based on confidence
            validationScore += pattern.Confidence * 0.25m;
            validationResult.ValidationFactors.Add($"Pattern confidence: {pattern.Confidence:P1}");

            // Validate based on institutional support
            var institutionalSupport = CalculateInstitutionalSupport(pattern, institutionalFootprints);
            validationScore += institutionalSupport * 0.25m;
            validationResult.ValidationFactors.Add($"Institutional support: {institutionalSupport:P1}");

            // Validate based on market conditions
            var marketConditionScore = GetMarketConditionScore(pattern.Category, marketContext);
            validationScore += marketConditionScore * 0.25m;
            validationResult.ValidationFactors.Add($"Market condition alignment: {marketConditionScore:P1}");

            // Apply institutional confirmation boost
            if (institutionalSupport >= 0.7m)
            {
                validationScore += INSTITUTIONAL_CONFIRMATION_BOOST;
                validationResult.ValidationFactors.Add($"Institutional confirmation boost: {INSTITUTIONAL_CONFIRMATION_BOOST:P1}");
            }

            validationResult.ValidationScore = validationScore;
            validationResult.IsValid = validationScore >= PATTERN_VALIDATION_THRESHOLD;

            // Update pattern with validation results
            pattern.Reliability = validationScore;
            pattern.RequiresConfirmation = !validationResult.IsValid;
            pattern.PatternFactors.AddRange(validationResult.ValidationFactors);

            if (validationResult.IsValid)
            {
                pattern.Confidence = Math.Min(1.0m, pattern.Confidence + (validationScore - PATTERN_VALIDATION_THRESHOLD) * 0.1m);
            }
        }

        /// <summary>
        /// Recognize basic patterns for degraded mode
        /// </summary>
        private List<AdvancedPattern> RecognizeBasicPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            List<CrossTimeframePattern> crossTimeframePatterns)
        {
            var patterns = new List<AdvancedPattern>();

            // Simple high-confidence pattern detection
            var strongPatterns = crossTimeframePatterns
                .Where(p => p.Confidence >= 0.8m)
                .ToList();

            foreach (var crossPattern in strongPatterns)
            {
                var pattern = new AdvancedPattern
                {
                    Timestamp = DateTime.UtcNow,
                    PatternName = $"Basic {crossPattern.PatternType}",
                    Category = AdvancedPatternCategory.Momentum,
                    Confidence = crossPattern.Confidence * 0.8m, // Discount for basic mode
                    Strength = crossPattern.PatternStrength,
                    Reliability = crossPattern.Confidence * 0.8m,
                    SupportingTimeframes = crossPattern.SupportingTimeframes,
                    EstimatedDuration = crossPattern.EstimatedDuration,
                    ExpectedDirection = DetermineExpectedDirection(timeframeSignals, crossPattern.SupportingTimeframes),
                    ExpectedMagnitude = crossPattern.Confidence
                };

                pattern.PatternFactors.Add("Basic pattern recognition (degraded mode)");
                patterns.Add(pattern);
            }

            return patterns;
        }

        /// <summary>
        /// Update pattern history
        /// </summary>
        private void UpdatePatternHistory(AdvancedPattern pattern)
        {
            _patternHistory.Enqueue(pattern);

            while (_patternHistory.Count > MAX_PATTERN_HISTORY)
            {
                _patternHistory.Dequeue();
            }

            // Update category confidence history
            if (_categoryConfidenceHistory.ContainsKey(pattern.Category))
            {
                var confidenceHistory = _categoryConfidenceHistory[pattern.Category];
                confidenceHistory.Enqueue(pattern.Confidence);

                while (confidenceHistory.Count > CONFIDENCE_HISTORY_SIZE)
                {
                    confidenceHistory.Dequeue();
                }
            }
        }

        // Helper methods for pattern analysis
        private decimal CalculateAlgorithmicReliability(List<CrossTimeframePattern> patterns)
        {
            if (patterns.Count == 0) return 0m;
            return patterns.Average(p => p.Confidence * p.PatternStrength);
        }

        private decimal CalculateVolumePriceReliability(List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> footprints, Dictionary<Timeframe, TradingSignal> signals)
        {
            if (footprints.Count == 0) return 0m;

            var volumeScore = footprints.Average(f => f.VolumeSignificance);
            var priceScore = signals.Values.Average(s => s.Confidence);

            return (volumeScore + priceScore) / 2m;
        }

        private decimal CalculateOrderFlowReliability(List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> footprints)
        {
            if (footprints.Count == 0) return 0m;
            return footprints.Average(f => f.Confidence * f.PriceImpact);
        }

        private decimal CalculateMicrostructureReliability(List<KeyValuePair<Timeframe, TradingSignal>> signals)
        {
            if (signals.Count == 0) return 0m;
            return signals.Average(kvp => kvp.Value.Confidence * (decimal)kvp.Value.Quality / 4m);
        }

        private decimal CalculateMomentumReliability(List<CrossTimeframePattern> patterns)
        {
            if (patterns.Count == 0) return 0m;
            return patterns.Average(p => p.Confidence * p.PatternStrength * 1.2m);
        }

        private decimal CalculateAlgorithmicScore(Dictionary<Timeframe, TradingSignal> signals)
        {
            var confidenceVariance = CalculateVariance(signals.Values.Select(s => s.Confidence).ToList());
            return Math.Max(0m, 1m - confidenceVariance * 2m); // Lower variance = higher algorithmic score
        }

        private decimal CalculateVolumePriceCorrelation(Dictionary<Timeframe, TradingSignal> signals, List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> footprints)
        {
            if (signals.Count == 0 || footprints.Count == 0) return 0.5m;

            var avgSignalConfidence = signals.Values.Average(s => s.Confidence);
            var avgVolumeSignificance = footprints.Average(f => f.VolumeSignificance);

            return Math.Min(1.0m, (avgSignalConfidence + avgVolumeSignificance) / 2m);
        }

        private decimal CalculateAccelerationFactor(Dictionary<Timeframe, TradingSignal> signals, List<Timeframe> timeframes)
        {
            var supportingSignals = signals.Where(kvp => timeframes.Contains(kvp.Key)).ToList();
            if (supportingSignals.Count < 2) return 1.0m;

            // Calculate confidence trend across timeframes
            var orderedSignals = supportingSignals.OrderBy(kvp => kvp.Key).ToList();
            var confidenceTrend = 0m;

            for (int i = 1; i < orderedSignals.Count; i++)
            {
                confidenceTrend += orderedSignals[i].Value.Confidence - orderedSignals[i-1].Value.Confidence;
            }

            return Math.Max(1.0m, 1.0m + confidenceTrend);
        }

        private decimal CalculateInstitutionalSupport(AdvancedPattern pattern, List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> footprints)
        {
            var relevantFootprints = footprints
                .Where(f => f.SupportingTimeframes.Any(tf => pattern.SupportingTimeframes.Contains(tf)))
                .ToList();

            if (relevantFootprints.Count == 0) return 0m;

            return relevantFootprints.Average(f => f.Confidence);
        }

        private decimal GetMarketConditionScore(AdvancedPatternCategory category, MarketContext marketContext)
        {
            return category switch
            {
                AdvancedPatternCategory.Institutional => marketContext.IsOptimalTradingTime ? 0.9m : 0.7m,
                AdvancedPatternCategory.Algorithmic => 0.8m, // Neutral to market conditions
                AdvancedPatternCategory.VolumePrice => marketContext.VolatilityRegime >= VolatilityRegime.Normal ? 0.8m : 0.6m,
                AdvancedPatternCategory.OrderFlow => marketContext.VolatilityRegime >= VolatilityRegime.High ? 0.9m : 0.7m,
                AdvancedPatternCategory.Microstructure => marketContext.VolatilityRegime <= VolatilityRegime.Normal ? 0.8m : 0.6m,
                AdvancedPatternCategory.Momentum => marketContext.VolatilityRegime >= VolatilityRegime.Normal ? 0.9m : 0.6m,
                _ => 0.7m
            };
        }

        private decimal CalculateVariance(List<decimal> values)
        {
            if (values.Count < 2) return 0m;

            var mean = values.Average();
            var variance = values.Sum(v => (v - mean) * (v - mean)) / values.Count;
            return variance;
        }

        private void InitializeCategoryHistory()
        {
            foreach (AdvancedPatternCategory category in Enum.GetValues<AdvancedPatternCategory>())
            {
                _categoryConfidenceHistory[category] = new Queue<decimal>();
            }
        }

        private decimal CalculateInstitutionalReliability(List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> footprints)
        {
            if (footprints.Count == 0) return 0m;

            var avgConfidence = footprints.Average(f => f.Confidence);
            var avgValidationScore = footprints.Where(f => f.IsValidated).Average(f => f.ValidationScore);
            var validationRatio = (decimal)footprints.Count(f => f.IsValidated) / footprints.Count;

            return (avgConfidence * 0.4m) + (avgValidationScore * 0.4m) + (validationRatio * 0.2m);
        }

        private SignalType DetermineExpectedDirection(Dictionary<Timeframe, TradingSignal> timeframeSignals, List<Timeframe> supportingTimeframes)
        {
            var supportingSignals = timeframeSignals
                .Where(kvp => supportingTimeframes.Contains(kvp.Key))
                .Select(kvp => kvp.Value)
                .ToList();

            if (supportingSignals.Count == 0) return SignalType.None;

            var signalTypes = supportingSignals.GroupBy(s => s.Type).OrderByDescending(g => g.Count()).ToList();
            return signalTypes.First().Key;
        }
    }
}
