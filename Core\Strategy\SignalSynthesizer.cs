using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Core.Analysis;
using SmartVolumeStrategy.Core.Models.Analysis;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;
using SignalConsistency = SmartVolumeStrategy.Models.SignalConsistency;
using ConflictResolutionStrategy = SmartVolumeStrategy.Models.ConflictResolutionStrategy;

namespace SmartVolumeStrategy.Core.Strategy
{
    /// <summary>
    /// Advanced signal synthesizer that combines multiple signal sources intelligently
    /// Provides signal correlation analysis, conflict resolution, and microstructure filtering
    /// </summary>
    public class SignalSynthesizer : ISignalSynthesizer
    {
        #region Fields

        private readonly object _lockObject = new object();
        private readonly Action<string> _logAction;

        private SignalSynthesisConfig _config;
        private SignalSynthesisMetrics _performanceMetrics;
        private List<SynthesizedSignal> _signalHistory;
        private bool _isInitialized;

        // Phase 2.2: Advanced Microstructure Filtering
        private readonly AdaptiveMicrostructureFilter _microstructureFilter;

        // Phase 2.3: Circuit Breaker Integration
        private CircuitBreakerManager _circuitBreakerManager;

        #endregion

        #region Constructor

        public SignalSynthesizer(Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _performanceMetrics = new SignalSynthesisMetrics();
            _signalHistory = new List<SynthesizedSignal>();
            _isInitialized = false;

            // Phase 2.2: Initialize Advanced Microstructure Filter
            _microstructureFilter = new AdaptiveMicrostructureFilter(logAction);
        }

        #endregion

        #region Public Methods

        public void Initialize(SignalSynthesisConfig config = null)
        {
            lock (_lockObject)
            {
                _config = config ?? new SignalSynthesisConfig();
                _performanceMetrics = new SignalSynthesisMetrics();
                _signalHistory = new List<SynthesizedSignal>();
                _isInitialized = true;

                _logAction("🔧 SignalSynthesizer initialized with advanced synthesis capabilities");
            }
        }

        /// <summary>
        /// Phase 2.3: Set circuit breaker manager for resilience integration
        /// </summary>
        public void SetCircuitBreakerManager(CircuitBreakerManager circuitBreakerManager)
        {
            _circuitBreakerManager = circuitBreakerManager;
            _logAction("🔧 Circuit Breaker Manager integrated with SignalSynthesizer");
        }

        public SynthesizedSignal SynthesizeSignal(MultiTimeframeAnalysisState multiTimeframeState, MarketContext marketContext)
        {
            if (!_isInitialized)
                throw new InvalidOperationException("SignalSynthesizer must be initialized before synthesizing signals");

            if (multiTimeframeState == null || marketContext == null)
                return null;

            lock (_lockObject)
            {
                var startTime = DateTime.UtcNow;

                try
                {
                    // Phase 2.3: Check circuit breaker status before proceeding
                    if (_circuitBreakerManager != null)
                    {
                        if (!_circuitBreakerManager.IsOperationAllowed(StrategyComponent.SignalGeneration))
                        {
                            _logAction("🔴 Signal synthesis blocked by circuit breaker");
                            _circuitBreakerManager.RecordOperation(StrategyComponent.SignalGeneration, false, "Operation blocked by circuit breaker", FailureSeverity.Medium);
                            return null;
                        }

                        // Check degradation level and adjust functionality
                        var degradationManager = _circuitBreakerManager.GetDegradationManager();
                        var degradationLevel = degradationManager.GetCurrentDegradationLevel();

                        if (degradationLevel >= DegradationLevel.Emergency)
                        {
                            _logAction("🚨 Signal synthesis disabled due to emergency degradation mode");
                            return null;
                        }
                    }

                    _logAction("🎯 Starting advanced signal synthesis...");

                    // Generate timeframe-specific signals
                    var timeframeSignals = GenerateTimeframeSignals(multiTimeframeState, marketContext);

                    // Analyze signal correlation
                    var correlationAnalysis = AnalyzeSignalCorrelation(timeframeSignals);

                    // Resolve conflicts if they exist
                    SynthesizedSignal synthesizedSignal;
                    if (correlationAnalysis.HasSignificantDivergence)
                    {
                        _logAction($"⚠️ Signal divergence detected across {correlationAnalysis.DivergentTimeframes.Count} timeframes");
                        synthesizedSignal = ResolveSignalConflicts(timeframeSignals.Values.ToList(), marketContext);
                        _performanceMetrics.ConflictResolutions++;
                    }
                    else
                    {
                        synthesizedSignal = CreateConsensusSignal(timeframeSignals, correlationAnalysis, multiTimeframeState);
                    }

                    // Phase 2.2: Apply advanced microstructure filter
                    if (_config.EnableMicrostructureFilter && synthesizedSignal != null)
                    {
                        var filterResult = _microstructureFilter.ApplyFilter(
                            synthesizedSignal,
                            marketContext,
                            multiTimeframeState.TimeframeStates.Values.FirstOrDefault(),
                            _config);

                        if (!filterResult.PassesFilter)
                        {
                            _logAction($"🚫 Signal filtered out by advanced microstructure analysis: {filterResult.FilterReason}");
                            _logAction($"   Filter Score: {filterResult.OverallScore:F3}, Quality: {filterResult.FilterQuality}");
                            synthesizedSignal = null;
                            _performanceMetrics.FilteredSignals++;
                        }
                        else
                        {
                            _logAction($"✅ Signal passed advanced microstructure filter (Score: {filterResult.OverallScore:F3}, Quality: {filterResult.FilterQuality})");
                            if (filterResult.HasHealthWarning)
                            {
                                _logAction($"⚠️ Microstructure health warning: {filterResult.FilterReason}");
                            }
                        }
                    }

                    // Track signal persistence if signal generated
                    if (synthesizedSignal != null)
                    {
                        var persistenceAnalysis = TrackSignalPersistence(synthesizedSignal, _signalHistory);
                        synthesizedSignal.Duration = persistenceAnalysis.SignalAge;
                        synthesizedSignal.Momentum = persistenceAnalysis.MomentumScore;
                        synthesizedSignal.IsPersistent = persistenceAnalysis.IsPersistent;

                        // Add to history
                        _signalHistory.Add(synthesizedSignal);
                        
                        // Maintain history size
                        while (_signalHistory.Count > _config.SignalHistorySize)
                        {
                            _signalHistory.RemoveAt(0);
                        }

                        _performanceMetrics.SuccessfulSyntheses++;
                        _logAction($"✅ Signal synthesized: {synthesizedSignal.Type} (Confidence: {synthesizedSignal.Confidence:P1}, Quality: {synthesizedSignal.Quality})");
                    }

                    _performanceMetrics.TotalSignalsSynthesized++;
                    _performanceMetrics.AverageSynthesisTime = (decimal)(DateTime.UtcNow - startTime).TotalMilliseconds;

                    // Phase 2.3: Record successful operation with circuit breaker
                    if (_circuitBreakerManager != null)
                    {
                        var wasSuccessful = synthesizedSignal != null;
                        var details = wasSuccessful ? $"Signal synthesized: {synthesizedSignal.Type}" : "No signal generated";
                        _circuitBreakerManager.RecordOperation(StrategyComponent.SignalGeneration, wasSuccessful, details);

                        // Update response time
                        var responseTime = DateTime.UtcNow - startTime;
                        _circuitBreakerManager.GetComponentHealthTracker().UpdateResponseTime(StrategyComponent.SignalGeneration, responseTime);
                    }

                    return synthesizedSignal;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Signal synthesis error: {ex.Message}");

                    // Phase 2.3: Record failure with circuit breaker
                    if (_circuitBreakerManager != null)
                    {
                        _circuitBreakerManager.RecordOperation(StrategyComponent.SignalGeneration, false, ex.Message, FailureSeverity.High);
                    }

                    return null;
                }
            }
        }

        public SignalCorrelationAnalysis AnalyzeSignalCorrelation(Dictionary<Timeframe, TradingSignal> timeframeSignals)
        {
            if (timeframeSignals == null || timeframeSignals.Count == 0)
                return new SignalCorrelationAnalysis { Consistency = SignalConsistency.NoSignal };

            var analysis = new SignalCorrelationAnalysis
            {
                Timestamp = DateTime.UtcNow
            };

            // Group signals by type
            var signalTypes = timeframeSignals.Values.GroupBy(s => s.Type).ToList();
            var dominantType = signalTypes.OrderByDescending(g => g.Count()).First();

            // Calculate consistency
            var agreementRatio = (decimal)dominantType.Count() / timeframeSignals.Count;
            analysis.Consistency = agreementRatio switch
            {
                >= 1.0m => SignalConsistency.FullyAligned,
                >= 0.75m => SignalConsistency.MostlyAligned,
                >= 0.5m => SignalConsistency.PartiallyAligned,
                > 0m => SignalConsistency.Conflicted,
                _ => SignalConsistency.NoSignal
            };

            // Calculate overall correlation
            analysis.OverallCorrelation = agreementRatio;

            // Identify divergent timeframes
            analysis.DivergentTimeframes = timeframeSignals
                .Where(kvp => kvp.Value.Type != dominantType.Key)
                .Select(kvp => kvp.Key)
                .ToList();

            analysis.HasSignificantDivergence = analysis.DivergentTimeframes.Count > timeframeSignals.Count * 0.3m;

            // Calculate timeframe-specific correlations
            foreach (var kvp in timeframeSignals)
            {
                var correlation = kvp.Value.Type == dominantType.Key ? 1.0m : -0.5m;
                analysis.TimeframeCorrelations[kvp.Key] = correlation;
            }

            // Add correlation factors
            analysis.CorrelationFactors.Add($"Dominant signal: {dominantType.Key}");
            analysis.CorrelationFactors.Add($"Agreement ratio: {agreementRatio:P1}");
            if (analysis.HasSignificantDivergence)
            {
                analysis.CorrelationFactors.Add($"Divergent timeframes: {string.Join(", ", analysis.DivergentTimeframes)}");
            }

            return analysis;
        }

        public SynthesizedSignal ResolveSignalConflicts(List<TradingSignal> conflictingSignals, MarketContext context)
        {
            if (conflictingSignals == null || conflictingSignals.Count == 0)
                return null;

            _logAction($"🔄 Resolving conflicts between {conflictingSignals.Count} signals using {_config.ConflictResolution} strategy");

            var resolution = _config.ConflictResolution;
            
            // Track resolution strategy usage
            if (!_performanceMetrics.ResolutionStrategyUsage.ContainsKey(resolution))
                _performanceMetrics.ResolutionStrategyUsage[resolution] = 0;
            _performanceMetrics.ResolutionStrategyUsage[resolution]++;

            return resolution switch
            {
                ConflictResolutionStrategy.WeightedConsensus => ResolveByWeightedConsensus(conflictingSignals, context),
                ConflictResolutionStrategy.HigherTimeframeBias => ResolveByHigherTimeframeBias(conflictingSignals),
                ConflictResolutionStrategy.HighestConfidence => ResolveByHighestConfidence(conflictingSignals),
                ConflictResolutionStrategy.MajorityRule => ResolveByMajorityRule(conflictingSignals),
                ConflictResolutionStrategy.NoSignal => null,
                _ => ResolveByWeightedConsensus(conflictingSignals, context)
            };
        }

        public SignalPersistenceAnalysis TrackSignalPersistence(SynthesizedSignal signal, List<SynthesizedSignal> historicalSignals)
        {
            if (signal == null)
                return new SignalPersistenceAnalysis();

            var analysis = new SignalPersistenceAnalysis
            {
                Timestamp = DateTime.UtcNow
            };

            // Find similar signals in history
            var similarSignals = historicalSignals
                .Where(s => s.Type == signal.Type && 
                           Math.Abs((s.Timestamp - signal.Timestamp).TotalMinutes) <= 30)
                .OrderByDescending(s => s.Timestamp)
                .ToList();

            if (similarSignals.Count > 0)
            {
                var latestSimilar = similarSignals.First();
                analysis.SignalAge = signal.Timestamp - latestSimilar.Timestamp;
                
                // Calculate persistence score based on signal consistency
                var consistentSignals = similarSignals.Count(s => Math.Abs(s.Confidence - signal.Confidence) <= 0.2m);
                analysis.PersistenceScore = (decimal)consistentSignals / Math.Max(similarSignals.Count, 1);
                
                // Calculate momentum based on confidence trend
                if (similarSignals.Count >= 2)
                {
                    var confidenceTrend = signal.Confidence - similarSignals.Skip(1).First().Confidence;
                    analysis.MomentumScore = Math.Max(0, Math.Min(1.0m, 0.5m + confidenceTrend));
                    analysis.IsAccelerating = confidenceTrend > 0.1m;
                    analysis.IsDecaying = confidenceTrend < -0.1m;
                }
            }
            else
            {
                // New signal
                analysis.SignalAge = TimeSpan.Zero;
                analysis.PersistenceScore = 0.5m;
                analysis.MomentumScore = signal.Confidence;
            }

            // Determine if signal is persistent
            analysis.IsPersistent = analysis.PersistenceScore >= _config.MinMomentumThreshold &&
                                   analysis.SignalAge >= _config.MinSignalDuration;

            // Add persistence factors
            if (analysis.IsPersistent)
                analysis.PersistenceFactors.Add("Signal shows persistence across time");
            if (analysis.IsAccelerating)
                analysis.PersistenceFactors.Add("Signal momentum is accelerating");
            if (analysis.IsDecaying)
                analysis.PersistenceFactors.Add("Signal momentum is decaying");

            // Estimate remaining duration
            if (analysis.IsPersistent)
            {
                var avgDuration = similarSignals.Count > 0 ? 
                    TimeSpan.FromMinutes(similarSignals.Average(s => s.Duration.TotalMinutes)) :
                    TimeSpan.FromMinutes(15);
                analysis.EstimatedRemainingDuration = TimeSpan.FromTicks(Math.Max(0, avgDuration.Ticks - analysis.SignalAge.Ticks));
            }

            return analysis;
        }

        /// <summary>
        /// Phase 2.2: Get microstructure health status
        /// </summary>
        public SmartVolumeStrategy.Core.Models.Analysis.MicrostructureHealthStatus GetMicrostructureHealthStatus()
        {
            return _microstructureFilter.GetHealthStatus();
        }

        /// <summary>
        /// Phase 2.2: Get microstructure filter statistics
        /// </summary>
        public MicrostructureFilterStatistics GetMicrostructureFilterStatistics()
        {
            return _microstructureFilter.GetStatistics();
        }

        /// <summary>
        /// Phase 2.3: Get system resilience status
        /// </summary>
        public SystemResilienceStatus GetSystemResilienceStatus()
        {
            return _circuitBreakerManager?.GetSystemStatus() ?? new SystemResilienceStatus
            {
                Timestamp = DateTime.UtcNow,
                DegradationLevel = DegradationLevel.Normal,
                OverallSystemHealth = 1.0m
            };
        }

        /// <summary>
        /// Phase 2.3: Get current degradation level
        /// </summary>
        public DegradationLevel GetCurrentDegradationLevel()
        {
            return _circuitBreakerManager?.GetDegradationManager()?.GetCurrentDegradationLevel() ?? DegradationLevel.Normal;
        }

        /// <summary>
        /// Phase 2.3: Check if a feature is available at current degradation level
        /// </summary>
        public bool IsFeatureAvailable(string feature)
        {
            return _circuitBreakerManager?.GetDegradationManager()?.IsFeatureAvailable(feature) ?? true;
        }

        /// <summary>
        /// Phase 2.2: Legacy method maintained for backward compatibility
        /// Now uses the advanced microstructure filter internally
        /// </summary>
        public bool PassesMicrostructureFilter(SynthesizedSignal signal, MarketContext context, AnalysisState analysisState)
        {
            if (signal == null || context == null)
                return false;

            try
            {
                var filterResult = _microstructureFilter.ApplyFilter(signal, context, analysisState, _config);
                return filterResult.PassesFilter;
            }
            catch (Exception ex)
            {
                _logAction($"❌ Error in microstructure filter: {ex.Message}");
                return false; // Fail safe
            }
        }

        public SignalSynthesisMetrics GetPerformanceMetrics()
        {
            lock (_lockObject)
            {
                return new SignalSynthesisMetrics
                {
                    LastUpdate = _performanceMetrics.LastUpdate,
                    TotalSignalsSynthesized = _performanceMetrics.TotalSignalsSynthesized,
                    SuccessfulSyntheses = _performanceMetrics.SuccessfulSyntheses,
                    ConflictResolutions = _performanceMetrics.ConflictResolutions,
                    FilteredSignals = _performanceMetrics.FilteredSignals,
                    AverageSignalQuality = _performanceMetrics.AverageSignalQuality,
                    AverageSynthesisTime = _performanceMetrics.AverageSynthesisTime,
                    ResolutionStrategyUsage = new Dictionary<ConflictResolutionStrategy, int>(_performanceMetrics.ResolutionStrategyUsage)
                };
            }
        }

        public void Reset()
        {
            lock (_lockObject)
            {
                _signalHistory.Clear();
                _performanceMetrics = new SignalSynthesisMetrics();
                _isInitialized = false;
                _logAction("🔄 SignalSynthesizer reset to initial state");
            }
        }

        #endregion

        #region Private Methods

        private Dictionary<Timeframe, TradingSignal> GenerateTimeframeSignals(MultiTimeframeAnalysisState multiTimeframeState, MarketContext marketContext)
        {
            var timeframeSignals = new Dictionary<Timeframe, TradingSignal>();

            foreach (var kvp in multiTimeframeState.TimeframeStates)
            {
                var timeframe = kvp.Key;
                var analysisState = kvp.Value;

                // Generate signal for this timeframe based on analysis state
                var signal = GenerateTimeframeSpecificSignal(timeframe, analysisState, marketContext);
                if (signal != null)
                {
                    timeframeSignals[timeframe] = signal;
                }
            }

            return timeframeSignals;
        }

        private TradingSignal GenerateTimeframeSpecificSignal(Timeframe timeframe, AnalysisState analysisState, MarketContext marketContext)
        {
            // Simplified signal generation - in production, this would use the actual SignalGenerator
            var volumeConfidence = analysisState.Volume?.VolumeConfidence ?? 0.5m;
            var deltaConfidence = analysisState.DeltaFlow?.DeltaConfidence ?? 0.5m;
            var overallConfidence = (volumeConfidence + deltaConfidence) / 2;

            if (overallConfidence < 0.5m)
                return null;

            // Determine signal type based on analysis
            var signalType = SignalType.None;
            if (analysisState.Volume?.IsVolumeSpikeActive == true)
            {
                signalType = analysisState.DeltaFlow?.CurrentBias == DeltaBias.BuyPressure ? SignalType.Long : SignalType.Short;
            }

            if (signalType == SignalType.None)
                return null;

            return new TradingSignal
            {
                Type = signalType,
                Confidence = overallConfidence,
                Quality = overallConfidence >= 0.8m ? SignalQuality.Excellent :
                         overallConfidence >= 0.6m ? SignalQuality.Good : SignalQuality.Fair,
                Timestamp = DateTime.UtcNow,
                PrimaryReason = $"Timeframe {timeframe} analysis",
                SecondaryReasons = new List<string> { $"Volume confidence: {volumeConfidence:P1}", $"Delta confidence: {deltaConfidence:P1}" }
            };
        }

        private SynthesizedSignal CreateConsensusSignal(Dictionary<Timeframe, TradingSignal> timeframeSignals, SignalCorrelationAnalysis correlationAnalysis, MultiTimeframeAnalysisState multiTimeframeState)
        {
            if (timeframeSignals.Count == 0)
                return null;

            // Find dominant signal type
            var signalTypes = timeframeSignals.Values.GroupBy(s => s.Type);
            var dominantType = signalTypes.OrderByDescending(g => g.Count()).First();

            // Calculate weighted confidence
            var weightedConfidence = CalculateWeightedSignalConfidence(timeframeSignals);

            // Create synthesized signal
            var synthesizedSignal = new SynthesizedSignal
            {
                Timestamp = DateTime.UtcNow,
                Type = dominantType.Key,
                Strength = correlationAnalysis.OverallCorrelation,
                Confidence = weightedConfidence,
                Quality = DetermineSignalQuality(weightedConfidence, correlationAnalysis.Consistency)
            };

            // Add timeframe contributions
            foreach (var kvp in timeframeSignals)
            {
                var weight = _config.TimeframeWeights.ContainsKey(kvp.Key) ? 
                    _config.TimeframeWeights[kvp.Key] : 0.25m;
                synthesizedSignal.TimeframeContributions[kvp.Key] = kvp.Value.Confidence * weight;
            }

            // Add supporting factors
            synthesizedSignal.SupportingFactors.Add($"Consensus across {timeframeSignals.Count} timeframes");
            synthesizedSignal.SupportingFactors.Add($"Signal consistency: {correlationAnalysis.Consistency}");
            synthesizedSignal.SupportingFactors.Add($"Overall correlation: {correlationAnalysis.OverallCorrelation:P1}");

            // Add pattern support if available
            if (multiTimeframeState.DetectedPatterns.Any())
            {
                var supportingPatterns = multiTimeframeState.DetectedPatterns
                    .Where(p => p.Confidence >= 0.6m)
                    .Select(p => p.PatternName)
                    .ToList();
                
                synthesizedSignal.SupportingPatterns.AddRange(supportingPatterns);
                if (supportingPatterns.Any())
                {
                    synthesizedSignal.PrimaryPattern = supportingPatterns.First();
                }
            }

            return synthesizedSignal;
        }

        private decimal CalculateWeightedSignalConfidence(Dictionary<Timeframe, TradingSignal> timeframeSignals)
        {
            decimal weightedSum = 0;
            decimal totalWeight = 0;

            foreach (var kvp in timeframeSignals)
            {
                var timeframe = kvp.Key;
                var signal = kvp.Value;
                var weight = _config.TimeframeWeights.ContainsKey(timeframe) ? 
                    _config.TimeframeWeights[timeframe] : 0.25m;

                weightedSum += signal.Confidence * weight;
                totalWeight += weight;
            }

            return totalWeight > 0 ? weightedSum / totalWeight : 0.5m;
        }

        private SignalQuality DetermineSignalQuality(decimal confidence, SignalConsistency consistency)
        {
            if (confidence >= 0.8m && consistency == SignalConsistency.FullyAligned)
                return SignalQuality.Excellent;
            if (confidence >= 0.7m && consistency >= SignalConsistency.MostlyAligned)
                return SignalQuality.Good;
            if (confidence >= 0.5m && consistency >= SignalConsistency.PartiallyAligned)
                return SignalQuality.Fair;
            
            return SignalQuality.Poor;
        }

        private SynthesizedSignal ResolveByWeightedConsensus(List<TradingSignal> signals, MarketContext context)
        {
            // Group by signal type and calculate weighted scores
            var typeScores = new Dictionary<SignalType, decimal>();
            
            foreach (var signal in signals)
            {
                if (!typeScores.ContainsKey(signal.Type))
                    typeScores[signal.Type] = 0;
                
                typeScores[signal.Type] += signal.Confidence;
            }

            var winningType = typeScores.OrderByDescending(kvp => kvp.Value).First();
            
            if (winningType.Value < _config.MinConsensusThreshold * signals.Count)
                return null; // No consensus

            return new SynthesizedSignal
            {
                Type = winningType.Key,
                Confidence = winningType.Value / signals.Count,
                Strength = 0.7m,
                Quality = SignalQuality.Fair,
                SupportingFactors = new List<string> { "Weighted consensus resolution" }
            };
        }

        private SynthesizedSignal ResolveByHigherTimeframeBias(List<TradingSignal> signals)
        {
            // Prefer signals from higher timeframes (H1 > M15 > M5 > M1)
            var timeframeOrder = new[] { Timeframe.H1, Timeframe.M15, Timeframe.M5, Timeframe.M1 };
            
            // This is simplified - in production, you'd need to track which signal came from which timeframe
            var bestSignal = signals.OrderByDescending(s => s.Confidence).First();
            
            return new SynthesizedSignal
            {
                Type = bestSignal.Type,
                Confidence = bestSignal.Confidence * 0.9m, // Slight reduction for conflict resolution
                Strength = 0.8m,
                Quality = SignalQuality.Good,
                SupportingFactors = new List<string> { "Higher timeframe bias resolution" }
            };
        }

        private SynthesizedSignal ResolveByHighestConfidence(List<TradingSignal> signals)
        {
            var bestSignal = signals.OrderByDescending(s => s.Confidence).First();
            
            return new SynthesizedSignal
            {
                Type = bestSignal.Type,
                Confidence = bestSignal.Confidence,
                Strength = 0.75m,
                Quality = SignalQuality.Good,
                SupportingFactors = new List<string> { "Highest confidence resolution" }
            };
        }

        private SynthesizedSignal ResolveByMajorityRule(List<TradingSignal> signals)
        {
            var typeGroups = signals.GroupBy(s => s.Type);
            var majorityGroup = typeGroups.OrderByDescending(g => g.Count()).First();
            
            if (majorityGroup.Count() <= signals.Count / 2)
                return null; // No majority

            return new SynthesizedSignal
            {
                Type = majorityGroup.Key,
                Confidence = majorityGroup.Average(s => s.Confidence),
                Strength = (decimal)majorityGroup.Count() / signals.Count,
                Quality = SignalQuality.Fair,
                SupportingFactors = new List<string> { "Majority rule resolution" }
            };
        }

        #endregion
    }
}
