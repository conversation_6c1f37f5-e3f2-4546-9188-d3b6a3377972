using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Interfaces;

namespace SmartVolumeStrategy.Core.Calibration
{
    /// <summary>
    /// Adaptive calibration system that dynamically adjusts strategy parameters
    /// based on performance metrics and market regime changes
    /// </summary>
    public class AdaptiveCalibrator : IAdaptiveCalibrator
    {
        private readonly ISettingsCalibrator _baseCalibrator;
        private readonly Action<string> _logAction;
        
        private SymbolProfile _symbolProfile;
        private OptimalSettings _currentSettings;
        private DateTime _lastCalibrationTime;
        private decimal _calibrationConfidence;
        private CalibrationHistory _calibrationHistory;
        private PerformanceMetrics _lastPerformanceMetrics;
        private MarketRegimeChange _lastRegimeChange;
        
        // Adaptive calibration parameters
        private readonly decimal _confidenceDecayRate = 0.95m; // Daily decay rate
        private readonly decimal _minConfidenceThreshold = 0.6m; // Minimum confidence before recalibration
        private readonly decimal _performanceDegradationThreshold = 0.15m; // 15% performance drop
        private readonly TimeSpan _maxTimeBetweenCalibrations = TimeSpan.FromDays(7);
        private readonly TimeSpan _minTimeBetweenCalibrations = TimeSpan.FromHours(4);
        
        public AdaptiveCalibrator(ISettingsCalibrator baseCalibrator, Action<string> logAction = null)
        {
            _baseCalibrator = baseCalibrator ?? throw new ArgumentNullException(nameof(baseCalibrator));
            _logAction = logAction ?? (_ => { });
            _calibrationHistory = new CalibrationHistory();
            _calibrationConfidence = 1.0m;
            _lastCalibrationTime = DateTime.UtcNow;
        }

        public void Initialize(SymbolProfile symbolProfile, OptimalSettings initialSettings)
        {
            _symbolProfile = symbolProfile ?? throw new ArgumentNullException(nameof(symbolProfile));
            _currentSettings = initialSettings ?? throw new ArgumentNullException(nameof(initialSettings));
            _lastCalibrationTime = DateTime.UtcNow;
            _calibrationConfidence = 1.0m;
            
            _logAction("🔧 AdaptiveCalibrator initialized with initial settings");
            _logAction($"  • Volume Threshold: {_currentSettings.VolumeThreshold:F2}");
            _logAction($"  • Signal Threshold: {_currentSettings.SignalThreshold:F2}");
            _logAction($"  • Initial Confidence: {_calibrationConfidence:P1}");
        }

        public bool ShouldRecalibrate(PerformanceMetrics currentPerformance, MarketRegimeChange regimeChange)
        {
            _lastPerformanceMetrics = currentPerformance;
            _lastRegimeChange = regimeChange;
            
            // Update confidence decay
            UpdateCalibrationConfidence();
            
            var reasons = new List<string>();
            
            // Check confidence decay
            if (_calibrationConfidence < _minConfidenceThreshold)
            {
                reasons.Add($"Confidence decay ({_calibrationConfidence:P1} < {_minConfidenceThreshold:P1})");
            }
            
            // Check performance degradation
            if (currentPerformance?.IsPerformanceDegrading == true)
            {
                var performanceScore = CalculatePerformanceScore(currentPerformance);
                if (performanceScore < (1.0m - _performanceDegradationThreshold))
                {
                    reasons.Add($"Performance degradation (Score: {performanceScore:P1})");
                }
            }
            
            // Check significant market regime change
            if (regimeChange?.IsSignificantChange == true && regimeChange.ChangeSignificance > 0.7m)
            {
                reasons.Add($"Significant regime change ({regimeChange.ChangeSignificance:P1})");
            }
            
            // Check time since last calibration
            var timeSinceLastCalibration = DateTime.UtcNow - _lastCalibrationTime;
            if (timeSinceLastCalibration > _maxTimeBetweenCalibrations)
            {
                reasons.Add($"Maximum time exceeded ({timeSinceLastCalibration.TotalDays:F1} days)");
            }
            
            // Ensure minimum time between calibrations
            if (reasons.Count > 0 && timeSinceLastCalibration < _minTimeBetweenCalibrations)
            {
                _logAction($"⏳ Recalibration needed but minimum time not met ({timeSinceLastCalibration.TotalHours:F1}h < {_minTimeBetweenCalibrations.TotalHours}h)");
                return false;
            }
            
            if (reasons.Count > 0)
            {
                _logAction($"🔄 Recalibration triggered: {string.Join(", ", reasons)}");
                return true;
            }
            
            return false;
        }

        public async Task<CalibrationResult> PerformAdaptiveCalibrationAsync(List<MarketDataPoint> recentData, PerformanceMetrics performance)
        {
            var startTime = DateTime.UtcNow;
            _logAction("🔄 Starting adaptive calibration...");
            
            try
            {
                // Create calibration event
                var calibrationEvent = new CalibrationEvent
                {
                    Timestamp = startTime,
                    Trigger = DetermineTriggerReason(performance, _lastRegimeChange),
                    PreviousSettings = CloneSettings(_currentSettings),
                    PerformanceBeforeCalibration = performance,
                    RegimeChange = _lastRegimeChange
                };
                
                // Perform base calibration with recent data
                var baseResult = await _baseCalibrator.CalibrateAsync(recentData, _symbolProfile);
                
                if (baseResult.IsSuccessful)
                {
                    // Apply adaptive adjustments based on recent performance
                    var adaptedSettings = ApplyAdaptiveAdjustments(baseResult.Settings, performance, _lastRegimeChange);
                    
                    // Update current settings
                    var previousSettings = _currentSettings;
                    _currentSettings = adaptedSettings;
                    _lastCalibrationTime = DateTime.UtcNow;
                    _calibrationConfidence = baseResult.OverallConfidence;
                    
                    // Complete calibration event
                    calibrationEvent.NewSettings = CloneSettings(_currentSettings);
                    calibrationEvent.ResultingConfidence = _calibrationConfidence;
                    calibrationEvent.CalibrationDuration = DateTime.UtcNow - startTime;
                    calibrationEvent.WasSuccessful = true;
                    calibrationEvent.Notes = $"Adaptive adjustments applied. Confidence: {_calibrationConfidence:P1}";
                    
                    // Add to history
                    _calibrationHistory.AddEvent(calibrationEvent);
                    
                    _logAction($"✅ Adaptive calibration completed successfully");
                    _logAction($"  • New Volume Threshold: {_currentSettings.VolumeThreshold:F2} (was {previousSettings.VolumeThreshold:F2})");
                    _logAction($"  • New Signal Threshold: {_currentSettings.SignalThreshold:F2} (was {previousSettings.SignalThreshold:F2})");
                    _logAction($"  • New Confidence: {_calibrationConfidence:P1}");
                    _logAction($"  • Duration: {calibrationEvent.CalibrationDuration.TotalSeconds:F1}s");
                    
                    return new CalibrationResult
                    {
                        IsSuccessful = true,
                        Settings = _currentSettings,
                        OverallConfidence = _calibrationConfidence,
                        VolumeAnalysisResult = baseResult.VolumeAnalysisResult,
                        DeltaAnalysisResult = baseResult.DeltaAnalysisResult,
                        VolatilityAnalysisResult = baseResult.VolatilityAnalysisResult,
                        MarketProfileResult = baseResult.MarketProfileResult,
                        CalibrationNotes = new List<string> { $"Adaptive calibration successful. {calibrationEvent.Notes}" }
                    };
                }
                else
                {
                    calibrationEvent.WasSuccessful = false;
                    calibrationEvent.CalibrationDuration = DateTime.UtcNow - startTime;
                    calibrationEvent.Notes = $"Base calibration failed: {baseResult.CalibrationNotes}";
                    _calibrationHistory.AddEvent(calibrationEvent);
                    
                    _logAction($"❌ Adaptive calibration failed: {baseResult.CalibrationNotes}");
                    return baseResult;
                }
            }
            catch (Exception ex)
            {
                _logAction($"❌ Adaptive calibration exception: {ex.Message}");
                return new CalibrationResult
                {
                    IsSuccessful = false,
                    CalibrationNotes = new List<string> { $"Adaptive calibration exception: {ex.Message}" }
                };
            }
        }

        public OptimalSettings GetCurrentSettings()
        {
            return CloneSettings(_currentSettings);
        }

        public decimal GetCalibrationConfidence()
        {
            UpdateCalibrationConfidence();
            return _calibrationConfidence;
        }

        public void UpdatePerformanceMetrics(PerformanceMetrics metrics)
        {
            _lastPerformanceMetrics = metrics;
        }

        public CalibrationHistory GetCalibrationHistory()
        {
            return _calibrationHistory;
        }

        public void Reset()
        {
            _calibrationHistory = new CalibrationHistory();
            _calibrationConfidence = 1.0m;
            _lastCalibrationTime = DateTime.UtcNow;
            _lastPerformanceMetrics = null;
            _lastRegimeChange = null;
            
            _logAction("🔄 AdaptiveCalibrator reset to initial state");
        }

        #region Private Methods

        private void UpdateCalibrationConfidence()
        {
            var timeSinceCalibration = DateTime.UtcNow - _lastCalibrationTime;
            var daysSinceCalibration = timeSinceCalibration.TotalDays;
            
            // Apply exponential decay
            _calibrationConfidence *= (decimal)Math.Pow((double)_confidenceDecayRate, daysSinceCalibration);
            _calibrationConfidence = Math.Max(0.1m, _calibrationConfidence);
        }

        private decimal CalculatePerformanceScore(PerformanceMetrics performance)
        {
            if (performance == null) return 0.5m;
            
            var factors = new List<decimal>();
            
            // Win rate factor
            if (performance.TotalTrades > 10)
            {
                factors.Add(performance.WinRate);
            }
            
            // Profit factor
            if (performance.ProfitFactor > 0)
            {
                factors.Add(Math.Min(1.0m, performance.ProfitFactor / 2.0m));
            }
            
            // Sharpe ratio factor
            if (performance.SharpeRatio > 0)
            {
                factors.Add(Math.Min(1.0m, performance.SharpeRatio / 3.0m));
            }
            
            // Drawdown factor (inverted)
            if (performance.MaxDrawdown < 0)
            {
                factors.Add(Math.Max(0, 1.0m + performance.MaxDrawdown / 0.2m)); // Normalize to 20% max drawdown
            }
            
            return factors.Count > 0 ? factors.Average() : 0.5m;
        }

        private string DetermineTriggerReason(PerformanceMetrics performance, MarketRegimeChange regimeChange)
        {
            var reasons = new List<string>();
            
            if (_calibrationConfidence < _minConfidenceThreshold)
                reasons.Add("Confidence Decay");
            
            if (performance?.IsPerformanceDegrading == true)
                reasons.Add("Performance Degradation");
            
            if (regimeChange?.IsSignificantChange == true)
                reasons.Add($"Regime Change ({regimeChange.ChangeDescription})");
            
            var timeSinceLastCalibration = DateTime.UtcNow - _lastCalibrationTime;
            if (timeSinceLastCalibration > _maxTimeBetweenCalibrations)
                reasons.Add("Scheduled Recalibration");
            
            return reasons.Count > 0 ? string.Join(", ", reasons) : "Manual Trigger";
        }

        private OptimalSettings ApplyAdaptiveAdjustments(OptimalSettings baseSettings, PerformanceMetrics performance, MarketRegimeChange regimeChange)
        {
            var adjustedSettings = CloneSettings(baseSettings);
            
            // Apply performance-based adjustments
            if (performance != null && performance.TotalTrades > 10)
            {
                // If win rate is low, be more conservative
                if (performance.WinRate < 0.4m)
                {
                    adjustedSettings.SignalThreshold *= 1.2m; // Increase threshold for more selective signals
                    adjustedSettings.VolumeThreshold *= 1.1m;
                }
                // If win rate is high but profit factor is low, adjust take profit
                else if (performance.WinRate > 0.6m && performance.ProfitFactor < 1.5m)
                {
                    adjustedSettings.TakeProfitPercent *= 1.1m; // Increase take profit
                }
            }
            
            // Apply regime-based adjustments
            if (regimeChange?.IsSignificantChange == true)
            {
                // High volatility regime - be more conservative
                if (regimeChange.CurrentVolatilityRegime == VolatilityRegime.VeryHigh || 
                    regimeChange.CurrentVolatilityRegime == VolatilityRegime.Extreme)
                {
                    adjustedSettings.StopLossPercent *= 0.8m; // Tighter stops
                    adjustedSettings.SignalThreshold *= 1.3m; // Higher signal threshold
                }
                
                // Low volume regime - adjust volume sensitivity
                if (regimeChange.CurrentVolumeRegime == VolumeRegime.VeryLow)
                {
                    adjustedSettings.VolumeThreshold *= 0.8m; // Lower volume threshold
                }
            }
            
            return adjustedSettings;
        }

        private OptimalSettings CloneSettings(OptimalSettings settings)
        {
            if (settings == null) return null;
            
            return new OptimalSettings
            {
                VolumeThreshold = settings.VolumeThreshold,
                SignalThreshold = settings.SignalThreshold,
                DeltaImbalanceThreshold = settings.DeltaImbalanceThreshold,
                TakeProfitPercent = settings.TakeProfitPercent,
                StopLossPercent = settings.StopLossPercent,
                EnableSessionAdjustments = settings.EnableSessionAdjustments,
                AsianSessionMultiplier = settings.AsianSessionMultiplier,
                EuropeanSessionMultiplier = settings.EuropeanSessionMultiplier,
                USSessionMultiplier = settings.USSessionMultiplier
            };
        }

        #endregion
    }
}
