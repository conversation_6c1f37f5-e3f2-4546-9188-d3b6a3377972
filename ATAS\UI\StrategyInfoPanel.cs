using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.ATAS.UI
{
    /// <summary>
    /// UI component for displaying strategy information, calibration results, and performance metrics
    /// </summary>
    public class StrategyInfoPanel
    {
        private readonly Font _headerFont;
        private readonly Font _normalFont;
        private readonly Font _smallFont;
        private readonly Brush _headerBrush;
        private readonly Brush _textBrush;
        private readonly Brush _successBrush;
        private readonly Brush _warningBrush;
        private readonly Brush _errorBrush;
        private readonly Pen _borderPen;

        public StrategyInfoPanel()
        {
            // Initialize fonts and brushes
            _headerFont = new Font("Arial", 10, FontStyle.Bold);
            _normalFont = new Font("Arial", 9, FontStyle.Regular);
            _smallFont = new Font("Arial", 8, FontStyle.Regular);
            
            _headerBrush = new SolidBrush(Color.White);
            _textBrush = new SolidBrush(Color.LightGray);
            _successBrush = new SolidBrush(Color.LightGreen);
            _warningBrush = new SolidBrush(Color.Orange);
            _errorBrush = new SolidBrush(Color.LightCoral);
            _borderPen = new Pen(Color.Gray, 1);
        }

        /// <summary>
        /// Draw strategy status panel
        /// </summary>
        public void DrawStatusPanel(Graphics graphics, RectangleF bounds, string statusMessage, Color statusColor, decimal confidence)
        {
            var panelRect = new RectangleF(bounds.X + 10, bounds.Y + 10, 300, 80);
            
            // Draw panel background
            using (var backgroundBrush = new SolidBrush(Color.FromArgb(180, Color.Black)))
            {
                graphics.FillRectangle(backgroundBrush, panelRect);
            }
            graphics.DrawRectangle(_borderPen, Rectangle.Round(panelRect));

            var y = panelRect.Y + 10;
            
            // Strategy title
            graphics.DrawString("Smart Volume Strategy", _headerFont, _headerBrush, panelRect.X + 10, y);
            y += 20;

            // Status message
            using (var statusBrush = new SolidBrush(statusColor))
            {
                graphics.DrawString($"Status: {statusMessage}", _normalFont, statusBrush, panelRect.X + 10, y);
            }
            y += 15;

            // Confidence score
            if (confidence > 0)
            {
                var confidenceColor = GetConfidenceColor(confidence);
                using (var confidenceBrush = new SolidBrush(confidenceColor))
                {
                    graphics.DrawString($"Confidence: {confidence:P1}", _normalFont, confidenceBrush, panelRect.X + 10, y);
                }
            }
        }

        /// <summary>
        /// Draw calibration information panel
        /// </summary>
        public void DrawCalibrationPanel(Graphics graphics, RectangleF bounds, CalibrationResult calibrationResult)
        {
            if (calibrationResult == null)
                return;

            var panelRect = new RectangleF(bounds.X + 10, bounds.Y + 100, 350, 200);
            
            // Draw panel background
            using (var backgroundBrush = new SolidBrush(Color.FromArgb(180, Color.Black)))
            {
                graphics.FillRectangle(backgroundBrush, panelRect);
            }
            graphics.DrawRectangle(_borderPen, Rectangle.Round(panelRect));

            var y = panelRect.Y + 10;
            
            // Panel title
            graphics.DrawString("Calibration Results", _headerFont, _headerBrush, panelRect.X + 10, y);
            y += 25;

            // Overall confidence
            var confidenceColor = GetConfidenceColor(calibrationResult.OverallConfidence);
            using (var confidenceBrush = new SolidBrush(confidenceColor))
            {
                graphics.DrawString($"Overall Confidence: {calibrationResult.OverallConfidence:P1}", _normalFont, confidenceBrush, panelRect.X + 10, y);
            }
            y += 20;

            // Calibrated settings
            graphics.DrawString("Optimized Settings:", _normalFont, _headerBrush, panelRect.X + 10, y);
            y += 15;

            var settings = calibrationResult.Settings;
            graphics.DrawString($"• Volume Threshold: {settings.VolumeThreshold:F1}x", _smallFont, _textBrush, panelRect.X + 20, y);
            y += 12;
            graphics.DrawString($"• Signal Threshold: {settings.SignalThreshold:F2}", _smallFont, _textBrush, panelRect.X + 20, y);
            y += 12;
            graphics.DrawString($"• Take Profit: {settings.TakeProfitPercent:F2}%", _smallFont, _textBrush, panelRect.X + 20, y);
            y += 12;
            graphics.DrawString($"• Stop Loss: {settings.StopLossPercent:F2}%", _smallFont, _textBrush, panelRect.X + 20, y);
            y += 12;
            graphics.DrawString($"• Position Size: {settings.PositionSizeUSDT:F0} USDT", _smallFont, _textBrush, panelRect.X + 20, y);
            y += 15;

            // Validation results
            if (calibrationResult.Validation != null && calibrationResult.Validation.ValidationPerformed)
            {
                graphics.DrawString("Backtest Validation:", _normalFont, _headerBrush, panelRect.X + 10, y);
                y += 15;
                
                var validation = calibrationResult.Validation;
                graphics.DrawString($"• Win Rate: {validation.WinRate:P1}", _smallFont, _textBrush, panelRect.X + 20, y);
                y += 12;
                graphics.DrawString($"• Profit Factor: {validation.ProfitFactor:F2}", _smallFont, _textBrush, panelRect.X + 20, y);
                y += 12;
                graphics.DrawString($"• Max Drawdown: {validation.MaxDrawdown:F1}%", _smallFont, _textBrush, panelRect.X + 20, y);
            }
        }

        /// <summary>
        /// Draw performance metrics panel
        /// </summary>
        public void DrawPerformancePanel(Graphics graphics, RectangleF bounds, PerformanceMetrics performance)
        {
            if (performance == null)
                return;

            var panelRect = new RectangleF(bounds.X + 370, bounds.Y + 100, 300, 200);
            
            // Draw panel background
            using (var backgroundBrush = new SolidBrush(Color.FromArgb(180, Color.Black)))
            {
                graphics.FillRectangle(backgroundBrush, panelRect);
            }
            graphics.DrawRectangle(_borderPen, Rectangle.Round(panelRect));

            var y = panelRect.Y + 10;
            
            // Panel title
            graphics.DrawString("Performance Metrics", _headerFont, _headerBrush, panelRect.X + 10, y);
            y += 25;

            // Trade statistics
            graphics.DrawString($"Total Trades: {performance.TotalTrades}", _normalFont, _textBrush, panelRect.X + 10, y);
            y += 15;

            var winRateColor = GetPerformanceColor(performance.WinRate, 0.5m, 0.7m);
            using (var winRateBrush = new SolidBrush(winRateColor))
            {
                graphics.DrawString($"Win Rate: {performance.WinRate:P1}", _normalFont, winRateBrush, panelRect.X + 10, y);
            }
            y += 15;

            var profitFactorColor = GetPerformanceColor(performance.ProfitFactor, 1.2m, 2.0m);
            using (var pfBrush = new SolidBrush(profitFactorColor))
            {
                graphics.DrawString($"Profit Factor: {performance.ProfitFactor:F2}", _normalFont, pfBrush, panelRect.X + 10, y);
            }
            y += 15;

            var pnlColor = performance.TotalPnL >= 0 ? Color.LightGreen : Color.LightCoral;
            using (var pnlBrush = new SolidBrush(pnlColor))
            {
                graphics.DrawString($"Total P&L: {performance.TotalPnL:F2} USDT", _normalFont, pnlBrush, panelRect.X + 10, y);
            }
            y += 15;

            var drawdownColor = GetDrawdownColor(performance.CurrentDrawdown);
            using (var ddBrush = new SolidBrush(drawdownColor))
            {
                graphics.DrawString($"Current Drawdown: {performance.CurrentDrawdown:F1}%", _normalFont, ddBrush, panelRect.X + 10, y);
            }
            y += 15;

            graphics.DrawString($"Max Drawdown: {performance.MaxDrawdown:F1}%", _normalFont, _textBrush, panelRect.X + 10, y);
            y += 20;

            // Recent performance
            if (performance.RecentTrades.Count > 0)
            {
                graphics.DrawString("Recent Performance:", _normalFont, _headerBrush, panelRect.X + 10, y);
                y += 15;
                
                var recentWinRateColor = GetPerformanceColor(performance.RecentWinRate / 100, 0.5m, 0.7m);
                using (var recentBrush = new SolidBrush(recentWinRateColor))
                {
                    graphics.DrawString($"• Last 10 trades: {performance.RecentWinRate:F1}%", _smallFont, recentBrush, panelRect.X + 20, y);
                }
                y += 12;
                
                graphics.DrawString($"• Consecutive losses: {performance.MaxConsecutiveLosses}", _smallFont, _textBrush, panelRect.X + 20, y);
            }
        }

        /// <summary>
        /// Draw trading signals on chart
        /// </summary>
        public void DrawSignalsOnChart(Graphics graphics, List<TradingSignal> signals, RectangleF chartBounds, Func<DateTime, float> timeToX, Func<decimal, float> priceToY)
        {
            if (signals == null || signals.Count == 0)
                return;

            foreach (var signal in signals.Where(s => s.Action == SignalAction.Entry))
            {
                var x = timeToX(signal.Timestamp);
                var y = priceToY(signal.CurrentPrice);

                if (x >= chartBounds.Left && x <= chartBounds.Right && y >= chartBounds.Top && y <= chartBounds.Bottom)
                {
                    DrawSignalMarker(graphics, x, y, signal);
                }
            }
        }

        /// <summary>
        /// Draw performance alerts
        /// </summary>
        public void DrawPerformanceAlerts(Graphics graphics, RectangleF bounds, List<PerformanceAlert> alerts)
        {
            if (alerts == null || alerts.Count == 0)
                return;

            var alertRect = new RectangleF(bounds.X + 10, bounds.Y + 320, 400, 100);
            
            // Draw panel background
            using (var backgroundBrush = new SolidBrush(Color.FromArgb(200, Color.DarkRed)))
            {
                graphics.FillRectangle(backgroundBrush, alertRect);
            }
            graphics.DrawRectangle(new Pen(Color.Red, 2), Rectangle.Round(alertRect));

            var y = alertRect.Y + 10;
            
            graphics.DrawString("Performance Alerts", _headerFont, _errorBrush, alertRect.X + 10, y);
            y += 20;

            foreach (var alert in alerts.Take(3)) // Show max 3 alerts
            {
                var alertColor = GetAlertColor(alert.Type);
                using (var alertBrush = new SolidBrush(alertColor))
                {
                    graphics.DrawString($"• {alert.Message}: {alert.Details}", _smallFont, alertBrush, alertRect.X + 10, y);
                }
                y += 12;
            }
        }

        #region Private Methods

        private Color GetConfidenceColor(decimal confidence)
        {
            if (confidence >= 0.8m) return Color.LightGreen;
            if (confidence >= 0.6m) return Color.Yellow;
            if (confidence >= 0.4m) return Color.Orange;
            return Color.LightCoral;
        }

        private Color GetPerformanceColor(decimal value, decimal goodThreshold, decimal excellentThreshold)
        {
            if (value >= excellentThreshold) return Color.LightGreen;
            if (value >= goodThreshold) return Color.Yellow;
            return Color.LightCoral;
        }

        private Color GetDrawdownColor(decimal drawdown)
        {
            if (drawdown <= 5) return Color.LightGreen;
            if (drawdown <= 10) return Color.Yellow;
            if (drawdown <= 15) return Color.Orange;
            return Color.LightCoral;
        }

        private Color GetAlertColor(AlertType alertType)
        {
            return alertType switch
            {
                AlertType.Info => Color.LightBlue,
                AlertType.Warning => Color.Orange,
                AlertType.Critical => Color.LightCoral,
                _ => Color.White
            };
        }

        private void DrawSignalMarker(Graphics graphics, float x, float y, TradingSignal signal)
        {
            var markerSize = 8;
            var markerColor = signal.Type == SignalType.Long ? Color.LimeGreen : Color.Red;
            var markerShape = signal.Type == SignalType.Long ? "▲" : "▼";

            using (var markerBrush = new SolidBrush(markerColor))
            {
                graphics.DrawString(markerShape, _normalFont, markerBrush, x - markerSize / 2, y - markerSize);
            }

            // Draw confidence indicator
            var confidenceColor = GetConfidenceColor(signal.Confidence);
            using (var confidencePen = new Pen(confidenceColor, 2))
            {
                graphics.DrawEllipse(confidencePen, x - markerSize, y - markerSize, markerSize * 2, markerSize * 2);
            }
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            _headerFont?.Dispose();
            _normalFont?.Dispose();
            _smallFont?.Dispose();
            _headerBrush?.Dispose();
            _textBrush?.Dispose();
            _successBrush?.Dispose();
            _warningBrush?.Dispose();
            _errorBrush?.Dispose();
            _borderPen?.Dispose();
        }

        #endregion
    }
}
