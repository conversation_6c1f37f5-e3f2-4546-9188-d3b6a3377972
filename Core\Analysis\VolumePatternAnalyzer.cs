using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Analyzes volume patterns to determine optimal volume thresholds and characteristics
    /// </summary>
    public class VolumePatternAnalyzer : IVolumePatternAnalyzer
    {
        private const decimal DEFAULT_VOLUME_THRESHOLD = 2.0m;
        private readonly decimal[] DEFAULT_TEST_THRESHOLDS = { 1.2m, 1.5m, 1.8m, 2.0m, 2.2m, 2.5m, 3.0m, 3.5m };

        /// <summary>
        /// Analyze volume patterns and characteristics
        /// </summary>
        public VolumeCharacteristics AnalyzeVolumePatterns(List<MarketDataPoint> marketData)
        {
            if (marketData == null || marketData.Count < 20)
                throw new ArgumentException("Need at least 20 bars for volume analysis", nameof(marketData));

            var volumes = marketData.Select(x => x.Volume).Where(v => v > 0).ToList();
            if (volumes.Count == 0)
                throw new ArgumentException("No valid volume data found", nameof(marketData));

            var characteristics = new VolumeCharacteristics();

            // Calculate basic volume statistics
            var (average, stdDev, median, percentile95) = CalculateVolumeStatistics(marketData);
            characteristics.AverageVolume = average;
            characteristics.VolumeStandardDeviation = stdDev;
            characteristics.MedianVolume = median;
            characteristics.Volume95thPercentile = percentile95;

            // Analyze volume spikes
            AnalyzeVolumeSpikes(marketData, characteristics, average);

            // Determine optimal volume threshold
            var (optimalThreshold, _) = OptimizeVolumeThreshold(marketData, DEFAULT_TEST_THRESHOLDS);
            characteristics.OptimalVolumeThreshold = optimalThreshold;

            // Classify volume regime
            characteristics.Regime = ClassifyVolumeRegime(characteristics);
            characteristics.IsHighVolumeSymbol = DetermineIfHighVolumeSymbol(characteristics);

            // Calculate volume consistency
            characteristics.VolumeConsistency = CalculateVolumeConsistency(volumes, average, stdDev);

            return characteristics;
        }

        /// <summary>
        /// Calculate optimal volume threshold for signal generation
        /// </summary>
        public (decimal OptimalThreshold, List<ThresholdTestResult> TestResults) OptimizeVolumeThreshold(
            List<MarketDataPoint> marketData, 
            decimal[] testThresholds = null)
        {
            testThresholds ??= DEFAULT_TEST_THRESHOLDS;
            var testResults = new List<ThresholdTestResult>();
            var averageVolume = marketData.Average(x => x.Volume);

            foreach (var threshold in testThresholds)
            {
                var result = TestVolumeThreshold(marketData, threshold, averageVolume);
                testResults.Add(result);
            }

            // Find optimal threshold based on combined score
            var optimalResult = testResults.OrderByDescending(x => x.Score).First();
            optimalResult.IsOptimal = true;

            return (optimalResult.ThresholdValue, testResults);
        }

        /// <summary>
        /// Detect volume spikes in real-time
        /// </summary>
        public VolumeSignalComponent DetectVolumeSpike(MarketDataPoint currentBar, decimal averageVolume, decimal threshold)
        {
            if (averageVolume <= 0)
                throw new ArgumentException("Average volume must be positive", nameof(averageVolume));

            var volumeRatio = currentBar.Volume / averageVolume;
            var isVolumeSpike = volumeRatio >= threshold;

            var component = new VolumeSignalComponent
            {
                VolumeRatio = volumeRatio,
                VolumeThreshold = threshold,
                IsVolumeSpike = isVolumeSpike,
                VolumeConfidence = CalculateVolumeConfidence(volumeRatio, threshold),
                VolumePattern = DetermineVolumePattern(volumeRatio, threshold)
            };

            return component;
        }

        /// <summary>
        /// Calculate volume statistics
        /// </summary>
        public (decimal Average, decimal StdDev, decimal Median, decimal Percentile95) CalculateVolumeStatistics(List<MarketDataPoint> marketData)
        {
            var volumes = marketData.Select(x => x.Volume).Where(v => v > 0).OrderBy(v => v).ToList();
            
            if (volumes.Count == 0)
                return (0, 0, 0, 0);

            var average = volumes.Average();
            var variance = volumes.Sum(v => (v - average) * (v - average)) / volumes.Count;
            var stdDev = (decimal)Math.Sqrt((double)variance);
            
            var median = volumes.Count % 2 == 0
                ? (volumes[volumes.Count / 2 - 1] + volumes[volumes.Count / 2]) / 2
                : volumes[volumes.Count / 2];

            var percentile95Index = (int)Math.Ceiling(volumes.Count * 0.95) - 1;
            var percentile95 = volumes[Math.Min(percentile95Index, volumes.Count - 1)];

            return (average, stdDev, median, percentile95);
        }

        /// <summary>
        /// Get current real-time volume analysis state
        /// </summary>
        public VolumeAnalysisState GetCurrentVolumeState(List<MarketDataPoint> recentData)
        {
            if (recentData == null || recentData.Count < 5)
            {
                return new VolumeAnalysisState
                {
                    CurrentRegime = VolumeRegime.Normal,
                    VolumeConfidence = 0.3m,
                    CurrentVolumePattern = "Insufficient Data"
                };
            }

            var (average, stdDev, _, _) = CalculateVolumeStatistics(recentData);
            var currentBar = recentData.Last();
            var currentRatio = currentBar.Volume / Math.Max(average, 1);

            // Detect volume spike
            var isSpike = currentRatio >= 2.0m;
            var spikeMagnitude = isSpike ? currentRatio : 0;

            // Count consecutive spike bars
            var consecutiveSpikes = 0;
            for (int i = recentData.Count - 1; i >= 0; i--)
            {
                var ratio = recentData[i].Volume / Math.Max(average, 1);
                if (ratio >= 2.0m)
                    consecutiveSpikes++;
                else
                    break;
            }

            // Determine volume pattern
            var pattern = DetermineCurrentVolumePattern(recentData, average);
            var consistency = CalculateVolumeConsistency(recentData.Select(x => x.Volume).ToList(), average, stdDev);

            return new VolumeAnalysisState
            {
                LastUpdate = DateTime.UtcNow,
                CurrentVolumeRatio = currentRatio,
                AverageVolume = average,
                VolumeStandardDeviation = stdDev,
                IsVolumeSpikeActive = isSpike,
                SpikeMagnitude = spikeMagnitude,
                ConsecutiveSpikeBars = consecutiveSpikes,
                LastSpikeTime = isSpike ? currentBar.Timestamp : DateTime.MinValue,
                CurrentVolumePattern = pattern,
                VolumeConsistency = consistency,
                VolumeConfidence = CalculateVolumeConfidence(currentRatio, 2.0m)
            };
        }

        /// <summary>
        /// Determine current volume regime in real-time
        /// </summary>
        public (VolumeRegime Regime, decimal Confidence) DetermineCurrentVolumeRegime(MarketDataPoint currentBar, List<MarketDataPoint> history)
        {
            if (history == null || history.Count < 10)
                return (VolumeRegime.Normal, 0.5m);

            var (average, stdDev, _, percentile95) = CalculateVolumeStatistics(history);
            var currentRatio = currentBar.Volume / Math.Max(average, 1);
            var coefficientOfVariation = average > 0 ? stdDev / average : 0;

            // Calculate spike frequency in recent history
            var spikeCount = history.Count(x => x.Volume / Math.Max(average, 1) >= 2.0m);
            var spikeFrequency = (decimal)spikeCount / history.Count * 100;

            VolumeRegime regime;
            decimal confidence;

            // Classify regime based on current volume and historical patterns
            if (coefficientOfVariation > 2.0m || spikeFrequency > 30)
            {
                regime = VolumeRegime.Erratic;
                confidence = Math.Min(0.9m, coefficientOfVariation / 3.0m);
            }
            else if (currentBar.Volume > percentile95 * 0.8m)
            {
                regime = VolumeRegime.VeryHigh;
                confidence = Math.Min(0.95m, currentRatio / 3.0m);
            }
            else if (currentBar.Volume > percentile95 * 0.6m)
            {
                regime = VolumeRegime.High;
                confidence = Math.Min(0.9m, currentRatio / 2.5m);
            }
            else if (currentBar.Volume < percentile95 * 0.2m)
            {
                regime = VolumeRegime.VeryLow;
                confidence = Math.Min(0.9m, (2.0m - currentRatio) / 2.0m);
            }
            else if (currentBar.Volume < percentile95 * 0.4m)
            {
                regime = VolumeRegime.Low;
                confidence = Math.Min(0.85m, (1.5m - currentRatio) / 1.5m);
            }
            else
            {
                regime = VolumeRegime.Normal;
                confidence = 0.8m;
            }

            return (regime, Math.Max(0.3m, confidence));
        }

        /// <summary>
        /// Analyze volume trend in real-time
        /// </summary>
        public (VolumeTrend Trend, decimal Strength, int Duration) AnalyzeVolumeTrend(List<MarketDataPoint> recentData, int lookbackPeriod = 10)
        {
            if (recentData == null || recentData.Count < lookbackPeriod)
                return (VolumeTrend.Stable, 0.5m, 0);

            var volumes = recentData.TakeLast(lookbackPeriod).Select(x => x.Volume).ToList();
            var firstHalf = volumes.Take(lookbackPeriod / 2).Average();
            var secondHalf = volumes.Skip(lookbackPeriod / 2).Average();

            var changePercent = firstHalf > 0 ? (secondHalf - firstHalf) / firstHalf : 0;
            var strength = Math.Min(1.0m, Math.Abs(changePercent) * 2);

            // Determine trend direction
            VolumeTrend trend;
            if (Math.Abs(changePercent) < 0.1m)
                trend = VolumeTrend.Stable;
            else if (changePercent > 0.5m)
                trend = VolumeTrend.Spiking;
            else if (changePercent > 0.2m)
                trend = VolumeTrend.Increasing;
            else if (changePercent < -0.2m)
                trend = VolumeTrend.Declining;
            else
                trend = VolumeTrend.Erratic;

            // Calculate duration by looking backwards for trend consistency
            var duration = CalculateTrendDuration(volumes, trend);

            return (trend, strength, duration);
        }

        #region Private Methods

        private void AnalyzeVolumeSpikes(List<MarketDataPoint> marketData, VolumeCharacteristics characteristics, decimal averageVolume)
        {
            var spikes = new List<decimal>();
            var spikeCount = 0;

            foreach (var bar in marketData)
            {
                var ratio = bar.Volume / averageVolume;
                if (ratio >= 2.0m) // Consider 2x average as a spike
                {
                    spikes.Add(ratio);
                    spikeCount++;
                }
            }

            characteristics.VolumeSpikeFrequency = (int)((decimal)spikeCount / marketData.Count * 100);
            characteristics.AverageSpikeMultiplier = spikes.Count > 0 ? spikes.Average() : 0;
        }

        private VolumeRegime ClassifyVolumeRegime(VolumeCharacteristics characteristics)
        {
            var coefficientOfVariation = characteristics.AverageVolume > 0 
                ? characteristics.VolumeStandardDeviation / characteristics.AverageVolume 
                : 0;

            // Classify based on volume consistency and spike frequency
            if (coefficientOfVariation > 2.0m || characteristics.VolumeSpikeFrequency > 30)
                return VolumeRegime.Erratic;
            
            if (characteristics.AverageVolume > characteristics.Volume95thPercentile * 0.8m)
                return VolumeRegime.VeryHigh;
            
            if (characteristics.AverageVolume > characteristics.Volume95thPercentile * 0.6m)
                return VolumeRegime.High;
            
            if (characteristics.AverageVolume < characteristics.Volume95thPercentile * 0.2m)
                return VolumeRegime.VeryLow;
            
            if (characteristics.AverageVolume < characteristics.Volume95thPercentile * 0.4m)
                return VolumeRegime.Low;

            return VolumeRegime.Normal;
        }

        private bool DetermineIfHighVolumeSymbol(VolumeCharacteristics characteristics)
        {
            // Consider high volume if average is close to 95th percentile or has frequent spikes
            return characteristics.AverageVolume > characteristics.Volume95thPercentile * 0.7m ||
                   characteristics.VolumeSpikeFrequency > 15;
        }

        private decimal CalculateVolumeConsistency(List<decimal> volumes, decimal average, decimal stdDev)
        {
            if (average <= 0)
                return 0;

            var coefficientOfVariation = stdDev / average;
            
            // Convert to 0-1 scale where 1 = very consistent, 0 = very inconsistent
            // CV of 0.5 or less = high consistency (0.8-1.0)
            // CV of 1.0 or less = moderate consistency (0.5-0.8)
            // CV > 1.0 = low consistency (0.0-0.5)
            
            if (coefficientOfVariation <= 0.5m)
                return 0.8m + (0.5m - coefficientOfVariation) * 0.4m; // 0.8-1.0
            
            if (coefficientOfVariation <= 1.0m)
                return 0.5m + (1.0m - coefficientOfVariation) * 0.6m; // 0.5-0.8
            
            return Math.Max(0, 0.5m - (coefficientOfVariation - 1.0m) * 0.25m); // 0.0-0.5
        }

        private ThresholdTestResult TestVolumeThreshold(List<MarketDataPoint> marketData, decimal threshold, decimal averageVolume)
        {
            var signals = 0;
            var profitableSignals = 0;
            var totalProfit = 0m;
            var totalLoss = 0m;

            for (int i = 1; i < marketData.Count - 1; i++) // Leave room for entry and exit
            {
                var currentBar = marketData[i];
                var nextBar = marketData[i + 1];
                
                var volumeRatio = currentBar.Volume / averageVolume;
                
                if (volumeRatio >= threshold)
                {
                    signals++;
                    
                    // Simple profit calculation: assume we trade in direction of price movement
                    var priceChange = nextBar.Close - currentBar.Close;
                    var priceChangePercent = currentBar.Close > 0 ? priceChange / currentBar.Close : 0;
                    
                    if (priceChangePercent > 0)
                    {
                        profitableSignals++;
                        totalProfit += Math.Abs(priceChangePercent);
                    }
                    else
                    {
                        totalLoss += Math.Abs(priceChangePercent);
                    }
                }
            }

            var winRate = signals > 0 ? (decimal)profitableSignals / signals : 0;
            var profitFactor = totalLoss > 0 ? totalProfit / totalLoss : (totalProfit > 0 ? 10 : 1);
            
            // Combined score: balance signal frequency, win rate, and profit factor
            var frequencyScore = Math.Min(1.0m, signals / (decimal)marketData.Count * 10); // Prefer reasonable signal frequency
            var winRateScore = winRate;
            var profitScore = Math.Min(1.0m, profitFactor / 3.0m); // Normalize profit factor
            
            var combinedScore = (frequencyScore * 0.3m + winRateScore * 0.4m + profitScore * 0.3m);

            return new ThresholdTestResult
            {
                ThresholdValue = threshold,
                SignalsGenerated = signals,
                WinRate = winRate,
                ProfitFactor = profitFactor,
                Score = combinedScore,
                IsOptimal = false
            };
        }

        private decimal CalculateVolumeConfidence(decimal volumeRatio, decimal threshold)
        {
            if (volumeRatio < threshold)
                return 0;

            // Higher confidence for higher volume ratios, but with diminishing returns
            var excessRatio = volumeRatio - threshold;
            var confidence = Math.Min(1.0m, 0.5m + excessRatio * 0.2m);
            
            return confidence;
        }

        private string DetermineVolumePattern(decimal volumeRatio, decimal threshold)
        {
            if (volumeRatio < threshold)
                return "Normal";
            
            if (volumeRatio >= threshold * 3)
                return "Massive Spike";
            
            if (volumeRatio >= threshold * 2)
                return "Large Spike";
            
            if (volumeRatio >= threshold * 1.5m)
                return "Significant Spike";
            
            return "Volume Spike";
        }

        private string DetermineCurrentVolumePattern(List<MarketDataPoint> recentData, decimal averageVolume)
        {
            if (recentData.Count < 3)
                return "Normal";

            var recent3 = recentData.TakeLast(3).ToList();
            var ratios = recent3.Select(x => x.Volume / Math.Max(averageVolume, 1)).ToList();

            // Check for sustained high volume
            if (ratios.All(r => r >= 1.5m))
                return "Sustained High";

            // Check for building volume
            if (ratios[0] < ratios[1] && ratios[1] < ratios[2] && ratios[2] >= 1.5m)
                return "Building";

            // Check for declining volume
            if (ratios[0] > ratios[1] && ratios[1] > ratios[2] && ratios[0] >= 2.0m)
                return "Declining";

            // Check for current spike
            if (ratios.Last() >= 2.0m)
                return "Spike";

            return "Normal";
        }

        private int CalculateTrendDuration(List<decimal> volumes, VolumeTrend trend)
        {
            if (volumes.Count < 2)
                return 0;

            int duration = 1; // Current bar
            var previousTrend = trend;

            // Look backwards to find how long this trend has been active
            for (int i = volumes.Count - 2; i >= 0; i--)
            {
                var currentChange = i > 0 ? (volumes[i] - volumes[i - 1]) / Math.Max(volumes[i - 1], 1) : 0;

                VolumeTrend currentTrend;
                if (Math.Abs(currentChange) < 0.1m)
                    currentTrend = VolumeTrend.Stable;
                else if (currentChange > 0.5m)
                    currentTrend = VolumeTrend.Spiking;
                else if (currentChange > 0.2m)
                    currentTrend = VolumeTrend.Increasing;
                else if (currentChange < -0.2m)
                    currentTrend = VolumeTrend.Declining;
                else
                    currentTrend = VolumeTrend.Erratic;

                if (currentTrend == previousTrend)
                    duration++;
                else
                    break;
            }

            return duration;
        }

        #endregion
    }
}
