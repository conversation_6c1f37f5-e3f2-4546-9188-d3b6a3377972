using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections.Concurrent;
using SmartVolumeStrategy.Core.Models.Performance;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;

namespace SmartVolumeStrategy.Core.Performance
{
    /// <summary>
    /// Phase 3.3: Performance Monitor
    /// Response time tracking and bottleneck identification with comprehensive monitoring
    /// </summary>
    public class PerformanceMonitor
    {
        private readonly Action<string> _logAction;
        private readonly ConcurrentDictionary<PerformanceComponent, ComponentPerformanceStats> _componentStats;
        private readonly Queue<PerformanceMetrics> _performanceHistory;
        private readonly Queue<PerformanceBottleneck> _bottleneckHistory;
        private readonly Queue<OptimizationRecommendation> _recommendationHistory;
        private readonly object _lockObject = new object();
        
        // Circuit breaker integration
        private CircuitBreakerManager _circuitBreakerManager;
        
        // Monitoring state
        private DateTime _monitoringStartTime;
        private DateTime _lastBottleneckCheck;
        private DateTime _lastRecommendationUpdate;
        private OverallPerformanceStats _overallStats;
        
        // Configuration
        private const int MAX_PERFORMANCE_HISTORY = 1000;
        private const int MAX_BOTTLENECK_HISTORY = 100;
        private const int MAX_RECOMMENDATION_HISTORY = 50;
        private static readonly TimeSpan BOTTLENECK_CHECK_INTERVAL = TimeSpan.FromMinutes(2);
        private static readonly TimeSpan RECOMMENDATION_UPDATE_INTERVAL = TimeSpan.FromMinutes(5);
        private const decimal BOTTLENECK_THRESHOLD = 0.8m; // 80% threshold for bottleneck detection

        public PerformanceMonitor(Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _componentStats = new ConcurrentDictionary<PerformanceComponent, ComponentPerformanceStats>();
            _performanceHistory = new Queue<PerformanceMetrics>();
            _bottleneckHistory = new Queue<PerformanceBottleneck>();
            _recommendationHistory = new Queue<OptimizationRecommendation>();
            
            _monitoringStartTime = DateTime.UtcNow;
            _lastBottleneckCheck = DateTime.UtcNow;
            _lastRecommendationUpdate = DateTime.UtcNow;
            _overallStats = new OverallPerformanceStats();
            
            InitializeComponentStats();
        }

        /// <summary>
        /// Set circuit breaker manager for fault tolerance
        /// </summary>
        public void SetCircuitBreakerManager(CircuitBreakerManager circuitBreakerManager)
        {
            _circuitBreakerManager = circuitBreakerManager;
        }

        /// <summary>
        /// Record performance metric
        /// </summary>
        public void RecordMetric(PerformanceMetrics metric)
        {
            if (metric == null) return;

            try
            {
                // Phase 3.3: Check circuit breaker status
                if (_circuitBreakerManager != null && !_circuitBreakerManager.IsOperationAllowed(StrategyComponent.PerformanceMonitor))
                {
                    return;
                }

                lock (_lockObject)
                {
                    // Add to performance history
                    _performanceHistory.Enqueue(metric);
                    while (_performanceHistory.Count > MAX_PERFORMANCE_HISTORY)
                    {
                        _performanceHistory.Dequeue();
                    }

                    // Update component statistics
                    UpdateComponentStats(metric);

                    // Update overall statistics
                    UpdateOverallStats(metric);

                    // Check for bottlenecks
                    if (ShouldCheckBottlenecks())
                    {
                        CheckForBottlenecks();
                    }

                    // Update recommendations
                    if (ShouldUpdateRecommendations())
                    {
                        UpdateOptimizationRecommendations();
                    }
                }
            }
            catch (Exception ex)
            {
                _logAction($"❌ Performance monitoring error: {ex.Message}");
                
                // Record failure with circuit breaker
                if (_circuitBreakerManager != null)
                {
                    _circuitBreakerManager.RecordOperation(StrategyComponent.PerformanceMonitor, false, ex.Message, FailureSeverity.Low);
                }
            }
        }

        /// <summary>
        /// Get comprehensive performance statistics
        /// </summary>
        public PerformanceStatistics GetPerformanceStatistics()
        {
            lock (_lockObject)
            {
                var stats = new PerformanceStatistics
                {
                    StartTime = _monitoringStartTime,
                    LastUpdate = DateTime.UtcNow,
                    ComponentStats = new Dictionary<PerformanceComponent, ComponentPerformanceStats>(_componentStats),
                    OverallStats = CloneOverallStats(_overallStats),
                    IdentifiedBottlenecks = _bottleneckHistory.ToList(),
                    ActiveRecommendations = _recommendationHistory.Where(r => !r.IsImplemented).ToList()
                };

                return stats;
            }
        }

        /// <summary>
        /// Get bottlenecks for specific component
        /// </summary>
        public List<PerformanceBottleneck> GetBottlenecks(PerformanceComponent? component = null, TimeSpan? maxAge = null)
        {
            lock (_lockObject)
            {
                var bottlenecks = _bottleneckHistory.AsEnumerable();

                if (component.HasValue)
                {
                    bottlenecks = bottlenecks.Where(b => b.Component == component.Value);
                }

                if (maxAge.HasValue)
                {
                    var cutoffTime = DateTime.UtcNow - maxAge.Value;
                    bottlenecks = bottlenecks.Where(b => b.DetectedAt >= cutoffTime);
                }

                return bottlenecks.Where(b => !b.IsResolved).OrderByDescending(b => b.Severity).ToList();
            }
        }

        /// <summary>
        /// Get optimization recommendations
        /// </summary>
        public List<OptimizationRecommendation> GetOptimizationRecommendations(PerformanceComponent? component = null)
        {
            lock (_lockObject)
            {
                var recommendations = _recommendationHistory.AsEnumerable();

                if (component.HasValue)
                {
                    recommendations = recommendations.Where(r => r.Component == component.Value);
                }

                return recommendations.Where(r => !r.IsImplemented)
                    .OrderByDescending(r => r.Priority)
                    .ToList();
            }
        }

        /// <summary>
        /// Mark recommendation as implemented
        /// </summary>
        public void MarkRecommendationImplemented(OptimizationRecommendation recommendation)
        {
            lock (_lockObject)
            {
                var existingRecommendation = _recommendationHistory.FirstOrDefault(r => 
                    r.Component == recommendation.Component && 
                    r.RecommendationType == recommendation.RecommendationType);

                if (existingRecommendation != null)
                {
                    existingRecommendation.IsImplemented = true;
                    existingRecommendation.ImplementationDate = DateTime.UtcNow;
                    
                    _logAction($"✅ Optimization recommendation implemented: {recommendation.RecommendationType} for {recommendation.Component}");
                }
            }
        }

        /// <summary>
        /// Force bottleneck check
        /// </summary>
        public void ForceBottleneckCheck()
        {
            lock (_lockObject)
            {
                CheckForBottlenecks();
            }
        }

        /// <summary>
        /// Initialize component statistics
        /// </summary>
        private void InitializeComponentStats()
        {
            foreach (PerformanceComponent component in Enum.GetValues<PerformanceComponent>())
            {
                _componentStats[component] = new ComponentPerformanceStats
                {
                    Component = component,
                    MinResponseTime = TimeSpan.MaxValue,
                    MaxResponseTime = TimeSpan.MinValue
                };
            }
        }

        /// <summary>
        /// Update component statistics
        /// </summary>
        private void UpdateComponentStats(PerformanceMetrics metric)
        {
            if (!_componentStats.TryGetValue(metric.Component, out var stats))
            {
                stats = new ComponentPerformanceStats
                {
                    Component = metric.Component,
                    MinResponseTime = TimeSpan.MaxValue,
                    MaxResponseTime = TimeSpan.MinValue
                };
                _componentStats[metric.Component] = stats;
            }

            // Update counters
            stats.TotalOperations++;
            if (metric.Success)
            {
                stats.SuccessfulOperations++;
            }
            else
            {
                stats.FailedOperations++;
            }

            // Update response times
            stats.TotalResponseTime = stats.TotalResponseTime.Add(metric.ResponseTime);
            stats.AverageResponseTime = TimeSpan.FromMilliseconds(
                stats.TotalResponseTime.TotalMilliseconds / stats.TotalOperations);

            if (metric.ResponseTime < stats.MinResponseTime)
            {
                stats.MinResponseTime = metric.ResponseTime;
            }
            if (metric.ResponseTime > stats.MaxResponseTime)
            {
                stats.MaxResponseTime = metric.ResponseTime;
            }

            // Update memory usage
            stats.TotalMemoryUsage += metric.MemoryUsage;
            stats.AverageMemoryUsage = stats.TotalMemoryUsage / stats.TotalOperations;

            // Calculate operations per second
            var uptime = DateTime.UtcNow - _monitoringStartTime;
            stats.OperationsPerSecond = uptime.TotalSeconds > 0 ? 
                (decimal)(stats.TotalOperations / uptime.TotalSeconds) : 0m;
        }

        /// <summary>
        /// Update overall statistics
        /// </summary>
        private void UpdateOverallStats(PerformanceMetrics metric)
        {
            _overallStats.TotalUptime = DateTime.UtcNow - _monitoringStartTime;
            _overallStats.TotalMemoryUsage += metric.MemoryUsage;
            _overallStats.TotalCacheHits += metric.CacheHits;
            _overallStats.TotalCacheMisses += metric.CacheMisses;

            // Calculate averages
            var totalOperations = _componentStats.Values.Sum(s => s.TotalOperations);
            if (totalOperations > 0)
            {
                _overallStats.AverageMemoryUsage = (decimal)_overallStats.TotalMemoryUsage / totalOperations;
                _overallStats.PeakMemoryUsage = Math.Max(_overallStats.PeakMemoryUsage, metric.MemoryUsage);
            }

            // Calculate system efficiency score
            _overallStats.SystemEfficiencyScore = CalculateSystemEfficiencyScore();

            // Update performance highlights
            UpdatePerformanceHighlights();
        }

        /// <summary>
        /// Check for performance bottlenecks
        /// </summary>
        private void CheckForBottlenecks()
        {
            _lastBottleneckCheck = DateTime.UtcNow;

            foreach (var kvp in _componentStats)
            {
                var component = kvp.Key;
                var stats = kvp.Value;

                // Check response time bottlenecks
                if (stats.AverageResponseTime > TimeSpan.FromMilliseconds(500))
                {
                    CreateBottleneck(component, "HighResponseTime", 
                        $"Average response time ({stats.AverageResponseTime.TotalMilliseconds:F0}ms) exceeds threshold",
                        CalculateBottleneckSeverity(stats.AverageResponseTime.TotalMilliseconds, 500, 2000));
                }

                // Check success rate bottlenecks
                if (stats.SuccessRate < 0.9m)
                {
                    CreateBottleneck(component, "LowSuccessRate",
                        $"Success rate ({stats.SuccessRate:P1}) below threshold",
                        CalculateBottleneckSeverity((double)(1m - stats.SuccessRate), 0.1, 0.5));
                }

                // Check memory usage bottlenecks
                if (stats.AverageMemoryUsage > 150 * 1024 * 1024) // 150MB
                {
                    CreateBottleneck(component, "HighMemoryUsage",
                        $"Average memory usage ({stats.AverageMemoryUsage / (1024 * 1024):F1}MB) exceeds threshold",
                        CalculateBottleneckSeverity(stats.AverageMemoryUsage, 150 * 1024 * 1024, 300 * 1024 * 1024));
                }
            }
        }

        /// <summary>
        /// Create performance bottleneck
        /// </summary>
        private void CreateBottleneck(PerformanceComponent component, string type, string description, decimal severity)
        {
            // Check if similar bottleneck already exists
            var existingBottleneck = _bottleneckHistory
                .Where(b => !b.IsResolved && b.Component == component && b.BottleneckType == type)
                .FirstOrDefault();

            if (existingBottleneck != null)
            {
                // Update existing bottleneck
                existingBottleneck.Duration = DateTime.UtcNow - existingBottleneck.DetectedAt;
                existingBottleneck.Severity = Math.Max(existingBottleneck.Severity, severity);
                return;
            }

            var bottleneck = new PerformanceBottleneck
            {
                DetectedAt = DateTime.UtcNow,
                Component = component,
                BottleneckType = type,
                Description = description,
                Severity = severity,
                Duration = TimeSpan.Zero
            };

            // Add component-specific data
            if (_componentStats.TryGetValue(component, out var stats))
            {
                bottleneck.BottleneckData["AverageResponseTime"] = stats.AverageResponseTime.TotalMilliseconds;
                bottleneck.BottleneckData["SuccessRate"] = stats.SuccessRate;
                bottleneck.BottleneckData["AverageMemoryUsage"] = stats.AverageMemoryUsage;
                bottleneck.BottleneckData["TotalOperations"] = stats.TotalOperations;
            }

            // Generate recommendations for this bottleneck
            bottleneck.Recommendations = GenerateBottleneckRecommendations(bottleneck);

            _bottleneckHistory.Enqueue(bottleneck);
            while (_bottleneckHistory.Count > MAX_BOTTLENECK_HISTORY)
            {
                _bottleneckHistory.Dequeue();
            }

            _logAction($"⚠️ Performance bottleneck detected: {component} - {type} (Severity: {severity:P1})");
        }

        /// <summary>
        /// Update optimization recommendations
        /// </summary>
        private void UpdateOptimizationRecommendations()
        {
            _lastRecommendationUpdate = DateTime.UtcNow;

            // Generate system-wide recommendations
            var systemRecommendations = GenerateSystemRecommendations();
            
            foreach (var recommendation in systemRecommendations)
            {
                // Check if similar recommendation already exists
                var existingRecommendation = _recommendationHistory
                    .Where(r => !r.IsImplemented && 
                               r.Component == recommendation.Component && 
                               r.RecommendationType == recommendation.RecommendationType)
                    .FirstOrDefault();

                if (existingRecommendation == null)
                {
                    _recommendationHistory.Enqueue(recommendation);
                    
                    while (_recommendationHistory.Count > MAX_RECOMMENDATION_HISTORY)
                    {
                        _recommendationHistory.Dequeue();
                    }
                }
            }
        }

        /// <summary>
        /// Calculate bottleneck severity
        /// </summary>
        private decimal CalculateBottleneckSeverity(double currentValue, double warningThreshold, double criticalThreshold)
        {
            if (currentValue <= warningThreshold)
            {
                return 0m;
            }

            if (currentValue >= criticalThreshold)
            {
                return 1m;
            }

            // Linear interpolation between warning and critical
            var range = criticalThreshold - warningThreshold;
            var position = currentValue - warningThreshold;
            return (decimal)(position / range);
        }

        /// <summary>
        /// Generate recommendations for specific bottleneck
        /// </summary>
        private List<OptimizationRecommendation> GenerateBottleneckRecommendations(PerformanceBottleneck bottleneck)
        {
            var recommendations = new List<OptimizationRecommendation>();

            switch (bottleneck.BottleneckType)
            {
                case "HighResponseTime":
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Timestamp = DateTime.UtcNow,
                        Component = bottleneck.Component,
                        RecommendationType = "ReduceResponseTime",
                        Description = $"Optimize {bottleneck.Component} to reduce response time",
                        Priority = bottleneck.Severity,
                        ExpectedImprovement = 0.3m,
                        ImplementationSteps = new List<string>
                        {
                            "Increase analysis intervals",
                            "Enable more aggressive caching",
                            "Optimize algorithm complexity",
                            "Consider parallel processing"
                        }
                    });
                    break;

                case "LowSuccessRate":
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Timestamp = DateTime.UtcNow,
                        Component = bottleneck.Component,
                        RecommendationType = "ImproveReliability",
                        Description = $"Improve {bottleneck.Component} reliability and error handling",
                        Priority = bottleneck.Severity,
                        ExpectedImprovement = 0.4m,
                        ImplementationSteps = new List<string>
                        {
                            "Add more robust error handling",
                            "Implement retry mechanisms",
                            "Add circuit breaker protection",
                            "Improve input validation"
                        }
                    });
                    break;

                case "HighMemoryUsage":
                    recommendations.Add(new OptimizationRecommendation
                    {
                        Timestamp = DateTime.UtcNow,
                        Component = bottleneck.Component,
                        RecommendationType = "OptimizeMemory",
                        Description = $"Reduce {bottleneck.Component} memory usage",
                        Priority = bottleneck.Severity,
                        ExpectedImprovement = 0.35m,
                        ImplementationSteps = new List<string>
                        {
                            "Reduce data retention periods",
                            "Optimize data structures",
                            "Implement memory pooling",
                            "Add garbage collection optimization"
                        }
                    });
                    break;
            }

            return recommendations;
        }

        /// <summary>
        /// Generate system-wide recommendations
        /// </summary>
        private List<OptimizationRecommendation> GenerateSystemRecommendations()
        {
            var recommendations = new List<OptimizationRecommendation>();

            // Overall system performance analysis
            var totalOperations = _componentStats.Values.Sum(s => s.TotalOperations);
            var overallSuccessRate = totalOperations > 0 ?
                (decimal)_componentStats.Values.Sum(s => s.SuccessfulOperations) / totalOperations : 1m;
            var averageResponseTime = _componentStats.Values
                .Where(s => s.TotalOperations > 0)
                .Average(s => s.AverageResponseTime.TotalMilliseconds);

            // System-wide response time recommendation
            if (averageResponseTime > 300) // 300ms average
            {
                recommendations.Add(new OptimizationRecommendation
                {
                    Timestamp = DateTime.UtcNow,
                    Component = PerformanceComponent.PerformanceMonitor,
                    RecommendationType = "SystemResponseTimeOptimization",
                    Description = "Optimize overall system response time",
                    Priority = 0.7m,
                    ExpectedImprovement = 0.25m,
                    ImplementationSteps = new List<string>
                    {
                        "Enable intelligent caching across all components",
                        "Optimize analysis intervals based on market conditions",
                        "Implement parallel processing where possible",
                        "Reduce data processing complexity"
                    }
                });
            }

            // System-wide reliability recommendation
            if (overallSuccessRate < 0.92m)
            {
                recommendations.Add(new OptimizationRecommendation
                {
                    Timestamp = DateTime.UtcNow,
                    Component = PerformanceComponent.PerformanceMonitor,
                    RecommendationType = "SystemReliabilityOptimization",
                    Description = "Improve overall system reliability",
                    Priority = 0.8m,
                    ExpectedImprovement = 0.3m,
                    ImplementationSteps = new List<string>
                    {
                        "Strengthen circuit breaker protection",
                        "Implement comprehensive error handling",
                        "Add system health monitoring",
                        "Improve graceful degradation mechanisms"
                    }
                });
            }

            // Cache performance recommendation
            if (_overallStats.OverallCacheHitRatio < 0.75m)
            {
                recommendations.Add(new OptimizationRecommendation
                {
                    Timestamp = DateTime.UtcNow,
                    Component = PerformanceComponent.CachingSystem,
                    RecommendationType = "CacheOptimization",
                    Description = "Improve cache performance and hit ratio",
                    Priority = 0.6m,
                    ExpectedImprovement = 0.2m,
                    ImplementationSteps = new List<string>
                    {
                        "Optimize cache TTL settings",
                        "Implement smarter cache invalidation",
                        "Increase cache sizes for frequently accessed data",
                        "Add cache warming strategies"
                    }
                });
            }

            return recommendations;
        }

        /// <summary>
        /// Calculate system efficiency score
        /// </summary>
        private decimal CalculateSystemEfficiencyScore()
        {
            if (_componentStats.Count == 0) return 0m;

            var totalOperations = _componentStats.Values.Sum(s => s.TotalOperations);
            if (totalOperations == 0) return 0m;

            // Calculate weighted scores
            var responseTimeScore = CalculateResponseTimeScore();
            var successRateScore = CalculateSuccessRateScore();
            var memoryEfficiencyScore = CalculateMemoryEfficiencyScore();
            var cacheEfficiencyScore = _overallStats.OverallCacheHitRatio;

            // Weighted average (response time and success rate are most important)
            return (responseTimeScore * 0.35m) + (successRateScore * 0.35m) +
                   (memoryEfficiencyScore * 0.2m) + (cacheEfficiencyScore * 0.1m);
        }

        /// <summary>
        /// Calculate response time score
        /// </summary>
        private decimal CalculateResponseTimeScore()
        {
            var componentsWithData = _componentStats.Values.Where(s => s.TotalOperations > 0).ToList();
            if (componentsWithData.Count == 0) return 1m;

            var averageResponseTime = componentsWithData.Average(s => s.AverageResponseTime.TotalMilliseconds);

            // Score based on response time (lower is better)
            // 0-100ms = 1.0, 100-500ms = linear decrease, 500ms+ = 0.0
            if (averageResponseTime <= 100) return 1m;
            if (averageResponseTime >= 500) return 0m;

            return 1m - (decimal)((averageResponseTime - 100) / 400);
        }

        /// <summary>
        /// Calculate success rate score
        /// </summary>
        private decimal CalculateSuccessRateScore()
        {
            var totalOperations = _componentStats.Values.Sum(s => s.TotalOperations);
            if (totalOperations == 0) return 1m;

            var totalSuccessful = _componentStats.Values.Sum(s => s.SuccessfulOperations);
            return (decimal)totalSuccessful / totalOperations;
        }

        /// <summary>
        /// Calculate memory efficiency score
        /// </summary>
        private decimal CalculateMemoryEfficiencyScore()
        {
            var totalOperations = _componentStats.Values.Sum(s => s.TotalOperations);
            if (totalOperations == 0) return 1m;

            var averageMemoryUsage = _overallStats.AverageMemoryUsage;
            var targetMemoryUsage = 100 * 1024 * 1024; // 100MB target

            // Score based on memory usage (lower is better)
            if (averageMemoryUsage <= targetMemoryUsage) return 1m;
            if (averageMemoryUsage >= targetMemoryUsage * 3) return 0m;

            return 1m - (averageMemoryUsage - targetMemoryUsage) / (targetMemoryUsage * 2);
        }

        /// <summary>
        /// Update performance highlights
        /// </summary>
        private void UpdatePerformanceHighlights()
        {
            _overallStats.PerformanceHighlights.Clear();

            // Efficiency score highlight
            if (_overallStats.SystemEfficiencyScore >= 0.9m)
            {
                _overallStats.PerformanceHighlights.Add($"Excellent system efficiency: {_overallStats.SystemEfficiencyScore:P1}");
            }
            else if (_overallStats.SystemEfficiencyScore >= 0.7m)
            {
                _overallStats.PerformanceHighlights.Add($"Good system efficiency: {_overallStats.SystemEfficiencyScore:P1}");
            }
            else
            {
                _overallStats.PerformanceHighlights.Add($"System efficiency needs improvement: {_overallStats.SystemEfficiencyScore:P1}");
            }

            // Cache performance highlight
            if (_overallStats.OverallCacheHitRatio >= 0.8m)
            {
                _overallStats.PerformanceHighlights.Add($"Excellent cache performance: {_overallStats.OverallCacheHitRatio:P1} hit ratio");
            }

            // Memory usage highlight
            var memoryUsageMB = _overallStats.AverageMemoryUsage / (1024 * 1024);
            if (memoryUsageMB <= 100)
            {
                _overallStats.PerformanceHighlights.Add($"Efficient memory usage: {memoryUsageMB:F1}MB average");
            }

            // Uptime highlight
            if (_overallStats.TotalUptime > TimeSpan.FromHours(1))
            {
                _overallStats.PerformanceHighlights.Add($"System uptime: {_overallStats.TotalUptime.TotalHours:F1} hours");
            }
        }

        /// <summary>
        /// Clone overall stats for thread safety
        /// </summary>
        private OverallPerformanceStats CloneOverallStats(OverallPerformanceStats original)
        {
            return new OverallPerformanceStats
            {
                TotalUptime = original.TotalUptime,
                TotalMemoryUsage = original.TotalMemoryUsage,
                AverageMemoryUsage = original.AverageMemoryUsage,
                PeakMemoryUsage = original.PeakMemoryUsage,
                TotalCacheHits = original.TotalCacheHits,
                TotalCacheMisses = original.TotalCacheMisses,
                SystemEfficiencyScore = original.SystemEfficiencyScore,
                PerformanceHighlights = new List<string>(original.PerformanceHighlights)
            };
        }

        private bool ShouldCheckBottlenecks()
        {
            return DateTime.UtcNow - _lastBottleneckCheck >= BOTTLENECK_CHECK_INTERVAL;
        }

        private bool ShouldUpdateRecommendations()
        {
            return DateTime.UtcNow - _lastRecommendationUpdate >= RECOMMENDATION_UPDATE_INTERVAL;
        }
    }
}
