# SmartVolumeStrategy API Reference

## 📋 Table of Contents
1. [Overview](#overview)
2. [Core Interfaces](#core-interfaces)
3. [Data Models](#data-models)
4. [Analysis Components](#analysis-components)
5. [Strategy Components](#strategy-components)
6. [Monitoring Components](#monitoring-components)
7. [Extension Points](#extension-points)
8. [Integration Examples](#integration-examples)

---

## 🔍 Overview

The SmartVolumeStrategy API provides a comprehensive set of interfaces and models for building intelligent trading systems. The architecture follows SOLID principles with clear separation of concerns and dependency injection.

### Key Design Principles
- **Interface-Driven**: All components implement well-defined interfaces
- **Async/Await**: Non-blocking operations for analysis and calibration
- **Thread-Safe**: Concurrent access protection with correlation IDs
- **Extensible**: Plugin architecture for custom analyzers and strategies
- **Testable**: Mockable interfaces for unit testing

---

## 🔌 Core Interfaces

### ISymbolAnalyzer
**Purpose**: Master coordinator for symbol analysis and profiling

```csharp
public interface ISymbolAnalyzer
{
    /// <summary>
    /// Analyzes symbol characteristics and generates comprehensive profile
    /// </summary>
    /// <param name="symbol">Trading symbol (e.g., "BTCUSDT")</param>
    /// <param name="marketData">Historical market data for analysis</param>
    /// <param name="lookbackBars">Number of bars to analyze</param>
    /// <returns>Complete symbol profile with analysis results</returns>
    Task<SymbolProfile> AnalyzeSymbolAsync(string symbol, List<MarketDataPoint> marketData, int lookbackBars);

    /// <summary>
    /// Gets current analysis status and progress
    /// </summary>
    /// <returns>Analysis status with progress information</returns>
    AnalysisStatus GetAnalysisStatus();

    /// <summary>
    /// Cancels ongoing analysis operation
    /// </summary>
    void CancelAnalysis();
}
```

### ISettingsCalibrator
**Purpose**: Optimizes strategy parameters based on symbol analysis

```csharp
public interface ISettingsCalibrator
{
    /// <summary>
    /// Calibrates optimal settings for a symbol
    /// </summary>
    /// <param name="symbolProfile">Symbol analysis results</param>
    /// <param name="marketData">Historical data for backtesting</param>
    /// <param name="userConstraints">User-defined risk and preference constraints</param>
    /// <returns>Calibrated settings with confidence scores</returns>
    Task<CalibrationResult> CalibrateSettingsAsync(SymbolProfile symbolProfile, 
        List<MarketDataPoint> marketData, UserConstraints userConstraints);

    /// <summary>
    /// Recalibrates settings based on performance feedback
    /// </summary>
    /// <param name="currentSettings">Current strategy settings</param>
    /// <param name="performanceMetrics">Recent performance data</param>
    /// <returns>Adjusted settings</returns>
    Task<OptimalSettings> RecalibrateAsync(OptimalSettings currentSettings, 
        PerformanceMetrics performanceMetrics);
}
```

### ISignalGenerator
**Purpose**: Generates trading signals with confidence scoring

```csharp
public interface ISignalGenerator
{
    /// <summary>
    /// Generates trading signal for current market conditions
    /// </summary>
    /// <param name="currentBar">Current market data point</param>
    /// <param name="volumeBlock">Volume block detection result</param>
    /// <param name="marketContext">Current market context</param>
    /// <param name="settings">Calibrated strategy settings</param>
    /// <returns>Trading signal with confidence score</returns>
    TradingSignal GenerateSignal(MarketDataPoint currentBar, VolumeBlockResult volumeBlock, 
        MarketContext marketContext, OptimalSettings settings);

    /// <summary>
    /// Calculates signal confidence based on multiple factors
    /// </summary>
    /// <param name="marketData">Current market data</param>
    /// <param name="settings">Strategy settings</param>
    /// <returns>Confidence score (0-100)</returns>
    decimal CalculateSignalConfidence(MarketDataPoint marketData, OptimalSettings settings);

    /// <summary>
    /// Validates signal against current market conditions
    /// </summary>
    /// <param name="signal">Generated signal</param>
    /// <param name="marketContext">Current market context</param>
    /// <returns>True if signal is valid for current conditions</returns>
    bool ValidateSignal(TradingSignal signal, MarketContext marketContext);
}
```

### IPositionManager
**Purpose**: Manages trading positions and risk

```csharp
public interface IPositionManager
{
    /// <summary>
    /// Checks if new position can be opened
    /// </summary>
    /// <param name="signal">Trading signal to evaluate</param>
    /// <returns>True if position can be opened</returns>
    bool CanOpenPosition(TradingSignal signal);

    /// <summary>
    /// Opens new trading position
    /// </summary>
    /// <param name="signal">Trading signal</param>
    /// <param name="entryPrice">Actual entry price</param>
    /// <param name="quantity">Position quantity</param>
    /// <param name="settings">Strategy settings</param>
    void OpenPosition(TradingSignal signal, decimal entryPrice, decimal quantity, OptimalSettings settings);

    /// <summary>
    /// Updates existing position information
    /// </summary>
    /// <param name="positionInfo">Updated position data</param>
    void UpdatePosition(PositionInfo positionInfo);

    /// <summary>
    /// Gets current position information
    /// </summary>
    /// <returns>Current position details or null if no position</returns>
    PositionInfo? GetCurrentPosition();

    /// <summary>
    /// Closes current position
    /// </summary>
    /// <param name="reason">Reason for closing</param>
    void ClosePosition(string reason);
}
```

---

## 📊 Data Models

### MarketDataPoint
**Purpose**: Represents a single bar of market data

```csharp
public class MarketDataPoint
{
    /// <summary>Bar timestamp</summary>
    public DateTime Timestamp { get; set; }
    
    /// <summary>Opening price</summary>
    public decimal Open { get; set; }
    
    /// <summary>Highest price</summary>
    public decimal High { get; set; }
    
    /// <summary>Lowest price</summary>
    public decimal Low { get; set; }
    
    /// <summary>Closing price</summary>
    public decimal Close { get; set; }
    
    /// <summary>Total volume</summary>
    public decimal Volume { get; set; }
    
    /// <summary>Buy volume (if available)</summary>
    public decimal BuyVolume { get; set; }
    
    /// <summary>Sell volume (if available)</summary>
    public decimal SellVolume { get; set; }
    
    /// <summary>Bar index in sequence</summary>
    public int BarIndex { get; set; }
    
    /// <summary>Calculated volume delta (buy - sell)</summary>
    public decimal VolumeDelta => BuyVolume - SellVolume;
    
    /// <summary>Price change from open to close</summary>
    public decimal PriceChange => Close - Open;
    
    /// <summary>Price change percentage</summary>
    public decimal PriceChangePercent => Open != 0 ? (PriceChange / Open) * 100 : 0;
}
```

### TradingSignal
**Purpose**: Represents a trading signal with metadata

```csharp
public class TradingSignal
{
    /// <summary>Signal type (Long/Short)</summary>
    public SignalType Type { get; set; }
    
    /// <summary>Signal confidence (0-100)</summary>
    public decimal Confidence { get; set; }
    
    /// <summary>Signal quality classification</summary>
    public SignalQuality Quality { get; set; }
    
    /// <summary>Signal generation timestamp</summary>
    public DateTime Timestamp { get; set; }
    
    /// <summary>Recommended entry price</summary>
    public decimal Price { get; set; }
    
    /// <summary>Human-readable reason for signal</summary>
    public string Reason { get; set; }
    
    /// <summary>Correlation ID for tracking</summary>
    public string CorrelationId { get; set; }
    
    /// <summary>Volume block that triggered signal</summary>
    public VolumeBlockResult? VolumeBlock { get; set; }
    
    /// <summary>Market context at signal time</summary>
    public MarketContext? MarketContext { get; set; }
}

public enum SignalType { Long, Short }
public enum SignalQuality { Poor, Fair, Good, Excellent }
```

### SymbolProfile
**Purpose**: Comprehensive analysis results for a trading symbol

```csharp
public class SymbolProfile
{
    /// <summary>Trading symbol</summary>
    public string Symbol { get; set; }
    
    /// <summary>Analysis timestamp</summary>
    public DateTime AnalysisTime { get; set; }
    
    /// <summary>Number of bars analyzed</summary>
    public int BarsAnalyzed { get; set; }
    
    /// <summary>Volume characteristics</summary>
    public VolumeCharacteristics VolumeProfile { get; set; }
    
    /// <summary>Delta flow patterns</summary>
    public DeltaFlowCharacteristics DeltaProfile { get; set; }
    
    /// <summary>Volatility measurements</summary>
    public VolatilityCharacteristics VolatilityProfile { get; set; }
    
    /// <summary>Market session analysis</summary>
    public MarketProfileCharacteristics MarketProfile { get; set; }
    
    /// <summary>Overall analysis confidence</summary>
    public decimal OverallConfidence { get; set; }
    
    /// <summary>Recommended trading approach</summary>
    public TradingApproach RecommendedApproach { get; set; }
}
```

### OptimalSettings
**Purpose**: Calibrated strategy parameters

```csharp
public class OptimalSettings
{
    /// <summary>Volume threshold multiplier</summary>
    public decimal VolumeThreshold { get; set; } = 2.0m;
    
    /// <summary>Signal confidence threshold</summary>
    public decimal SignalThreshold { get; set; } = 1.0m;
    
    /// <summary>Take profit percentage</summary>
    public decimal TakeProfitPercent { get; set; } = 1.2m;
    
    /// <summary>Stop loss percentage</summary>
    public decimal StopLossPercent { get; set; } = 0.8m;
    
    /// <summary>Position size in USDT</summary>
    public decimal PositionSizeUSDT { get; set; } = 1000m;
    
    /// <summary>Minimum signal confidence required</summary>
    public decimal MinimumConfidence { get; set; } = 70m;
    
    /// <summary>Position cooldown in seconds</summary>
    public int CooldownSeconds { get; set; } = 30;
    
    /// <summary>Settings calibration timestamp</summary>
    public DateTime CalibrationTime { get; set; }
    
    /// <summary>Confidence in these settings</summary>
    public decimal SettingsConfidence { get; set; }
}
```

---

## 🔬 Analysis Components

### IVolumePatternAnalyzer
**Purpose**: Analyzes volume patterns and characteristics

```csharp
public interface IVolumePatternAnalyzer
{
    /// <summary>
    /// Analyzes volume patterns in market data
    /// </summary>
    /// <param name="marketData">Historical market data</param>
    /// <param name="lookbackBars">Number of bars to analyze</param>
    /// <returns>Volume characteristics and patterns</returns>
    Task<VolumeCharacteristics> AnalyzeVolumePatterns(List<MarketDataPoint> marketData, int lookbackBars);

    /// <summary>
    /// Calculates optimal volume threshold for signal generation
    /// </summary>
    /// <param name="volumeData">Volume data points</param>
    /// <returns>Optimal threshold multiplier</returns>
    decimal CalculateOptimalVolumeThreshold(List<decimal> volumeData);

    /// <summary>
    /// Classifies volume regime (High/Medium/Low activity)
    /// </summary>
    /// <param name="averageVolume">Average volume</param>
    /// <param name="currentVolume">Current volume</param>
    /// <returns>Volume regime classification</returns>
    VolumeRegime ClassifyVolumeRegime(decimal averageVolume, decimal currentVolume);
}
```

### IDeltaFlowAnalyzer
**Purpose**: Analyzes buy/sell pressure and institutional activity

```csharp
public interface IDeltaFlowAnalyzer
{
    /// <summary>
    /// Analyzes delta flow patterns
    /// </summary>
    /// <param name="marketData">Market data with buy/sell volumes</param>
    /// <param name="lookbackBars">Analysis period</param>
    /// <returns>Delta flow characteristics</returns>
    Task<DeltaFlowCharacteristics> AnalyzeDeltaFlow(List<MarketDataPoint> marketData, int lookbackBars);

    /// <summary>
    /// Detects institutional activity patterns
    /// </summary>
    /// <param name="deltaData">Volume delta data</param>
    /// <returns>Institutional activity indicators</returns>
    InstitutionalActivity DetectInstitutionalActivity(List<decimal> deltaData);

    /// <summary>
    /// Calculates optimal delta significance threshold
    /// </summary>
    /// <param name="deltaData">Historical delta data</param>
    /// <returns>Optimal threshold for significant delta moves</returns>
    decimal CalculateOptimalDeltaThreshold(List<decimal> deltaData);
}
```

---

## 🎯 Strategy Components

### IVolumeBlockDetector
**Purpose**: Detects volume blocks using calibrated parameters

```csharp
public interface IVolumeBlockDetector
{
    /// <summary>
    /// Detects volume block in current market data
    /// </summary>
    /// <param name="currentBar">Current market data</param>
    /// <param name="historicalData">Recent historical data for context</param>
    /// <param name="settings">Calibrated detection settings</param>
    /// <returns>Volume block detection result</returns>
    VolumeBlockResult DetectVolumeBlock(MarketDataPoint currentBar, 
        List<MarketDataPoint> historicalData, OptimalSettings settings);

    /// <summary>
    /// Calculates volume block strength
    /// </summary>
    /// <param name="currentVolume">Current bar volume</param>
    /// <param name="averageVolume">Average volume</param>
    /// <param name="threshold">Volume threshold</param>
    /// <returns>Block strength (0-100)</returns>
    decimal CalculateBlockStrength(decimal currentVolume, decimal averageVolume, decimal threshold);
}
```

### IOrderExecutor
**Purpose**: Executes trading orders with proper risk management

```csharp
public interface IOrderExecutor
{
    /// <summary>
    /// Executes entry order for trading signal
    /// </summary>
    /// <param name="signal">Trading signal</param>
    /// <param name="settings">Strategy settings</param>
    /// <returns>Order execution result</returns>
    Task<OrderResult> ExecuteEntryOrder(TradingSignal signal, OptimalSettings settings);

    /// <summary>
    /// Places take profit order
    /// </summary>
    /// <param name="position">Current position</param>
    /// <param name="takeProfitPrice">Target price</param>
    /// <returns>Order execution result</returns>
    Task<OrderResult> PlaceTakeProfitOrder(PositionInfo position, decimal takeProfitPrice);

    /// <summary>
    /// Places stop loss order
    /// </summary>
    /// <param name="position">Current position</param>
    /// <param name="stopLossPrice">Stop price</param>
    /// <returns>Order execution result</returns>
    Task<OrderResult> PlaceStopLossOrder(PositionInfo position, decimal stopLossPrice);

    /// <summary>
    /// Cancels existing order
    /// </summary>
    /// <param name="orderId">Order identifier</param>
    /// <returns>Cancellation result</returns>
    Task<bool> CancelOrder(string orderId);
}
```

---

## 📊 Monitoring Components

### IPerformanceTracker
**Purpose**: Tracks and analyzes strategy performance

```csharp
public interface IPerformanceTracker
{
    /// <summary>
    /// Records trade execution
    /// </summary>
    /// <param name="trade">Completed trade information</param>
    void RecordTrade(TradeRecord trade);

    /// <summary>
    /// Gets current performance metrics
    /// </summary>
    /// <returns>Real-time performance data</returns>
    PerformanceMetrics GetCurrentMetrics();

    /// <summary>
    /// Gets performance over specified period
    /// </summary>
    /// <param name="period">Time period for analysis</param>
    /// <returns>Period-specific performance metrics</returns>
    PerformanceMetrics GetPerformanceForPeriod(TimeSpan period);

    /// <summary>
    /// Calculates performance statistics
    /// </summary>
    /// <param name="trades">Trade history</param>
    /// <returns>Statistical analysis of performance</returns>
    PerformanceStatistics CalculateStatistics(List<TradeRecord> trades);
}
```

### IAdaptiveAdjuster
**Purpose**: Automatically adjusts strategy parameters based on performance

```csharp
public interface IAdaptiveAdjuster
{
    /// <summary>
    /// Evaluates current performance and suggests adjustments
    /// </summary>
    /// <param name="currentSettings">Current strategy settings</param>
    /// <param name="performanceMetrics">Recent performance data</param>
    /// <returns>Suggested parameter adjustments</returns>
    Task<AdjustmentRecommendation> EvaluatePerformance(OptimalSettings currentSettings, 
        PerformanceMetrics performanceMetrics);

    /// <summary>
    /// Applies automatic adjustments to strategy settings
    /// </summary>
    /// <param name="currentSettings">Current settings</param>
    /// <param name="adjustments">Recommended adjustments</param>
    /// <returns>Updated settings</returns>
    OptimalSettings ApplyAdjustments(OptimalSettings currentSettings, 
        AdjustmentRecommendation adjustments);

    /// <summary>
    /// Checks if recalibration is needed
    /// </summary>
    /// <param name="performanceMetrics">Current performance</param>
    /// <param name="lastCalibration">Last calibration time</param>
    /// <returns>True if recalibration is recommended</returns>
    bool ShouldRecalibrate(PerformanceMetrics performanceMetrics, DateTime lastCalibration);
}
```

---

## 🔧 Extension Points

### Custom Analyzer Implementation
```csharp
public class CustomVolumeAnalyzer : IVolumePatternAnalyzer
{
    public async Task<VolumeCharacteristics> AnalyzeVolumePatterns(
        List<MarketDataPoint> marketData, int lookbackBars)
    {
        // Custom volume analysis logic
        var characteristics = new VolumeCharacteristics();
        
        // Implement custom analysis
        await AnalyzeCustomPatterns(marketData, characteristics);
        
        return characteristics;
    }
    
    private async Task AnalyzeCustomPatterns(List<MarketDataPoint> data, 
        VolumeCharacteristics characteristics)
    {
        // Custom pattern detection logic
    }
}
```

### Custom Signal Generator
```csharp
public class CustomSignalGenerator : ISignalGenerator
{
    public TradingSignal GenerateSignal(MarketDataPoint currentBar, 
        VolumeBlockResult volumeBlock, MarketContext marketContext, 
        OptimalSettings settings)
    {
        // Custom signal generation logic
        var signal = new TradingSignal
        {
            Type = DetermineSignalType(currentBar, volumeBlock),
            Confidence = CalculateCustomConfidence(currentBar, settings),
            Quality = ClassifySignalQuality(currentBar, marketContext),
            Timestamp = DateTime.UtcNow,
            Price = currentBar.Close,
            Reason = "Custom signal logic",
            CorrelationId = Guid.NewGuid().ToString("N")[..8]
        };
        
        return signal;
    }
}
```

---

## 💡 Integration Examples

### Basic Strategy Integration
```csharp
public class MyCustomStrategy : ChartStrategy
{
    private ISymbolAnalyzer _symbolAnalyzer;
    private ISignalGenerator _signalGenerator;
    private IPositionManager _positionManager;
    
    protected override void OnInitialize()
    {
        // Initialize components
        _symbolAnalyzer = new SymbolAnalyzer(/* dependencies */);
        _signalGenerator = new CustomSignalGenerator();
        _positionManager = new PositionManager();
    }
    
    protected override void OnCalculate(int bar, decimal value)
    {
        var marketData = ConvertToMarketDataPoint(bar);
        
        // Generate signals
        var signal = _signalGenerator.GenerateSignal(marketData, 
            volumeBlock, marketContext, settings);
            
        // Execute trades
        if (signal.Quality >= SignalQuality.Good && 
            _positionManager.CanOpenPosition(signal))
        {
            ExecuteTrade(signal);
        }
    }
}
```

### Custom Performance Monitor
```csharp
public class CustomPerformanceMonitor : IPerformanceTracker
{
    private readonly List<TradeRecord> _trades = new();
    private readonly Dictionary<string, decimal> _customMetrics = new();
    
    public void RecordTrade(TradeRecord trade)
    {
        _trades.Add(trade);
        UpdateCustomMetrics(trade);
    }
    
    public PerformanceMetrics GetCurrentMetrics()
    {
        return new PerformanceMetrics
        {
            TotalTrades = _trades.Count,
            WinRate = CalculateWinRate(),
            ProfitFactor = CalculateProfitFactor(),
            MaxDrawdown = CalculateMaxDrawdown(),
            CustomMetrics = _customMetrics
        };
    }
}
```

---

*This API reference provides comprehensive documentation for extending and integrating with SmartVolumeStrategy. For usage examples, see the User Guide and Technical Documentation.*
