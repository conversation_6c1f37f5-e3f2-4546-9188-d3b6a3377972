using System;
using System.Collections.Generic;

namespace SmartVolumeStrategy.Models
{
    /// <summary>
    /// Comprehensive symbol profile containing analyzed characteristics and optimal settings
    /// </summary>
    public class SymbolProfile
    {
        public string Symbol { get; set; } = "";
        public DateTime AnalysisTimestamp { get; set; } = DateTime.UtcNow;
        public int BarsAnalyzed { get; set; }
        public string CorrelationId { get; set; } = Guid.NewGuid().ToString("N")[..8];

        // Market Characteristics
        public VolumeCharacteristics Volume { get; set; } = new();
        public DeltaFlowCharacteristics DeltaFlow { get; set; } = new();
        public VolatilityCharacteristics Volatility { get; set; } = new();
        public MarketProfileCharacteristics MarketProfile { get; set; } = new();

        // Analysis Results (for test integration)
        public VolumeAnalysisResult VolumeAnalysisResult { get; set; } = new();
        public DeltaFlowAnalysisResult DeltaFlowAnalysisResult { get; set; } = new();
        public VolatilityAnalysisResult VolatilityAnalysisResult { get; set; } = new();
        public MarketProfileAnalysisResult MarketProfileAnalysisResult { get; set; } = new();

        // Analysis Quality
        public decimal AnalysisConfidence { get; set; }
        public List<string> AnalysisWarnings { get; set; } = new();
        public TimeSpan AnalysisDuration { get; set; }
    }

    /// <summary>
    /// Volume pattern characteristics for the symbol
    /// </summary>
    public class VolumeCharacteristics
    {
        public decimal AverageVolume { get; set; }
        public decimal VolumeStandardDeviation { get; set; }
        public decimal MedianVolume { get; set; }
        public decimal Volume95thPercentile { get; set; }
        
        // Spike Analysis
        public int VolumeSpikeFrequency { get; set; } // Spikes per 100 bars
        public decimal AverageSpikeMultiplier { get; set; } // Average spike size (2.5x, 3x, etc.)
        public decimal OptimalVolumeThreshold { get; set; } // Recommended threshold
        
        // Pattern Classification
        public VolumeRegime Regime { get; set; } = VolumeRegime.Normal;
        public decimal VolumeConsistency { get; set; } // 0-1 score
        public bool IsHighVolumeSymbol { get; set; }
    }

    /// <summary>
    /// Delta flow and buy/sell pressure characteristics
    /// </summary>
    public class DeltaFlowCharacteristics
    {
        public decimal AverageDeltaImbalance { get; set; }
        public decimal DeltaImbalanceStdDev { get; set; }
        public decimal InstitutionalActivityLevel { get; set; } // 0-1 score
        
        // CVD Analysis
        public decimal CVDTrendStrength { get; set; } // How often CVD trends
        public decimal CVDReversalFrequency { get; set; } // How often CVD reverses
        public decimal OptimalDeltaThreshold { get; set; } // Recommended delta threshold
        
        // Pattern Classification
        public DeltaFlowRegime Regime { get; set; } = DeltaFlowRegime.Balanced;
        public bool HasStrongInstitutionalActivity { get; set; }
        public decimal BuyPressureDominance { get; set; } // -1 to 1 (sell to buy bias)
    }

    /// <summary>
    /// Price volatility and movement characteristics
    /// </summary>
    public class VolatilityCharacteristics
    {
        public decimal AverageVolatility { get; set; } // Average price movement per bar
        public decimal VolatilityStandardDeviation { get; set; }
        public decimal MaxVolatility { get; set; }
        public decimal VolatilityPercentile95 { get; set; }
        
        // Movement Analysis
        public decimal AverageTrueRange { get; set; }
        public decimal TrendingFrequency { get; set; } // How often price trends
        public decimal RangingFrequency { get; set; } // How often price ranges
        
        // Risk Metrics
        public decimal OptimalTakeProfitPercent { get; set; }
        public decimal OptimalStopLossPercent { get; set; }
        public decimal RiskAdjustmentFactor { get; set; } // Position size multiplier
        
        // Pattern Classification
        public VolatilityRegime Regime { get; set; } = VolatilityRegime.Normal;
        public bool IsHighVolatilitySymbol { get; set; }
    }

    /// <summary>
    /// Market profile and session characteristics
    /// </summary>
    public class MarketProfileCharacteristics
    {
        public TradingSession MostActiveSession { get; set; } = TradingSession.Unknown;
        public decimal AsianSessionActivity { get; set; } // 0-1 score
        public decimal EuropeanSessionActivity { get; set; }
        public decimal USSessionActivity { get; set; }
        
        // Time-based patterns
        public List<int> HighActivityHours { get; set; } = new(); // UTC hours
        public List<int> LowActivityHours { get; set; } = new();
        public decimal SessionVolatilityVariation { get; set; } // How much volatility varies by session
        
        // Optimal trading windows
        public List<TimeRange> OptimalTradingWindows { get; set; } = new();
        public bool Is24HourMarket { get; set; } = true; // Crypto vs traditional markets
    }

    /// <summary>
    /// Time range for optimal trading windows
    /// </summary>
    public class TimeRange
    {
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public decimal ActivityScore { get; set; } // 0-1 score
        public string Description { get; set; } = "";
    }

    /// <summary>
    /// Volume regime classification
    /// </summary>
    public enum VolumeRegime
    {
        VeryLow,
        Low,
        Normal,
        High,
        VeryHigh,
        Erratic
    }

    /// <summary>
    /// Delta flow regime classification
    /// </summary>
    public enum DeltaFlowRegime
    {
        StrongSellPressure,
        SellPressure,
        Balanced,
        BuyPressure,
        StrongBuyPressure,
        Erratic
    }

    /// <summary>
    /// Volatility regime classification
    /// </summary>
    public enum VolatilityRegime
    {
        VeryLow,
        Low,
        Medium,      // Added for enhanced confidence calculator
        Normal,
        High,
        VeryHigh,
        Extreme
    }

    /// <summary>
    /// Trading session classification
    /// </summary>
    public enum TradingSession
    {
        Unknown,
        Asian,
        European,
        US,
        Overlap_AsianEuropean,
        Overlap_EuropeanUS,
        Crypto24H
    }

    /// <summary>
    /// Volume analysis result - simplified version for test integration
    /// </summary>
    public class VolumeAnalysisResult
    {
        public decimal AverageVolume { get; set; }
        public decimal VolumeStandardDeviation { get; set; }
        public VolumeRegime VolumeRegime { get; set; } = VolumeRegime.Normal;
        public decimal OptimalVolumeThreshold { get; set; }
        public decimal VolumeConsistency { get; set; }
    }

    /// <summary>
    /// Delta flow analysis result - simplified version for test integration
    /// </summary>
    public class DeltaFlowAnalysisResult
    {
        public decimal AverageDeltaImbalance { get; set; }
        public decimal DeltaImbalanceStdDev { get; set; }
        public DeltaFlowRegime DeltaFlowRegime { get; set; } = DeltaFlowRegime.Balanced;
        public decimal OptimalDeltaThreshold { get; set; }
        public decimal InstitutionalActivityLevel { get; set; }
    }

    /// <summary>
    /// Volatility analysis result - simplified version for test integration
    /// </summary>
    public class VolatilityAnalysisResult
    {
        public decimal AverageVolatility { get; set; }
        public decimal VolatilityStandardDeviation { get; set; }
        public VolatilityRegime VolatilityRegime { get; set; } = VolatilityRegime.Normal;
        public decimal OptimalTakeProfitPercent { get; set; }
        public decimal OptimalStopLossPercent { get; set; }
    }

    /// <summary>
    /// Market profile analysis result - simplified version for test integration
    /// </summary>
    public class MarketProfileAnalysisResult
    {
        public TradingSession MostActiveSession { get; set; } = TradingSession.Unknown;
        public bool Is24HourMarket { get; set; } = true;
        public decimal AsianSessionActivity { get; set; }
        public decimal EuropeanSessionActivity { get; set; }
        public decimal USSessionActivity { get; set; }
    }
}
