using System;
using System.Collections.Generic;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models;

namespace SmartVolumeStrategy.Core.Models.Analysis
{
    /// <summary>
    /// Phase 2: Enhanced signal quality result with multi-factor analysis
    /// </summary>
    public class EnhancedSignalQuality
    {
        /// <summary>
        /// Overall quality level determined by enhanced analysis
        /// </summary>
        public SignalQuality Level { get; set; }
        
        /// <summary>
        /// Weighted quality score (0.0 to 1.0)
        /// </summary>
        public decimal Score { get; set; }
        
        /// <summary>
        /// Individual quality factors that contributed to the score
        /// </summary>
        public QualityFactors Factors { get; set; }
        
        /// <summary>
        /// Quality trend analysis result
        /// </summary>
        public QualityTrend Trend { get; set; }
        
        /// <summary>
        /// Whether market condition adjustments were applied
        /// </summary>
        public bool MarketConditionAdjustment { get; set; }
        
        /// <summary>
        /// Time taken for quality analysis
        /// </summary>
        public TimeSpan AnalysisTime { get; set; }
        
        /// <summary>
        /// Confidence in the quality assessment (0.0 to 1.0)
        /// </summary>
        public decimal Confidence { get; set; }
        
        /// <summary>
        /// Timestamp when quality was analyzed
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Individual quality factors with their weights
    /// </summary>
    public class QualityFactors
    {
        // Factor 1: Signal Confidence
        public decimal SignalConfidence { get; set; }
        public decimal SignalConfidenceWeight { get; set; }
        
        // Factor 2: Market Context Alignment
        public decimal MarketAlignment { get; set; }
        public decimal MarketAlignmentWeight { get; set; }
        
        // Factor 3: Multi-timeframe Consistency
        public decimal TimeframeConsistency { get; set; }
        public decimal TimeframeConsistencyWeight { get; set; }
        
        // Factor 4: Historical Performance
        public decimal HistoricalPerformance { get; set; }
        public decimal HistoricalPerformanceWeight { get; set; }
        
        // Factor 5: Signal Strength
        public decimal SignalStrength { get; set; }
        public decimal SignalStrengthWeight { get; set; }
        
        // Factor 6: Volatility Adjustment
        public decimal VolatilityAdjustment { get; set; }
        public decimal VolatilityAdjustmentWeight { get; set; }
    }

    /// <summary>
    /// Quality trend analysis enumeration
    /// </summary>
    public enum QualityTrend
    {
        Declining,
        Stable,
        Improving
    }

    /// <summary>
    /// Adaptive quality thresholds
    /// </summary>
    public class QualityThresholds
    {
        public decimal Poor { get; set; }
        public decimal Fair { get; set; }
        public decimal Good { get; set; }
        public decimal Excellent { get; set; }
        public decimal Exceptional { get; set; }
        
        /// <summary>
        /// Create a copy of the thresholds
        /// </summary>
        public QualityThresholds Clone()
        {
            return new QualityThresholds
            {
                Poor = this.Poor,
                Fair = this.Fair,
                Good = this.Good,
                Excellent = this.Excellent,
                Exceptional = this.Exceptional
            };
        }
    }

    /// <summary>
    /// Quality history entry for trend analysis
    /// </summary>
    public class QualityHistoryEntry
    {
        public DateTime Timestamp { get; set; }
        public decimal Score { get; set; }
        public SignalQuality Level { get; set; }
        public SignalType SignalType { get; set; }
        public QualityTrend Trend { get; set; }
    }

    /// <summary>
    /// Quality statistics for signal types
    /// </summary>
    public class QualityStatistics
    {
        public int TotalSignals { get; set; }
        public decimal TotalQualityScore { get; set; }
        public decimal AverageQuality { get; set; }
        public DateTime LastUpdate { get; set; }
        
        /// <summary>
        /// Quality distribution by level
        /// </summary>
        public Dictionary<SignalQuality, int> QualityDistribution { get; set; } = new Dictionary<SignalQuality, int>();
        
        /// <summary>
        /// Recent quality trend
        /// </summary>
        public QualityTrend RecentTrend { get; set; }
    }
}
