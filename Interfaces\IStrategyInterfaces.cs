using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Interfaces
{
    /// <summary>
    /// Core volume block detection interface
    /// </summary>
    public interface IVolumeBlockDetector
    {
        /// <summary>
        /// Detect volume blocks in real-time
        /// </summary>
        /// <param name="currentBar">Current market data</param>
        /// <param name="historicalData">Historical data for context</param>
        /// <param name="settings">Strategy settings</param>
        /// <returns>Volume block detection result</returns>
        VolumeBlockResult DetectVolumeBlock(
            MarketDataPoint currentBar,
            List<MarketDataPoint> historicalData,
            OptimalSettings settings);

        /// <summary>
        /// Calculate cumulative impact from volume blocks
        /// </summary>
        /// <param name="recentBars">Recent market data</param>
        /// <param name="settings">Strategy settings</param>
        /// <returns>Cumulative impact value</returns>
        decimal CalculateCumulativeImpact(
            List<MarketDataPoint> recentBars,
            OptimalSettings settings);

        /// <summary>
        /// Update volume statistics for threshold calculations
        /// </summary>
        /// <param name="newBar">New market data bar</param>
        /// <param name="lookbackPeriod">Period for statistics calculation</param>
        void UpdateVolumeStatistics(MarketDataPoint newBar, int lookbackPeriod);

        /// <summary>
        /// Get current volume statistics
        /// </summary>
        /// <returns>Current volume statistics</returns>
        (decimal Average, decimal StdDev, decimal Threshold) GetVolumeStatistics();

        /// <summary>
        /// Reset detector state
        /// </summary>
        void Reset();
    }

    /// <summary>
    /// Signal generation interface
    /// </summary>
    public interface ISignalGenerator
    {
        /// <summary>
        /// Generate trading signal based on current market conditions
        /// </summary>
        /// <param name="currentBar">Current market data</param>
        /// <param name="volumeBlock">Volume block detection result</param>
        /// <param name="marketContext">Additional market context</param>
        /// <param name="settings">Strategy settings</param>
        /// <returns>Trading signal with confidence and reasoning</returns>
        TradingSignal GenerateSignal(
            MarketDataPoint currentBar,
            VolumeBlockResult volumeBlock,
            MarketContext marketContext,
            OptimalSettings settings);

        /// <summary>
        /// Generate exit signal for existing position
        /// </summary>
        /// <param name="currentBar">Current market data</param>
        /// <param name="currentPosition">Current position information</param>
        /// <param name="settings">Strategy settings</param>
        /// <returns>Exit signal if conditions are met</returns>
        TradingSignal GenerateExitSignal(
            MarketDataPoint currentBar,
            PositionInfo currentPosition,
            OptimalSettings settings);

        /// <summary>
        /// Update signal generation parameters
        /// </summary>
        /// <param name="newSettings">Updated strategy settings</param>
        void UpdateSettings(OptimalSettings newSettings);

        /// <summary>
        /// Get signal generation statistics
        /// </summary>
        /// <returns>Signal generation statistics</returns>
        SignalStatistics GetSignalStatistics();

        /// <summary>
        /// Reset signal generator state
        /// </summary>
        void Reset();
    }

    /// <summary>
    /// Position management interface
    /// </summary>
    public interface IPositionManager
    {
        /// <summary>
        /// Check if new position can be opened
        /// </summary>
        /// <param name="signal">Trading signal</param>
        /// <param name="currentMarketData">Current market conditions</param>
        /// <param name="minimumQuality">Minimum required signal quality</param>
        /// <param name="minimumConfidence">Minimum signal confidence percentage</param>
        /// <param name="cooldownSeconds">Cooldown period in seconds</param>
        /// <returns>True if position can be opened</returns>
        bool CanOpenPosition(TradingSignal signal, MarketDataPoint currentMarketData,
            SignalQuality minimumQuality = SignalQuality.Good,
            decimal minimumConfidence = 70m,
            int cooldownSeconds = 30);

        /// <summary>
        /// Synchronize position state with ATAS actual position
        /// </summary>
        /// <param name="actualATASPosition">Current ATAS position size</param>
        void SynchronizeWithATAS(decimal actualATASPosition);

        /// <summary>
        /// Calculate position size based on risk parameters
        /// </summary>
        /// <param name="signal">Trading signal</param>
        /// <param name="settings">Strategy settings</param>
        /// <param name="accountBalance">Current account balance</param>
        /// <returns>Position size in USDT</returns>
        decimal CalculatePositionSize(
            TradingSignal signal,
            OptimalSettings settings,
            decimal accountBalance);

        /// <summary>
        /// Calculate take profit and stop loss levels
        /// </summary>
        /// <param name="entryPrice">Entry price</param>
        /// <param name="direction">Position direction</param>
        /// <param name="settings">Strategy settings</param>
        /// <returns>TP and SL levels</returns>
        (decimal TakeProfit, decimal StopLoss) CalculateTPSL(
            decimal entryPrice,
            SignalType direction,
            OptimalSettings settings);

        /// <summary>
        /// Open a new position
        /// </summary>
        /// <param name="signal">Trading signal</param>
        /// <param name="entryPrice">Entry price</param>
        /// <param name="positionSize">Position size in USDT</param>
        /// <param name="settings">Strategy settings</param>
        void OpenPosition(TradingSignal signal, decimal entryPrice, decimal positionSize, OptimalSettings settings);

        /// <summary>
        /// Update position information
        /// </summary>
        /// <param name="positionInfo">Current position information</param>
        void UpdatePosition(PositionInfo positionInfo);

        /// <summary>
        /// Get current position status
        /// </summary>
        /// <returns>Current position information</returns>
        PositionInfo GetCurrentPosition();

        /// <summary>
        /// Check if position should be closed based on current conditions
        /// </summary>
        /// <param name="currentBar">Current market data</param>
        /// <param name="settings">Strategy settings</param>
        /// <returns>True if position should be closed</returns>
        bool ShouldClosePosition(MarketDataPoint currentBar, OptimalSettings settings);

        /// <summary>
        /// Reset position manager state
        /// </summary>
        void Reset();
    }

    /// <summary>
    /// Performance tracking interface
    /// </summary>
    public interface IPerformanceTracker
    {
        /// <summary>
        /// Record trade result
        /// </summary>
        /// <param name="trade">Completed trade result</param>
        void RecordTrade(TradeResult trade);

        /// <summary>
        /// Record signal generation
        /// </summary>
        /// <param name="signal">Generated signal</param>
        /// <param name="wasExecuted">Whether signal was executed</param>
        void RecordSignal(TradingSignal signal, bool wasExecuted);

        /// <summary>
        /// Get current performance metrics
        /// </summary>
        /// <returns>Current performance metrics</returns>
        PerformanceMetrics GetPerformanceMetrics();

        /// <summary>
        /// Get performance for specific time period
        /// </summary>
        /// <param name="startTime">Start time</param>
        /// <param name="endTime">End time</param>
        /// <returns>Performance metrics for period</returns>
        PerformanceMetrics GetPerformanceForPeriod(DateTime startTime, DateTime endTime);

        /// <summary>
        /// Check if performance is degrading
        /// </summary>
        /// <param name="lookbackTrades">Number of recent trades to analyze</param>
        /// <returns>True if performance is degrading</returns>
        bool IsPerformanceDegrading(int lookbackTrades = 10);

        /// <summary>
        /// Get performance alerts
        /// </summary>
        /// <returns>List of performance alerts</returns>
        List<PerformanceAlert> GetPerformanceAlerts();

        /// <summary>
        /// Reset performance tracking
        /// </summary>
        void Reset();
    }

    /// <summary>
    /// Adaptive adjustment interface
    /// </summary>
    public interface IAdaptiveAdjuster
    {
        /// <summary>
        /// Check if settings need adjustment based on performance
        /// </summary>
        /// <param name="currentSettings">Current strategy settings</param>
        /// <param name="performance">Recent performance metrics</param>
        /// <param name="marketData">Recent market data</param>
        /// <returns>True if adjustment is needed</returns>
        bool NeedsAdjustment(
            OptimalSettings currentSettings,
            PerformanceMetrics performance,
            List<MarketDataPoint> marketData);

        /// <summary>
        /// Suggest settings adjustments
        /// </summary>
        /// <param name="currentSettings">Current strategy settings</param>
        /// <param name="performance">Recent performance metrics</param>
        /// <param name="marketData">Recent market data</param>
        /// <returns>Suggested adjustments</returns>
        Task<List<SettingsAdjustment>> SuggestAdjustmentsAsync(
            OptimalSettings currentSettings,
            PerformanceMetrics performance,
            List<MarketDataPoint> marketData);

        /// <summary>
        /// Apply automatic adjustments
        /// </summary>
        /// <param name="currentSettings">Current strategy settings</param>
        /// <param name="adjustments">Adjustments to apply</param>
        /// <returns>Updated settings</returns>
        OptimalSettings ApplyAdjustments(
            OptimalSettings currentSettings,
            List<SettingsAdjustment> adjustments);

        /// <summary>
        /// Get adjustment history
        /// </summary>
        /// <returns>History of adjustments made</returns>
        List<SettingsAdjustment> GetAdjustmentHistory();

        /// <summary>
        /// Set adjustment parameters
        /// </summary>
        /// <param name="parameters">Adjustment parameters</param>
        void SetAdjustmentParameters(AdaptiveParameters parameters);
    }

}
