using System;
using System.Collections.Generic;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Strategy
{
    /// <summary>
    /// Risk management and position tracking using calibrated parameters with single position limit
    /// </summary>
    public class PositionManager : IPositionManager
    {
        private PositionInfo _currentPosition;
        private readonly List<TradeResult> _tradeHistory;
        private readonly object _lockObject = new object();
        private DateTime _lastTradeTime;
        private const int COOLDOWN_SECONDS = 30; // Minimum time between trades

        public PositionManager()
        {
            _currentPosition = new PositionInfo
            {
                HasPosition = false,
                State = TradingState.Ready
            };
            _tradeHistory = new List<TradeResult>();
            _lastTradeTime = DateTime.MinValue;
        }

        /// <summary>
        /// Check if new position can be opened using intelligent validation
        /// </summary>
        public bool CanOpenPosition(TradingSignal signal, MarketDataPoint currentMarketData,
            SignalQuality minimumQuality = SignalQuality.Good,
            decimal minimumConfidence = 70m,
            int cooldownSeconds = 30)
        {
            if (signal == null)
                throw new ArgumentNullException(nameof(signal));

            if (currentMarketData == null)
                throw new ArgumentNullException(nameof(currentMarketData));

            lock (_lockObject)
            {
                var timeSinceLastTrade = DateTime.UtcNow - _lastTradeTime;

                // CRITICAL DEBUG: Check each constraint and return specific failure reason

                // Check 1: Position limit
                if (_currentPosition.HasPosition)
                {
                    return false; // CONSTRAINT 1 FAILED: Already has position
                }

                // Check 2: Strategy state
                if (_currentPosition.State != TradingState.Ready)
                {
                    return false; // CONSTRAINT 2 FAILED: Strategy not in Ready state
                }

                // Check 3: Signal quality
                if (signal.Quality < minimumQuality)
                {
                    return false; // CONSTRAINT 3 FAILED: Signal quality too low
                }

                // Check 4: Signal confidence
                var requiredConfidenceDecimal = minimumConfidence / 100m;
                if (signal.Confidence < requiredConfidenceDecimal)
                {
                    return false; // CONSTRAINT 4 FAILED: Signal confidence too low
                }

                // Check 5: Cooldown period
                if (timeSinceLastTrade.TotalSeconds < cooldownSeconds)
                {
                    return false; // CONSTRAINT 5 FAILED: Cooldown period active
                }

                // Check 6: Signal action
                if (signal.Action != SignalAction.Entry)
                {
                    return false; // CONSTRAINT 6 FAILED: Signal action is not Entry
                }

                // Check 7: Signal type validity
                if (signal.Type == SignalType.None || signal.Type == SignalType.Exit)
                {
                    return false; // CONSTRAINT 7 FAILED: Invalid signal type for entry
                }

                return true; // ALL CONSTRAINTS PASSED
            }
        }

        /// <summary>
        /// Calculate position size based on calibrated risk parameters and volatility adjustment
        /// </summary>
        public decimal CalculatePositionSize(
            TradingSignal signal,
            OptimalSettings settings,
            decimal accountBalance)
        {
            if (signal == null)
                throw new ArgumentNullException(nameof(signal));
            
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));

            // Start with base position size from calibrated settings
            var basePositionSize = settings.PositionSizeUSDT;

            // Apply risk adjustment factor (volatility-based)
            var adjustedPositionSize = basePositionSize * settings.RiskAdjustmentFactor;

            // Apply signal confidence adjustment (higher confidence = larger position)
            var confidenceAdjustment = 0.7m + (signal.Confidence * 0.3m); // Range: 0.7 to 1.0
            adjustedPositionSize *= confidenceAdjustment;

            // Apply account balance constraint (max 10% of account per trade)
            var maxPositionByAccount = accountBalance * 0.10m;
            adjustedPositionSize = Math.Min(adjustedPositionSize, maxPositionByAccount);

            // Apply minimum position size (at least $100)
            adjustedPositionSize = Math.Max(adjustedPositionSize, 100m);

            // Apply maximum position size safety limit (max $5000 per trade)
            adjustedPositionSize = Math.Min(adjustedPositionSize, 5000m);

            return Math.Round(adjustedPositionSize, 2);
        }

        /// <summary>
        /// Calculate take profit and stop loss levels using calibrated percentages
        /// </summary>
        public (decimal TakeProfit, decimal StopLoss) CalculateTPSL(
            decimal entryPrice,
            SignalType direction,
            OptimalSettings settings)
        {
            if (entryPrice <= 0)
                throw new ArgumentException("Entry price must be positive", nameof(entryPrice));
            
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));

            decimal takeProfit, stopLoss;

            if (direction == SignalType.Long)
            {
                // Long position: TP above entry, SL below entry
                takeProfit = entryPrice * (1 + settings.TakeProfitPercent / 100m);
                stopLoss = entryPrice * (1 - settings.StopLossPercent / 100m);
            }
            else if (direction == SignalType.Short)
            {
                // Short position: TP below entry, SL above entry
                takeProfit = entryPrice * (1 - settings.TakeProfitPercent / 100m);
                stopLoss = entryPrice * (1 + settings.StopLossPercent / 100m);
            }
            else
            {
                throw new ArgumentException("Invalid signal direction for TP/SL calculation", nameof(direction));
            }

            return (Math.Round(takeProfit, 6), Math.Round(stopLoss, 6));
        }

        /// <summary>
        /// Synchronize position state with ATAS actual position
        /// </summary>
        public void SynchronizeWithATAS(decimal actualATASPosition)
        {
            lock (_lockObject)
            {
                bool hasATASPosition = Math.Abs(actualATASPosition) > 0.000001m;

                // If we think we have a position but ATAS shows no position, sync the state
                if (_currentPosition.HasPosition && !hasATASPosition)
                {
                    // Position was closed (by TP/SL or manual close)
                    _currentPosition.HasPosition = false;
                    _currentPosition.State = TradingState.Ready;
                    _lastTradeTime = DateTime.UtcNow;
                }
                // If we think we have no position but ATAS shows a position, sync the state
                else if (!_currentPosition.HasPosition && hasATASPosition)
                {
                    // Position was opened externally or state got out of sync
                    _currentPosition.HasPosition = true;
                    _currentPosition.State = actualATASPosition > 0 ? TradingState.InLongPosition : TradingState.InShortPosition;
                }
            }
        }

        /// <summary>
        /// Update position information with current market data
        /// </summary>
        public void UpdatePosition(PositionInfo positionInfo)
        {
            if (positionInfo == null)
                throw new ArgumentNullException(nameof(positionInfo));

            lock (_lockObject)
            {
                _currentPosition = new PositionInfo
                {
                    HasPosition = positionInfo.HasPosition,
                    Direction = positionInfo.Direction,
                    EntryPrice = positionInfo.EntryPrice,
                    Quantity = positionInfo.Quantity,
                    TakeProfit = positionInfo.TakeProfit,
                    StopLoss = positionInfo.StopLoss,
                    EntryTime = positionInfo.EntryTime,
                    UnrealizedPnL = positionInfo.UnrealizedPnL,
                    UnrealizedPnLPercent = positionInfo.UnrealizedPnLPercent,
                    CorrelationId = positionInfo.CorrelationId,
                    State = positionInfo.State
                };

                // If position was closed, record the trade and update state
                if (!positionInfo.HasPosition && _currentPosition.HasPosition)
                {
                    RecordCompletedTrade(positionInfo);
                    _currentPosition.State = TradingState.Cooldown;
                    _lastTradeTime = DateTime.UtcNow;
                    
                    // Reset to ready state after cooldown
                    Task.Delay(TimeSpan.FromSeconds(COOLDOWN_SECONDS)).ContinueWith(_ =>
                    {
                        lock (_lockObject)
                        {
                            if (_currentPosition.State == TradingState.Cooldown)
                            {
                                _currentPosition.State = TradingState.Ready;
                            }
                        }
                    });
                }
            }
        }

        /// <summary>
        /// Get current position status
        /// </summary>
        public PositionInfo GetCurrentPosition()
        {
            lock (_lockObject)
            {
                return new PositionInfo
                {
                    HasPosition = _currentPosition.HasPosition,
                    Direction = _currentPosition.Direction,
                    EntryPrice = _currentPosition.EntryPrice,
                    Quantity = _currentPosition.Quantity,
                    TakeProfit = _currentPosition.TakeProfit,
                    StopLoss = _currentPosition.StopLoss,
                    EntryTime = _currentPosition.EntryTime,
                    UnrealizedPnL = _currentPosition.UnrealizedPnL,
                    UnrealizedPnLPercent = _currentPosition.UnrealizedPnLPercent,
                    CorrelationId = _currentPosition.CorrelationId,
                    State = _currentPosition.State
                };
            }
        }

        /// <summary>
        /// Check if position should be closed based on current conditions and risk management
        /// </summary>
        public bool ShouldClosePosition(MarketDataPoint currentBar, OptimalSettings settings)
        {
            if (currentBar == null)
                throw new ArgumentNullException(nameof(currentBar));
            
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));

            lock (_lockObject)
            {
                if (!_currentPosition.HasPosition)
                    return false;

                var currentPrice = currentBar.Close;

                // Check TP/SL levels
                if (_currentPosition.Direction == SignalType.Long)
                {
                    if (currentPrice >= _currentPosition.TakeProfit || currentPrice <= _currentPosition.StopLoss)
                        return true;
                }
                else if (_currentPosition.Direction == SignalType.Short)
                {
                    if (currentPrice <= _currentPosition.TakeProfit || currentPrice >= _currentPosition.StopLoss)
                        return true;
                }

                // Check maximum hold time (24 hours)
                var holdTime = DateTime.UtcNow - _currentPosition.EntryTime;
                if (holdTime.TotalHours > 24)
                    return true;

                // Check for emergency exit conditions
                if (IsEmergencyExitRequired(currentBar, settings))
                    return true;

                return false;
            }
        }

        /// <summary>
        /// Reset position manager state
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                _currentPosition = new PositionInfo
                {
                    HasPosition = false,
                    State = TradingState.Ready
                };
                _tradeHistory.Clear();
                _lastTradeTime = DateTime.MinValue;
            }
        }

        /// <summary>
        /// Get trade history for performance analysis
        /// </summary>
        public List<TradeResult> GetTradeHistory()
        {
            lock (_lockObject)
            {
                return new List<TradeResult>(_tradeHistory);
            }
        }

        /// <summary>
        /// Open new position with specified parameters
        /// </summary>
        public void OpenPosition(TradingSignal signal, decimal entryPrice, decimal positionSize, OptimalSettings settings)
        {
            if (signal == null)
                throw new ArgumentNullException(nameof(signal));
            
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));

            lock (_lockObject)
            {
                // Restore proper position check - only allow one position at a time
                if (_currentPosition.HasPosition)
                    throw new InvalidOperationException("Cannot open position - position already exists");

                var (takeProfit, stopLoss) = CalculateTPSL(entryPrice, signal.Type, settings);

                _currentPosition = new PositionInfo
                {
                    HasPosition = true,
                    Direction = signal.Type,
                    EntryPrice = entryPrice,
                    Quantity = positionSize,
                    TakeProfit = takeProfit,
                    StopLoss = stopLoss,
                    EntryTime = DateTime.UtcNow,
                    UnrealizedPnL = 0,
                    UnrealizedPnLPercent = 0,
                    CorrelationId = signal.CorrelationId,
                    State = signal.Type == SignalType.Long ? TradingState.InLongPosition : TradingState.InShortPosition
                };

                _lastTradeTime = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Update unrealized PnL for current position
        /// </summary>
        public void UpdateUnrealizedPnL(decimal currentPrice)
        {
            lock (_lockObject)
            {
                if (!_currentPosition.HasPosition)
                    return;

                decimal pnl = 0;
                decimal pnlPercent = 0;

                if (_currentPosition.Direction == SignalType.Long)
                {
                    pnl = (currentPrice - _currentPosition.EntryPrice) * _currentPosition.Quantity;
                    pnlPercent = _currentPosition.EntryPrice > 0 ? (currentPrice - _currentPosition.EntryPrice) / _currentPosition.EntryPrice * 100 : 0;
                }
                else if (_currentPosition.Direction == SignalType.Short)
                {
                    pnl = (_currentPosition.EntryPrice - currentPrice) * _currentPosition.Quantity;
                    pnlPercent = _currentPosition.EntryPrice > 0 ? (_currentPosition.EntryPrice - currentPrice) / _currentPosition.EntryPrice * 100 : 0;
                }

                _currentPosition.UnrealizedPnL = Math.Round(pnl, 2);
                _currentPosition.UnrealizedPnLPercent = Math.Round(pnlPercent, 4);
            }
        }

        #region Private Methods

        private void RecordCompletedTrade(PositionInfo closedPosition)
        {
            var trade = new TradeResult
            {
                EntryTime = closedPosition.EntryTime,
                ExitTime = DateTime.UtcNow,
                EntryPrice = closedPosition.EntryPrice,
                ExitPrice = 0, // This would be set by the calling code
                Quantity = closedPosition.Quantity,
                Direction = closedPosition.Direction,
                PnL = closedPosition.UnrealizedPnL,
                PnLPercent = closedPosition.UnrealizedPnLPercent,
                ExitReason = "Unknown", // This would be set by the calling code
                SignalConfidence = 0, // This would be set by the calling code
                SignalStrength = 0, // This would be set by the calling code
                HoldTime = DateTime.UtcNow - closedPosition.EntryTime,
                CorrelationId = closedPosition.CorrelationId
            };

            _tradeHistory.Add(trade);

            // Keep only last 100 trades
            while (_tradeHistory.Count > 100)
            {
                _tradeHistory.RemoveAt(0);
            }
        }

        private bool IsEmergencyExitRequired(MarketDataPoint currentBar, OptimalSettings settings)
        {
            // Check for extreme volatility conditions
            var priceChangePercent = _currentPosition.EntryPrice > 0 
                ? Math.Abs(currentBar.Close - _currentPosition.EntryPrice) / _currentPosition.EntryPrice 
                : 0;

            // Emergency exit if price moves more than 5% against us (beyond normal SL)
            if (priceChangePercent > 0.05m)
            {
                if (_currentPosition.Direction == SignalType.Long && currentBar.Close < _currentPosition.EntryPrice * 0.95m)
                    return true;
                
                if (_currentPosition.Direction == SignalType.Short && currentBar.Close > _currentPosition.EntryPrice * 1.05m)
                    return true;
            }

            return false;
        }

        #endregion
    }
}
