{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"SmartVolumeStrategy/1.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "System.Drawing.Common": "9.0.5", "ATAS.DataFeedsCore": "7.0.7.331", "ATAS.Indicators": "7.0.7.331", "ATAS.Indicators.Technical": "7.0.7.331", "ATAS.Strategies": "7.0.7.331", "OFT.Attributes": "*******", "OFT.Rendering": "7.0.7.331", "OFT.Rendering.OpenGL": "7.0.7.331", "OFT.Rendering.Vortice": "7.0.7.331", "OFT.Rendering.Wpf": "7.0.7.331", "PresentationCore": "*******", "Utils.Common": "*******", "WindowsBase": "*******"}, "runtime": {"SmartVolumeStrategy.dll": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Win32.SystemEvents/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.525.21509"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "9.0.525.21509"}}}, "System.Drawing.Common/9.0.5": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.5"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.525.21601"}, "lib/net8.0/System.Private.Windows.Core.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.525.21601"}}}, "ATAS.DataFeedsCore/7.0.7.331": {"runtime": {"ATAS.DataFeedsCore.dll": {"assemblyVersion": "7.0.7.331", "fileVersion": "7.0.7.331"}}}, "ATAS.Indicators/7.0.7.331": {"runtime": {"ATAS.Indicators.dll": {"assemblyVersion": "7.0.7.331", "fileVersion": "7.0.7.331"}}}, "ATAS.Indicators.Technical/7.0.7.331": {"runtime": {"ATAS.Indicators.Technical.dll": {"assemblyVersion": "7.0.7.331", "fileVersion": "7.0.7.331"}}, "resources": {"de-de/ATAS.Indicators.Technical.resources.dll": {"locale": "de-de"}, "ru-ru/ATAS.Indicators.Technical.resources.dll": {"locale": "ru-ru"}}}, "ATAS.Strategies/7.0.7.331": {"runtime": {"ATAS.Strategies.dll": {"assemblyVersion": "7.0.7.331", "fileVersion": "7.0.7.331"}}}, "OFT.Attributes/*******": {"runtime": {"OFT.Attributes.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OFT.Rendering/7.0.7.331": {"runtime": {"OFT.Rendering.dll": {"assemblyVersion": "7.0.7.331", "fileVersion": "7.0.7.331"}}}, "OFT.Rendering.OpenGL/7.0.7.331": {"runtime": {"OFT.Rendering.OpenGL.dll": {"assemblyVersion": "7.0.7.331", "fileVersion": "7.0.7.331"}}}, "OFT.Rendering.Vortice/7.0.7.331": {"runtime": {"OFT.Rendering.Vortice.dll": {"assemblyVersion": "7.0.7.331", "fileVersion": "7.0.7.331"}}}, "OFT.Rendering.Wpf/7.0.7.331": {"runtime": {"OFT.Rendering.Wpf.dll": {"assemblyVersion": "7.0.7.331", "fileVersion": "7.0.7.331"}}}, "PresentationCore/*******": {"runtime": {"PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1425.11302"}}}, "Utils.Common/*******": {"runtime": {"Utils.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "WindowsBase/*******": {"runtime": {"WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1425.11302"}}}, "Newtonsoft.Json/********": {"runtime": {"Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "OFT.Localization/*******": {"runtime": {"OFT.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "resources": {"de-de/OFT.Localization.resources.dll": {"locale": "de-de"}, "es-es/OFT.Localization.resources.dll": {"locale": "es-es"}, "fr-fr/OFT.Localization.resources.dll": {"locale": "fr-fr"}, "hi-in/OFT.Localization.resources.dll": {"locale": "hi-in"}, "ru-ru/OFT.Localization.resources.dll": {"locale": "ru-ru"}, "zh-cn/OFT.Localization.resources.dll": {"locale": "zh-cn"}}}, "System.Xaml/*******": {"runtime": {"System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1425.11302"}}}, "CommandLine/*******": {"runtime": {"CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "websocket-sharp/*******": {"runtime": {"websocket-sharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.IO.Pipelines/*******": {"runtime": {"System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.12001"}}}, "Pipelines.Sockets.Unofficial/*******": {"runtime": {"Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.11.56636"}}}, "log4net/********": {"runtime": {"log4net.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "protobuf-net/*******": {"runtime": {"protobuf-net.dll": {"assemblyVersion": "*******", "fileVersion": "3.2.45.36865"}}}, "Macross.Json.Extensions/3.0.0.22164": {"runtime": {"Macross.Json.Extensions.dll": {"assemblyVersion": "3.0.0.22164", "fileVersion": "*******"}}}, "System.Security.Cryptography.ProtectedData/*******": {"runtime": {"System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Windows.Input.Manipulations/*******": {"runtime": {"System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1425.11302"}}}, "UIAutomationTypes/*******": {"runtime": {"UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1425.11302"}}}, "System.IO.Packaging/*******": {"runtime": {"System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1425.11118"}}}, "System.Security.Cryptography.Xml/*******": {"runtime": {"System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1425.11118"}}}, "System.Security.Permissions/*******": {"runtime": {"System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1425.11118"}}}, "SkiaSharp/*********": {"runtime": {"SkiaSharp.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "OpenTK.Graphics/*******": {"runtime": {"OpenTK.Graphics.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OpenTK.Mathematics/*******": {"runtime": {"OpenTK.Mathematics.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OpenTK.Windowing.Common/*******": {"runtime": {"OpenTK.Windowing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OpenTK.Windowing.Desktop/*******": {"runtime": {"OpenTK.Windowing.Desktop.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.HighPerformance/*******": {"runtime": {"CommunityToolkit.HighPerformance.dll": {"assemblyVersion": "*******", "fileVersion": "8.2.2.1"}}}, "OpenTK.Windowing.GraphicsLibraryFramework/*******": {"runtime": {"OpenTK.Windowing.GraphicsLibraryFramework.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StbImageSharp/2.27.13.0": {"runtime": {"StbImageSharp.dll": {"assemblyVersion": "2.27.13.0", "fileVersion": "2.27.13.0"}}}, "Vortice.Direct2D1/*******": {"runtime": {"Vortice.Direct2D1.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Vortice.DXGI/*******": {"runtime": {"Vortice.DXGI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Vortice.Direct3D11/*******": {"runtime": {"Vortice.Direct3D11.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Vortice.DirectX/*******": {"runtime": {"Vortice.DirectX.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpGen.Runtime/*******": {"runtime": {"SharpGen.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Vortice.Mathematics/*******": {"runtime": {"Vortice.Mathematics.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OpenTK.WinForms/*******": {"runtime": {"OpenTK.WinForms.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Rendering.GDIPlus/*******": {"runtime": {"Rendering.GDIPlus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Windows.Extensions/*******": {"runtime": {"System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1425.11118"}}}, "System.Configuration.ConfigurationManager/*******": {"runtime": {"System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "protobuf-net.Core/*******": {"runtime": {"protobuf-net.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.2.45.36865"}}}, "OpenTK.Core/*******": {"runtime": {"OpenTK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpGen.Runtime.COM/*******": {"runtime": {"SharpGen.Runtime.COM.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"SmartVolumeStrategy/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-D4OYNpmvAsF9MkaY2W8Jue2XuNHDhygvwzo019hs+lP85KaVnOlXmqsjDKr1dHb1DPxDnOKpe6mAgJN7S6ttwg==", "path": "microsoft.win32.systemevents/9.0.5", "hashPath": "microsoft.win32.systemevents.9.0.5.nupkg.sha512"}, "System.Drawing.Common/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-T/6nqx0B7/uTe5JjBwrKZilLuwfhHLOVmNKlT/wr4A9Dna94mgTdz3lTfrdJ72QRx7IHCv/LzoJPmFSfK/N6WA==", "path": "system.drawing.common/9.0.5", "hashPath": "system.drawing.common.9.0.5.nupkg.sha512"}, "ATAS.DataFeedsCore/7.0.7.331": {"type": "reference", "serviceable": false, "sha512": ""}, "ATAS.Indicators/7.0.7.331": {"type": "reference", "serviceable": false, "sha512": ""}, "ATAS.Indicators.Technical/7.0.7.331": {"type": "reference", "serviceable": false, "sha512": ""}, "ATAS.Strategies/7.0.7.331": {"type": "reference", "serviceable": false, "sha512": ""}, "OFT.Attributes/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "OFT.Rendering/7.0.7.331": {"type": "reference", "serviceable": false, "sha512": ""}, "OFT.Rendering.OpenGL/7.0.7.331": {"type": "reference", "serviceable": false, "sha512": ""}, "OFT.Rendering.Vortice/7.0.7.331": {"type": "reference", "serviceable": false, "sha512": ""}, "OFT.Rendering.Wpf/7.0.7.331": {"type": "reference", "serviceable": false, "sha512": ""}, "PresentationCore/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Utils.Common/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Newtonsoft.Json/********": {"type": "reference", "serviceable": false, "sha512": ""}, "OFT.Localization/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Xaml/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "CommandLine/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "websocket-sharp/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.IO.Pipelines/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Pipelines.Sockets.Unofficial/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "log4net/********": {"type": "reference", "serviceable": false, "sha512": ""}, "protobuf-net/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Macross.Json.Extensions/3.0.0.22164": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.ProtectedData/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Windows.Input.Manipulations/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "UIAutomationTypes/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.IO.Packaging/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Security.Permissions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SkiaSharp/*********": {"type": "reference", "serviceable": false, "sha512": ""}, "OpenTK.Graphics/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "OpenTK.Mathematics/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "OpenTK.Windowing.Common/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "OpenTK.Windowing.Desktop/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "CommunityToolkit.HighPerformance/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "OpenTK.Windowing.GraphicsLibraryFramework/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "StbImageSharp/2.27.13.0": {"type": "reference", "serviceable": false, "sha512": ""}, "Vortice.Direct2D1/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Vortice.DXGI/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Vortice.Direct3D11/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Vortice.DirectX/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SharpGen.Runtime/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Vortice.Mathematics/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "OpenTK.WinForms/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Rendering.GDIPlus/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Windows.Extensions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Configuration.ConfigurationManager/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "protobuf-net.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "OpenTK.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SharpGen.Runtime.COM/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}