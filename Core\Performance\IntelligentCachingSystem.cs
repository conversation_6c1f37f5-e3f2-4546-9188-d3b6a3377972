using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections.Concurrent;
using SmartVolumeStrategy.Core.Models.Performance;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;

namespace SmartVolumeStrategy.Core.Performance
{
    /// <summary>
    /// Phase 3.3: Intelligent Caching System
    /// Advanced multi-level caching with intelligent invalidation strategies
    /// </summary>
    public class IntelligentCachingSystem
    {
        private readonly Action<string> _logAction;
        private readonly IntelligentCachingConfig _config;
        private readonly object _lockObject = new object();
        
        // Multi-level cache storage
        private readonly ConcurrentDictionary<string, CacheEntry<object>> _l1Cache; // Recent data
        private readonly ConcurrentDictionary<string, CacheEntry<object>> _l2Cache; // Historical patterns
        private readonly ConcurrentDictionary<string, CacheEntry<object>> _l3Cache; // Statistical data
        
        // Cache statistics
        private int _totalHits;
        private int _totalMisses;
        private long _totalMemoryUsage;
        private DateTime _lastCleanup;
        private readonly Queue<PerformanceMetrics> _cachePerformanceHistory;
        
        // Circuit breaker integration
        private CircuitBreakerManager _circuitBreakerManager;
        
        // Configuration
        private const int MAX_PERFORMANCE_HISTORY = 100;
        private static readonly TimeSpan CLEANUP_INTERVAL = TimeSpan.FromMinutes(2);
        private const long BYTES_PER_MB = 1024 * 1024;

        public IntelligentCachingSystem(IntelligentCachingConfig config = null, Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _config = config ?? new IntelligentCachingConfig();
            
            _l1Cache = new ConcurrentDictionary<string, CacheEntry<object>>();
            _l2Cache = new ConcurrentDictionary<string, CacheEntry<object>>();
            _l3Cache = new ConcurrentDictionary<string, CacheEntry<object>>();
            
            _cachePerformanceHistory = new Queue<PerformanceMetrics>();
            _lastCleanup = DateTime.UtcNow;
        }

        /// <summary>
        /// Set circuit breaker manager for fault tolerance
        /// </summary>
        public void SetCircuitBreakerManager(CircuitBreakerManager circuitBreakerManager)
        {
            _circuitBreakerManager = circuitBreakerManager;
        }

        /// <summary>
        /// Get cached value with intelligent cache level selection
        /// </summary>
        public T Get<T>(string key, string dataType = null) where T : class
        {
            if (!_config.EnableCaching)
            {
                return null;
            }

            var startTime = DateTime.UtcNow;
            
            try
            {
                // Phase 3.3: Check circuit breaker status
                if (_circuitBreakerManager != null && !_circuitBreakerManager.IsOperationAllowed(StrategyComponent.MultiTimeframeAnalysis))
                {
                    return null;
                }

                // Check degradation level
                if (_circuitBreakerManager != null)
                {
                    var degradationLevel = _circuitBreakerManager.GetDegradationManager().GetCurrentDegradationLevel();
                    if (degradationLevel >= DegradationLevel.Minimal)
                    {
                        // Only use L1 cache in degraded mode
                        return GetFromCache<T>(_l1Cache, key);
                    }
                }

                // Try L1 cache first (recent data)
                var result = GetFromCache<T>(_l1Cache, key);
                if (result != null)
                {
                    RecordCacheHit(1, DateTime.UtcNow - startTime);
                    return result;
                }

                // Try L2 cache (historical patterns)
                if (_config.EnableMultiLevelCaching)
                {
                    result = GetFromCache<T>(_l2Cache, key);
                    if (result != null)
                    {
                        // Promote to L1 cache
                        PromoteToL1Cache(key, result, dataType);
                        RecordCacheHit(2, DateTime.UtcNow - startTime);
                        return result;
                    }

                    // Try L3 cache (statistical data)
                    result = GetFromCache<T>(_l3Cache, key);
                    if (result != null)
                    {
                        // Promote to L2 cache
                        PromoteToL2Cache(key, result, dataType);
                        RecordCacheHit(3, DateTime.UtcNow - startTime);
                        return result;
                    }
                }

                // Cache miss
                RecordCacheMiss(DateTime.UtcNow - startTime);
                return null;
            }
            catch (Exception ex)
            {
                _logAction($"❌ Cache get error for key {key}: {ex.Message}");
                
                // Record failure with circuit breaker
                if (_circuitBreakerManager != null)
                {
                    _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, false, ex.Message, FailureSeverity.Low);
                }
                
                return null;
            }
        }

        /// <summary>
        /// Set cached value with intelligent cache level selection
        /// </summary>
        public void Set<T>(string key, T value, string dataType = null, CacheInvalidationStrategy? strategy = null) where T : class
        {
            if (!_config.EnableCaching || value == null)
            {
                return;
            }

            var startTime = DateTime.UtcNow;
            
            try
            {
                // Phase 3.3: Check circuit breaker status
                if (_circuitBreakerManager != null && !_circuitBreakerManager.IsOperationAllowed(StrategyComponent.MultiTimeframeAnalysis))
                {
                    return;
                }

                // Determine cache level and TTL based on data type
                var cacheLevel = DetermineCacheLevel(dataType);
                var ttl = DetermineTTL(dataType, cacheLevel);
                var invalidationStrategy = strategy ?? _config.DefaultInvalidationStrategy;

                // Create cache entry
                var entry = new CacheEntry<object>
                {
                    Key = key,
                    Value = value,
                    CreatedAt = DateTime.UtcNow,
                    LastAccessed = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.Add(ttl),
                    AccessCount = 0,
                    SizeBytes = EstimateObjectSize(value),
                    InvalidationStrategy = invalidationStrategy
                };

                if (dataType != null)
                {
                    entry.Metadata["DataType"] = dataType;
                }

                // Add to appropriate cache level
                SetInCache(cacheLevel, key, entry);

                // Update memory usage
                lock (_lockObject)
                {
                    _totalMemoryUsage += entry.SizeBytes;
                }

                // Trigger cleanup if needed
                if (ShouldTriggerCleanup())
                {
                    TriggerCleanup();
                }

                // Record successful operation
                if (_circuitBreakerManager != null)
                {
                    var responseTime = DateTime.UtcNow - startTime;
                    _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, true, $"Cache set for {key}");
                }
            }
            catch (Exception ex)
            {
                _logAction($"❌ Cache set error for key {key}: {ex.Message}");
                
                // Record failure with circuit breaker
                if (_circuitBreakerManager != null)
                {
                    _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, false, ex.Message, FailureSeverity.Low);
                }
            }
        }

        /// <summary>
        /// Invalidate cache entries based on strategy
        /// </summary>
        public void Invalidate(string keyPattern = null, string dataType = null, CacheInvalidationStrategy? strategy = null)
        {
            try
            {
                var invalidationStrategy = strategy ?? _config.DefaultInvalidationStrategy;
                var invalidatedCount = 0;

                switch (invalidationStrategy)
                {
                    case CacheInvalidationStrategy.TimeBasedTTL:
                        invalidatedCount = InvalidateExpiredEntries();
                        break;
                        
                    case CacheInvalidationStrategy.EventBased:
                        invalidatedCount = InvalidateByPattern(keyPattern);
                        break;
                        
                    case CacheInvalidationStrategy.PerformanceBased:
                        invalidatedCount = InvalidateByPerformance();
                        break;
                        
                    case CacheInvalidationStrategy.MemoryPressure:
                        invalidatedCount = InvalidateByMemoryPressure();
                        break;
                        
                    case CacheInvalidationStrategy.Hybrid:
                        invalidatedCount = InvalidateHybrid(keyPattern, dataType);
                        break;
                }

                if (invalidatedCount > 0)
                {
                    _logAction($"🧹 Cache invalidation completed - Removed {invalidatedCount} entries using {invalidationStrategy}");
                }
            }
            catch (Exception ex)
            {
                _logAction($"❌ Cache invalidation error: {ex.Message}");
            }
        }

        /// <summary>
        /// Get cache statistics
        /// </summary>
        public CacheStatistics GetCacheStatistics()
        {
            lock (_lockObject)
            {
                var stats = new CacheStatistics
                {
                    L1CacheSize = _l1Cache.Count,
                    L2CacheSize = _l2Cache.Count,
                    L3CacheSize = _l3Cache.Count,
                    TotalCacheSize = _l1Cache.Count + _l2Cache.Count + _l3Cache.Count,
                    TotalHits = _totalHits,
                    TotalMisses = _totalMisses,
                    HitRatio = _totalHits + _totalMisses > 0 ? (decimal)_totalHits / (_totalHits + _totalMisses) : 0m,
                    TotalMemoryUsageMB = _totalMemoryUsage / (decimal)BYTES_PER_MB,
                    LastCleanup = _lastCleanup
                };

                if (_cachePerformanceHistory.Count > 0)
                {
                    stats.AverageResponseTime = TimeSpan.FromMilliseconds(_cachePerformanceHistory.Average(p => p.ResponseTime.TotalMilliseconds));
                }

                return stats;
            }
        }

        /// <summary>
        /// Clear all caches
        /// </summary>
        public void ClearAll()
        {
            lock (_lockObject)
            {
                _l1Cache.Clear();
                _l2Cache.Clear();
                _l3Cache.Clear();
                _totalMemoryUsage = 0;
                _totalHits = 0;
                _totalMisses = 0;
                
                _logAction("🧹 All caches cleared");
            }
        }

        /// <summary>
        /// Get value from specific cache level
        /// </summary>
        private T GetFromCache<T>(ConcurrentDictionary<string, CacheEntry<object>> cache, string key) where T : class
        {
            if (cache.TryGetValue(key, out var entry))
            {
                if (!entry.IsExpired)
                {
                    entry.LastAccessed = DateTime.UtcNow;
                    entry.AccessCount++;
                    return entry.Value as T;
                }
                else
                {
                    // Remove expired entry
                    cache.TryRemove(key, out _);
                    lock (_lockObject)
                    {
                        _totalMemoryUsage -= entry.SizeBytes;
                    }
                }
            }
            
            return null;
        }

        /// <summary>
        /// Determine appropriate cache level for data type
        /// </summary>
        private int DetermineCacheLevel(string dataType)
        {
            if (dataType == null) return 1;

            return dataType switch
            {
                "TradingSignal" => 1,           // Recent data - L1
                "PerformanceMetrics" => 1,      // Recent data - L1
                "InstitutionalFootprint" => 2,  // Historical patterns - L2
                "AdvancedPattern" => 2,         // Historical patterns - L2
                "CorrelationAnalysis" => 3,     // Statistical data - L3
                "ConflictAnalysis" => 3,        // Statistical data - L3
                _ => 1                          // Default to L1
            };
        }

        /// <summary>
        /// Determine TTL based on data type and cache level
        /// </summary>
        private TimeSpan DetermineTTL(string dataType, int cacheLevel)
        {
            // Use data type specific TTL if available
            if (dataType != null && _config.DataTypeTTL.ContainsKey(dataType))
            {
                return _config.DataTypeTTL[dataType];
            }

            // Use cache level default TTL
            return cacheLevel switch
            {
                1 => _config.L1CacheTTL,
                2 => _config.L2CacheTTL,
                3 => _config.L3CacheTTL,
                _ => _config.L1CacheTTL
            };
        }

        /// <summary>
        /// Set entry in appropriate cache level
        /// </summary>
        private void SetInCache(int cacheLevel, string key, CacheEntry<object> entry)
        {
            var cache = cacheLevel switch
            {
                1 => _l1Cache,
                2 => _l2Cache,
                3 => _l3Cache,
                _ => _l1Cache
            };

            // Check cache size limits
            var maxSize = cacheLevel switch
            {
                1 => _config.MaxL1CacheSize,
                2 => _config.MaxL2CacheSize,
                3 => _config.MaxL3CacheSize,
                _ => _config.MaxL1CacheSize
            };

            // Evict LRU entries if cache is full
            if (cache.Count >= maxSize)
            {
                EvictLRUEntries(cache, maxSize / 4); // Evict 25% of entries
            }

            cache.AddOrUpdate(key, entry, (k, existing) => entry);
        }

        /// <summary>
        /// Promote entry to L1 cache
        /// </summary>
        private void PromoteToL1Cache<T>(string key, T value, string dataType) where T : class
        {
            if (_l1Cache.Count < _config.MaxL1CacheSize)
            {
                var entry = new CacheEntry<object>
                {
                    Key = key,
                    Value = value,
                    CreatedAt = DateTime.UtcNow,
                    LastAccessed = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.Add(_config.L1CacheTTL),
                    AccessCount = 1,
                    SizeBytes = EstimateObjectSize(value),
                    InvalidationStrategy = _config.DefaultInvalidationStrategy
                };

                if (dataType != null)
                {
                    entry.Metadata["DataType"] = dataType;
                }

                _l1Cache.TryAdd(key, entry);
            }
        }

        /// <summary>
        /// Promote entry to L2 cache
        /// </summary>
        private void PromoteToL2Cache<T>(string key, T value, string dataType) where T : class
        {
            if (_l2Cache.Count < _config.MaxL2CacheSize)
            {
                var entry = new CacheEntry<object>
                {
                    Key = key,
                    Value = value,
                    CreatedAt = DateTime.UtcNow,
                    LastAccessed = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.Add(_config.L2CacheTTL),
                    AccessCount = 1,
                    SizeBytes = EstimateObjectSize(value),
                    InvalidationStrategy = _config.DefaultInvalidationStrategy
                };

                if (dataType != null)
                {
                    entry.Metadata["DataType"] = dataType;
                }

                _l2Cache.TryAdd(key, entry);
            }
        }

        /// <summary>
        /// Invalidate expired entries
        /// </summary>
        private int InvalidateExpiredEntries()
        {
            var invalidatedCount = 0;
            var caches = new[] { _l1Cache, _l2Cache, _l3Cache };

            foreach (var cache in caches)
            {
                var expiredKeys = cache.Where(kvp => kvp.Value.IsExpired).Select(kvp => kvp.Key).ToList();

                foreach (var key in expiredKeys)
                {
                    if (cache.TryRemove(key, out var entry))
                    {
                        lock (_lockObject)
                        {
                            _totalMemoryUsage -= entry.SizeBytes;
                        }
                        invalidatedCount++;
                    }
                }
            }

            return invalidatedCount;
        }

        /// <summary>
        /// Invalidate entries by pattern
        /// </summary>
        private int InvalidateByPattern(string keyPattern)
        {
            if (string.IsNullOrEmpty(keyPattern)) return 0;

            var invalidatedCount = 0;
            var caches = new[] { _l1Cache, _l2Cache, _l3Cache };

            foreach (var cache in caches)
            {
                var matchingKeys = cache.Keys.Where(key => key.Contains(keyPattern)).ToList();

                foreach (var key in matchingKeys)
                {
                    if (cache.TryRemove(key, out var entry))
                    {
                        lock (_lockObject)
                        {
                            _totalMemoryUsage -= entry.SizeBytes;
                        }
                        invalidatedCount++;
                    }
                }
            }

            return invalidatedCount;
        }

        /// <summary>
        /// Invalidate entries based on performance metrics
        /// </summary>
        private int InvalidateByPerformance()
        {
            var invalidatedCount = 0;

            // Remove entries with low access count and old age
            var caches = new[] { _l1Cache, _l2Cache, _l3Cache };

            foreach (var cache in caches)
            {
                var lowPerformanceEntries = cache.Where(kvp =>
                    kvp.Value.AccessCount <= 1 &&
                    kvp.Value.Age > TimeSpan.FromMinutes(10)).ToList();

                foreach (var kvp in lowPerformanceEntries)
                {
                    if (cache.TryRemove(kvp.Key, out var entry))
                    {
                        lock (_lockObject)
                        {
                            _totalMemoryUsage -= entry.SizeBytes;
                        }
                        invalidatedCount++;
                    }
                }
            }

            return invalidatedCount;
        }

        /// <summary>
        /// Invalidate entries based on memory pressure
        /// </summary>
        private int InvalidateByMemoryPressure()
        {
            var invalidatedCount = 0;

            if (_totalMemoryUsage > _config.MaxMemoryUsageBytes)
            {
                var targetReduction = _totalMemoryUsage - (_config.MaxMemoryUsageBytes * 0.8m); // Reduce to 80% of max
                var currentReduction = 0L;

                // Start with L3 cache (least critical)
                var caches = new[] { _l3Cache, _l2Cache, _l1Cache };

                foreach (var cache in caches)
                {
                    if (currentReduction >= targetReduction) break;

                    var entriesToRemove = cache.OrderBy(kvp => kvp.Value.LastAccessed).ToList();

                    foreach (var kvp in entriesToRemove)
                    {
                        if (currentReduction >= targetReduction) break;

                        if (cache.TryRemove(kvp.Key, out var entry))
                        {
                            lock (_lockObject)
                            {
                                _totalMemoryUsage -= entry.SizeBytes;
                                currentReduction += entry.SizeBytes;
                            }
                            invalidatedCount++;
                        }
                    }
                }
            }

            return invalidatedCount;
        }

        /// <summary>
        /// Hybrid invalidation strategy
        /// </summary>
        private int InvalidateHybrid(string keyPattern, string dataType)
        {
            var invalidatedCount = 0;

            // 1. Remove expired entries
            invalidatedCount += InvalidateExpiredEntries();

            // 2. Check memory pressure
            if (_totalMemoryUsage > _config.MaxMemoryUsageBytes * 0.9m)
            {
                invalidatedCount += InvalidateByMemoryPressure();
            }

            // 3. Remove low-performance entries
            if (_cachePerformanceHistory.Count > 10)
            {
                var recentHitRatio = CalculateRecentHitRatio();
                if (recentHitRatio < _config.MinCacheHitRatio)
                {
                    invalidatedCount += InvalidateByPerformance();
                }
            }

            // 4. Pattern-based invalidation if specified
            if (!string.IsNullOrEmpty(keyPattern))
            {
                invalidatedCount += InvalidateByPattern(keyPattern);
            }

            return invalidatedCount;
        }

        /// <summary>
        /// Evict LRU entries from cache
        /// </summary>
        private void EvictLRUEntries(ConcurrentDictionary<string, CacheEntry<object>> cache, int countToEvict)
        {
            var entriesToEvict = cache.OrderBy(kvp => kvp.Value.LastAccessed).Take(countToEvict).ToList();

            foreach (var kvp in entriesToEvict)
            {
                if (cache.TryRemove(kvp.Key, out var entry))
                {
                    lock (_lockObject)
                    {
                        _totalMemoryUsage -= entry.SizeBytes;
                    }
                }
            }
        }

        /// <summary>
        /// Check if cleanup should be triggered
        /// </summary>
        private bool ShouldTriggerCleanup()
        {
            var timeSinceLastCleanup = DateTime.UtcNow - _lastCleanup;
            var memoryPressure = _totalMemoryUsage > _config.MaxMemoryUsageBytes * 0.8m;
            var intervalPassed = timeSinceLastCleanup >= CLEANUP_INTERVAL;

            return memoryPressure || intervalPassed;
        }

        /// <summary>
        /// Trigger cache cleanup
        /// </summary>
        private void TriggerCleanup()
        {
            var cleanupStartTime = DateTime.UtcNow;
            var initialMemoryUsage = _totalMemoryUsage;

            // Perform hybrid invalidation
            var invalidatedCount = InvalidateHybrid(null, null);

            _lastCleanup = DateTime.UtcNow;
            var cleanupDuration = DateTime.UtcNow - cleanupStartTime;
            var memoryFreed = initialMemoryUsage - _totalMemoryUsage;

            _logAction($"🧹 Cache cleanup completed - Removed {invalidatedCount} entries, freed {memoryFreed / BYTES_PER_MB:F1}MB in {cleanupDuration.TotalMilliseconds:F0}ms");
        }

        /// <summary>
        /// Calculate recent cache hit ratio
        /// </summary>
        private decimal CalculateRecentHitRatio()
        {
            if (_cachePerformanceHistory.Count < 5) return 1.0m;

            var recentMetrics = _cachePerformanceHistory.TakeLast(10).ToList();
            var totalHits = recentMetrics.Sum(m => m.CacheHits);
            var totalMisses = recentMetrics.Sum(m => m.CacheMisses);

            return totalHits + totalMisses > 0 ? (decimal)totalHits / (totalHits + totalMisses) : 0m;
        }

        private long EstimateObjectSize(object obj)
        {
            // Simple size estimation - in production, use more sophisticated methods
            if (obj == null) return 0;

            return obj.ToString().Length * 2; // Rough estimate: 2 bytes per character
        }

        private void RecordCacheHit(int level, TimeSpan responseTime)
        {
            lock (_lockObject)
            {
                _totalHits++;
                RecordPerformanceMetric(true, responseTime, level);
            }
        }

        private void RecordCacheMiss(TimeSpan responseTime)
        {
            lock (_lockObject)
            {
                _totalMisses++;
                RecordPerformanceMetric(false, responseTime, 0);
            }
        }

        private void RecordPerformanceMetric(bool hit, TimeSpan responseTime, int level)
        {
            var metric = new PerformanceMetrics
            {
                Timestamp = DateTime.UtcNow,
                Component = PerformanceComponent.CachingSystem,
                OperationName = hit ? $"CacheHit_L{level}" : "CacheMiss",
                ResponseTime = responseTime,
                Success = hit,
                CacheHits = hit ? 1 : 0,
                CacheMisses = hit ? 0 : 1
            };

            _cachePerformanceHistory.Enqueue(metric);

            while (_cachePerformanceHistory.Count > MAX_PERFORMANCE_HISTORY)
            {
                _cachePerformanceHistory.Dequeue();
            }
        }
    }

    /// <summary>
    /// Cache statistics for monitoring
    /// </summary>
    public class CacheStatistics
    {
        public int L1CacheSize { get; set; }
        public int L2CacheSize { get; set; }
        public int L3CacheSize { get; set; }
        public int TotalCacheSize { get; set; }
        public int TotalHits { get; set; }
        public int TotalMisses { get; set; }
        public decimal HitRatio { get; set; }
        public decimal TotalMemoryUsageMB { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public DateTime LastCleanup { get; set; }
    }
}
