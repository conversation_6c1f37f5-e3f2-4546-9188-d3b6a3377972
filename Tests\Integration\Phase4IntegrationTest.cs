using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartVolumeStrategy.Core.Analysis;
using SmartVolumeStrategy.Core.Strategy;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Interfaces;

namespace SmartVolumeStrategy.Tests.Integration
{
    /// <summary>
    /// Integration test for Phase 4: Advanced Signal Synthesis & Multi-Timeframe Analysis
    /// Tests the complete enhanced system with multi-timeframe analysis, signal synthesis, and pattern recognition
    /// </summary>
    public class Phase4IntegrationTest
    {
        public static async Task<bool> RunIntegrationTest()
        {
            try
            {
                Console.WriteLine("🧪 === PHASE 4 INTEGRATION TEST STARTING ===");
                
                // Step 1: Initialize all Phase 4 components
                Console.WriteLine("🔧 Initializing Phase 4 components...");
                var volumeAnalyzer = new VolumePatternAnalyzer();
                var deltaAnalyzer = new DeltaFlowAnalyzer();
                var volatilityAnalyzer = new VolatilityAnalyzer();
                var marketProfileAnalyzer = new MarketProfileAnalyzer();
                var patternRecognizer = new EnhancedPatternRecognizer(Console.WriteLine);
                
                // Step 2: Initialize multi-timeframe analyzer
                var multiTimeframeAnalyzer = new MultiTimeframeAnalyzer(
                    volumeAnalyzer, deltaAnalyzer, volatilityAnalyzer, marketProfileAnalyzer, patternRecognizer);
                
                var symbolProfile = CreateTestSymbolProfile();
                var optimalSettings = CreateTestOptimalSettings();
                var multiTimeframeConfig = CreateMultiTimeframeConfig();
                
                multiTimeframeAnalyzer.Initialize(symbolProfile, optimalSettings, multiTimeframeConfig);
                Console.WriteLine("✅ Multi-timeframe analyzer initialized");
                
                // Step 3: Initialize signal synthesizer
                var signalSynthesizer = new SignalSynthesizer(Console.WriteLine);
                var synthesisConfig = CreateSignalSynthesisConfig();
                signalSynthesizer.Initialize(synthesisConfig);
                Console.WriteLine("✅ Signal synthesizer initialized");
                
                // Step 4: Initialize enhanced signal generator
                var baseSignalGenerator = new SignalGenerator(Console.WriteLine);
                var enhancedSignalGenerator = new EnhancedSignalGenerator(
                    baseSignalGenerator, multiTimeframeAnalyzer, signalSynthesizer, Console.WriteLine);
                Console.WriteLine("✅ Enhanced signal generator initialized");
                
                // Step 5: Test multi-timeframe analysis
                Console.WriteLine("📊 Testing multi-timeframe analysis...");
                var testData = GenerateMultiTimeframeTestData(200);
                
                foreach (var dataPoint in testData)
                {
                    multiTimeframeAnalyzer.UpdateMarketData(dataPoint);
                }
                
                var multiTimeframeState = multiTimeframeAnalyzer.GetCurrentAnalysisState();
                if (multiTimeframeState.TimeframeStates.Count < 2)
                {
                    Console.WriteLine("❌ MULTI-TIMEFRAME TEST FAILED: Insufficient timeframe states");
                    return false;
                }
                Console.WriteLine($"✅ Multi-timeframe analysis working: {multiTimeframeState.TimeframeStates.Count} timeframes analyzed");
                
                // Step 6: Test pattern recognition
                Console.WriteLine("🔍 Testing pattern recognition...");
                var timeframeData = CreateTimeframeDataForPatternTesting();
                
                var volumePatterns = patternRecognizer.DetectVolumePatterns(timeframeData);
                var institutionalFootprint = patternRecognizer.DetectInstitutionalFootprint(timeframeData);
                var marketParticipants = patternRecognizer.AnalyzeMarketParticipants(timeframeData);
                var orderFlowImbalance = patternRecognizer.DetectOrderFlowImbalance(timeframeData);
                
                if (volumePatterns.Count == 0 && !institutionalFootprint.IsDetected)
                {
                    Console.WriteLine("⚠️ Pattern recognition test: No patterns detected (may be normal)");
                }
                else
                {
                    Console.WriteLine($"✅ Pattern recognition working: {volumePatterns.Count} volume patterns, " +
                                    $"Institutional: {institutionalFootprint.IsDetected}, " +
                                    $"Participants: {marketParticipants.Dominance}");
                }
                
                // Step 7: Test signal synthesis
                Console.WriteLine("🎯 Testing signal synthesis...");
                var marketContext = CreateTestMarketContext();
                var synthesizedSignal = signalSynthesizer.SynthesizeSignal(multiTimeframeState, marketContext);
                
                if (synthesizedSignal == null)
                {
                    Console.WriteLine("⚠️ Signal synthesis test: No signal generated (may be normal)");
                }
                else
                {
                    Console.WriteLine($"✅ Signal synthesis working: {synthesizedSignal.Type} signal " +
                                    $"(Confidence: {synthesizedSignal.Confidence:P1}, Quality: {synthesizedSignal.Quality})");
                }
                
                // Step 8: Test enhanced signal generation
                Console.WriteLine("🚀 Testing enhanced signal generation...");
                var currentBar = testData.Last();
                var volumeBlock = CreateTestVolumeBlock();
                
                var enhancedSignal = enhancedSignalGenerator.GenerateSignal(currentBar, volumeBlock, marketContext, optimalSettings);
                
                if (enhancedSignal == null)
                {
                    Console.WriteLine("❌ ENHANCED SIGNAL TEST FAILED: No signal generated");
                    return false;
                }
                Console.WriteLine($"✅ Enhanced signal generation working: {enhancedSignal.Type} " +
                                $"(Confidence: {enhancedSignal.Confidence:P1}, Quality: {enhancedSignal.Quality})");
                
                // Step 9: Test trend alignment analysis
                Console.WriteLine("📈 Testing trend alignment analysis...");
                var volumeTrendAlignment = multiTimeframeAnalyzer.AnalyzeTrendAlignment("volume");
                var deltaTrendAlignment = multiTimeframeAnalyzer.AnalyzeTrendAlignment("delta");
                
                if (volumeTrendAlignment.AlignmentScore < 0 || deltaTrendAlignment.AlignmentScore < 0)
                {
                    Console.WriteLine("❌ TREND ALIGNMENT TEST FAILED: Invalid alignment scores");
                    return false;
                }
                Console.WriteLine($"✅ Trend alignment working: Volume {volumeTrendAlignment.AlignmentScore:P1}, " +
                                $"Delta {deltaTrendAlignment.AlignmentScore:P1}");
                
                // Step 10: Test performance under load
                Console.WriteLine("⚡ Testing performance under load...");
                var loadTestData = GenerateHighVolumeTestData(500);
                var startTime = DateTime.UtcNow;
                
                var signalsGenerated = 0;
                foreach (var dataPoint in loadTestData)
                {
                    multiTimeframeAnalyzer.UpdateMarketData(dataPoint);
                    
                    // Generate signal every 10th bar to test performance
                    if (dataPoint.BarIndex % 10 == 0)
                    {
                        var signal = enhancedSignalGenerator.GenerateSignal(dataPoint, volumeBlock, marketContext, optimalSettings);
                        if (signal.Type != SignalType.None)
                            signalsGenerated++;
                    }
                }
                
                var loadTestDuration = DateTime.UtcNow - startTime;
                var multiTimeframeMetrics = multiTimeframeAnalyzer.GetPerformanceMetrics();
                var synthesisMetrics = signalSynthesizer.GetPerformanceMetrics();
                
                if (loadTestDuration.TotalSeconds > 30) // Should complete within 30 seconds
                {
                    Console.WriteLine($"❌ PERFORMANCE TEST FAILED: Load test too slow ({loadTestDuration.TotalSeconds:F1}s)");
                    return false;
                }
                Console.WriteLine($"✅ Performance test passed: {loadTestDuration.TotalSeconds:F1}s, " +
                                $"{signalsGenerated} signals generated");
                
                // Step 11: Test signal correlation analysis
                Console.WriteLine("🔗 Testing signal correlation analysis...");
                var timeframeSignals = CreateTestTimeframeSignals();
                var correlationAnalysis = signalSynthesizer.AnalyzeSignalCorrelation(timeframeSignals);
                
                if (correlationAnalysis.Consistency == SignalConsistency.NoSignal)
                {
                    Console.WriteLine("⚠️ Signal correlation test: No signal consistency detected");
                }
                else
                {
                    Console.WriteLine($"✅ Signal correlation working: {correlationAnalysis.Consistency} " +
                                    $"(Correlation: {correlationAnalysis.OverallCorrelation:P1})");
                }
                
                // Step 12: Test conflict resolution
                Console.WriteLine("⚖️ Testing conflict resolution...");
                var conflictingSignals = CreateConflictingSignals();
                var resolvedSignal = signalSynthesizer.ResolveSignalConflicts(conflictingSignals, marketContext);
                
                if (resolvedSignal != null)
                {
                    Console.WriteLine($"✅ Conflict resolution working: Resolved to {resolvedSignal.Type} " +
                                    $"(Confidence: {resolvedSignal.Confidence:P1})");
                }
                else
                {
                    Console.WriteLine("✅ Conflict resolution working: No signal generated (conflict too severe)");
                }
                
                // Step 13: Test backward compatibility
                Console.WriteLine("🔄 Testing backward compatibility...");
                var baseSignal = baseSignalGenerator.GenerateSignal(currentBar, volumeBlock, marketContext, optimalSettings);
                var enhancedSignal2 = enhancedSignalGenerator.GenerateSignal(currentBar, volumeBlock, marketContext, optimalSettings);
                
                if (baseSignal == null || enhancedSignal2 == null)
                {
                    Console.WriteLine("❌ BACKWARD COMPATIBILITY TEST FAILED: Signal generation failed");
                    return false;
                }
                Console.WriteLine($"✅ Backward compatibility verified: Base {baseSignal.Type}, Enhanced {enhancedSignal2.Type}");
                
                // Step 14: Validate integration with Phase 3 features
                Console.WriteLine("🔗 Testing Phase 3 integration...");
                var phase3Metrics = multiTimeframeMetrics;
                
                if (phase3Metrics.TotalAnalysisTime.TotalMilliseconds > phase3Metrics.MaxAcceptableAnalysisTimeMs)
                {
                    Console.WriteLine($"❌ PHASE 3 INTEGRATION TEST FAILED: Analysis too slow " +
                                    $"({phase3Metrics.TotalAnalysisTime.TotalMilliseconds:F1}ms > {phase3Metrics.MaxAcceptableAnalysisTimeMs}ms)");
                    return false;
                }
                Console.WriteLine($"✅ Phase 3 integration verified: Analysis time {phase3Metrics.TotalAnalysisTime.TotalMilliseconds:F1}ms");
                
                Console.WriteLine("🎉 === PHASE 4 INTEGRATION TEST COMPLETED SUCCESSFULLY ===");
                Console.WriteLine($"✅ Multi-Timeframe Analysis: {multiTimeframeState.TimeframeStates.Count} timeframes");
                Console.WriteLine($"✅ Pattern Recognition: {volumePatterns.Count} patterns detected");
                Console.WriteLine($"✅ Signal Synthesis: {synthesisMetrics.TotalSignalsSynthesized} signals synthesized");
                Console.WriteLine($"✅ Enhanced Signal Generation: Working with {enhancedSignal.Quality} quality");
                Console.WriteLine($"✅ Trend Alignment: Volume {volumeTrendAlignment.AlignmentScore:P1}, Delta {deltaTrendAlignment.AlignmentScore:P1}");
                Console.WriteLine($"✅ Performance: {loadTestDuration.TotalSeconds:F1}s for {loadTestData.Count} bars");
                Console.WriteLine($"✅ Signal Correlation: {correlationAnalysis.Consistency} consistency");
                Console.WriteLine($"✅ Conflict Resolution: Operational");
                Console.WriteLine($"✅ Backward Compatibility: Verified");
                Console.WriteLine($"✅ Phase 3 Integration: Analysis time {phase3Metrics.TotalAnalysisTime.TotalMilliseconds:F1}ms");
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ PHASE 4 INTEGRATION TEST EXCEPTION: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                return false;
            }
        }
        
        private static SymbolProfile CreateTestSymbolProfile()
        {
            return new SymbolProfile
            {
                Symbol = "BTCUSDT",
                CorrelationId = Guid.NewGuid().ToString(),
                VolumeAnalysisResult = new VolumeAnalysisResult
                {
                    AverageVolume = 2000000,
                    VolumeStandardDeviation = 400000,
                    VolumeRegime = VolumeRegime.Normal
                },
                DeltaFlowAnalysisResult = new DeltaFlowAnalysisResult
                {
                    AverageDeltaImbalance = 0.35m,
                    DeltaFlowRegime = DeltaFlowRegime.Balanced
                },
                VolatilityAnalysisResult = new VolatilityAnalysisResult
                {
                    AverageVolatility = 0.02m,
                    VolatilityRegime = VolatilityRegime.Normal
                },
                MarketProfileAnalysisResult = new MarketProfileAnalysisResult
                {
                    MostActiveSession = TradingSession.US,
                    Is24HourMarket = true
                }
            };
        }
        
        private static OptimalSettings CreateTestOptimalSettings()
        {
            return new OptimalSettings
            {
                VolumeThreshold = 2.5m,
                SignalThreshold = 1.8m,
                DeltaImbalanceThreshold = 0.45m,
                TakeProfitPercent = 1.5m,
                StopLossPercent = 1.0m,
                EnableSessionAdjustments = true
            };
        }
        
        private static MultiTimeframeConfig CreateMultiTimeframeConfig()
        {
            return new MultiTimeframeConfig
            {
                TimeframeWeights = new Dictionary<Timeframe, decimal>
                {
                    { Timeframe.M1, 0.15m },
                    { Timeframe.M5, 0.35m },
                    { Timeframe.M15, 0.35m },
                    { Timeframe.H1, 0.15m }
                },
                MinTrendAlignmentScore = 0.6m,
                MinAlignedTimeframes = 2,
                EnablePatternDetection = true,
                MinPatternConfidence = 0.5m,
                EnableParallelProcessing = false // Disable for testing
            };
        }
        
        private static SignalSynthesisConfig CreateSignalSynthesisConfig()
        {
            return new SignalSynthesisConfig
            {
                VolumeAnalysisWeight = 0.35m,
                DeltaAnalysisWeight = 0.35m,
                VolatilityAnalysisWeight = 0.15m,
                MarketProfileWeight = 0.15m,
                ConflictResolution = ConflictResolutionStrategy.WeightedConsensus,
                MinConsensusThreshold = 0.6m,
                EnableMicrostructureFilter = true,
                MinVolumeConfidence = 0.4m,
                MinDeltaConfidence = 0.4m,
                MinVolatilityConfidence = 0.3m
            };
        }
        
        private static List<MarketDataPoint> GenerateMultiTimeframeTestData(int count)
        {
            var data = new List<MarketDataPoint>();
            var random = new Random(42);
            var basePrice = 50000m;
            var baseVolume = 2000000m;
            
            for (int i = 0; i < count; i++)
            {
                // Create realistic market data with trends and patterns
                var trendFactor = (decimal)Math.Sin(i * 0.1) * 0.02m; // Sine wave trend
                var noiseFactor = (decimal)(random.NextDouble() - 0.5) * 0.01m;
                
                var price = basePrice * (1 + trendFactor + noiseFactor);
                var volume = baseVolume * (1 + (decimal)(random.NextDouble() - 0.3) * 0.5m);
                var delta = volume * (decimal)(random.NextDouble() - 0.5) * 0.6m;
                
                data.Add(new MarketDataPoint
                {
                    Timestamp = DateTime.UtcNow.AddMinutes(-count + i),
                    Open = price - 5,
                    High = price + 15,
                    Low = price - 15,
                    Close = price,
                    Volume = volume,
                    BuyVolume = volume * 0.55m,
                    SellVolume = volume * 0.45m,
                    BarIndex = i
                });
            }
            
            return data;
        }
        
        private static Dictionary<Timeframe, List<MarketDataPoint>> CreateTimeframeDataForPatternTesting()
        {
            var timeframeData = new Dictionary<Timeframe, List<MarketDataPoint>>();
            
            foreach (Timeframe timeframe in Enum.GetValues<Timeframe>())
            {
                var data = GeneratePatternTestData((int)timeframe * 20);
                timeframeData[timeframe] = data;
            }
            
            return timeframeData;
        }
        
        private static List<MarketDataPoint> GeneratePatternTestData(int count)
        {
            var data = new List<MarketDataPoint>();
            var random = new Random(123);
            var basePrice = 50000m;
            var baseVolume = 1500000m;
            
            for (int i = 0; i < count; i++)
            {
                // Create accumulation pattern in first half
                var volumeMultiplier = i < count / 2 ? 1.0m + (i * 0.02m) : 1.5m;
                var priceStability = i < count / 2 ? 0.005m : 0.015m;
                
                var price = basePrice + (decimal)(random.NextDouble() - 0.5) * priceStability * basePrice;
                var volume = baseVolume * volumeMultiplier;
                var delta = volume * (decimal)(random.NextDouble() - 0.4) * 0.3m; // Slight buy bias
                
                data.Add(new MarketDataPoint
                {
                    Timestamp = DateTime.UtcNow.AddMinutes(-count + i),
                    Open = price - 2,
                    High = price + 8,
                    Low = price - 8,
                    Close = price,
                    Volume = volume,
                    BuyVolume = volume * 0.6m,
                    SellVolume = volume * 0.4m,
                    BarIndex = i
                });
            }
            
            return data;
        }
        
        private static MarketContext CreateTestMarketContext()
        {
            return new MarketContext
            {
                CurrentSession = TradingSession.US,
                IsOptimalTradingTime = true,
                VolatilityRegime = VolatilityRegime.Normal,
                CVDTrend = 25.5m,
                DeltaImbalance = 0.35m,
                MarketConditions = new List<string> { "Normal volatility", "Active session" }
            };
        }
        
        private static VolumeBlockResult CreateTestVolumeBlock()
        {
            return new VolumeBlockResult
            {
                IsVolumeBlock = true,
                VolumeRatio = 2.8m,
                CumulativeImpact = 0.025m,
                Confidence = 0.85m,
                Description = "Strong volume block detected"
            };
        }
        
        private static List<MarketDataPoint> GenerateHighVolumeTestData(int count)
        {
            var data = new List<MarketDataPoint>();
            var random = new Random(456);
            var basePrice = 50000m;
            var baseVolume = 3000000m; // Higher volume for load testing
            
            for (int i = 0; i < count; i++)
            {
                var price = basePrice + (decimal)(random.NextDouble() - 0.5) * 2000;
                var volume = baseVolume + (decimal)(random.NextDouble() - 0.5) * 1500000;
                var delta = (decimal)(random.NextDouble() - 0.5) * volume * 0.4m;
                
                data.Add(new MarketDataPoint
                {
                    Timestamp = DateTime.UtcNow.AddSeconds(-count + i),
                    Open = price - 10,
                    High = price + 25,
                    Low = price - 25,
                    Close = price,
                    Volume = volume,
                    BuyVolume = volume * 0.52m,
                    SellVolume = volume * 0.48m,
                    BarIndex = i
                });
            }
            
            return data;
        }
        
        private static Dictionary<Timeframe, TradingSignal> CreateTestTimeframeSignals()
        {
            return new Dictionary<Timeframe, TradingSignal>
            {
                {
                    Timeframe.M1,
                    new TradingSignal
                    {
                        Type = SignalType.Long,
                        Confidence = 0.7m,
                        Quality = SignalQuality.Good,
                        PrimaryReason = "M1 bullish signal"
                    }
                },
                {
                    Timeframe.M5,
                    new TradingSignal
                    {
                        Type = SignalType.Long,
                        Confidence = 0.8m,
                        Quality = SignalQuality.Good,
                        PrimaryReason = "M5 bullish signal"
                    }
                },
                {
                    Timeframe.M15,
                    new TradingSignal
                    {
                        Type = SignalType.Short,
                        Confidence = 0.6m,
                        Quality = SignalQuality.Fair,
                        PrimaryReason = "M15 bearish signal"
                    }
                }
            };
        }
        
        private static List<TradingSignal> CreateConflictingSignals()
        {
            return new List<TradingSignal>
            {
                new TradingSignal
                {
                    Type = SignalType.Long,
                    Confidence = 0.8m,
                    Quality = SignalQuality.Good,
                    PrimaryReason = "Strong bullish signal"
                },
                new TradingSignal
                {
                    Type = SignalType.Short,
                    Confidence = 0.7m,
                    Quality = SignalQuality.Good,
                    PrimaryReason = "Strong bearish signal"
                },
                new TradingSignal
                {
                    Type = SignalType.Long,
                    Confidence = 0.6m,
                    Quality = SignalQuality.Fair,
                    PrimaryReason = "Moderate bullish signal"
                }
            };
        }
    }
}
