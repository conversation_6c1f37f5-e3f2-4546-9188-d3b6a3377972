using System;
using System.Collections.Generic;

namespace SmartVolumeStrategy.Models
{
    /// <summary>
    /// Real-time analysis state containing current market analysis from all Phase 1 components
    /// </summary>
    public class AnalysisState
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        public string CorrelationId { get; set; } = Guid.NewGuid().ToString("N")[..8];
        public int BarsProcessed { get; set; }
        
        // Component States
        public VolumeAnalysisState Volume { get; set; } = new();
        public DeltaAnalysisState DeltaFlow { get; set; } = new();
        public VolatilityAnalysisState Volatility { get; set; } = new();
        public MarketProfileAnalysisState MarketProfile { get; set; } = new();
        
        // Overall Analysis Quality
        public decimal OverallConfidence { get; set; }
        public List<string> ActiveConditions { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }

    /// <summary>
    /// Real-time volume analysis state from VolumePatternAnalyzer
    /// </summary>
    public class VolumeAnalysisState
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        
        // Current Volume Characteristics
        public VolumeRegime CurrentRegime { get; set; } = VolumeRegime.Normal;
        public decimal CurrentVolumeRatio { get; set; } // Current volume vs average
        public decimal AverageVolume { get; set; }
        public decimal VolumeStandardDeviation { get; set; }
        
        // Volume Spike Detection
        public bool IsVolumeSpikeActive { get; set; }
        public decimal SpikeMagnitude { get; set; } // How many times above average
        public int ConsecutiveSpikeBars { get; set; }
        public DateTime LastSpikeTime { get; set; }
        
        // Volume Pattern Analysis
        public string CurrentVolumePattern { get; set; } = "Normal"; // "Spike", "Sustained", "Building", "Declining"
        public decimal VolumeConsistency { get; set; } // 0-1 score
        public decimal VolumeConfidence { get; set; } // 0-1 confidence in current analysis
        
        // Trend Analysis
        public VolumeTrend VolumeTrend { get; set; } = VolumeTrend.Stable;
        public int TrendDuration { get; set; } // Bars in current trend
        public decimal TrendStrength { get; set; } // 0-1 strength of trend
    }

    /// <summary>
    /// Real-time delta flow analysis state from DeltaFlowAnalyzer
    /// </summary>
    public class DeltaAnalysisState
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        
        // Current Delta Characteristics
        public DeltaFlowRegime CurrentRegime { get; set; } = DeltaFlowRegime.Balanced;
        public decimal CurrentDeltaImbalance { get; set; }
        public decimal CurrentCVDTrend { get; set; }
        public DeltaBias CurrentBias { get; set; } = DeltaBias.Neutral;
        
        // Institutional Activity Detection
        public bool IsInstitutionalActivityDetected { get; set; }
        public decimal InstitutionalActivityLevel { get; set; } // 0-1 score
        public string InstitutionalPattern { get; set; } = "None"; // "Accumulation", "Distribution", "Absorption"
        public DateTime LastInstitutionalActivity { get; set; }
        
        // CVD Analysis
        public decimal CVDTrendStrength { get; set; } // 0-1 strength of CVD trend
        public int CVDTrendDuration { get; set; } // Bars in current CVD trend
        public bool IsCVDDivergence { get; set; } // CVD diverging from price
        public decimal CVDConfidence { get; set; } // 0-1 confidence in CVD analysis
        
        // Delta Flow Patterns
        public string CurrentDeltaPattern { get; set; } = "Normal"; // "Strong Pressure", "Moderate Pressure", "Normal Flow"
        public decimal DeltaConfidence { get; set; } // 0-1 confidence in delta analysis
        public decimal BuyPressureDominance { get; set; } // -1 to 1 (sell to buy bias)
    }

    /// <summary>
    /// Real-time volatility analysis state from VolatilityAnalyzer
    /// </summary>
    public class VolatilityAnalysisState
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        
        // Current Volatility Characteristics
        public VolatilityRegime CurrentRegime { get; set; } = VolatilityRegime.Normal;
        public decimal CurrentVolatility { get; set; }
        public decimal AverageVolatility { get; set; }
        public decimal VolatilityPercentile { get; set; } // Where current volatility ranks (0-100)
        
        // Volatility Patterns
        public bool IsVolatilityExpansion { get; set; }
        public bool IsVolatilityContraction { get; set; }
        public string VolatilityPattern { get; set; } = "Normal"; // "Expansion", "Contraction", "Breakout", "Calm"
        public decimal VolatilityConfidence { get; set; } // 0-1 confidence in volatility analysis
        
        // Risk Metrics
        public decimal CurrentTrueRange { get; set; }
        public decimal AverageTrueRange { get; set; }
        public decimal RiskAdjustmentFactor { get; set; } // Multiplier for position sizing
        
        // Trend Analysis
        public decimal TrendingProbability { get; set; } // 0-1 probability of trending vs ranging
        public decimal RangingProbability { get; set; } // 0-1 probability of ranging vs trending
        public VolatilityTrend VolatilityTrend { get; set; } = VolatilityTrend.Stable;
    }

    /// <summary>
    /// Real-time market profile analysis state from MarketProfileAnalyzer
    /// </summary>
    public class MarketProfileAnalysisState
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        
        // Current Session Information
        public TradingSession CurrentSession { get; set; } = TradingSession.Unknown;
        public bool IsOptimalTradingTime { get; set; }
        public decimal CurrentSessionActivity { get; set; } // 0-1 activity level for current session
        public TimeSpan TimeInCurrentSession { get; set; }
        
        // Session Analysis
        public decimal AsianSessionActivity { get; set; } // 0-1 score
        public decimal EuropeanSessionActivity { get; set; } // 0-1 score
        public decimal USSessionActivity { get; set; } // 0-1 score
        public decimal SessionVolatilityVariation { get; set; } // How much volatility varies by session
        
        // Time-based Patterns
        public List<int> CurrentHighActivityHours { get; set; } = new(); // UTC hours with high activity
        public List<int> CurrentLowActivityHours { get; set; } = new(); // UTC hours with low activity
        public bool IsInOptimalWindow { get; set; }
        public decimal OptimalWindowConfidence { get; set; } // 0-1 confidence in timing
        
        // Market Characteristics
        public bool Is24HourMarket { get; set; } = true;
        public string MarketCondition { get; set; } = "Normal"; // "Active", "Quiet", "Transition", "Overlap"
        public decimal MarketProfileConfidence { get; set; } // 0-1 confidence in market profile analysis
    }

    /// <summary>
    /// Volume trend enumeration for real-time analysis
    /// </summary>
    public enum VolumeTrend
    {
        Declining,
        Stable,
        Increasing,
        Spiking,
        Erratic
    }

    /// <summary>
    /// Volatility trend enumeration for real-time analysis
    /// </summary>
    public enum VolatilityTrend
    {
        Contracting,
        Stable,
        Expanding,
        Extreme,
        Normalizing
    }

    /// <summary>
    /// Enhanced real-time analysis configuration with performance optimization
    /// </summary>
    public class RealTimeAnalysisConfig
    {
        // Analysis Windows
        public int VolumeAnalysisWindow { get; set; } = 20; // Bars for volume analysis
        public int DeltaAnalysisWindow { get; set; } = 30; // Bars for delta analysis
        public int VolatilityAnalysisWindow { get; set; } = 14; // Bars for volatility analysis
        public int MarketProfileWindow { get; set; } = 100; // Bars for market profile analysis

        // Sensitivity Settings
        public decimal VolumeSpikeSensitivity { get; set; } = 2.0m; // Multiplier for volume spike detection
        public decimal DeltaImbalanceSensitivity { get; set; } = 0.15m; // Threshold for delta imbalance
        public decimal VolatilityExpansionThreshold { get; set; } = 1.5m; // Multiplier for volatility expansion

        // Component Toggles
        public bool EnableVolumeAnalysis { get; set; } = true;
        public bool EnableDeltaAnalysis { get; set; } = true;
        public bool EnableVolatilityAnalysis { get; set; } = true;
        public bool EnableMarketProfileAnalysis { get; set; } = true;

        // Performance Optimization Settings
        public TimeSpan AnalysisUpdateInterval { get; set; } = TimeSpan.FromSeconds(1);
        public int MaxHistoryBars { get; set; } = 500; // Maximum bars to keep in memory

        // Phase 3: Adaptive Analysis Intervals
        public TimeSpan HighActivityInterval { get; set; } = TimeSpan.FromSeconds(1);
        public TimeSpan MediumActivityInterval { get; set; } = TimeSpan.FromSeconds(5);
        public TimeSpan LowActivityInterval { get; set; } = TimeSpan.FromSeconds(15);
        public decimal HighActivityThreshold { get; set; } = 2.0m; // Volume ratio threshold for high activity
        public decimal LowActivityThreshold { get; set; } = 0.5m; // Volume ratio threshold for low activity

        // Caching Configuration
        public bool EnableIntelligentCaching { get; set; } = true;
        public TimeSpan CacheExpirationTime { get; set; } = TimeSpan.FromSeconds(30);
        public int MaxCacheSize { get; set; } = 1000; // Maximum cached analysis results

        // Memory Management
        public bool EnableSlidingWindow { get; set; } = true;
        public int SlidingWindowSize { get; set; } = 200; // Bars to keep for sliding window
        public TimeSpan MemoryCleanupInterval { get; set; } = TimeSpan.FromMinutes(5);

        // Circuit Breaker Settings
        public bool EnableCircuitBreaker { get; set; } = true;
        public int CircuitBreakerFailureThreshold { get; set; } = 5; // Failures before opening circuit
        public TimeSpan CircuitBreakerTimeout { get; set; } = TimeSpan.FromMinutes(2);
        public TimeSpan CircuitBreakerRetryInterval { get; set; } = TimeSpan.FromSeconds(30);
    }

    /// <summary>
    /// Enhanced analysis performance metrics for monitoring and optimization
    /// </summary>
    public class AnalysisPerformanceMetrics
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;

        // Timing Metrics
        public TimeSpan VolumeAnalysisTime { get; set; }
        public TimeSpan DeltaAnalysisTime { get; set; }
        public TimeSpan VolatilityAnalysisTime { get; set; }
        public TimeSpan MarketProfileAnalysisTime { get; set; }
        public TimeSpan TotalAnalysisTime { get; set; }
        public double AverageAnalysisTimeMs { get; set; }

        // Quality Metrics
        public decimal AverageConfidence { get; set; }
        public int AnalysisUpdatesPerMinute { get; set; }
        public decimal CacheHitRate { get; set; } // 0.0 to 1.0
        public long MemoryUsageBytes { get; set; }

        // Error Tracking
        public int ErrorCount { get; set; }
        public int TotalAnalysisCount { get; set; }
        public DateTime LastError { get; set; }
        public string LastErrorMessage { get; set; } = "";

        // Phase 3: Enhanced Performance Metrics
        public int CacheHits { get; set; }
        public int CacheMisses { get; set; }
        public int CircuitBreakerTrips { get; set; }
        public TimeSpan CurrentAnalysisInterval { get; set; }
        public MarketActivityLevel CurrentActivityLevel { get; set; }
        public int SlidingWindowSize { get; set; }
        public int MemoryCleanupCount { get; set; }
        public DateTime LastMemoryCleanup { get; set; }

        // Performance Thresholds
        public double MaxAcceptableAnalysisTimeMs { get; set; } = 100;
        public decimal MinAcceptableCacheHitRate { get; set; } = 0.7m;
        public long MaxAcceptableMemoryUsageBytes { get; set; } = 100 * 1024 * 1024; // 100MB
    }

    /// <summary>
    /// Market activity level for adaptive analysis intervals
    /// </summary>
    public enum MarketActivityLevel
    {
        Low,
        Medium,
        High,
        Extreme
    }

    /// <summary>
    /// Market regime change detection for adaptive calibration
    /// </summary>
    public class MarketRegimeChange
    {
        public DateTime DetectionTime { get; set; } = DateTime.UtcNow;
        public VolumeRegime PreviousVolumeRegime { get; set; }
        public VolumeRegime CurrentVolumeRegime { get; set; }
        public VolatilityRegime PreviousVolatilityRegime { get; set; }
        public VolatilityRegime CurrentVolatilityRegime { get; set; }
        public DeltaFlowRegime PreviousDeltaRegime { get; set; }
        public DeltaFlowRegime CurrentDeltaRegime { get; set; }
        public decimal ChangeSignificance { get; set; } // 0.0 to 1.0
        public bool IsSignificantChange { get; set; }
        public string ChangeDescription { get; set; } = "";
    }

    /// <summary>
    /// Performance metrics for strategy monitoring
    /// </summary>
    public class StrategyPerformanceMetrics
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        public decimal TotalReturn { get; set; }
        public decimal DailyReturn { get; set; }
        public decimal WeeklyReturn { get; set; }
        public decimal MaxDrawdown { get; set; }
        public decimal SharpeRatio { get; set; }
        public int TotalTrades { get; set; }
        public int WinningTrades { get; set; }
        public int LosingTrades { get; set; }
        public decimal WinRate { get; set; }
        public decimal AverageWin { get; set; }
        public decimal AverageLoss { get; set; }
        public decimal ProfitFactor { get; set; }
        public decimal AverageSignalConfidence { get; set; }
        public bool IsPerformanceDegrading { get; set; }
        public string PerformanceStatus { get; set; } = "Normal";
    }

    /// <summary>
    /// Calibration history for tracking adaptive adjustments
    /// </summary>
    public class CalibrationHistory
    {
        public List<CalibrationEvent> Events { get; set; } = new List<CalibrationEvent>();
        public DateTime LastCalibration { get; set; }
        public int TotalCalibrations { get; set; }
        public decimal AverageCalibrationConfidence { get; set; }
        public TimeSpan AverageTimeBetweenCalibrations { get; set; }

        public void AddEvent(CalibrationEvent calibrationEvent)
        {
            Events.Add(calibrationEvent);
            TotalCalibrations++;
            LastCalibration = calibrationEvent.Timestamp;

            // Update averages
            if (Events.Count > 0)
            {
                AverageCalibrationConfidence = Events.Average(e => e.ResultingConfidence);

                if (Events.Count > 1)
                {
                    var intervals = new List<TimeSpan>();
                    for (int i = 1; i < Events.Count; i++)
                    {
                        intervals.Add(Events[i].Timestamp - Events[i - 1].Timestamp);
                    }
                    AverageTimeBetweenCalibrations = TimeSpan.FromTicks((long)intervals.Average(ts => ts.Ticks));
                }
            }
        }
    }

    /// <summary>
    /// Individual calibration event record
    /// </summary>
    public class CalibrationEvent
    {
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string Trigger { get; set; } = ""; // What triggered the calibration
        public OptimalSettings PreviousSettings { get; set; }
        public OptimalSettings NewSettings { get; set; }
        public decimal ResultingConfidence { get; set; }
        public PerformanceMetrics PerformanceBeforeCalibration { get; set; }
        public MarketRegimeChange RegimeChange { get; set; }
        public TimeSpan CalibrationDuration { get; set; }
        public bool WasSuccessful { get; set; }
        public string Notes { get; set; } = "";
    }

    #region Phase 4: Multi-Timeframe Analysis Models

    /// <summary>
    /// Timeframe enumeration for multi-timeframe analysis
    /// </summary>
    public enum Timeframe
    {
        M1 = 1,     // 1 minute
        M5 = 5,     // 5 minutes
        M15 = 15,   // 15 minutes
        H1 = 60     // 1 hour
    }

    /// <summary>
    /// Multi-timeframe analysis state containing analysis for all timeframes
    /// </summary>
    public class MultiTimeframeAnalysisState
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        public string CorrelationId { get; set; } = Guid.NewGuid().ToString();

        // Individual timeframe analysis states
        public Dictionary<Timeframe, AnalysisState> TimeframeStates { get; set; } = new Dictionary<Timeframe, AnalysisState>();

        // Cross-timeframe synthesis
        public TimeframeSynthesis Synthesis { get; set; } = new TimeframeSynthesis();

        // Multi-timeframe patterns
        public List<MultiTimeframePattern> DetectedPatterns { get; set; } = new List<MultiTimeframePattern>();

        // Overall multi-timeframe confidence
        public decimal OverallConfidence { get; set; }
        public decimal TrendAlignment { get; set; }
        public bool IsTimeframeConsistent { get; set; }

        // Performance metrics
        public MultiTimeframePerformanceMetrics PerformanceMetrics { get; set; } = new MultiTimeframePerformanceMetrics();
    }

    /// <summary>
    /// Cross-timeframe synthesis results
    /// </summary>
    public class TimeframeSynthesis
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;

        // Weighted analysis results
        public decimal WeightedVolumeConfidence { get; set; }
        public decimal WeightedDeltaConfidence { get; set; }
        public decimal WeightedVolatilityConfidence { get; set; }
        public decimal WeightedMarketProfileConfidence { get; set; }

        // Trend alignment across timeframes
        public TrendAlignment VolumeTrendAlignment { get; set; }
        public TrendAlignment DeltaTrendAlignment { get; set; }
        public TrendAlignment VolatilityTrendAlignment { get; set; }

        // Signal synthesis
        public SynthesizedSignal PrimarySignal { get; set; }
        public List<SynthesizedSignal> ConflictingSignals { get; set; } = new List<SynthesizedSignal>();
        public SignalConsistency SignalConsistency { get; set; }

        // Pattern synthesis
        public List<string> CrossTimeframePatterns { get; set; } = new List<string>();
        public InstitutionalFootprint InstitutionalFootprint { get; set; } = new InstitutionalFootprint();
        public OrderFlowImbalance OrderFlowImbalance { get; set; } = new OrderFlowImbalance();
    }

    /// <summary>
    /// Trend alignment across timeframes
    /// </summary>
    public class TrendAlignment
    {
        public decimal AlignmentScore { get; set; } // 0.0 to 1.0
        public int AlignedTimeframes { get; set; }
        public int TotalTimeframes { get; set; }
        public List<Timeframe> ConflictingTimeframes { get; set; } = new List<Timeframe>();
        public TrendDirection DominantDirection { get; set; }
        public decimal DirectionConfidence { get; set; }
    }

    /// <summary>
    /// Trend direction enumeration
    /// </summary>
    public enum TrendDirection
    {
        Bullish,
        Bearish,
        Neutral,
        Conflicted
    }

    /// <summary>
    /// Synthesized signal from multi-timeframe analysis
    /// </summary>
    public class SynthesizedSignal
    {
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public SignalType Type { get; set; }
        public decimal Strength { get; set; } // 0.0 to 1.0
        public decimal Confidence { get; set; } // 0.0 to 1.0
        public SignalQuality Quality { get; set; }

        // Multi-timeframe components
        public Dictionary<Timeframe, decimal> TimeframeContributions { get; set; } = new Dictionary<Timeframe, decimal>();
        public List<string> SupportingFactors { get; set; } = new List<string>();
        public List<string> ConflictingFactors { get; set; } = new List<string>();

        // Signal persistence
        public TimeSpan Duration { get; set; }
        public decimal Momentum { get; set; }
        public bool IsPersistent { get; set; }

        // Pattern support
        public List<string> SupportingPatterns { get; set; } = new List<string>();
        public string PrimaryPattern { get; set; } = "";
    }

    /// <summary>
    /// Signal consistency across timeframes
    /// </summary>
    public enum SignalConsistency
    {
        FullyAligned,      // All timeframes agree
        MostlyAligned,     // 75%+ timeframes agree
        PartiallyAligned,  // 50-75% timeframes agree
        Conflicted,        // <50% timeframes agree
        NoSignal          // No clear signals
    }

    /// <summary>
    /// Multi-timeframe pattern detection
    /// </summary>
    public class MultiTimeframePattern
    {
        public string PatternName { get; set; } = "";
        public PatternType Type { get; set; }
        public decimal Confidence { get; set; }
        public List<Timeframe> DetectedTimeframes { get; set; } = new List<Timeframe>();
        public DateTime FirstDetected { get; set; }
        public DateTime LastConfirmed { get; set; }
        public PatternPhase CurrentPhase { get; set; }
        public string Description { get; set; } = "";
    }

    /// <summary>
    /// Pattern type enumeration
    /// </summary>
    public enum PatternType
    {
        Accumulation,
        Distribution,
        InstitutionalFootprint,
        MarketMakerActivity,
        RetailFlow,
        OrderFlowImbalance,
        VolumeCluster,
        DeltaDivergence
    }

    /// <summary>
    /// Pattern phase enumeration
    /// </summary>
    public enum PatternPhase
    {
        Formation,
        Development,
        Maturation,
        Completion,
        Breakdown
    }

    /// <summary>
    /// Institutional footprint analysis
    /// </summary>
    public class InstitutionalFootprint
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        public bool IsDetected { get; set; }
        public decimal ActivityLevel { get; set; } // 0.0 to 1.0
        public decimal Confidence { get; set; }

        // Cross-timeframe analysis
        public Dictionary<Timeframe, decimal> TimeframeActivity { get; set; } = new Dictionary<Timeframe, decimal>();
        public List<string> DetectedPatterns { get; set; } = new List<string>();

        // Activity characteristics
        public InstitutionalActivityType ActivityType { get; set; }
        public decimal StealthLevel { get; set; } // How hidden the activity is
        public decimal VolumeImpact { get; set; }
        public decimal PriceImpact { get; set; }

        // Timing analysis
        public TimeSpan Duration { get; set; }
        public bool IsOngoing { get; set; }
        public DateTime EstimatedCompletion { get; set; }
    }

    /// <summary>
    /// Institutional activity type
    /// </summary>
    public enum InstitutionalActivityType
    {
        Accumulation,
        Distribution,
        Absorption,
        StealthTrading,
        LiquidityProvision,
        MarketMaking,
        Unknown
    }

    /// <summary>
    /// Order flow imbalance analysis
    /// </summary>
    public class OrderFlowImbalance
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        public decimal ImbalanceRatio { get; set; } // -1.0 to 1.0 (sell to buy pressure)
        public decimal Magnitude { get; set; } // 0.0 to 1.0
        public decimal Persistence { get; set; } // How long the imbalance has lasted

        // Cross-timeframe validation
        public Dictionary<Timeframe, decimal> TimeframeImbalances { get; set; } = new Dictionary<Timeframe, decimal>();
        public bool IsCrossTimeframeConfirmed { get; set; }
        public decimal ValidationScore { get; set; }

        // Imbalance characteristics
        public OrderFlowDirection Direction { get; set; }
        public OrderFlowIntensity Intensity { get; set; }
        public bool IsAccelerating { get; set; }
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// Order flow direction
    /// </summary>
    public enum OrderFlowDirection
    {
        BuyPressure,
        SellPressure,
        Balanced,
        Erratic
    }

    /// <summary>
    /// Order flow intensity
    /// </summary>
    public enum OrderFlowIntensity
    {
        Low,
        Moderate,
        High,
        Extreme
    }

    /// <summary>
    /// Multi-timeframe performance metrics
    /// </summary>
    public class MultiTimeframePerformanceMetrics
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;

        // Analysis timing per timeframe
        public Dictionary<Timeframe, TimeSpan> TimeframeAnalysisTimes { get; set; } = new Dictionary<Timeframe, TimeSpan>();
        public TimeSpan TotalSynthesisTime { get; set; }
        public TimeSpan TotalAnalysisTime { get; set; }

        // Signal quality metrics
        public decimal SignalAccuracy { get; set; }
        public decimal FalsePositiveRate { get; set; }
        public decimal SignalPersistence { get; set; }
        public int TotalSignalsGenerated { get; set; }
        public int SuccessfulSignals { get; set; }

        // Pattern detection metrics
        public int PatternsDetected { get; set; }
        public int PatternsConfirmed { get; set; }
        public decimal PatternAccuracy { get; set; }

        // Performance thresholds
        public double MaxAcceptableAnalysisTimeMs { get; set; } = 200; // Higher for multi-timeframe
        public decimal MinAcceptableSignalAccuracy { get; set; } = 0.6m;
        public decimal MaxAcceptableFalsePositiveRate { get; set; } = 0.3m;
    }

    #endregion

    #region Phase 4: Configuration and Supporting Models

    /// <summary>
    /// Multi-timeframe analysis configuration
    /// </summary>
    public class MultiTimeframeConfig
    {
        // Timeframe weights for synthesis
        public Dictionary<Timeframe, decimal> TimeframeWeights { get; set; } = new Dictionary<Timeframe, decimal>
        {
            { Timeframe.M1, 0.1m },   // Short-term noise filtering
            { Timeframe.M5, 0.3m },   // Primary execution timeframe
            { Timeframe.M15, 0.4m },  // Main trend confirmation
            { Timeframe.H1, 0.2m }    // Long-term context
        };

        // Analysis windows per timeframe
        public Dictionary<Timeframe, int> AnalysisWindows { get; set; } = new Dictionary<Timeframe, int>
        {
            { Timeframe.M1, 60 },     // 1 hour of 1m bars
            { Timeframe.M5, 48 },     // 4 hours of 5m bars
            { Timeframe.M15, 32 },    // 8 hours of 15m bars
            { Timeframe.H1, 24 }      // 24 hours of 1h bars
        };

        // Trend alignment thresholds
        public decimal MinTrendAlignmentScore { get; set; } = 0.7m;
        public int MinAlignedTimeframes { get; set; } = 3;

        // Pattern detection settings
        public bool EnablePatternDetection { get; set; } = true;
        public decimal MinPatternConfidence { get; set; } = 0.6m;
        public TimeSpan PatternValidationWindow { get; set; } = TimeSpan.FromMinutes(30);

        // Performance settings
        public bool EnableParallelProcessing { get; set; } = true;
        public TimeSpan MaxAnalysisTime { get; set; } = TimeSpan.FromMilliseconds(200);
    }

    /// <summary>
    /// Signal synthesis configuration
    /// </summary>
    public class SignalSynthesisConfig
    {
        // Signal weighting factors
        public decimal VolumeAnalysisWeight { get; set; } = 0.3m;
        public decimal DeltaAnalysisWeight { get; set; } = 0.3m;
        public decimal VolatilityAnalysisWeight { get; set; } = 0.2m;
        public decimal MarketProfileWeight { get; set; } = 0.2m;

        // Timeframe weights for synthesis
        public Dictionary<Timeframe, decimal> TimeframeWeights { get; set; } = new Dictionary<Timeframe, decimal>
        {
            { Timeframe.M1, 0.1m },
            { Timeframe.M5, 0.3m },
            { Timeframe.M15, 0.4m },
            { Timeframe.H1, 0.2m }
        };

        // Conflict resolution settings
        public ConflictResolutionStrategy ConflictResolution { get; set; } = ConflictResolutionStrategy.WeightedConsensus;
        public decimal MinConsensusThreshold { get; set; } = 0.6m;

        // Signal persistence settings
        public TimeSpan MinSignalDuration { get; set; } = TimeSpan.FromMinutes(5);
        public decimal MinMomentumThreshold { get; set; } = 0.5m;
        public int SignalHistorySize { get; set; } = 100;

        // Microstructure filter settings
        public bool EnableMicrostructureFilter { get; set; } = true;
        public decimal MinVolumeConfidence { get; set; } = 0.5m;
        public decimal MinDeltaConfidence { get; set; } = 0.5m;
        public decimal MinVolatilityConfidence { get; set; } = 0.4m;
    }

    /// <summary>
    /// Conflict resolution strategy enumeration
    /// </summary>
    public enum ConflictResolutionStrategy
    {
        WeightedConsensus,    // Use timeframe weights to resolve
        HigherTimeframeBias,  // Prefer higher timeframes
        HighestConfidence,    // Use signal with highest confidence
        MajorityRule,         // Use most common signal
        NoSignal             // Don't generate signal on conflict
    }

    /// <summary>
    /// Pattern recognition configuration
    /// </summary>
    public class PatternRecognitionConfig
    {
        // Volume pattern settings
        public decimal AccumulationThreshold { get; set; } = 0.7m;
        public decimal DistributionThreshold { get; set; } = 0.7m;
        public TimeSpan MinPatternDuration { get; set; } = TimeSpan.FromMinutes(15);

        // Institutional detection settings
        public decimal InstitutionalVolumeThreshold { get; set; } = 2.0m;
        public decimal StealthTradingThreshold { get; set; } = 0.8m;
        public decimal MinInstitutionalConfidence { get; set; } = 0.6m;

        // Order flow settings
        public decimal OrderFlowImbalanceThreshold { get; set; } = 0.6m;
        public TimeSpan OrderFlowValidationWindow { get; set; } = TimeSpan.FromMinutes(10);
        public int MinCrossTimeframeConfirmations { get; set; } = 2;

        // Performance settings
        public int MaxPatternsToTrack { get; set; } = 50;
        public TimeSpan PatternExpirationTime { get; set; } = TimeSpan.FromHours(4);
    }

    /// <summary>
    /// Signal correlation analysis result
    /// </summary>
    public class SignalCorrelationAnalysis
    {
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public decimal OverallCorrelation { get; set; } // -1.0 to 1.0
        public Dictionary<Timeframe, decimal> TimeframeCorrelations { get; set; } = new Dictionary<Timeframe, decimal>();
        public SignalConsistency Consistency { get; set; }
        public List<string> CorrelationFactors { get; set; } = new List<string>();
        public bool HasSignificantDivergence { get; set; }
        public List<Timeframe> DivergentTimeframes { get; set; } = new List<Timeframe>();
    }

    /// <summary>
    /// Signal persistence analysis result
    /// </summary>
    public class SignalPersistenceAnalysis
    {
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public TimeSpan SignalAge { get; set; }
        public decimal PersistenceScore { get; set; } // 0.0 to 1.0
        public decimal MomentumScore { get; set; } // 0.0 to 1.0
        public bool IsPersistent { get; set; }
        public bool IsAccelerating { get; set; }
        public bool IsDecaying { get; set; }
        public List<string> PersistenceFactors { get; set; } = new List<string>();
        public TimeSpan EstimatedRemainingDuration { get; set; }
    }

    /// <summary>
    /// Signal synthesis performance metrics
    /// </summary>
    public class SignalSynthesisMetrics
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        public int TotalSignalsSynthesized { get; set; }
        public int SuccessfulSyntheses { get; set; }
        public int ConflictResolutions { get; set; }
        public int FilteredSignals { get; set; }
        public decimal AverageSignalQuality { get; set; }
        public decimal AverageSynthesisTime { get; set; }
        public Dictionary<ConflictResolutionStrategy, int> ResolutionStrategyUsage { get; set; } = new Dictionary<ConflictResolutionStrategy, int>();
    }

    /// <summary>
    /// Volume pattern for accumulation/distribution analysis
    /// </summary>
    public class VolumePattern
    {
        public string PatternName { get; set; } = "";
        public PatternType Type { get; set; }
        public decimal Confidence { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public PatternPhase Phase { get; set; }
        public List<Timeframe> ConfirmingTimeframes { get; set; } = new List<Timeframe>();
        public decimal VolumeAccumulation { get; set; }
        public decimal PriceImpact { get; set; }
        public string Description { get; set; } = "";
    }

    /// <summary>
    /// Market participant analysis result
    /// </summary>
    public class MarketParticipantAnalysis
    {
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public decimal InstitutionalActivity { get; set; } // 0.0 to 1.0
        public decimal RetailActivity { get; set; } // 0.0 to 1.0
        public decimal MarketMakerActivity { get; set; } // 0.0 to 1.0
        public ParticipantDominance Dominance { get; set; }
        public Dictionary<Timeframe, ParticipantBreakdown> TimeframeBreakdown { get; set; } = new Dictionary<Timeframe, ParticipantBreakdown>();
        public List<string> ActivityIndicators { get; set; } = new List<string>();
    }

    /// <summary>
    /// Market participant dominance
    /// </summary>
    public enum ParticipantDominance
    {
        Institutional,
        Retail,
        MarketMaker,
        Balanced,
        Unknown
    }

    /// <summary>
    /// Participant breakdown per timeframe
    /// </summary>
    public class ParticipantBreakdown
    {
        public decimal InstitutionalPercentage { get; set; }
        public decimal RetailPercentage { get; set; }
        public decimal MarketMakerPercentage { get; set; }
        public decimal Confidence { get; set; }
    }

    /// <summary>
    /// Pattern validation result
    /// </summary>
    public class PatternValidationResult
    {
        public bool IsValid { get; set; }
        public decimal ValidationScore { get; set; } // 0.0 to 1.0
        public List<Timeframe> ValidatingTimeframes { get; set; } = new List<Timeframe>();
        public List<Timeframe> ConflictingTimeframes { get; set; } = new List<Timeframe>();
        public List<string> ValidationFactors { get; set; } = new List<string>();
        public List<string> ConflictingFactors { get; set; } = new List<string>();
        public DateTime ValidationTimestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Pattern recognition performance metrics
    /// </summary>
    public class PatternRecognitionMetrics
    {
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        public int TotalPatternsDetected { get; set; }
        public int ValidatedPatterns { get; set; }
        public int FalsePositives { get; set; }
        public decimal PatternAccuracy { get; set; }
        public decimal AverageDetectionTime { get; set; }
        public Dictionary<PatternType, int> PatternTypeFrequency { get; set; } = new Dictionary<PatternType, int>();
        public Dictionary<PatternType, decimal> PatternTypeAccuracy { get; set; } = new Dictionary<PatternType, decimal>();
    }

    #endregion
}
