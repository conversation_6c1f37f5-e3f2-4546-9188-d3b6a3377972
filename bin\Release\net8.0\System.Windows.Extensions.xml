﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Windows.Extensions</name>
  </assembly>
  <members>
    <member name="T:System.Media.SoundPlayer">
      <summary>Controls playback of a sound from a .wav file.</summary>
    </member>
    <member name="E:System.Media.SoundPlayer.LoadCompleted">
      <summary>Occurs when a .wav file has been successfully or unsuccessfully loaded.</summary>
    </member>
    <member name="E:System.Media.SoundPlayer.SoundLocationChanged">
      <summary>Occurs when a new audio source path for this <see cref="T:System.Media.SoundPlayer" /> has been set.</summary>
    </member>
    <member name="E:System.Media.SoundPlayer.StreamChanged">
      <summary>Occurs when a new <see cref="T:System.IO.Stream" /> audio source for this <see cref="T:System.Media.SoundPlayer" /> has been set.</summary>
    </member>
    <member name="M:System.Media.SoundPlayer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Media.SoundPlayer" /> class.</summary>
    </member>
    <member name="M:System.Media.SoundPlayer.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.Media.SoundPlayer" /> class, and attaches the .wav file within the specified <see cref="T:System.IO.Stream" />.</summary>
      <param name="stream">A <see cref="T:System.IO.Stream" /> to a .wav file.</param>
    </member>
    <member name="M:System.Media.SoundPlayer.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Media.SoundPlayer" /> class.</summary>
      <param name="serializationInfo">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to be used for deserialization.</param>
      <param name="context">The destination to be used for deserialization.</param>
      <exception cref="T:System.UriFormatException">The <see cref="P:System.Media.SoundPlayer.SoundLocation" /> specified in <paramref name="serializationInfo" /> cannot be resolved.</exception>
    </member>
    <member name="M:System.Media.SoundPlayer.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Media.SoundPlayer" /> class, and attaches the specified .wav file.</summary>
      <param name="soundLocation">The location of a .wav file to load.</param>
      <exception cref="T:System.UriFormatException">The URL value specified by <paramref name="soundLocation" /> cannot be resolved.</exception>
    </member>
    <member name="M:System.Media.SoundPlayer.Load">
      <summary>Loads a sound synchronously.</summary>
      <exception cref="T:System.ServiceProcess.TimeoutException">The elapsed time during loading exceeds the time, in milliseconds, specified by <see cref="P:System.Media.SoundPlayer.LoadTimeout" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <see cref="P:System.Media.SoundPlayer.SoundLocation" /> cannot be found.</exception>
    </member>
    <member name="M:System.Media.SoundPlayer.LoadAsync">
      <summary>Loads a .wav file from a stream or a Web resource using a new thread.</summary>
      <exception cref="T:System.ServiceProcess.TimeoutException">The elapsed time during loading exceeds the time, in milliseconds, specified by <see cref="P:System.Media.SoundPlayer.LoadTimeout" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <see cref="P:System.Media.SoundPlayer.SoundLocation" /> cannot be found.</exception>
    </member>
    <member name="M:System.Media.SoundPlayer.OnLoadCompleted(System.ComponentModel.AsyncCompletedEventArgs)">
      <summary>Raises the <see cref="E:System.Media.SoundPlayer.LoadCompleted" /> event.</summary>
      <param name="e">An <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.Media.SoundPlayer.OnSoundLocationChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:System.Media.SoundPlayer.SoundLocationChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.Media.SoundPlayer.OnStreamChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:System.Media.SoundPlayer.StreamChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:System.Media.SoundPlayer.Play">
      <summary>Plays the .wav file using a new thread, and loads the .wav file first if it has not been loaded.</summary>
      <exception cref="T:System.ServiceProcess.TimeoutException">The elapsed time during loading exceeds the time, in milliseconds, specified by <see cref="P:System.Media.SoundPlayer.LoadTimeout" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <see cref="P:System.Media.SoundPlayer.SoundLocation" /> cannot be found.</exception>
      <exception cref="T:System.InvalidOperationException">The .wav header is corrupted; the file specified by <see cref="P:System.Media.SoundPlayer.SoundLocation" /> is not a PCM .wav file.</exception>
    </member>
    <member name="M:System.Media.SoundPlayer.PlayLooping">
      <summary>Plays and loops the .wav file using a new thread, and loads the .wav file first if it has not been loaded.</summary>
      <exception cref="T:System.ServiceProcess.TimeoutException">The elapsed time during loading exceeds the time, in milliseconds, specified by <see cref="P:System.Media.SoundPlayer.LoadTimeout" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <see cref="P:System.Media.SoundPlayer.SoundLocation" /> cannot be found.</exception>
      <exception cref="T:System.InvalidOperationException">The .wav header is corrupted; the file specified by <see cref="P:System.Media.SoundPlayer.SoundLocation" /> is not a PCM .wav file.</exception>
    </member>
    <member name="M:System.Media.SoundPlayer.PlaySync">
      <summary>Plays the .wav file and loads the .wav file first if it has not been loaded.</summary>
      <exception cref="T:System.ServiceProcess.TimeoutException">The elapsed time during loading exceeds the time, in milliseconds, specified by <see cref="P:System.Media.SoundPlayer.LoadTimeout" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified by <see cref="P:System.Media.SoundPlayer.SoundLocation" /> cannot be found.</exception>
      <exception cref="T:System.InvalidOperationException">The .wav header is corrupted; the file specified by <see cref="P:System.Media.SoundPlayer.SoundLocation" /> is not a PCM .wav file.</exception>
    </member>
    <member name="M:System.Media.SoundPlayer.Stop">
      <summary>Stops playback of the sound if playback is occurring.</summary>
    </member>
    <member name="M:System.Media.SoundPlayer.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>For a description of this member, see the <see cref="M:System.Runtime.Serialization.ISerializable.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> method.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext" />) for this serialization.</param>
    </member>
    <member name="P:System.Media.SoundPlayer.IsLoadCompleted">
      <summary>Gets a value indicating whether loading of a .wav file has successfully completed.</summary>
      <returns>
        <see langword="true" /> if a .wav file is loaded; <see langword="false" /> if a .wav file has not yet been loaded.</returns>
    </member>
    <member name="P:System.Media.SoundPlayer.LoadTimeout">
      <summary>Gets or sets the time, in milliseconds, in which the .wav file must load.</summary>
      <returns>The number of milliseconds to wait. The default is 10000 (10 seconds).</returns>
    </member>
    <member name="P:System.Media.SoundPlayer.SoundLocation">
      <summary>Gets or sets the file path or URL of the .wav file to load.</summary>
      <returns>The file path or URL from which to load a .wav file, or <see cref="F:System.String.Empty" /> if no file path is present. The default is <see cref="F:System.String.Empty" />.</returns>
    </member>
    <member name="P:System.Media.SoundPlayer.Stream">
      <summary>Gets or sets the <see cref="T:System.IO.Stream" /> from which to load the .wav file.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> from which to load the .wav file, or <see langword="null" /> if no stream is available. The default is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Media.SoundPlayer.Tag">
      <summary>Gets or sets the <see cref="T:System.Object" /> that contains data about the <see cref="T:System.Media.SoundPlayer" />.</summary>
      <returns>An <see cref="T:System.Object" /> that contains data about the <see cref="T:System.Media.SoundPlayer" />.</returns>
    </member>
    <member name="T:System.Media.SystemSound">
      <summary>Represents a system sound type.</summary>
    </member>
    <member name="M:System.Media.SystemSound.Play">
      <summary>Plays the system sound type.</summary>
    </member>
    <member name="T:System.Media.SystemSounds">
      <summary>Retrieves sounds associated with a set of Windows operating system sound-event types. This class cannot be inherited.</summary>
    </member>
    <member name="P:System.Media.SystemSounds.Asterisk">
      <summary>Gets the sound associated with the <see langword="Asterisk" /> program event in the current Windows sound scheme.</summary>
      <returns>A <see cref="T:System.Media.SystemSound" /> associated with the <see langword="Asterisk" /> program event in the current Windows sound scheme.</returns>
    </member>
    <member name="P:System.Media.SystemSounds.Beep">
      <summary>Gets the sound associated with the <see langword="Beep" /> program event in the current Windows sound scheme.</summary>
      <returns>A <see cref="T:System.Media.SystemSound" /> associated with the <see langword="Beep" /> program event in the current Windows sound scheme.</returns>
    </member>
    <member name="P:System.Media.SystemSounds.Exclamation">
      <summary>Gets the sound associated with the <see langword="Exclamation" /> program event in the current Windows sound scheme.</summary>
      <returns>A <see cref="T:System.Media.SystemSound" /> associated with the <see langword="Exclamation" /> program event in the current Windows sound scheme.</returns>
    </member>
    <member name="P:System.Media.SystemSounds.Hand">
      <summary>Gets the sound associated with the <see langword="Hand" /> program event in the current Windows sound scheme.</summary>
      <returns>A <see cref="T:System.Media.SystemSound" /> associated with the <see langword="Hand" /> program event in the current Windows sound scheme.</returns>
    </member>
    <member name="P:System.Media.SystemSounds.Question">
      <summary>Gets the sound associated with the <see langword="Question" /> program event in the current Windows sound scheme.</summary>
      <returns>A <see cref="T:System.Media.SystemSound" /> associated with the <see langword="Question" /> program event in the current Windows sound scheme.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2UI">
      <summary>Displays user interface dialogs that allow you to select and view X.509 certificates. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2UI.#ctor">
      <summary>Initializes an instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2UI" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2UI.DisplayCertificate(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Displays a dialog box that contains the properties of an X.509 certificate and its associated certificate chain.</summary>
      <param name="certificate">The X.509 certificate to display.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificate" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="certificate" /> parameter is invalid.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2UI.DisplayCertificate(System.Security.Cryptography.X509Certificates.X509Certificate2,System.IntPtr)">
      <summary>Displays a dialog box that contains the properties of an X.509 certificate and its associated certificate chain using a handle to a parent window.</summary>
      <param name="certificate">The X.509 certificate to display.</param>
      <param name="hwndParent">A handle to the parent window to use for the display dialog.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificate" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="certificate" /> parameter is invalid.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2UI.SelectFromCollection(System.Security.Cryptography.X509Certificates.X509Certificate2Collection,System.String,System.String,System.Security.Cryptography.X509Certificates.X509SelectionFlag)">
      <summary>Displays a dialog box for selecting an X.509 certificate from a certificate collection.</summary>
      <param name="certificates">A collection of X.509 certificates to select from.</param>
      <param name="title">The title of the dialog box.</param>
      <param name="message">A descriptive message to guide the user.  The message is displayed in the dialog box.</param>
      <param name="selectionFlag">One of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SelectionFlag" /> values that specifies whether single or multiple selections are allowed.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="selectionFlag" /> parameter is not a valid flag.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificates" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="certificates" /> parameter is invalid.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object that contains the selected certificate or certificates.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2UI.SelectFromCollection(System.Security.Cryptography.X509Certificates.X509Certificate2Collection,System.String,System.String,System.Security.Cryptography.X509Certificates.X509SelectionFlag,System.IntPtr)">
      <summary>Displays a dialog box for selecting an X.509 certificate from a certificate collection using a handle to a parent window.</summary>
      <param name="certificates">A collection of X.509 certificates to select from.</param>
      <param name="title">The title of the dialog box.</param>
      <param name="message">A descriptive message to guide the user.  The message is displayed in the dialog box.</param>
      <param name="selectionFlag">One of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SelectionFlag" /> values that specifies whether single or multiple selections are allowed.</param>
      <param name="hwndParent">A handle to the parent window to use for the display dialog box.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="selectionFlag" /> parameter is not a valid flag.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificates" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="certificates" /> parameter is invalid.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object that contains the selected certificate or certificates.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SelectionFlag">
      <summary>Specifies the type of selection requested using the <see cref="Overload:System.Security.Cryptography.X509Certificates.X509Certificate2UI.SelectFromCollection" /> method.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SelectionFlag.MultiSelection">
      <summary>A multiple selection. The user can use the SHIFT or CRTL keys to select more than one X.509 certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SelectionFlag.SingleSelection">
      <summary>A single selection. The UI allows the user to select one X.509 certificate.</summary>
    </member>
    <member name="T:System.Xaml.Permissions.XamlAccessLevel">
      <summary>Defines access control to assemblies and types for purposes of XAML loading.</summary>
    </member>
    <member name="M:System.Xaml.Permissions.XamlAccessLevel.AssemblyAccessTo(System.Reflection.Assembly)">
      <summary>Returns a <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> instance based on an assembly requested by <see cref="T:System.Reflection.Assembly" />.</summary>
      <param name="assembly">The assembly for the request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="assembly" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> instance, with the <paramref name="assembly" /> value used as source information for <see cref="P:System.Xaml.Permissions.XamlAccessLevel.AssemblyAccessToAssemblyName" />.</returns>
    </member>
    <member name="M:System.Xaml.Permissions.XamlAccessLevel.AssemblyAccessTo(System.Reflection.AssemblyName)">
      <summary>Returns a <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> instance based on an assembly requested by <see cref="T:System.Reflection.AssemblyName" />.</summary>
      <param name="assemblyName">The assembly name for the request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="assemblyName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="assemblyName" /> is not a valid fully qualified assembly name.</exception>
      <returns>A <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> instance, with the <paramref name="assemblyName" /> value used as source information for <see cref="P:System.Xaml.Permissions.XamlAccessLevel.AssemblyAccessToAssemblyName" />.</returns>
    </member>
    <member name="M:System.Xaml.Permissions.XamlAccessLevel.PrivateAccessTo(System.String)">
      <summary>Returns a <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> instance based on a specific type specified by its qualified name.</summary>
      <param name="assemblyQualifiedTypeName">A string that is parsed as an assembly-qualified type name.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="assemblyQualifiedTypeName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="assemblyQualifiedTypeName" /> is not a valid assembly-qualified type name.  
  
 -or-  
  
 Assembly name within <paramref name="assemblyQualifiedTypeName" /> is not a valid assembly name.</exception>
      <returns>A <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> instance, with the <paramref name="assemblyQualifiedTypeName" /> value used as source information for <see cref="P:System.Xaml.Permissions.XamlAccessLevel.PrivateAccessToTypeName" /> and <see cref="P:System.Xaml.Permissions.XamlAccessLevel.AssemblyAccessToAssemblyName" /> determined by reflection on the type.</returns>
    </member>
    <member name="M:System.Xaml.Permissions.XamlAccessLevel.PrivateAccessTo(System.Type)">
      <summary>Returns a <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> instance based on a specific type specified by <see cref="T:System.Type" />.</summary>
      <param name="type">The <see cref="T:System.Type" /> to request access for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> instance, with the <paramref name="type" /> value used as source information for <see cref="P:System.Xaml.Permissions.XamlAccessLevel.PrivateAccessToTypeName" /> and <see cref="P:System.Xaml.Permissions.XamlAccessLevel.AssemblyAccessToAssemblyName" /> determined by reflection on the type.</returns>
    </member>
    <member name="P:System.Xaml.Permissions.XamlAccessLevel.AssemblyAccessToAssemblyName">
      <summary>Gets the <see cref="T:System.Reflection.AssemblyName" /> of the assembly for which this <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> determines permissions.</summary>
      <returns>The <see cref="T:System.Reflection.AssemblyName" /> of the assembly for which this <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> determines permissions.</returns>
    </member>
    <member name="P:System.Xaml.Permissions.XamlAccessLevel.PrivateAccessToTypeName">
      <summary>Gets the unqualified string name of the type for which this <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> determines permissions.</summary>
      <returns>The unqualified string name of the type for which this <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> determines permissions.</returns>
    </member>
  </members>
</doc>