using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models.Performance;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;

namespace SmartVolumeStrategy.Core.Performance
{
    /// <summary>
    /// Phase 3.3: Configurable Analysis Interval Manager
    /// Intelligent interval switching based on market conditions and performance metrics
    /// </summary>
    public class ConfigurableAnalysisIntervalManager
    {
        private readonly Action<string> _logAction;
        private readonly AnalysisIntervalConfig _config;
        private readonly Queue<SmartVolumeStrategy.Core.Models.Performance.PerformanceMetrics> _performanceHistory;
        private readonly Dictionary<AnalysisInterval, Queue<TimeSpan>> _intervalPerformanceHistory;
        private readonly object _lockObject = new object();
        
        // Circuit breaker integration
        private CircuitBreakerManager _circuitBreakerManager;
        
        // Current state
        private AnalysisInterval _currentInterval;
        private DateTime _lastIntervalChange;
        private DateTime _lastPerformanceEvaluation;
        private int _intervalChangeCount;
        
        // Configuration
        private const int MAX_PERFORMANCE_HISTORY = 200;
        private const int INTERVAL_PERFORMANCE_HISTORY_SIZE = 50;
        private static readonly TimeSpan MIN_INTERVAL_DURATION = TimeSpan.FromMinutes(1);
        private static readonly TimeSpan PERFORMANCE_EVALUATION_INTERVAL = TimeSpan.FromSeconds(30);

        public ConfigurableAnalysisIntervalManager(AnalysisIntervalConfig config = null, Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _config = config ?? new AnalysisIntervalConfig();
            _performanceHistory = new Queue<SmartVolumeStrategy.Core.Models.Performance.PerformanceMetrics>();
            _intervalPerformanceHistory = new Dictionary<AnalysisInterval, Queue<TimeSpan>>();
            
            _currentInterval = _config.DefaultInterval;
            _lastIntervalChange = DateTime.UtcNow;
            _lastPerformanceEvaluation = DateTime.UtcNow;
            
            InitializeIntervalHistory();
        }

        /// <summary>
        /// Set circuit breaker manager for fault tolerance
        /// </summary>
        public void SetCircuitBreakerManager(CircuitBreakerManager circuitBreakerManager)
        {
            _circuitBreakerManager = circuitBreakerManager;
        }

        /// <summary>
        /// Get current analysis interval with intelligent switching
        /// </summary>
        public AnalysisInterval GetCurrentInterval(MarketContext marketContext, SmartVolumeStrategy.Core.Models.Performance.PerformanceMetrics currentPerformance = null)
        {
            lock (_lockObject)
            {
                try
                {
                    // Phase 3.3: Check circuit breaker status
                    if (_circuitBreakerManager != null && !_circuitBreakerManager.IsOperationAllowed(StrategyComponent.MultiTimeframeAnalysis))
                    {
                        _logAction("🔴 Analysis interval management blocked by circuit breaker");
                        return _config.DefaultInterval;
                    }

                    // Check degradation level
                    if (_circuitBreakerManager != null)
                    {
                        var degradationLevel = _circuitBreakerManager.GetDegradationManager().GetCurrentDegradationLevel();
                        if (degradationLevel >= DegradationLevel.Minimal)
                        {
                            _logAction("⚠️ Analysis interval management running in degraded mode");
                            return GetDegradedInterval(degradationLevel);
                        }
                    }

                    // Add current performance to history
                    if (currentPerformance != null)
                    {
                        UpdatePerformanceHistory(currentPerformance);
                    }

                    // Evaluate interval switching if enabled and enough time has passed
                    if (_config.EnableIntelligentSwitching && ShouldEvaluateInterval())
                    {
                        var optimalInterval = DetermineOptimalInterval(marketContext);
                        
                        if (optimalInterval != _currentInterval && CanChangeInterval())
                        {
                            ChangeInterval(optimalInterval, marketContext);
                        }
                    }

                    return _currentInterval;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Analysis interval management error: {ex.Message}");
                    
                    // Record failure with circuit breaker
                    if (_circuitBreakerManager != null)
                    {
                        _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, false, ex.Message, FailureSeverity.Low);
                    }
                    
                    return _config.DefaultInterval;
                }
            }
        }

        /// <summary>
        /// Force interval change (for testing or manual override)
        /// </summary>
        public void ForceIntervalChange(AnalysisInterval newInterval, string reason)
        {
            lock (_lockObject)
            {
                _logAction($"🔧 Forcing interval change to {newInterval}: {reason}");
                _currentInterval = newInterval;
                _lastIntervalChange = DateTime.UtcNow;
                _intervalChangeCount++;
            }
        }

        /// <summary>
        /// Get interval management statistics
        /// </summary>
        public AnalysisIntervalStatistics GetIntervalStatistics()
        {
            lock (_lockObject)
            {
                var stats = new AnalysisIntervalStatistics
                {
                    CurrentInterval = _currentInterval,
                    LastIntervalChange = _lastIntervalChange,
                    IntervalChangeCount = _intervalChangeCount,
                    TotalPerformanceMetrics = _performanceHistory.Count
                };

                if (_performanceHistory.Count > 0)
                {
                    stats.AverageResponseTime = TimeSpan.FromMilliseconds(_performanceHistory.Average(p => p.ResponseTime.TotalMilliseconds));
                    stats.SuccessRate = (decimal)_performanceHistory.Count(p => p.Success) / _performanceHistory.Count;
                }

                // Interval performance breakdown
                foreach (var kvp in _intervalPerformanceHistory)
                {
                    if (kvp.Value.Count > 0)
                    {
                        stats.IntervalPerformance[kvp.Key] = TimeSpan.FromMilliseconds(kvp.Value.Average(t => t.TotalMilliseconds));
                    }
                }

                return stats;
            }
        }

        /// <summary>
        /// Determine optimal interval based on market conditions and performance
        /// </summary>
        private AnalysisInterval DetermineOptimalInterval(MarketContext marketContext)
        {
            var scores = new Dictionary<AnalysisInterval, decimal>();

            // Score each interval based on multiple factors
            foreach (AnalysisInterval interval in Enum.GetValues<AnalysisInterval>())
            {
                var score = 0m;

                // Market condition scoring
                if (_config.EnableMarketConditionAdaptation)
                {
                    score += ScoreIntervalForMarketConditions(interval, marketContext);
                }

                // Performance-based scoring
                if (_config.EnablePerformanceBasedSwitching)
                {
                    score += ScoreIntervalForPerformance(interval);
                }

                // Resource utilization scoring
                score += ScoreIntervalForResourceUtilization(interval);

                scores[interval] = score;
            }

            // Return interval with highest score
            var optimalInterval = scores.OrderByDescending(kvp => kvp.Value).First().Key;
            
            _logAction($"📊 Interval scoring: {string.Join(", ", scores.Select(kvp => $"{kvp.Key}={kvp.Value:F2}"))}");
            
            return optimalInterval;
        }

        /// <summary>
        /// Score interval based on market conditions
        /// </summary>
        private decimal ScoreIntervalForMarketConditions(AnalysisInterval interval, MarketContext marketContext)
        {
            var score = 0m;

            // Volatility-based scoring
            if (_config.VolatilityIntervals.ContainsKey(marketContext.VolatilityRegime))
            {
                var preferredInterval = _config.VolatilityIntervals[marketContext.VolatilityRegime];
                if (interval == preferredInterval)
                {
                    score += 0.4m;
                }
                else
                {
                    // Penalty for deviation from preferred interval
                    var deviation = Math.Abs((int)interval - (int)preferredInterval);
                    score -= deviation * 0.1m;
                }
            }

            // Session-based scoring
            if (_config.SessionMultipliers.ContainsKey(marketContext.CurrentSession))
            {
                var sessionMultiplier = _config.SessionMultipliers[marketContext.CurrentSession];
                
                // Lower multiplier favors faster intervals
                if (sessionMultiplier < 1.0m)
                {
                    score += interval == AnalysisInterval.OneSecond ? 0.2m : 
                             interval == AnalysisInterval.FiveSeconds ? 0.1m : 0m;
                }
                else if (sessionMultiplier > 1.0m)
                {
                    score += interval == AnalysisInterval.FifteenSeconds ? 0.2m : 
                             interval == AnalysisInterval.FiveSeconds ? 0.1m : 0m;
                }
            }

            // Volume-based scoring
            var volumeScore = marketContext.CVDTrend / 100m; // Normalize CVD trend
            if (Math.Abs(volumeScore) > _config.HighVolumeThreshold)
            {
                score += interval == AnalysisInterval.OneSecond ? 0.3m : 
                         interval == AnalysisInterval.FiveSeconds ? 0.1m : -0.1m;
            }
            else if (Math.Abs(volumeScore) < _config.LowVolumeThreshold)
            {
                score += interval == AnalysisInterval.FifteenSeconds ? 0.3m : 
                         interval == AnalysisInterval.FiveSeconds ? 0.1m : -0.1m;
            }

            return Math.Max(0m, score);
        }

        /// <summary>
        /// Score interval based on performance metrics
        /// </summary>
        private decimal ScoreIntervalForPerformance(AnalysisInterval interval)
        {
            var score = 0m;

            if (_intervalPerformanceHistory.ContainsKey(interval) && _intervalPerformanceHistory[interval].Count > 0)
            {
                var avgResponseTime = TimeSpan.FromMilliseconds(_intervalPerformanceHistory[interval].Average(t => t.TotalMilliseconds));
                
                // Score based on response time performance
                if (avgResponseTime <= _config.MinResponseTimeThreshold)
                {
                    score += 0.3m; // Excellent performance
                }
                else if (avgResponseTime <= _config.MaxResponseTimeThreshold)
                {
                    score += 0.1m; // Acceptable performance
                }
                else
                {
                    score -= 0.2m; // Poor performance
                }
            }

            // Recent performance trend
            if (_performanceHistory.Count >= 10)
            {
                var recentMetrics = _performanceHistory.TakeLast(10).ToList();
                var recentSuccessRate = (decimal)recentMetrics.Count(p => p.Success) / recentMetrics.Count;
                
                if (recentSuccessRate >= 0.9m)
                {
                    score += 0.2m;
                }
                else if (recentSuccessRate < 0.7m)
                {
                    score -= 0.3m;
                }
            }

            return score;
        }

        /// <summary>
        /// Score interval based on resource utilization
        /// </summary>
        private decimal ScoreIntervalForResourceUtilization(AnalysisInterval interval)
        {
            var score = 0m;

            // Memory usage consideration
            if (_performanceHistory.Count > 0)
            {
                var avgMemoryUsage = _performanceHistory.Average(p => p.MemoryUsage);
                var memoryPressure = (decimal)avgMemoryUsage / (100 * 1024 * 1024m); // Normalize to 100MB baseline
                
                if (memoryPressure > 0.8m)
                {
                    // High memory pressure favors longer intervals
                    score += interval == AnalysisInterval.FifteenSeconds ? 0.3m : 
                             interval == AnalysisInterval.FiveSeconds ? 0.1m : -0.2m;
                }
                else if (memoryPressure < 0.3m)
                {
                    // Low memory pressure allows shorter intervals
                    score += interval == AnalysisInterval.OneSecond ? 0.2m : 
                             interval == AnalysisInterval.FiveSeconds ? 0.1m : 0m;
                }
            }

            // CPU utilization proxy (based on response times)
            if (_performanceHistory.Count >= 5)
            {
                var recentResponseTimes = _performanceHistory.TakeLast(5).Select(p => p.ResponseTime).ToList();
                var avgResponseTime = TimeSpan.FromMilliseconds(recentResponseTimes.Average(t => t.TotalMilliseconds));
                
                if (avgResponseTime > _config.MaxResponseTimeThreshold)
                {
                    // High CPU load favors longer intervals
                    score += interval == AnalysisInterval.FifteenSeconds ? 0.4m : 
                             interval == AnalysisInterval.FiveSeconds ? 0.2m : -0.1m;
                }
            }

            return Math.Max(0m, score);
        }

        /// <summary>
        /// Check if interval can be changed (respects minimum duration)
        /// </summary>
        private bool CanChangeInterval()
        {
            var timeSinceLastChange = DateTime.UtcNow - _lastIntervalChange;
            return timeSinceLastChange >= MIN_INTERVAL_DURATION;
        }

        /// <summary>
        /// Check if interval should be evaluated
        /// </summary>
        private bool ShouldEvaluateInterval()
        {
            var timeSinceLastEvaluation = DateTime.UtcNow - _lastPerformanceEvaluation;
            return timeSinceLastEvaluation >= PERFORMANCE_EVALUATION_INTERVAL;
        }

        /// <summary>
        /// Change current interval
        /// </summary>
        private void ChangeInterval(AnalysisInterval newInterval, MarketContext marketContext)
        {
            var oldInterval = _currentInterval;
            _currentInterval = newInterval;
            _lastIntervalChange = DateTime.UtcNow;
            _intervalChangeCount++;
            
            _logAction($"⚡ Analysis interval changed: {oldInterval} → {newInterval} (Volatility: {marketContext.VolatilityRegime}, Session: {marketContext.CurrentSession})");
            
            // Record successful operation
            if (_circuitBreakerManager != null)
            {
                _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, true, $"Interval changed to {newInterval}");
            }
        }

        /// <summary>
        /// Get degraded interval based on degradation level
        /// </summary>
        private AnalysisInterval GetDegradedInterval(DegradationLevel degradationLevel)
        {
            return degradationLevel switch
            {
                DegradationLevel.Reduced => AnalysisInterval.FiveSeconds,
                DegradationLevel.Minimal => AnalysisInterval.FifteenSeconds,
                DegradationLevel.EmergencyStop => AnalysisInterval.FifteenSeconds,
                _ => _config.DefaultInterval
            };
        }

        /// <summary>
        /// Update performance history
        /// </summary>
        private void UpdatePerformanceHistory(SmartVolumeStrategy.Core.Models.Performance.PerformanceMetrics metrics)
        {
            _performanceHistory.Enqueue(metrics);
            
            while (_performanceHistory.Count > MAX_PERFORMANCE_HISTORY)
            {
                _performanceHistory.Dequeue();
            }

            // Update interval-specific performance history
            if (_intervalPerformanceHistory.ContainsKey(_currentInterval))
            {
                var intervalHistory = _intervalPerformanceHistory[_currentInterval];
                intervalHistory.Enqueue(metrics.ResponseTime);
                
                while (intervalHistory.Count > INTERVAL_PERFORMANCE_HISTORY_SIZE)
                {
                    intervalHistory.Dequeue();
                }
            }

            _lastPerformanceEvaluation = DateTime.UtcNow;
        }

        /// <summary>
        /// Initialize interval performance history
        /// </summary>
        private void InitializeIntervalHistory()
        {
            foreach (AnalysisInterval interval in Enum.GetValues<AnalysisInterval>())
            {
                _intervalPerformanceHistory[interval] = new Queue<TimeSpan>();
            }
        }
    }

    /// <summary>
    /// Analysis interval statistics
    /// </summary>
    public class AnalysisIntervalStatistics
    {
        public AnalysisInterval CurrentInterval { get; set; }
        public DateTime LastIntervalChange { get; set; }
        public int IntervalChangeCount { get; set; }
        public int TotalPerformanceMetrics { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public decimal SuccessRate { get; set; }
        public Dictionary<AnalysisInterval, TimeSpan> IntervalPerformance { get; set; } = new Dictionary<AnalysisInterval, TimeSpan>();
    }
}
