using System;
using System.Collections.Generic;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Models.Analysis
{
    /// <summary>
    /// Original Phase 2: Signal Persistence Validation Models
    /// Tracks signal consistency across multiple bars to reduce false breakouts
    /// </summary>
    
    /// <summary>
    /// Signal persistence validation result
    /// </summary>
    public class SignalPersistenceValidationResult
    {
        public bool IsValid { get; set; }
        public int ConsistentBars { get; set; }
        public int RequiredBars { get; set; }
        public decimal AverageConfidence { get; set; }
        public decimal ConfidenceStability { get; set; }
        public string ValidationReason { get; set; } = string.Empty;
        public List<string> PersistenceFactors { get; set; } = new List<string>();
        public List<HistoricalSignalData> SupportingSignals { get; set; } = new List<HistoricalSignalData>();
        public bool HasDirectionConsistency { get; set; }
        public bool HasStrengthConsistency { get; set; }
        public TimeSpan PersistenceDuration { get; set; }
    }

    /// <summary>
    /// Historical signal data for persistence tracking
    /// </summary>
    public class HistoricalSignalData
    {
        public DateTime Timestamp { get; set; }
        public SignalType Type { get; set; }
        public decimal Confidence { get; set; }
        public decimal Strength { get; set; }
        public SignalQuality Quality { get; set; }
        public decimal Price { get; set; }
        public string PrimaryReason { get; set; } = string.Empty;
        public int BarIndex { get; set; }
        public bool WasValidated { get; set; }
    }

    /// <summary>
    /// Signal persistence configuration
    /// </summary>
    public class SignalPersistenceConfig
    {
        public int MinimumPersistenceBars { get; set; } = 2;
        public int MaximumPersistenceBars { get; set; } = 5;
        public decimal MinimumConfidenceStability { get; set; } = 0.8m; // 80% confidence stability
        public decimal MinimumStrengthStability { get; set; } = 0.7m; // 70% strength stability
        public TimeSpan MaximumPersistenceWindow { get; set; } = TimeSpan.FromMinutes(15);
        public bool RequireDirectionConsistency { get; set; } = true;
        public bool RequireStrengthConsistency { get; set; } = true;
        public decimal ConfidenceDecayTolerance { get; set; } = 0.1m; // 10% confidence decay allowed
    }

    /// <summary>
    /// Original Phase 2: Enhanced Noise Filtering Models
    /// Detects market conditions and filters low-quality signals
    /// </summary>
    
    /// <summary>
    /// Market condition types
    /// </summary>
    public enum MarketCondition
    {
        Trending,           // Clear directional movement
        Ranging,            // Sideways/choppy movement
        Volatile,           // High volatility periods
        Consolidating,      // Low volatility consolidation
        Breakout,           // Breaking out of range
        Reversal,           // Potential trend reversal
        Unknown             // Cannot determine condition
    }

    /// <summary>
    /// Market noise level
    /// </summary>
    public enum NoiseLevel
    {
        Low,                // Clean, clear signals
        Moderate,           // Some noise but manageable
        High,               // Noisy, choppy conditions
        Extreme             // Very noisy, avoid trading
    }

    /// <summary>
    /// Enhanced noise filtering validation result
    /// </summary>
    public class NoiseFilteringValidationResult
    {
        public bool IsValid { get; set; }
        public MarketCondition MarketCondition { get; set; }
        public NoiseLevel NoiseLevel { get; set; }
        public decimal NoiseScore { get; set; }
        public decimal TrendStrength { get; set; }
        public decimal VolatilityScore { get; set; }
        public string ValidationReason { get; set; } = string.Empty;
        public List<string> FilteringFactors { get; set; } = new List<string>();
        public bool IsTrendingMarket { get; set; }
        public bool IsChoppyMarket { get; set; }
        public bool IsHighVolatility { get; set; }
        public decimal SignalToNoiseRatio { get; set; }
        public Dictionary<string, decimal> MarketMetrics { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// Market condition analysis data
    /// </summary>
    public class MarketConditionAnalysis
    {
        public DateTime Timestamp { get; set; }
        public MarketCondition Condition { get; set; }
        public NoiseLevel NoiseLevel { get; set; }
        public decimal TrendStrength { get; set; }
        public decimal VolatilityScore { get; set; }
        public decimal RangeEfficiency { get; set; }
        public decimal DirectionalMovement { get; set; }
        public decimal PriceStability { get; set; }
        public List<string> ConditionFactors { get; set; } = new List<string>();
        public Dictionary<string, decimal> TechnicalIndicators { get; set; } = new Dictionary<string, decimal>();
        public TimeSpan AnalysisWindow { get; set; }
        public int BarsAnalyzed { get; set; }
    }

    /// <summary>
    /// Enhanced noise filtering configuration
    /// </summary>
    public class NoiseFilteringConfig
    {
        public decimal MaximumNoiseScore { get; set; } = 0.7m; // 70% maximum noise tolerance
        public decimal MinimumTrendStrength { get; set; } = 0.3m; // 30% minimum trend strength
        public decimal MaximumVolatilityScore { get; set; } = 0.8m; // 80% maximum volatility
        public decimal MinimumSignalToNoiseRatio { get; set; } = 1.5m; // 1.5:1 minimum ratio
        public int MarketConditionLookback { get; set; } = 20; // 20 bars for condition analysis
        public bool FilterDuringHighVolatility { get; set; } = true;
        public bool FilterDuringChoppyMarkets { get; set; } = true;
        public bool FilterDuringLowTrend { get; set; } = true;
        public Dictionary<MarketCondition, decimal> ConditionThresholds { get; set; } = new Dictionary<MarketCondition, decimal>();
        public Dictionary<NoiseLevel, decimal> NoiseThresholds { get; set; } = new Dictionary<NoiseLevel, decimal>();
    }

    /// <summary>
    /// Volatility analysis data
    /// </summary>
    public class VolatilityAnalysisData
    {
        public decimal CurrentVolatility { get; set; }
        public decimal AverageVolatility { get; set; }
        public decimal VolatilityRatio { get; set; }
        public decimal PriceRange { get; set; }
        public decimal AverageTrueRange { get; set; }
        public bool IsHighVolatility { get; set; }
        public bool IsLowVolatility { get; set; }
        public List<decimal> VolatilityHistory { get; set; } = new List<decimal>();
        public TimeSpan AnalysisWindow { get; set; }
    }

    /// <summary>
    /// Trend analysis data
    /// </summary>
    public class TrendAnalysisData
    {
        public decimal TrendStrength { get; set; }
        public decimal TrendDirection { get; set; }
        public decimal TrendConsistency { get; set; }
        public bool IsUptrend { get; set; }
        public bool IsDowntrend { get; set; }
        public bool IsSideways { get; set; }
        public decimal MovingAverageSlope { get; set; }
        public decimal PriceMovementEfficiency { get; set; }
        public List<decimal> TrendHistory { get; set; } = new List<decimal>();
        public TimeSpan AnalysisWindow { get; set; }
    }

    /// <summary>
    /// Original Phase 2 validation statistics
    /// </summary>
    public class OriginalPhase2Statistics
    {
        public int TotalSignalsPersistenceChecked { get; set; }
        public int SignalsPersistenceValidated { get; set; }
        public int SignalsPersistenceRejected { get; set; }
        public decimal PersistenceValidationRate { get; set; }
        
        public int TotalSignalsNoiseFiltered { get; set; }
        public int SignalsNoiseValidated { get; set; }
        public int SignalsNoiseRejected { get; set; }
        public decimal NoiseFilteringRate { get; set; }
        
        public decimal AveragePersistenceBars { get; set; }
        public decimal AverageNoiseScore { get; set; }
        public decimal AverageTrendStrength { get; set; }
        public decimal AverageVolatilityScore { get; set; }
        
        public Dictionary<MarketCondition, int> MarketConditionDistribution { get; set; } = new Dictionary<MarketCondition, int>();
        public Dictionary<NoiseLevel, int> NoiseLevelDistribution { get; set; } = new Dictionary<NoiseLevel, int>();
        
        public DateTime LastValidation { get; set; }
        public TimeSpan AverageValidationTime { get; set; }
        public decimal OverallFilteringEffectiveness { get; set; }
    }
}
