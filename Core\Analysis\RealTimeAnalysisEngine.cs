using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Real-time analysis engine that coordinates all Phase 1 analyzers for continuous market analysis
    /// Bridges the gap between sophisticated calibration analysis and real-time signal generation
    /// </summary>
    public class RealTimeAnalysisEngine : IRealTimeAnalysisEngine
    {
        #region Fields

        private readonly IVolumePatternAnalyzer _volumeAnalyzer;
        private readonly IDeltaFlowAnalyzer _deltaAnalyzer;
        private readonly IVolatilityAnalyzer _volatilityAnalyzer;
        private readonly IMarketProfileAnalyzer _marketProfileAnalyzer;

        private readonly object _lockObject = new object();
        private readonly Queue<MarketDataPoint> _marketDataHistory;
        private readonly Stopwatch _performanceStopwatch;

        // State Management
        private SymbolProfile _symbolProfile;
        private OptimalSettings _settings;
        private RealTimeAnalysisConfig _config;
        private AnalysisState _currentAnalysisState;
        private AnalysisPerformanceMetrics _performanceMetrics;

        // Caching for Performance
        private readonly Dictionary<string, object> _analysisCache;
        private DateTime _lastCacheUpdate;
        private int _cacheHits;
        private int _cacheMisses;

        // Phase 3: Enhanced Performance Optimization
        private readonly Dictionary<string, CachedAnalysisResult> _enhancedCache;
        private readonly Dictionary<string, CircuitBreakerState> _circuitBreakers;
        private DateTime _lastMemoryCleanup;
        private DateTime _lastCacheCleanup;
        private MarketActivityLevel _currentActivityLevel;
        private TimeSpan _currentAnalysisInterval;

        #endregion

        #region Properties

        public bool IsInitialized { get; private set; }
        public RealTimeAnalysisConfig Configuration => _config;

        #endregion

        #region Constructor

        public RealTimeAnalysisEngine(
            IVolumePatternAnalyzer volumeAnalyzer,
            IDeltaFlowAnalyzer deltaAnalyzer,
            IVolatilityAnalyzer volatilityAnalyzer,
            IMarketProfileAnalyzer marketProfileAnalyzer)
        {
            _volumeAnalyzer = volumeAnalyzer ?? throw new ArgumentNullException(nameof(volumeAnalyzer));
            _deltaAnalyzer = deltaAnalyzer ?? throw new ArgumentNullException(nameof(deltaAnalyzer));
            _volatilityAnalyzer = volatilityAnalyzer ?? throw new ArgumentNullException(nameof(volatilityAnalyzer));
            _marketProfileAnalyzer = marketProfileAnalyzer ?? throw new ArgumentNullException(nameof(marketProfileAnalyzer));

            _marketDataHistory = new Queue<MarketDataPoint>();
            _performanceStopwatch = new Stopwatch();
            _analysisCache = new Dictionary<string, object>();
            _currentAnalysisState = new AnalysisState();
            _performanceMetrics = new AnalysisPerformanceMetrics();

            // Phase 3: Initialize enhanced performance features
            _enhancedCache = new Dictionary<string, CachedAnalysisResult>();
            _circuitBreakers = new Dictionary<string, CircuitBreakerState>
            {
                ["Volume"] = new CircuitBreakerState("Volume"),
                ["Delta"] = new CircuitBreakerState("Delta"),
                ["Volatility"] = new CircuitBreakerState("Volatility"),
                ["MarketProfile"] = new CircuitBreakerState("MarketProfile")
            };
            _lastMemoryCleanup = DateTime.UtcNow;
            _lastCacheCleanup = DateTime.UtcNow;
            _currentActivityLevel = MarketActivityLevel.Medium;
            _currentAnalysisInterval = TimeSpan.FromSeconds(5);

            IsInitialized = false;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initialize the analysis engine with symbol profile and settings
        /// </summary>
        public void Initialize(SymbolProfile symbolProfile, OptimalSettings settings, RealTimeAnalysisConfig config = null)
        {
            if (symbolProfile == null)
                throw new ArgumentNullException(nameof(symbolProfile));
            
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));

            lock (_lockObject)
            {
                _symbolProfile = symbolProfile;
                _settings = settings;
                _config = config ?? new RealTimeAnalysisConfig();

                // Initialize analysis state with symbol profile data
                InitializeAnalysisState();

                // Clear any existing data
                _marketDataHistory.Clear();
                _analysisCache.Clear();
                _cacheHits = 0;
                _cacheMisses = 0;

                IsInitialized = true;
            }
        }

        /// <summary>
        /// Update the engine with new market data with adaptive analysis intervals
        /// </summary>
        public void UpdateMarketData(MarketDataPoint newBar)
        {
            if (!IsInitialized)
                throw new InvalidOperationException("Engine must be initialized before updating market data");

            if (newBar == null)
                return;

            lock (_lockObject)
            {
                _performanceStopwatch.Restart();

                // Add to history
                _marketDataHistory.Enqueue(newBar);

                // Phase 3: Adaptive memory management with sliding window
                if (_config.EnableSlidingWindow)
                {
                    while (_marketDataHistory.Count > _config.SlidingWindowSize)
                    {
                        _marketDataHistory.Dequeue();
                    }
                }
                else
                {
                    // Maintain history size (legacy behavior)
                    while (_marketDataHistory.Count > _config.MaxHistoryBars)
                    {
                        _marketDataHistory.Dequeue();
                    }
                }

                // Phase 3: Determine market activity level and adaptive analysis interval
                DetermineMarketActivityLevel(newBar);

                // Phase 3: Perform periodic maintenance
                PerformPeriodicMaintenance();

                // Update analysis state if enough data and sufficient time has passed
                var timeSinceLastUpdate = DateTime.UtcNow - _currentAnalysisState.LastUpdate;
                if (_marketDataHistory.Count >= 20 && timeSinceLastUpdate >= _currentAnalysisInterval)
                {
                    UpdateAnalysisState(newBar);
                }

                _performanceStopwatch.Stop();
                UpdatePerformanceMetrics();
            }
        }

        /// <summary>
        /// Get current market context with real-time analysis data
        /// </summary>
        public MarketContext GetCurrentMarketContext(MarketDataPoint currentBar)
        {
            if (!IsInitialized)
                throw new InvalidOperationException("Engine must be initialized before getting market context");

            if (currentBar == null)
                throw new ArgumentNullException(nameof(currentBar));

            lock (_lockObject)
            {
                // Ensure we have current analysis state
                if ((DateTime.UtcNow - _currentAnalysisState.LastUpdate).TotalSeconds > 30)
                {
                    UpdateAnalysisState(currentBar);
                }

                return new MarketContext
                {
                    CurrentPrice = currentBar.Close,
                    PriceChange = currentBar.Close - currentBar.Open,
                    PriceChangePercent = currentBar.Open > 0 ? (currentBar.Close - currentBar.Open) / currentBar.Open * 100 : 0,
                    DeltaImbalance = Math.Abs(currentBar.Delta) / Math.Max(currentBar.Volume, 1),
                    
                    // REAL DATA from Phase 1 analyzers (not placeholders!)
                    CVDTrend = _currentAnalysisState.DeltaFlow.CurrentCVDTrend,
                    VolatilityRegime = _currentAnalysisState.Volatility.CurrentRegime,
                    CurrentSession = _currentAnalysisState.MarketProfile.CurrentSession,
                    IsOptimalTradingTime = _currentAnalysisState.MarketProfile.IsOptimalTradingTime,
                    
                    MarketConditions = BuildMarketConditions(_currentAnalysisState)
                };
            }
        }

        /// <summary>
        /// Get current analysis state from all components
        /// </summary>
        public AnalysisState GetCurrentAnalysisState()
        {
            if (!IsInitialized)
                throw new InvalidOperationException("Engine must be initialized before getting analysis state");

            lock (_lockObject)
            {
                // Return a copy to prevent external modification
                return new AnalysisState
                {
                    LastUpdate = _currentAnalysisState.LastUpdate,
                    CorrelationId = _currentAnalysisState.CorrelationId,
                    BarsProcessed = _currentAnalysisState.BarsProcessed,
                    Volume = CloneVolumeState(_currentAnalysisState.Volume),
                    DeltaFlow = CloneDeltaState(_currentAnalysisState.DeltaFlow),
                    Volatility = CloneVolatilityState(_currentAnalysisState.Volatility),
                    MarketProfile = CloneMarketProfileState(_currentAnalysisState.MarketProfile),
                    OverallConfidence = _currentAnalysisState.OverallConfidence,
                    ActiveConditions = new List<string>(_currentAnalysisState.ActiveConditions),
                    Warnings = new List<string>(_currentAnalysisState.Warnings)
                };
            }
        }

        /// <summary>
        /// Get analysis performance metrics
        /// </summary>
        public AnalysisPerformanceMetrics GetPerformanceMetrics()
        {
            lock (_lockObject)
            {
                return new AnalysisPerformanceMetrics
                {
                    LastUpdate = _performanceMetrics.LastUpdate,
                    VolumeAnalysisTime = _performanceMetrics.VolumeAnalysisTime,
                    DeltaAnalysisTime = _performanceMetrics.DeltaAnalysisTime,
                    VolatilityAnalysisTime = _performanceMetrics.VolatilityAnalysisTime,
                    MarketProfileAnalysisTime = _performanceMetrics.MarketProfileAnalysisTime,
                    TotalAnalysisTime = _performanceMetrics.TotalAnalysisTime,
                    AverageConfidence = _performanceMetrics.AverageConfidence,
                    AnalysisUpdatesPerMinute = _performanceMetrics.AnalysisUpdatesPerMinute,
                    CacheHitRate = _cacheHits + _cacheMisses > 0 ? (_cacheHits * 100) / (_cacheHits + _cacheMisses) : 0,
                    MemoryUsageBytes = _performanceMetrics.MemoryUsageBytes,
                    ErrorCount = _performanceMetrics.ErrorCount,
                    LastError = _performanceMetrics.LastError,
                    LastErrorMessage = _performanceMetrics.LastErrorMessage
                };
            }
        }

        /// <summary>
        /// Reset the analysis engine state
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                _marketDataHistory.Clear();
                _analysisCache.Clear();
                _currentAnalysisState = new AnalysisState();
                _performanceMetrics = new AnalysisPerformanceMetrics();
                _cacheHits = 0;
                _cacheMisses = 0;
                IsInitialized = false;
            }
        }

        #endregion

        #region Private Methods

        #region Phase 3: Performance Optimization Methods

        /// <summary>
        /// Determine market activity level and set adaptive analysis interval
        /// </summary>
        private void DetermineMarketActivityLevel(MarketDataPoint currentBar)
        {
            if (_marketDataHistory.Count < 10) return;

            var recentBars = _marketDataHistory.TakeLast(10).ToList();
            var averageVolume = recentBars.Average(b => b.Volume);
            var currentVolumeRatio = averageVolume > 0 ? currentBar.Volume / averageVolume : 1;

            var previousActivityLevel = _currentActivityLevel;

            // Determine activity level based on volume ratio
            if (currentVolumeRatio >= _config.HighActivityThreshold)
            {
                _currentActivityLevel = MarketActivityLevel.High;
                _currentAnalysisInterval = _config.HighActivityInterval;
            }
            else if (currentVolumeRatio <= _config.LowActivityThreshold)
            {
                _currentActivityLevel = MarketActivityLevel.Low;
                _currentAnalysisInterval = _config.LowActivityInterval;
            }
            else
            {
                _currentActivityLevel = MarketActivityLevel.Medium;
                _currentAnalysisInterval = _config.MediumActivityInterval;
            }

            // Update performance metrics
            _performanceMetrics.CurrentActivityLevel = _currentActivityLevel;
            _performanceMetrics.CurrentAnalysisInterval = _currentAnalysisInterval;

            // Log activity level changes
            if (previousActivityLevel != _currentActivityLevel)
            {
                _currentAnalysisState.Warnings.Add($"Activity level changed: {previousActivityLevel} → {_currentActivityLevel} (Interval: {_currentAnalysisInterval.TotalSeconds}s)");
            }
        }

        /// <summary>
        /// Perform periodic maintenance tasks
        /// </summary>
        private void PerformPeriodicMaintenance()
        {
            var now = DateTime.UtcNow;

            // Cache cleanup
            if (_config.EnableIntelligentCaching && (now - _lastCacheCleanup) > TimeSpan.FromMinutes(1))
            {
                CleanupExpiredCache();
                _lastCacheCleanup = now;
            }

            // Memory cleanup
            if ((now - _lastMemoryCleanup) > _config.MemoryCleanupInterval)
            {
                PerformMemoryCleanup();
                _lastMemoryCleanup = now;
                _performanceMetrics.MemoryCleanupCount++;
                _performanceMetrics.LastMemoryCleanup = now;
            }

            // Circuit breaker maintenance
            if (_config.EnableCircuitBreaker)
            {
                foreach (var circuitBreaker in _circuitBreakers.Values)
                {
                    circuitBreaker.TryHalfOpen();
                }
            }
        }

        /// <summary>
        /// Clean up expired cache entries
        /// </summary>
        private void CleanupExpiredCache()
        {
            var expiredKeys = _enhancedCache.Where(kvp => kvp.Value.IsExpired).Select(kvp => kvp.Key).ToList();
            foreach (var key in expiredKeys)
            {
                _enhancedCache.Remove(key);
            }

            // Also clean up legacy cache if it gets too large
            if (_analysisCache.Count > _config.MaxCacheSize)
            {
                var keysToRemove = _analysisCache.Keys.Take(_analysisCache.Count - _config.MaxCacheSize).ToList();
                foreach (var key in keysToRemove)
                {
                    _analysisCache.Remove(key);
                }
            }
        }

        /// <summary>
        /// Perform memory cleanup and optimization
        /// </summary>
        private void PerformMemoryCleanup()
        {
            // Force garbage collection if memory usage is high
            var memoryBefore = GC.GetTotalMemory(false);

            if (memoryBefore > _performanceMetrics.MaxAcceptableMemoryUsageBytes)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }

            var memoryAfter = GC.GetTotalMemory(false);
            _performanceMetrics.MemoryUsageBytes = memoryAfter;

            // Update sliding window size if needed
            if (_config.EnableSlidingWindow)
            {
                _performanceMetrics.SlidingWindowSize = _marketDataHistory.Count;
            }
        }

        /// <summary>
        /// Execute analysis component with circuit breaker protection
        /// </summary>
        private T ExecuteWithCircuitBreaker<T>(string componentName, Func<T> analysisFunction, T fallbackValue = default(T))
        {
            if (!_config.EnableCircuitBreaker || !_circuitBreakers.ContainsKey(componentName))
            {
                return analysisFunction();
            }

            var circuitBreaker = _circuitBreakers[componentName];

            if (!circuitBreaker.CanExecute())
            {
                _performanceMetrics.CircuitBreakerTrips++;
                _currentAnalysisState.Warnings.Add($"Circuit breaker OPEN for {componentName} - using fallback");
                return fallbackValue;
            }

            try
            {
                var result = analysisFunction();
                circuitBreaker.RecordSuccess();
                return result;
            }
            catch (Exception ex)
            {
                circuitBreaker.RecordFailure(ex.Message, _config.CircuitBreakerFailureThreshold, _config.CircuitBreakerRetryInterval);
                _performanceMetrics.ErrorCount++;
                _performanceMetrics.LastError = DateTime.UtcNow;
                _performanceMetrics.LastErrorMessage = $"{componentName}: {ex.Message}";

                _currentAnalysisState.Warnings.Add($"Circuit breaker failure in {componentName}: {ex.Message}");
                return fallbackValue;
            }
        }

        /// <summary>
        /// Get cached analysis result with intelligent caching
        /// </summary>
        private T GetCachedResult<T>(string cacheKey, Func<T> analysisFunction, TimeSpan? customExpiration = null) where T : class
        {
            if (!_config.EnableIntelligentCaching)
            {
                return analysisFunction();
            }

            // Check enhanced cache first
            if (_enhancedCache.ContainsKey(cacheKey))
            {
                var cachedResult = _enhancedCache[cacheKey];
                if (!cachedResult.IsExpired)
                {
                    cachedResult.MarkAccessed();
                    _performanceMetrics.CacheHits++;
                    return (T)cachedResult.Result;
                }
                else
                {
                    _enhancedCache.Remove(cacheKey);
                }
            }

            // Cache miss - execute analysis
            _performanceMetrics.CacheMisses++;
            var result = analysisFunction();

            // Store in enhanced cache
            var expiration = customExpiration ?? _config.CacheExpirationTime;
            _enhancedCache[cacheKey] = new CachedAnalysisResult(result, expiration, cacheKey);

            return result;
        }

        #endregion

        private void InitializeAnalysisState()
        {
            _currentAnalysisState = new AnalysisState
            {
                LastUpdate = DateTime.UtcNow,
                CorrelationId = _symbolProfile.CorrelationId,
                BarsProcessed = 0,
                
                // Initialize with symbol profile baseline data
                Volume = new VolumeAnalysisState
                {
                    CurrentRegime = _symbolProfile.Volume.Regime,
                    AverageVolume = _symbolProfile.Volume.AverageVolume,
                    VolumeStandardDeviation = _symbolProfile.Volume.VolumeStandardDeviation,
                    VolumeConsistency = _symbolProfile.Volume.VolumeConsistency,
                    VolumeConfidence = 0.8m
                },
                
                DeltaFlow = new DeltaAnalysisState
                {
                    CurrentRegime = _symbolProfile.DeltaFlow.Regime,
                    InstitutionalActivityLevel = _symbolProfile.DeltaFlow.InstitutionalActivityLevel,
                    IsInstitutionalActivityDetected = _symbolProfile.DeltaFlow.HasStrongInstitutionalActivity,
                    BuyPressureDominance = _symbolProfile.DeltaFlow.BuyPressureDominance,
                    DeltaConfidence = 0.8m
                },
                
                Volatility = new VolatilityAnalysisState
                {
                    CurrentRegime = _symbolProfile.Volatility.Regime,
                    AverageVolatility = _symbolProfile.Volatility.AverageVolatility,
                    RiskAdjustmentFactor = _symbolProfile.Volatility.RiskAdjustmentFactor,
                    TrendingProbability = _symbolProfile.Volatility.TrendingFrequency,
                    RangingProbability = _symbolProfile.Volatility.RangingFrequency,
                    VolatilityConfidence = 0.8m
                },
                
                MarketProfile = new MarketProfileAnalysisState
                {
                    CurrentSession = _symbolProfile.MarketProfile.MostActiveSession,
                    AsianSessionActivity = _symbolProfile.MarketProfile.AsianSessionActivity,
                    EuropeanSessionActivity = _symbolProfile.MarketProfile.EuropeanSessionActivity,
                    USSessionActivity = _symbolProfile.MarketProfile.USSessionActivity,
                    Is24HourMarket = _symbolProfile.MarketProfile.Is24HourMarket,
                    SessionVolatilityVariation = _symbolProfile.MarketProfile.SessionVolatilityVariation,
                    MarketProfileConfidence = 0.8m
                },
                
                OverallConfidence = _symbolProfile.AnalysisConfidence
            };
        }

        private void UpdateAnalysisState(MarketDataPoint currentBar)
        {
            try
            {
                var recentData = _marketDataHistory.ToList();
                var currentTime = DateTime.UtcNow;

                // Update Volume Analysis
                if (_config.EnableVolumeAnalysis)
                {
                    var volumeStopwatch = Stopwatch.StartNew();
                    UpdateVolumeAnalysisState(currentBar, recentData);
                    volumeStopwatch.Stop();
                    _performanceMetrics.VolumeAnalysisTime = volumeStopwatch.Elapsed;
                }

                // Update Delta Analysis
                if (_config.EnableDeltaAnalysis)
                {
                    var deltaStopwatch = Stopwatch.StartNew();
                    UpdateDeltaAnalysisState(currentBar, recentData);
                    deltaStopwatch.Stop();
                    _performanceMetrics.DeltaAnalysisTime = deltaStopwatch.Elapsed;
                }

                // Update Volatility Analysis
                if (_config.EnableVolatilityAnalysis)
                {
                    var volatilityStopwatch = Stopwatch.StartNew();
                    UpdateVolatilityAnalysisState(currentBar, recentData);
                    volatilityStopwatch.Stop();
                    _performanceMetrics.VolatilityAnalysisTime = volatilityStopwatch.Elapsed;
                }

                // Update Market Profile Analysis
                if (_config.EnableMarketProfileAnalysis)
                {
                    var marketProfileStopwatch = Stopwatch.StartNew();
                    UpdateMarketProfileAnalysisState(currentTime, recentData);
                    marketProfileStopwatch.Stop();
                    _performanceMetrics.MarketProfileAnalysisTime = marketProfileStopwatch.Elapsed;
                }

                // Update overall state
                _currentAnalysisState.LastUpdate = currentTime;
                _currentAnalysisState.BarsProcessed++;
                _currentAnalysisState.OverallConfidence = CalculateOverallConfidence();
                _currentAnalysisState.ActiveConditions = BuildActiveConditions();
                _currentAnalysisState.Warnings = BuildWarnings();
            }
            catch (Exception ex)
            {
                _performanceMetrics.ErrorCount++;
                _performanceMetrics.LastError = DateTime.UtcNow;
                _performanceMetrics.LastErrorMessage = ex.Message;

                // Add warning but don't throw - maintain system stability
                _currentAnalysisState.Warnings.Add($"Analysis update error: {ex.Message}");
            }
        }

        private void UpdateVolumeAnalysisState(MarketDataPoint currentBar, List<MarketDataPoint> recentData)
        {
            var cacheKey = $"volume_{currentBar.Timestamp:yyyyMMddHHmm}";

            // Phase 3: Use enhanced caching and circuit breaker protection
            var volumeState = GetCachedResult(cacheKey, () =>
            {
                return ExecuteWithCircuitBreaker("Volume", () =>
                {
                    // Get real-time volume analysis from VolumePatternAnalyzer
                    var state = _volumeAnalyzer.GetCurrentVolumeState(recentData);
                    var (regime, confidence) = _volumeAnalyzer.DetermineCurrentVolumeRegime(currentBar, recentData);
                    var (trend, strength, duration) = _volumeAnalyzer.AnalyzeVolumeTrend(recentData, _config.VolumeAnalysisWindow);

                    // Create comprehensive volume state
                    return new VolumeAnalysisState
                    {
                        LastUpdate = DateTime.UtcNow,
                        CurrentRegime = regime,
                        CurrentVolumeRatio = currentBar.Volume / Math.Max(state.AverageVolume, 1),
                        AverageVolume = state.AverageVolume,
                        VolumeStandardDeviation = state.VolumeStandardDeviation,
                        IsVolumeSpikeActive = state.IsVolumeSpikeActive,
                        SpikeMagnitude = state.SpikeMagnitude,
                        ConsecutiveSpikeBars = state.ConsecutiveSpikeBars,
                        CurrentVolumePattern = state.CurrentVolumePattern,
                        VolumeConsistency = state.VolumeConsistency,
                        VolumeConfidence = confidence,
                        VolumeTrend = trend,
                        TrendDuration = duration,
                        TrendStrength = strength
                    };
                }, CreateFallbackVolumeState(currentBar, recentData));
            });

            if (volumeState != null)
            {
                _currentAnalysisState.Volume = volumeState;
            }
        }

        private void UpdateDeltaAnalysisState(MarketDataPoint currentBar, List<MarketDataPoint> recentData)
        {
            var cacheKey = $"delta_{currentBar.Timestamp:yyyyMMddHHmm}";
            if (_analysisCache.ContainsKey(cacheKey))
            {
                _cacheHits++;
                _currentAnalysisState.DeltaFlow = (DeltaAnalysisState)_analysisCache[cacheKey];
                return;
            }

            _cacheMisses++;

            // Get real-time delta analysis from DeltaFlowAnalyzer
            var deltaState = _deltaAnalyzer.GetCurrentDeltaState(recentData);
            var (cvdTrend, trendStrength, isDivergence) = _deltaAnalyzer.CalculateRealtimeCVD(recentData, _config.DeltaAnalysisWindow);
            var (isInstitutional, activityLevel, pattern) = _deltaAnalyzer.DetectRealtimeInstitutionalActivity(currentBar, recentData);

            // Update delta analysis state with real data
            _currentAnalysisState.DeltaFlow.LastUpdate = DateTime.UtcNow;
            _currentAnalysisState.DeltaFlow.CurrentRegime = deltaState.CurrentRegime;
            _currentAnalysisState.DeltaFlow.CurrentDeltaImbalance = Math.Abs(currentBar.Delta) / Math.Max(currentBar.Volume, 1);
            _currentAnalysisState.DeltaFlow.CurrentCVDTrend = cvdTrend;
            _currentAnalysisState.DeltaFlow.CurrentBias = deltaState.CurrentBias;
            _currentAnalysisState.DeltaFlow.IsInstitutionalActivityDetected = isInstitutional;
            _currentAnalysisState.DeltaFlow.InstitutionalActivityLevel = activityLevel;
            _currentAnalysisState.DeltaFlow.InstitutionalPattern = pattern;
            _currentAnalysisState.DeltaFlow.CVDTrendStrength = trendStrength;
            _currentAnalysisState.DeltaFlow.CVDTrendDuration = deltaState.CVDTrendDuration;
            _currentAnalysisState.DeltaFlow.IsCVDDivergence = isDivergence;
            _currentAnalysisState.DeltaFlow.CVDConfidence = deltaState.CVDConfidence;
            _currentAnalysisState.DeltaFlow.CurrentDeltaPattern = deltaState.CurrentDeltaPattern;
            _currentAnalysisState.DeltaFlow.DeltaConfidence = deltaState.DeltaConfidence;
            _currentAnalysisState.DeltaFlow.BuyPressureDominance = deltaState.BuyPressureDominance;

            // Cache the result
            _analysisCache[cacheKey] = CloneDeltaState(_currentAnalysisState.DeltaFlow);
        }

        private void UpdateVolatilityAnalysisState(MarketDataPoint currentBar, List<MarketDataPoint> recentData)
        {
            var cacheKey = $"volatility_{currentBar.Timestamp:yyyyMMddHHmm}";
            if (_analysisCache.ContainsKey(cacheKey))
            {
                _cacheHits++;
                _currentAnalysisState.Volatility = (VolatilityAnalysisState)_analysisCache[cacheKey];
                return;
            }

            _cacheMisses++;

            // Get real-time volatility analysis from VolatilityAnalyzer
            var volatilityState = _volatilityAnalyzer.GetCurrentVolatilityState(recentData);
            var (currentVol, atr, percentile) = _volatilityAnalyzer.CalculateRealtimeVolatility(currentBar, recentData);
            var (isExpansion, isContraction, changeRate) = _volatilityAnalyzer.DetectVolatilityChange(recentData, _config.VolatilityAnalysisWindow);

            // Update volatility analysis state with real data
            _currentAnalysisState.Volatility.LastUpdate = DateTime.UtcNow;
            _currentAnalysisState.Volatility.CurrentRegime = volatilityState.CurrentRegime;
            _currentAnalysisState.Volatility.CurrentVolatility = currentVol;
            _currentAnalysisState.Volatility.AverageVolatility = volatilityState.AverageVolatility;
            _currentAnalysisState.Volatility.VolatilityPercentile = percentile;
            _currentAnalysisState.Volatility.IsVolatilityExpansion = isExpansion;
            _currentAnalysisState.Volatility.IsVolatilityContraction = isContraction;
            _currentAnalysisState.Volatility.VolatilityPattern = volatilityState.VolatilityPattern;
            _currentAnalysisState.Volatility.VolatilityConfidence = volatilityState.VolatilityConfidence;
            _currentAnalysisState.Volatility.CurrentTrueRange = Math.Abs(currentBar.High - currentBar.Low);
            _currentAnalysisState.Volatility.AverageTrueRange = atr;
            _currentAnalysisState.Volatility.RiskAdjustmentFactor = volatilityState.RiskAdjustmentFactor;
            _currentAnalysisState.Volatility.TrendingProbability = volatilityState.TrendingProbability;
            _currentAnalysisState.Volatility.RangingProbability = volatilityState.RangingProbability;
            _currentAnalysisState.Volatility.VolatilityTrend = volatilityState.VolatilityTrend;

            // Cache the result
            _analysisCache[cacheKey] = CloneVolatilityState(_currentAnalysisState.Volatility);
        }

        private void UpdateMarketProfileAnalysisState(DateTime currentTime, List<MarketDataPoint> recentData)
        {
            var cacheKey = $"marketprofile_{currentTime:yyyyMMddHH}";
            if (_analysisCache.ContainsKey(cacheKey))
            {
                _cacheHits++;
                _currentAnalysisState.MarketProfile = (MarketProfileAnalysisState)_analysisCache[cacheKey];
                return;
            }

            _cacheMisses++;

            // Get real-time market profile analysis from MarketProfileAnalyzer
            var marketProfileState = _marketProfileAnalyzer.GetCurrentMarketProfileState(currentTime, recentData);
            var (currentSession, activityLevel, isOptimal) = _marketProfileAnalyzer.AnalyzeRealtimeSessionActivity(currentTime, recentData);
            var (condition, confidence) = _marketProfileAnalyzer.DetermineMarketCondition(currentTime, recentData);

            // Update market profile analysis state with real data
            _currentAnalysisState.MarketProfile.LastUpdate = DateTime.UtcNow;
            _currentAnalysisState.MarketProfile.CurrentSession = currentSession;
            _currentAnalysisState.MarketProfile.IsOptimalTradingTime = isOptimal;
            _currentAnalysisState.MarketProfile.CurrentSessionActivity = activityLevel;
            _currentAnalysisState.MarketProfile.TimeInCurrentSession = marketProfileState.TimeInCurrentSession;
            _currentAnalysisState.MarketProfile.AsianSessionActivity = marketProfileState.AsianSessionActivity;
            _currentAnalysisState.MarketProfile.EuropeanSessionActivity = marketProfileState.EuropeanSessionActivity;
            _currentAnalysisState.MarketProfile.USSessionActivity = marketProfileState.USSessionActivity;
            _currentAnalysisState.MarketProfile.SessionVolatilityVariation = marketProfileState.SessionVolatilityVariation;
            _currentAnalysisState.MarketProfile.CurrentHighActivityHours = marketProfileState.CurrentHighActivityHours;
            _currentAnalysisState.MarketProfile.CurrentLowActivityHours = marketProfileState.CurrentLowActivityHours;
            _currentAnalysisState.MarketProfile.IsInOptimalWindow = marketProfileState.IsInOptimalWindow;
            _currentAnalysisState.MarketProfile.OptimalWindowConfidence = marketProfileState.OptimalWindowConfidence;
            _currentAnalysisState.MarketProfile.Is24HourMarket = marketProfileState.Is24HourMarket;
            _currentAnalysisState.MarketProfile.MarketCondition = condition;
            _currentAnalysisState.MarketProfile.MarketProfileConfidence = confidence;

            // Cache the result
            _analysisCache[cacheKey] = CloneMarketProfileState(_currentAnalysisState.MarketProfile);
        }

        private decimal CalculateOverallConfidence()
        {
            var confidences = new List<decimal>();

            if (_config.EnableVolumeAnalysis)
                confidences.Add(_currentAnalysisState.Volume.VolumeConfidence);

            if (_config.EnableDeltaAnalysis)
                confidences.Add(_currentAnalysisState.DeltaFlow.DeltaConfidence);

            if (_config.EnableVolatilityAnalysis)
                confidences.Add(_currentAnalysisState.Volatility.VolatilityConfidence);

            if (_config.EnableMarketProfileAnalysis)
                confidences.Add(_currentAnalysisState.MarketProfile.MarketProfileConfidence);

            return confidences.Count > 0 ? confidences.Average() : 0.5m;
        }

        private List<string> BuildActiveConditions()
        {
            var conditions = new List<string>();

            // Volume conditions
            if (_currentAnalysisState.Volume.IsVolumeSpikeActive)
                conditions.Add($"Volume Spike: {_currentAnalysisState.Volume.SpikeMagnitude:F1}x");

            if (_currentAnalysisState.Volume.VolumeTrend != VolumeTrend.Stable)
                conditions.Add($"Volume Trend: {_currentAnalysisState.Volume.VolumeTrend}");

            // Delta conditions
            if (_currentAnalysisState.DeltaFlow.IsInstitutionalActivityDetected)
                conditions.Add($"Institutional Activity: {_currentAnalysisState.DeltaFlow.InstitutionalPattern}");

            if (_currentAnalysisState.DeltaFlow.IsCVDDivergence)
                conditions.Add("CVD Divergence");

            // Volatility conditions
            if (_currentAnalysisState.Volatility.IsVolatilityExpansion)
                conditions.Add("Volatility Expansion");

            if (_currentAnalysisState.Volatility.IsVolatilityContraction)
                conditions.Add("Volatility Contraction");

            // Market profile conditions
            if (_currentAnalysisState.MarketProfile.IsOptimalTradingTime)
                conditions.Add($"Optimal Session: {_currentAnalysisState.MarketProfile.CurrentSession}");

            return conditions;
        }

        private List<string> BuildWarnings()
        {
            var warnings = new List<string>();

            // Check for low confidence
            if (_currentAnalysisState.OverallConfidence < 0.5m)
                warnings.Add("Low overall analysis confidence");

            // Check for extreme volatility
            if (_currentAnalysisState.Volatility.CurrentRegime == VolatilityRegime.Extreme)
                warnings.Add("Extreme volatility detected");

            // Check for data quality issues
            if (_marketDataHistory.Count < 20)
                warnings.Add("Insufficient data for reliable analysis");

            // Check for performance issues
            if (_performanceMetrics.TotalAnalysisTime.TotalMilliseconds > 100)
                warnings.Add("Analysis performance degraded");

            return warnings;
        }

        private List<string> BuildMarketConditions(AnalysisState analysisState)
        {
            var conditions = new List<string>();

            // Add volume conditions
            conditions.Add($"Volume: {analysisState.Volume.CurrentRegime}");

            // Add delta conditions
            conditions.Add($"Delta: {analysisState.DeltaFlow.CurrentRegime}");

            // Add volatility conditions
            conditions.Add($"Volatility: {analysisState.Volatility.CurrentRegime}");

            // Add session conditions
            conditions.Add($"Session: {analysisState.MarketProfile.CurrentSession}");

            return conditions;
        }

        private void UpdatePerformanceMetrics()
        {
            _performanceMetrics.LastUpdate = DateTime.UtcNow;
            _performanceMetrics.TotalAnalysisTime = _performanceStopwatch.Elapsed;
            _performanceMetrics.AverageConfidence = _currentAnalysisState.OverallConfidence;

            // Calculate updates per minute (simplified)
            _performanceMetrics.AnalysisUpdatesPerMinute = Math.Min(60, _currentAnalysisState.BarsProcessed);

            // Estimate memory usage (simplified)
            _performanceMetrics.MemoryUsageBytes = _marketDataHistory.Count * 100 + _analysisCache.Count * 50;
        }

        // Clone methods for thread-safe state copying
        private VolumeAnalysisState CloneVolumeState(VolumeAnalysisState original)
        {
            return new VolumeAnalysisState
            {
                LastUpdate = original.LastUpdate,
                CurrentRegime = original.CurrentRegime,
                CurrentVolumeRatio = original.CurrentVolumeRatio,
                AverageVolume = original.AverageVolume,
                VolumeStandardDeviation = original.VolumeStandardDeviation,
                IsVolumeSpikeActive = original.IsVolumeSpikeActive,
                SpikeMagnitude = original.SpikeMagnitude,
                ConsecutiveSpikeBars = original.ConsecutiveSpikeBars,
                LastSpikeTime = original.LastSpikeTime,
                CurrentVolumePattern = original.CurrentVolumePattern,
                VolumeConsistency = original.VolumeConsistency,
                VolumeConfidence = original.VolumeConfidence,
                VolumeTrend = original.VolumeTrend,
                TrendDuration = original.TrendDuration,
                TrendStrength = original.TrendStrength
            };
        }

        private DeltaAnalysisState CloneDeltaState(DeltaAnalysisState original)
        {
            return new DeltaAnalysisState
            {
                LastUpdate = original.LastUpdate,
                CurrentRegime = original.CurrentRegime,
                CurrentDeltaImbalance = original.CurrentDeltaImbalance,
                CurrentCVDTrend = original.CurrentCVDTrend,
                CurrentBias = original.CurrentBias,
                IsInstitutionalActivityDetected = original.IsInstitutionalActivityDetected,
                InstitutionalActivityLevel = original.InstitutionalActivityLevel,
                InstitutionalPattern = original.InstitutionalPattern,
                LastInstitutionalActivity = original.LastInstitutionalActivity,
                CVDTrendStrength = original.CVDTrendStrength,
                CVDTrendDuration = original.CVDTrendDuration,
                IsCVDDivergence = original.IsCVDDivergence,
                CVDConfidence = original.CVDConfidence,
                CurrentDeltaPattern = original.CurrentDeltaPattern,
                DeltaConfidence = original.DeltaConfidence,
                BuyPressureDominance = original.BuyPressureDominance
            };
        }

        private VolatilityAnalysisState CloneVolatilityState(VolatilityAnalysisState original)
        {
            return new VolatilityAnalysisState
            {
                LastUpdate = original.LastUpdate,
                CurrentRegime = original.CurrentRegime,
                CurrentVolatility = original.CurrentVolatility,
                AverageVolatility = original.AverageVolatility,
                VolatilityPercentile = original.VolatilityPercentile,
                IsVolatilityExpansion = original.IsVolatilityExpansion,
                IsVolatilityContraction = original.IsVolatilityContraction,
                VolatilityPattern = original.VolatilityPattern,
                VolatilityConfidence = original.VolatilityConfidence,
                CurrentTrueRange = original.CurrentTrueRange,
                AverageTrueRange = original.AverageTrueRange,
                RiskAdjustmentFactor = original.RiskAdjustmentFactor,
                TrendingProbability = original.TrendingProbability,
                RangingProbability = original.RangingProbability,
                VolatilityTrend = original.VolatilityTrend
            };
        }

        private MarketProfileAnalysisState CloneMarketProfileState(MarketProfileAnalysisState original)
        {
            return new MarketProfileAnalysisState
            {
                LastUpdate = original.LastUpdate,
                CurrentSession = original.CurrentSession,
                IsOptimalTradingTime = original.IsOptimalTradingTime,
                CurrentSessionActivity = original.CurrentSessionActivity,
                TimeInCurrentSession = original.TimeInCurrentSession,
                AsianSessionActivity = original.AsianSessionActivity,
                EuropeanSessionActivity = original.EuropeanSessionActivity,
                USSessionActivity = original.USSessionActivity,
                SessionVolatilityVariation = original.SessionVolatilityVariation,
                CurrentHighActivityHours = new List<int>(original.CurrentHighActivityHours),
                CurrentLowActivityHours = new List<int>(original.CurrentLowActivityHours),
                IsInOptimalWindow = original.IsInOptimalWindow,
                OptimalWindowConfidence = original.OptimalWindowConfidence,
                Is24HourMarket = original.Is24HourMarket,
                MarketCondition = original.MarketCondition,
                MarketProfileConfidence = original.MarketProfileConfidence
            };
        }

        #region Phase 3: Fallback Methods for Graceful Degradation

        /// <summary>
        /// Create fallback volume state when primary analysis fails
        /// </summary>
        private VolumeAnalysisState CreateFallbackVolumeState(MarketDataPoint currentBar, List<MarketDataPoint> recentData)
        {
            var averageVolume = recentData.Count > 0 ? recentData.Average(b => b.Volume) : currentBar.Volume;
            var volumeRatio = averageVolume > 0 ? currentBar.Volume / averageVolume : 1;

            return new VolumeAnalysisState
            {
                LastUpdate = DateTime.UtcNow,
                CurrentRegime = volumeRatio > 2.0m ? VolumeRegime.High : VolumeRegime.Normal,
                CurrentVolumeRatio = volumeRatio,
                AverageVolume = averageVolume,
                VolumeStandardDeviation = averageVolume * 0.3m, // Estimated
                IsVolumeSpikeActive = volumeRatio > 2.0m,
                SpikeMagnitude = volumeRatio > 2.0m ? volumeRatio : 0,
                ConsecutiveSpikeBars = volumeRatio > 2.0m ? 1 : 0,
                CurrentVolumePattern = "Fallback Analysis",
                VolumeConsistency = 0.5m,
                VolumeConfidence = 0.3m, // Low confidence for fallback
                VolumeTrend = VolumeTrend.Stable,
                TrendDuration = 1,
                TrendStrength = 0.5m
            };
        }

        /// <summary>
        /// Create fallback delta state when primary analysis fails
        /// </summary>
        private DeltaAnalysisState CreateFallbackDeltaState(MarketDataPoint currentBar, List<MarketDataPoint> recentData)
        {
            var deltaImbalance = currentBar.Volume > 0 ? Math.Abs(currentBar.Delta) / currentBar.Volume : 0;

            return new DeltaAnalysisState
            {
                LastUpdate = DateTime.UtcNow,
                CurrentRegime = DeltaFlowRegime.Balanced,
                CurrentDeltaImbalance = deltaImbalance,
                CurrentCVDTrend = 0,
                CurrentBias = currentBar.Delta > 0 ? DeltaBias.BuyPressure : DeltaBias.SellPressure,
                IsInstitutionalActivityDetected = false,
                InstitutionalActivityLevel = 0,
                InstitutionalPattern = "None",
                CVDTrendStrength = 0,
                CVDTrendDuration = 0,
                IsCVDDivergence = false,
                CVDConfidence = 0.3m,
                CurrentDeltaPattern = "Fallback Analysis",
                DeltaConfidence = 0.3m,
                BuyPressureDominance = currentBar.Delta > 0 ? 0.6m : 0.4m
            };
        }

        /// <summary>
        /// Create fallback volatility state when primary analysis fails
        /// </summary>
        private VolatilityAnalysisState CreateFallbackVolatilityState(MarketDataPoint currentBar, List<MarketDataPoint> recentData)
        {
            var priceRange = currentBar.High - currentBar.Low;
            var averageRange = recentData.Count > 0 ? recentData.Average(b => b.High - b.Low) : priceRange;

            return new VolatilityAnalysisState
            {
                LastUpdate = DateTime.UtcNow,
                CurrentRegime = VolatilityRegime.Normal,
                CurrentVolatility = priceRange,
                AverageVolatility = averageRange,
                VolatilityPercentile = 50,
                IsVolatilityExpansion = priceRange > averageRange * 1.5m,
                IsVolatilityContraction = priceRange < averageRange * 0.7m,
                VolatilityPattern = "Fallback Analysis",
                VolatilityConfidence = 0.3m,
                CurrentTrueRange = priceRange,
                AverageTrueRange = averageRange,
                RiskAdjustmentFactor = 1.0m,
                TrendingProbability = 0.5m,
                RangingProbability = 0.5m,
                VolatilityTrend = VolatilityTrend.Stable
            };
        }

        /// <summary>
        /// Create fallback market profile state when primary analysis fails
        /// </summary>
        private MarketProfileAnalysisState CreateFallbackMarketProfileState(DateTime currentTime, List<MarketDataPoint> recentData)
        {
            return new MarketProfileAnalysisState
            {
                LastUpdate = DateTime.UtcNow,
                CurrentSession = TradingSession.Crypto24H,
                IsOptimalTradingTime = true, // Conservative assumption
                CurrentSessionActivity = 0.5m,
                TimeInCurrentSession = TimeSpan.FromHours(1),
                AsianSessionActivity = 0.3m,
                EuropeanSessionActivity = 0.7m,
                USSessionActivity = 0.8m,
                SessionVolatilityVariation = 0.2m,
                CurrentHighActivityHours = new List<int> { 14, 15, 16 }, // US market hours
                CurrentLowActivityHours = new List<int> { 2, 3, 4 }, // Asian quiet hours
                IsInOptimalWindow = true,
                OptimalWindowConfidence = 0.3m,
                Is24HourMarket = true,
                MarketCondition = "Fallback Analysis",
                MarketProfileConfidence = 0.3m
            };
        }

        #endregion

        #endregion
    }

    /// <summary>
    /// Enhanced cached analysis result with expiration and metadata
    /// </summary>
    public class CachedAnalysisResult
    {
        public object Result { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public int AccessCount { get; set; }
        public DateTime LastAccessed { get; set; }
        public string CacheKey { get; set; }
        public bool IsExpired => DateTime.UtcNow > ExpiresAt;

        public CachedAnalysisResult(object result, TimeSpan expirationTime, string cacheKey)
        {
            Result = result;
            CreatedAt = DateTime.UtcNow;
            ExpiresAt = CreatedAt.Add(expirationTime);
            AccessCount = 0;
            LastAccessed = CreatedAt;
            CacheKey = cacheKey;
        }

        public void MarkAccessed()
        {
            AccessCount++;
            LastAccessed = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Circuit breaker state for component failure handling
    /// </summary>
    public class CircuitBreakerState
    {
        public string ComponentName { get; set; }
        public CircuitBreakerStatus Status { get; set; }
        public int FailureCount { get; set; }
        public DateTime LastFailure { get; set; }
        public DateTime LastSuccess { get; set; }
        public DateTime NextRetryTime { get; set; }
        public string LastErrorMessage { get; set; }

        public CircuitBreakerState(string componentName)
        {
            ComponentName = componentName;
            Status = CircuitBreakerStatus.Closed;
            FailureCount = 0;
            LastSuccess = DateTime.UtcNow;
        }

        public bool CanExecute()
        {
            return Status == CircuitBreakerStatus.Closed ||
                   (Status == CircuitBreakerStatus.HalfOpen && DateTime.UtcNow >= NextRetryTime);
        }

        public void RecordSuccess()
        {
            FailureCount = 0;
            LastSuccess = DateTime.UtcNow;
            Status = CircuitBreakerStatus.Closed;
        }

        public void RecordFailure(string errorMessage, int threshold, TimeSpan retryInterval)
        {
            FailureCount++;
            LastFailure = DateTime.UtcNow;
            LastErrorMessage = errorMessage;

            if (FailureCount >= threshold)
            {
                Status = CircuitBreakerStatus.Open;
                NextRetryTime = DateTime.UtcNow.Add(retryInterval);
            }
        }

        public void TryHalfOpen()
        {
            if (Status == CircuitBreakerStatus.Open && DateTime.UtcNow >= NextRetryTime)
            {
                Status = CircuitBreakerStatus.HalfOpen;
            }
        }
    }

    /// <summary>
    /// Circuit breaker status enumeration
    /// </summary>
    public enum CircuitBreakerStatus
    {
        Closed,   // Normal operation
        Open,     // Failures detected, blocking execution
        HalfOpen  // Testing if component has recovered
    }
}
