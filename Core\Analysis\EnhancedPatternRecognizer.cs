using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Interfaces;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Enhanced pattern recognizer for institutional footprint and order flow analysis
    /// Provides sophisticated pattern detection across multiple timeframes
    /// </summary>
    public class EnhancedPatternRecognizer : IPatternRecognizer
    {
        #region Fields

        private readonly object _lockObject = new object();
        private readonly Action<string> _logAction;

        private PatternRecognitionConfig _config;
        private PatternRecognitionMetrics _performanceMetrics;
        private List<MultiTimeframePattern> _patternHistory;
        private bool _isInitialized;

        // Pattern detection state
        private Dictionary<PatternType, DateTime> _lastPatternDetection;
        private Dictionary<Timeframe, List<VolumeCluster>> _volumeClusters;

        #endregion

        #region Constructor

        public EnhancedPatternRecognizer(Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _performanceMetrics = new PatternRecognitionMetrics();
            _patternHistory = new List<MultiTimeframePattern>();
            _lastPatternDetection = new Dictionary<PatternType, DateTime>();
            _volumeClusters = new Dictionary<Timeframe, List<VolumeCluster>>();
            _isInitialized = false;
        }

        #endregion

        #region Public Methods

        public void Initialize(PatternRecognitionConfig config = null)
        {
            lock (_lockObject)
            {
                _config = config ?? new PatternRecognitionConfig();
                _performanceMetrics = new PatternRecognitionMetrics();
                _patternHistory = new List<MultiTimeframePattern>();
                _lastPatternDetection = new Dictionary<PatternType, DateTime>();
                _volumeClusters = new Dictionary<Timeframe, List<VolumeCluster>>();
                _isInitialized = true;

                _logAction("🔧 Enhanced Pattern Recognizer initialized with advanced detection algorithms");
            }
        }

        public List<VolumePattern> DetectVolumePatterns(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            if (!_isInitialized)
                throw new InvalidOperationException("Pattern recognizer must be initialized before detecting patterns");

            if (timeframeData == null || timeframeData.Count == 0)
                return new List<VolumePattern>();

            lock (_lockObject)
            {
                var detectedPatterns = new List<VolumePattern>();
                var startTime = DateTime.UtcNow;

                try
                {
                    _logAction("🔍 Starting volume pattern detection across timeframes...");

                    // Detect accumulation patterns
                    var accumulationPatterns = DetectAccumulationPatterns(timeframeData);
                    detectedPatterns.AddRange(accumulationPatterns);

                    // Detect distribution patterns
                    var distributionPatterns = DetectDistributionPatterns(timeframeData);
                    detectedPatterns.AddRange(distributionPatterns);

                    // Detect volume clusters
                    var volumeClusterPatterns = DetectVolumeClusterPatterns(timeframeData);
                    detectedPatterns.AddRange(volumeClusterPatterns);

                    // Update performance metrics
                    _performanceMetrics.TotalPatternsDetected += detectedPatterns.Count;
                    _performanceMetrics.AverageDetectionTime = (decimal)(DateTime.UtcNow - startTime).TotalMilliseconds;

                    // Update pattern type frequency
                    foreach (var pattern in detectedPatterns)
                    {
                        if (!_performanceMetrics.PatternTypeFrequency.ContainsKey(pattern.Type))
                            _performanceMetrics.PatternTypeFrequency[pattern.Type] = 0;
                        _performanceMetrics.PatternTypeFrequency[pattern.Type]++;
                    }

                    _logAction($"✅ Volume pattern detection completed: {detectedPatterns.Count} patterns found");
                    return detectedPatterns;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Volume pattern detection error: {ex.Message}");
                    return new List<VolumePattern>();
                }
            }
        }

        public InstitutionalFootprint DetectInstitutionalFootprint(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            if (!_isInitialized || timeframeData == null)
                return new InstitutionalFootprint();

            lock (_lockObject)
            {
                try
                {
                    _logAction("🏛️ Analyzing institutional footprint across timeframes...");

                    var footprint = new InstitutionalFootprint
                    {
                        LastUpdate = DateTime.UtcNow
                    };

                    // Analyze each timeframe for institutional activity
                    foreach (var kvp in timeframeData)
                    {
                        var timeframe = kvp.Key;
                        var data = kvp.Value;

                        if (data.Count < 10) continue;

                        var activity = AnalyzeInstitutionalActivityInTimeframe(data, timeframe);
                        footprint.TimeframeActivity[timeframe] = activity;
                    }

                    // Calculate overall institutional activity
                    if (footprint.TimeframeActivity.Count > 0)
                    {
                        footprint.ActivityLevel = footprint.TimeframeActivity.Values.Average();
                        footprint.IsDetected = footprint.ActivityLevel >= _config.MinInstitutionalConfidence;
                        footprint.Confidence = Math.Min(1.0m, footprint.ActivityLevel * 1.2m);
                    }

                    // Determine activity type
                    footprint.ActivityType = DetermineInstitutionalActivityType(timeframeData, footprint);

                    // Calculate stealth level and impact
                    footprint.StealthLevel = CalculateStealthLevel(timeframeData, footprint);
                    footprint.VolumeImpact = CalculateVolumeImpact(timeframeData);
                    footprint.PriceImpact = CalculatePriceImpact(timeframeData);

                    // Estimate duration and completion
                    EstimateInstitutionalActivityDuration(footprint, timeframeData);

                    _logAction($"🏛️ Institutional footprint analysis: {(footprint.IsDetected ? "DETECTED" : "Not detected")} " +
                              $"(Activity: {footprint.ActivityLevel:P1}, Type: {footprint.ActivityType})");

                    return footprint;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Institutional footprint detection error: {ex.Message}");
                    return new InstitutionalFootprint();
                }
            }
        }

        public MarketParticipantAnalysis AnalyzeMarketParticipants(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            if (!_isInitialized || timeframeData == null)
                return new MarketParticipantAnalysis();

            lock (_lockObject)
            {
                try
                {
                    _logAction("👥 Analyzing market participant behavior across timeframes...");

                    var analysis = new MarketParticipantAnalysis
                    {
                        Timestamp = DateTime.UtcNow
                    };

                    // Analyze each timeframe
                    foreach (var kvp in timeframeData)
                    {
                        var timeframe = kvp.Key;
                        var data = kvp.Value;

                        if (data.Count < 10) continue;

                        var breakdown = AnalyzeParticipantBreakdown(data, timeframe);
                        analysis.TimeframeBreakdown[timeframe] = breakdown;
                    }

                    // Calculate overall participant activity
                    if (analysis.TimeframeBreakdown.Count > 0)
                    {
                        analysis.InstitutionalActivity = analysis.TimeframeBreakdown.Values.Average(b => b.InstitutionalPercentage);
                        analysis.RetailActivity = analysis.TimeframeBreakdown.Values.Average(b => b.RetailPercentage);
                        analysis.MarketMakerActivity = analysis.TimeframeBreakdown.Values.Average(b => b.MarketMakerPercentage);

                        // Determine dominance
                        analysis.Dominance = DetermineParticipantDominance(analysis);
                    }

                    // Add activity indicators
                    analysis.ActivityIndicators = GenerateActivityIndicators(analysis, timeframeData);

                    _logAction($"👥 Market participant analysis: {analysis.Dominance} dominance " +
                              $"(Institutional: {analysis.InstitutionalActivity:P1}, Retail: {analysis.RetailActivity:P1}, MM: {analysis.MarketMakerActivity:P1})");

                    return analysis;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Market participant analysis error: {ex.Message}");
                    return new MarketParticipantAnalysis();
                }
            }
        }

        public OrderFlowImbalance DetectOrderFlowImbalance(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            if (!_isInitialized || timeframeData == null)
                return new OrderFlowImbalance();

            lock (_lockObject)
            {
                try
                {
                    _logAction("⚖️ Detecting order flow imbalances across timeframes...");

                    var imbalance = new OrderFlowImbalance
                    {
                        LastUpdate = DateTime.UtcNow
                    };

                    // Analyze imbalance in each timeframe
                    foreach (var kvp in timeframeData)
                    {
                        var timeframe = kvp.Key;
                        var data = kvp.Value;

                        if (data.Count < 5) continue;

                        var timeframeImbalance = CalculateTimeframeImbalance(data);
                        imbalance.TimeframeImbalances[timeframe] = timeframeImbalance;
                    }

                    // Calculate overall imbalance
                    if (imbalance.TimeframeImbalances.Count > 0)
                    {
                        imbalance.ImbalanceRatio = imbalance.TimeframeImbalances.Values.Average();
                        imbalance.Magnitude = Math.Abs(imbalance.ImbalanceRatio);
                    }

                    // Determine direction and intensity
                    imbalance.Direction = DetermineOrderFlowDirection(imbalance.ImbalanceRatio);
                    imbalance.Intensity = DetermineOrderFlowIntensity(imbalance.Magnitude);

                    // Cross-timeframe validation
                    var confirmingTimeframes = imbalance.TimeframeImbalances
                        .Where(kvp => Math.Sign(kvp.Value) == Math.Sign(imbalance.ImbalanceRatio) && Math.Abs(kvp.Value) >= _config.OrderFlowImbalanceThreshold)
                        .Count();

                    imbalance.IsCrossTimeframeConfirmed = confirmingTimeframes >= _config.MinCrossTimeframeConfirmations;
                    imbalance.ValidationScore = (decimal)confirmingTimeframes / imbalance.TimeframeImbalances.Count;

                    // Calculate persistence and acceleration
                    CalculateImbalancePersistence(imbalance, timeframeData);

                    _logAction($"⚖️ Order flow imbalance: {imbalance.Direction} ({imbalance.Intensity}) " +
                              $"Ratio: {imbalance.ImbalanceRatio:F3}, Validated: {imbalance.IsCrossTimeframeConfirmed}");

                    return imbalance;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Order flow imbalance detection error: {ex.Message}");
                    return new OrderFlowImbalance();
                }
            }
        }

        public PatternValidationResult ValidatePattern(MultiTimeframePattern pattern, Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            if (!_isInitialized || pattern == null || timeframeData == null)
                return new PatternValidationResult { IsValid = false };

            lock (_lockObject)
            {
                try
                {
                    _logAction($"✅ Validating pattern: {pattern.PatternName} across timeframes...");

                    var validation = new PatternValidationResult
                    {
                        ValidationTimestamp = DateTime.UtcNow
                    };

                    // Validate pattern in each timeframe
                    foreach (var kvp in timeframeData)
                    {
                        var timeframe = kvp.Key;
                        var data = kvp.Value;

                        if (data.Count < 10) continue;

                        var isValidInTimeframe = ValidatePatternInTimeframe(pattern, data, timeframe);
                        if (isValidInTimeframe)
                        {
                            validation.ValidatingTimeframes.Add(timeframe);
                            validation.ValidationFactors.Add($"Pattern confirmed in {timeframe} timeframe");
                        }
                        else
                        {
                            validation.ConflictingTimeframes.Add(timeframe);
                            validation.ConflictingFactors.Add($"Pattern not confirmed in {timeframe} timeframe");
                        }
                    }

                    // Calculate validation score
                    var totalTimeframes = validation.ValidatingTimeframes.Count + validation.ConflictingTimeframes.Count;
                    validation.ValidationScore = totalTimeframes > 0 ? 
                        (decimal)validation.ValidatingTimeframes.Count / totalTimeframes : 0;

                    // Determine if pattern is valid
                    validation.IsValid = validation.ValidationScore >= 0.6m && 
                                        validation.ValidatingTimeframes.Count >= 2;

                    if (validation.IsValid)
                    {
                        _performanceMetrics.ValidatedPatterns++;
                        if (!_performanceMetrics.PatternTypeAccuracy.ContainsKey(pattern.Type))
                            _performanceMetrics.PatternTypeAccuracy[pattern.Type] = 0;
                        _performanceMetrics.PatternTypeAccuracy[pattern.Type] = 
                            (_performanceMetrics.PatternTypeAccuracy[pattern.Type] + validation.ValidationScore) / 2;
                    }

                    _logAction($"✅ Pattern validation: {(validation.IsValid ? "VALID" : "INVALID")} " +
                              $"(Score: {validation.ValidationScore:P1}, Confirming: {validation.ValidatingTimeframes.Count})");

                    return validation;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Pattern validation error: {ex.Message}");
                    return new PatternValidationResult { IsValid = false };
                }
            }
        }

        public PatternRecognitionMetrics GetPerformanceMetrics()
        {
            lock (_lockObject)
            {
                return new PatternRecognitionMetrics
                {
                    LastUpdate = _performanceMetrics.LastUpdate,
                    TotalPatternsDetected = _performanceMetrics.TotalPatternsDetected,
                    ValidatedPatterns = _performanceMetrics.ValidatedPatterns,
                    FalsePositives = _performanceMetrics.FalsePositives,
                    PatternAccuracy = _performanceMetrics.PatternAccuracy,
                    AverageDetectionTime = _performanceMetrics.AverageDetectionTime,
                    PatternTypeFrequency = new Dictionary<PatternType, int>(_performanceMetrics.PatternTypeFrequency),
                    PatternTypeAccuracy = new Dictionary<PatternType, decimal>(_performanceMetrics.PatternTypeAccuracy)
                };
            }
        }

        public void Reset()
        {
            lock (_lockObject)
            {
                _patternHistory.Clear();
                _lastPatternDetection.Clear();
                _volumeClusters.Clear();
                _performanceMetrics = new PatternRecognitionMetrics();
                _isInitialized = false;
                _logAction("🔄 Enhanced Pattern Recognizer reset to initial state");
            }
        }

        #endregion

        #region Private Methods - Volume Pattern Detection

        private List<VolumePattern> DetectAccumulationPatterns(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            var patterns = new List<VolumePattern>();

            foreach (var kvp in timeframeData)
            {
                var timeframe = kvp.Key;
                var data = kvp.Value;

                if (data.Count < 20) continue;

                // Look for accumulation: increasing volume with stable/rising prices
                var recentData = data.TakeLast(20).ToList();
                var volumeTrend = CalculateVolumeTrend(recentData);
                var priceStability = CalculatePriceStability(recentData);
                var volumeIncrease = CalculateVolumeIncrease(recentData);

                if (volumeTrend > 0.3m && priceStability > 0.7m && volumeIncrease > _config.AccumulationThreshold)
                {
                    var pattern = new VolumePattern
                    {
                        PatternName = $"Accumulation_{timeframe}",
                        Type = PatternType.Accumulation,
                        Confidence = (volumeTrend + priceStability + volumeIncrease) / 3,
                        StartTime = recentData.First().Timestamp,
                        EndTime = recentData.Last().Timestamp,
                        Phase = PatternPhase.Development,
                        ConfirmingTimeframes = new List<Timeframe> { timeframe },
                        VolumeAccumulation = recentData.Sum(d => d.Volume),
                        PriceImpact = CalculatePriceImpactForPattern(recentData),
                        Description = $"Accumulation pattern detected in {timeframe} timeframe with {volumeIncrease:P1} volume increase"
                    };

                    patterns.Add(pattern);
                }
            }

            return patterns;
        }

        private List<VolumePattern> DetectDistributionPatterns(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            var patterns = new List<VolumePattern>();

            foreach (var kvp in timeframeData)
            {
                var timeframe = kvp.Key;
                var data = kvp.Value;

                if (data.Count < 20) continue;

                // Look for distribution: high volume with price weakness
                var recentData = data.TakeLast(20).ToList();
                var volumeSpike = CalculateVolumeSpike(recentData);
                var priceWeakness = CalculatePriceWeakness(recentData);
                var distributionScore = (volumeSpike + priceWeakness) / 2;

                if (distributionScore > _config.DistributionThreshold)
                {
                    var pattern = new VolumePattern
                    {
                        PatternName = $"Distribution_{timeframe}",
                        Type = PatternType.Distribution,
                        Confidence = distributionScore,
                        StartTime = recentData.First().Timestamp,
                        EndTime = recentData.Last().Timestamp,
                        Phase = PatternPhase.Development,
                        ConfirmingTimeframes = new List<Timeframe> { timeframe },
                        VolumeAccumulation = recentData.Sum(d => d.Volume),
                        PriceImpact = CalculatePriceImpactForPattern(recentData),
                        Description = $"Distribution pattern detected in {timeframe} timeframe with {distributionScore:P1} confidence"
                    };

                    patterns.Add(pattern);
                }
            }

            return patterns;
        }

        private List<VolumePattern> DetectVolumeClusterPatterns(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            var patterns = new List<VolumePattern>();

            foreach (var kvp in timeframeData)
            {
                var timeframe = kvp.Key;
                var data = kvp.Value;

                if (data.Count < 10) continue;

                // Detect volume clusters (areas of high volume concentration)
                var clusters = IdentifyVolumeClusters(data, timeframe);
                _volumeClusters[timeframe] = clusters;

                foreach (var cluster in clusters)
                {
                    if (cluster.Significance > 0.7m)
                    {
                        var pattern = new VolumePattern
                        {
                            PatternName = $"VolumeCluster_{timeframe}",
                            Type = PatternType.VolumeCluster,
                            Confidence = cluster.Significance,
                            StartTime = cluster.StartTime,
                            EndTime = cluster.EndTime,
                            Phase = PatternPhase.Maturation,
                            ConfirmingTimeframes = new List<Timeframe> { timeframe },
                            VolumeAccumulation = cluster.TotalVolume,
                            PriceImpact = cluster.PriceRange,
                            Description = $"Volume cluster at {cluster.CenterPrice:F2} with {cluster.TotalVolume:N0} volume"
                        };

                        patterns.Add(pattern);
                    }
                }
            }

            return patterns;
        }

        #region Private Methods - Institutional Analysis

        private decimal AnalyzeInstitutionalActivityInTimeframe(List<MarketDataPoint> data, Timeframe timeframe)
        {
            if (data.Count < 5) return 0;

            var recentData = data.TakeLast(10).ToList();
            var averageVolume = recentData.Average(d => d.Volume);
            var averageDelta = recentData.Average(d => Math.Abs(d.Delta));

            // Look for institutional signatures
            var largeBlockActivity = DetectLargeBlockActivity(recentData, averageVolume);
            var stealthTradingActivity = DetectStealthTradingActivity(recentData);
            var absorptionActivity = DetectAbsorptionActivity(recentData);

            // Weight the activities
            var institutionalScore = (largeBlockActivity * 0.4m + stealthTradingActivity * 0.3m + absorptionActivity * 0.3m);

            return Math.Min(1.0m, institutionalScore);
        }

        private decimal DetectLargeBlockActivity(List<MarketDataPoint> data, decimal averageVolume)
        {
            var largeBlocks = data.Where(d => d.Volume > averageVolume * _config.InstitutionalVolumeThreshold).ToList();
            if (largeBlocks.Count == 0) return 0;

            var blockRatio = (decimal)largeBlocks.Count / data.Count;
            var volumeRatio = largeBlocks.Sum(d => d.Volume) / data.Sum(d => d.Volume);

            return (blockRatio + volumeRatio) / 2;
        }

        private decimal DetectStealthTradingActivity(List<MarketDataPoint> data)
        {
            // Look for consistent small orders that accumulate to large positions
            var volumeConsistency = CalculateVolumeConsistency(data);
            var deltaConsistency = CalculateDeltaConsistency(data);
            var priceImpactMinimization = CalculatePriceImpactMinimization(data);

            var stealthScore = (volumeConsistency + deltaConsistency + priceImpactMinimization) / 3;
            return stealthScore > _config.StealthTradingThreshold ? stealthScore : 0;
        }

        private decimal DetectAbsorptionActivity(List<MarketDataPoint> data)
        {
            // Look for high volume with minimal price movement
            var totalVolume = data.Sum(d => d.Volume);
            var priceRange = data.Max(d => d.High) - data.Min(d => d.Low);
            var averagePrice = data.Average(d => (d.High + d.Low) / 2);

            if (averagePrice == 0) return 0;

            var volumeToRangeRatio = totalVolume / Math.Max(1, (decimal)(priceRange / averagePrice * 10000)); // basis points
            return Math.Min(1.0m, volumeToRangeRatio / 1000000); // Normalize
        }

        private InstitutionalActivityType DetermineInstitutionalActivityType(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData, InstitutionalFootprint footprint)
        {
            if (!footprint.IsDetected) return InstitutionalActivityType.Unknown;

            // Analyze patterns across timeframes to determine activity type
            var volumeIncreasing = IsVolumeIncreasingAcrossTimeframes(timeframeData);
            var priceStable = IsPriceStableAcrossTimeframes(timeframeData);
            var deltaImbalance = GetDeltaImbalanceAcrossTimeframes(timeframeData);

            if (volumeIncreasing && priceStable && Math.Abs(deltaImbalance) < 0.3m)
                return InstitutionalActivityType.Accumulation;

            if (volumeIncreasing && !priceStable && deltaImbalance < -0.5m)
                return InstitutionalActivityType.Distribution;

            if (footprint.StealthLevel > 0.7m)
                return InstitutionalActivityType.StealthTrading;

            if (priceStable && footprint.VolumeImpact > 0.8m)
                return InstitutionalActivityType.Absorption;

            return InstitutionalActivityType.Unknown;
        }

        private decimal CalculateStealthLevel(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData, InstitutionalFootprint footprint)
        {
            if (!footprint.IsDetected) return 0;

            var stealthFactors = new List<decimal>();

            foreach (var kvp in timeframeData)
            {
                var data = kvp.Value;
                if (data.Count < 5) continue;

                var volumeConsistency = CalculateVolumeConsistency(data);
                var priceImpactMinimization = CalculatePriceImpactMinimization(data);

                stealthFactors.Add((volumeConsistency + priceImpactMinimization) / 2);
            }

            return stealthFactors.Count > 0 ? stealthFactors.Average() : 0;
        }

        private decimal CalculateVolumeImpact(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            var impacts = new List<decimal>();

            foreach (var kvp in timeframeData)
            {
                var data = kvp.Value;
                if (data.Count < 5) continue;

                var totalVolume = data.Sum(d => d.Volume);
                var averageVolume = data.Average(d => d.Volume);
                var volumeSpikes = data.Count(d => d.Volume > averageVolume * 2);

                var impact = (decimal)volumeSpikes / data.Count;
                impacts.Add(impact);
            }

            return impacts.Count > 0 ? impacts.Average() : 0;
        }

        private decimal CalculatePriceImpact(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            var impacts = new List<decimal>();

            foreach (var kvp in timeframeData)
            {
                var data = kvp.Value;
                if (data.Count < 5) continue;

                var priceRange = data.Max(d => d.High) - data.Min(d => d.Low);
                var averagePrice = data.Average(d => (d.High + d.Low) / 2);

                if (averagePrice > 0)
                {
                    var impact = priceRange / averagePrice;
                    impacts.Add(impact);
                }
            }

            return impacts.Count > 0 ? impacts.Average() : 0;
        }

        private void EstimateInstitutionalActivityDuration(InstitutionalFootprint footprint, Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            if (!footprint.IsDetected) return;

            // Estimate based on activity type and current progress
            var baseDuration = footprint.ActivityType switch
            {
                InstitutionalActivityType.Accumulation => TimeSpan.FromHours(4),
                InstitutionalActivityType.Distribution => TimeSpan.FromHours(2),
                InstitutionalActivityType.StealthTrading => TimeSpan.FromHours(8),
                InstitutionalActivityType.Absorption => TimeSpan.FromMinutes(30),
                _ => TimeSpan.FromHours(1)
            };

            footprint.Duration = baseDuration;
            footprint.IsOngoing = footprint.ActivityLevel > 0.5m;
            footprint.EstimatedCompletion = DateTime.UtcNow.Add(baseDuration);
        }

        #endregion

        #region Private Methods - Market Participant Analysis

        private ParticipantBreakdown AnalyzeParticipantBreakdown(List<MarketDataPoint> data, Timeframe timeframe)
        {
            if (data.Count < 5) return new ParticipantBreakdown();

            var recentData = data.TakeLast(10).ToList();

            // Analyze trading patterns to identify participant types
            var institutionalSignals = AnalyzeInstitutionalSignals(recentData);
            var retailSignals = AnalyzeRetailSignals(recentData);
            var marketMakerSignals = AnalyzeMarketMakerSignals(recentData);

            var total = institutionalSignals + retailSignals + marketMakerSignals;
            if (total == 0) total = 1; // Avoid division by zero

            return new ParticipantBreakdown
            {
                InstitutionalPercentage = institutionalSignals / total,
                RetailPercentage = retailSignals / total,
                MarketMakerPercentage = marketMakerSignals / total,
                Confidence = Math.Min(1.0m, total / 3) // Confidence based on signal strength
            };
        }

        private decimal AnalyzeInstitutionalSignals(List<MarketDataPoint> data)
        {
            var averageVolume = data.Average(d => d.Volume);
            var largeOrders = data.Count(d => d.Volume > averageVolume * 2);
            var stealthTrading = DetectStealthTradingActivity(data);

            return ((decimal)largeOrders / data.Count) + stealthTrading;
        }

        private decimal AnalyzeRetailSignals(List<MarketDataPoint> data)
        {
            // Retail typically shows: small orders, momentum following, emotional trading
            var averageVolume = data.Average(d => d.Volume);
            var smallOrders = data.Count(d => d.Volume < averageVolume * 0.5m);
            var momentumFollowing = CalculateMomentumFollowing(data);

            return ((decimal)smallOrders / data.Count) + momentumFollowing;
        }

        private decimal AnalyzeMarketMakerSignals(List<MarketDataPoint> data)
        {
            // Market makers typically show: consistent volume, tight spreads, mean reversion
            var volumeConsistency = CalculateVolumeConsistency(data);
            var meanReversion = CalculateMeanReversionTendency(data);

            return (volumeConsistency + meanReversion) / 2;
        }

        private ParticipantDominance DetermineParticipantDominance(MarketParticipantAnalysis analysis)
        {
            var maxActivity = Math.Max(analysis.InstitutionalActivity,
                             Math.Max(analysis.RetailActivity, analysis.MarketMakerActivity));

            if (maxActivity < 0.4m) return ParticipantDominance.Balanced;

            if (analysis.InstitutionalActivity == maxActivity) return ParticipantDominance.Institutional;
            if (analysis.RetailActivity == maxActivity) return ParticipantDominance.Retail;
            if (analysis.MarketMakerActivity == maxActivity) return ParticipantDominance.MarketMaker;

            return ParticipantDominance.Unknown;
        }

        private List<string> GenerateActivityIndicators(MarketParticipantAnalysis analysis, Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            var indicators = new List<string>();

            if (analysis.InstitutionalActivity > 0.6m)
                indicators.Add("High institutional activity detected");

            if (analysis.RetailActivity > 0.6m)
                indicators.Add("High retail activity detected");

            if (analysis.MarketMakerActivity > 0.6m)
                indicators.Add("High market maker activity detected");

            if (analysis.Dominance == ParticipantDominance.Balanced)
                indicators.Add("Balanced participant activity");

            return indicators;
        }

        #endregion

        #region Private Methods - Order Flow Analysis

        private decimal CalculateTimeframeImbalance(List<MarketDataPoint> data)
        {
            if (data.Count == 0) return 0;

            var totalVolume = data.Sum(d => d.Volume);
            var totalDelta = data.Sum(d => d.Delta);

            if (totalVolume == 0) return 0;

            return totalDelta / totalVolume; // Returns -1 to 1 (sell to buy pressure)
        }

        private OrderFlowDirection DetermineOrderFlowDirection(decimal imbalanceRatio)
        {
            if (imbalanceRatio > 0.3m) return OrderFlowDirection.BuyPressure;
            if (imbalanceRatio < -0.3m) return OrderFlowDirection.SellPressure;
            if (Math.Abs(imbalanceRatio) < 0.1m) return OrderFlowDirection.Balanced;
            return OrderFlowDirection.Erratic;
        }

        private OrderFlowIntensity DetermineOrderFlowIntensity(decimal magnitude)
        {
            if (magnitude > 0.8m) return OrderFlowIntensity.Extreme;
            if (magnitude > 0.6m) return OrderFlowIntensity.High;
            if (magnitude > 0.3m) return OrderFlowIntensity.Moderate;
            return OrderFlowIntensity.Low;
        }

        private void CalculateImbalancePersistence(OrderFlowImbalance imbalance, Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            // Calculate how long the imbalance has persisted
            var persistenceDurations = new List<TimeSpan>();

            foreach (var kvp in timeframeData)
            {
                var data = kvp.Value;
                if (data.Count < 5) continue;

                var duration = CalculateImbalanceDurationInTimeframe(data, imbalance.Direction);
                persistenceDurations.Add(duration);
            }

            if (persistenceDurations.Count > 0)
            {
                imbalance.Duration = TimeSpan.FromTicks((long)persistenceDurations.Average(d => d.Ticks));
                imbalance.Persistence = Math.Min(1.0m, (decimal)imbalance.Duration.TotalMinutes / 30); // Normalize to 30 minutes

                // Check if accelerating
                var recentImbalances = GetRecentImbalances(timeframeData);
                imbalance.IsAccelerating = IsImbalanceAccelerating(recentImbalances);
            }
        }

        private TimeSpan CalculateImbalanceDurationInTimeframe(List<MarketDataPoint> data, OrderFlowDirection direction)
        {
            var consecutiveBars = 0;
            var timeframeMinutes = 1; // Default to 1 minute, would be determined by actual timeframe

            for (int i = data.Count - 1; i >= 0; i--)
            {
                var bar = data[i];
                var barDirection = DetermineOrderFlowDirection(bar.Volume > 0 ? bar.Delta / bar.Volume : 0);

                if (barDirection == direction)
                    consecutiveBars++;
                else
                    break;
            }

            return TimeSpan.FromMinutes(consecutiveBars * timeframeMinutes);
        }

        private List<decimal> GetRecentImbalances(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            var imbalances = new List<decimal>();

            foreach (var kvp in timeframeData)
            {
                var data = kvp.Value.TakeLast(5).ToList();
                foreach (var bar in data)
                {
                    if (bar.Volume > 0)
                        imbalances.Add(bar.Delta / bar.Volume);
                }
            }

            return imbalances;
        }

        private bool IsImbalanceAccelerating(List<decimal> recentImbalances)
        {
            if (recentImbalances.Count < 3) return false;

            var recent = recentImbalances.TakeLast(3).ToList();
            var trend = (recent[2] - recent[0]) / 2; // Simple trend calculation

            return Math.Abs(trend) > 0.1m; // Threshold for acceleration
        }

        #endregion

        #region Private Methods - Pattern Validation

        private bool ValidatePatternInTimeframe(MultiTimeframePattern pattern, List<MarketDataPoint> data, Timeframe timeframe)
        {
            if (data.Count < 5) return false;

            return pattern.Type switch
            {
                PatternType.Accumulation => ValidateAccumulationPattern(data),
                PatternType.Distribution => ValidateDistributionPattern(data),
                PatternType.VolumeCluster => ValidateVolumeClusterPattern(data),
                PatternType.InstitutionalFootprint => ValidateInstitutionalPattern(data),
                PatternType.OrderFlowImbalance => ValidateOrderFlowPattern(data),
                _ => false
            };
        }

        private bool ValidateAccumulationPattern(List<MarketDataPoint> data)
        {
            var recentData = data.TakeLast(10).ToList();
            var volumeTrend = CalculateVolumeTrend(recentData);
            var priceStability = CalculatePriceStability(recentData);

            return volumeTrend > 0.3m && priceStability > 0.6m;
        }

        private bool ValidateDistributionPattern(List<MarketDataPoint> data)
        {
            var recentData = data.TakeLast(10).ToList();
            var volumeSpike = CalculateVolumeSpike(recentData);
            var priceWeakness = CalculatePriceWeakness(recentData);

            return volumeSpike > 0.6m && priceWeakness > 0.5m;
        }

        private bool ValidateVolumeClusterPattern(List<MarketDataPoint> data)
        {
            var clusters = IdentifyVolumeClusters(data, Timeframe.M5); // Use M5 as default
            return clusters.Any(c => c.Significance > 0.6m);
        }

        private bool ValidateInstitutionalPattern(List<MarketDataPoint> data)
        {
            var institutionalActivity = AnalyzeInstitutionalActivityInTimeframe(data, Timeframe.M5);
            return institutionalActivity > 0.6m;
        }

        private bool ValidateOrderFlowPattern(List<MarketDataPoint> data)
        {
            var imbalance = CalculateTimeframeImbalance(data);
            return Math.Abs(imbalance) > 0.5m;
        }

        #endregion

        #region Private Methods - Utility Calculations

        private decimal CalculateVolumeTrend(List<MarketDataPoint> data)
        {
            if (data.Count < 3) return 0;

            var firstHalf = data.Take(data.Count / 2).Average(d => d.Volume);
            var secondHalf = data.Skip(data.Count / 2).Average(d => d.Volume);

            return firstHalf > 0 ? (secondHalf - firstHalf) / firstHalf : 0;
        }

        private decimal CalculatePriceStability(List<MarketDataPoint> data)
        {
            if (data.Count < 2) return 0;

            var priceRange = data.Max(d => d.High) - data.Min(d => d.Low);
            var averagePrice = data.Average(d => (d.High + d.Low) / 2);

            if (averagePrice == 0) return 0;

            var volatility = priceRange / averagePrice;
            return Math.Max(0, 1 - volatility * 10); // Invert volatility to get stability
        }

        private decimal CalculateVolumeIncrease(List<MarketDataPoint> data)
        {
            if (data.Count < 5) return 0;

            var baseline = data.Take(data.Count / 2).Average(d => d.Volume);
            var recent = data.Skip(data.Count / 2).Average(d => d.Volume);

            return baseline > 0 ? (recent - baseline) / baseline : 0;
        }

        private decimal CalculateVolumeSpike(List<MarketDataPoint> data)
        {
            if (data.Count < 3) return 0;

            var averageVolume = data.Average(d => d.Volume);
            var maxVolume = data.Max(d => d.Volume);

            return averageVolume > 0 ? maxVolume / averageVolume - 1 : 0;
        }

        private decimal CalculatePriceWeakness(List<MarketDataPoint> data)
        {
            if (data.Count < 3) return 0;

            var priceChange = (data.Last().Close - data.First().Open) / Math.Max(data.First().Open, 1);
            var volumeIncrease = CalculateVolumeIncrease(data);

            // Price weakness = high volume but negative or minimal price movement
            if (volumeIncrease > 0.5m && priceChange < 0.02m)
                return volumeIncrease * (1 - Math.Max(0, priceChange * 10));

            return 0;
        }

        private decimal CalculatePriceImpactForPattern(List<MarketDataPoint> data)
        {
            if (data.Count < 2) return 0;

            var priceChange = Math.Abs(data.Last().Close - data.First().Open);
            var averagePrice = data.Average(d => (d.High + d.Low) / 2);

            return averagePrice > 0 ? priceChange / averagePrice : 0;
        }

        private List<VolumeCluster> IdentifyVolumeClusters(List<MarketDataPoint> data, Timeframe timeframe)
        {
            var clusters = new List<VolumeCluster>();
            if (data.Count < 5) return clusters;

            var averageVolume = data.Average(d => d.Volume);
            var significantBars = data.Where(d => d.Volume > averageVolume * 1.5m).ToList();

            // Group nearby significant bars into clusters
            var currentCluster = new List<MarketDataPoint>();

            foreach (var bar in significantBars.OrderBy(b => b.Timestamp))
            {
                if (currentCluster.Count == 0 ||
                    (bar.Timestamp - currentCluster.Last().Timestamp).TotalMinutes <= (int)timeframe * 3)
                {
                    currentCluster.Add(bar);
                }
                else
                {
                    // Finalize current cluster
                    if (currentCluster.Count >= 2)
                    {
                        clusters.Add(CreateVolumeCluster(currentCluster));
                    }
                    currentCluster = new List<MarketDataPoint> { bar };
                }
            }

            // Don't forget the last cluster
            if (currentCluster.Count >= 2)
            {
                clusters.Add(CreateVolumeCluster(currentCluster));
            }

            return clusters;
        }

        private VolumeCluster CreateVolumeCluster(List<MarketDataPoint> clusterBars)
        {
            return new VolumeCluster
            {
                StartTime = clusterBars.First().Timestamp,
                EndTime = clusterBars.Last().Timestamp,
                CenterPrice = clusterBars.Average(b => (b.High + b.Low) / 2),
                PriceRange = clusterBars.Max(b => b.High) - clusterBars.Min(b => b.Low),
                TotalVolume = clusterBars.Sum(b => b.Volume),
                BarCount = clusterBars.Count,
                Significance = CalculateClusterSignificance(clusterBars)
            };
        }

        private decimal CalculateClusterSignificance(List<MarketDataPoint> clusterBars)
        {
            var totalVolume = clusterBars.Sum(b => b.Volume);
            var averageVolume = clusterBars.Average(b => b.Volume);
            var priceRange = clusterBars.Max(b => b.High) - clusterBars.Min(b => b.Low);
            var averagePrice = clusterBars.Average(b => (b.High + b.Low) / 2);

            // Significance based on volume concentration and price stability
            var volumeConcentration = totalVolume / (clusterBars.Count * averageVolume);
            var priceStability = averagePrice > 0 ? 1 - (priceRange / averagePrice) : 0;

            return Math.Min(1.0m, (volumeConcentration + priceStability) / 2);
        }

        private decimal CalculateVolumeConsistency(List<MarketDataPoint> data)
        {
            if (data.Count < 3) return 0;

            var volumes = data.Select(d => d.Volume).ToList();
            var average = volumes.Average();
            var variance = volumes.Sum(v => (v - average) * (v - average)) / volumes.Count;
            var standardDeviation = (decimal)Math.Sqrt((double)variance);

            return average > 0 ? Math.Max(0, 1 - (standardDeviation / average)) : 0;
        }

        private decimal CalculateDeltaConsistency(List<MarketDataPoint> data)
        {
            if (data.Count < 3) return 0;

            var deltas = data.Select(d => d.Volume > 0 ? d.Delta / d.Volume : 0).ToList();
            var average = deltas.Average();
            var variance = deltas.Sum(d => (d - average) * (d - average)) / deltas.Count;
            var standardDeviation = (decimal)Math.Sqrt((double)variance);

            return Math.Max(0, 1 - standardDeviation * 2); // Lower standard deviation = higher consistency
        }

        private decimal CalculatePriceImpactMinimization(List<MarketDataPoint> data)
        {
            if (data.Count < 2) return 0;

            var totalVolume = data.Sum(d => d.Volume);
            var priceRange = data.Max(d => d.High) - data.Min(d => d.Low);
            var averagePrice = data.Average(d => (d.High + d.Low) / 2);

            if (averagePrice == 0 || totalVolume == 0) return 0;

            var priceImpactRatio = (priceRange / averagePrice) / (totalVolume / 1000000m); // Normalize volume
            return Math.Max(0, 1 - priceImpactRatio * 100); // Lower impact = higher score
        }

        private decimal CalculateMomentumFollowing(List<MarketDataPoint> data)
        {
            if (data.Count < 3) return 0;

            var priceChanges = new List<decimal>();
            var volumeChanges = new List<decimal>();

            for (int i = 1; i < data.Count; i++)
            {
                var priceChange = (data[i].Close - data[i-1].Close) / Math.Max(data[i-1].Close, 1);
                var volumeChange = (data[i].Volume - data[i-1].Volume) / Math.Max(data[i-1].Volume, 1);

                priceChanges.Add(priceChange);
                volumeChanges.Add(volumeChange);
            }

            // Momentum following = volume increases when price moves in same direction
            var correlationSum = 0m;
            for (int i = 0; i < priceChanges.Count; i++)
            {
                if (Math.Sign(priceChanges[i]) == Math.Sign(volumeChanges[i]))
                    correlationSum += Math.Abs(priceChanges[i]) * Math.Abs(volumeChanges[i]);
            }

            return Math.Min(1.0m, correlationSum * 10);
        }

        private decimal CalculateMeanReversionTendency(List<MarketDataPoint> data)
        {
            if (data.Count < 5) return 0;

            var prices = data.Select(d => (d.High + d.Low) / 2).ToList();
            var average = prices.Average();

            var reversionCount = 0;
            for (int i = 1; i < prices.Count - 1; i++)
            {
                var prevDeviation = prices[i-1] - average;
                var currentDeviation = prices[i] - average;
                var nextDeviation = prices[i+1] - average;

                // Check if price reverts toward mean
                if (Math.Abs(nextDeviation) < Math.Abs(currentDeviation) &&
                    Math.Sign(currentDeviation) == Math.Sign(prevDeviation))
                {
                    reversionCount++;
                }
            }

            return (decimal)reversionCount / Math.Max(1, prices.Count - 2);
        }

        private bool IsVolumeIncreasingAcrossTimeframes(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            var increasingCount = 0;
            var totalTimeframes = 0;

            foreach (var kvp in timeframeData)
            {
                var data = kvp.Value;
                if (data.Count < 5) continue;

                totalTimeframes++;
                var volumeTrend = CalculateVolumeTrend(data);
                if (volumeTrend > 0.2m) increasingCount++;
            }

            return totalTimeframes > 0 && (decimal)increasingCount / totalTimeframes >= 0.6m;
        }

        private bool IsPriceStableAcrossTimeframes(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            var stableCount = 0;
            var totalTimeframes = 0;

            foreach (var kvp in timeframeData)
            {
                var data = kvp.Value;
                if (data.Count < 5) continue;

                totalTimeframes++;
                var priceStability = CalculatePriceStability(data);
                if (priceStability > 0.6m) stableCount++;
            }

            return totalTimeframes > 0 && (decimal)stableCount / totalTimeframes >= 0.6m;
        }

        private decimal GetDeltaImbalanceAcrossTimeframes(Dictionary<Timeframe, List<MarketDataPoint>> timeframeData)
        {
            var imbalances = new List<decimal>();

            foreach (var kvp in timeframeData)
            {
                var data = kvp.Value;
                if (data.Count < 3) continue;

                var imbalance = CalculateTimeframeImbalance(data);
                imbalances.Add(imbalance);
            }

            return imbalances.Count > 0 ? imbalances.Average() : 0;
        }

        #endregion

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Volume cluster for pattern analysis
    /// </summary>
    public class VolumeCluster
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public decimal CenterPrice { get; set; }
        public decimal PriceRange { get; set; }
        public decimal TotalVolume { get; set; }
        public decimal Significance { get; set; }
        public int BarCount { get; set; }
    }

    #endregion
}
