using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Monitoring
{
    /// <summary>
    /// Real-time performance monitoring with session-based analysis and alert generation
    /// </summary>
    public class PerformanceTracker : IPerformanceTracker
    {
        private PerformanceMetrics _currentMetrics;
        private readonly List<TradeResult> _allTrades;
        private readonly List<TradingSignal> _allSignals;
        private readonly List<PerformanceAlert> _activeAlerts;
        private readonly object _lockObject = new object();
        
        private decimal _peakEquity;
        private decimal _currentEquity;
        private int _consecutiveWins;
        private int _consecutiveLosses;
        private DateTime _lastPerformanceCheck;

        public PerformanceTracker()
        {
            _allTrades = new List<TradeResult>();
            _allSignals = new List<TradingSignal>();
            _activeAlerts = new List<PerformanceAlert>();
            _currentEquity = 10000m; // Starting equity
            _peakEquity = _currentEquity;
            _lastPerformanceCheck = DateTime.UtcNow;
            
            InitializeMetrics();
        }

        /// <summary>
        /// Record completed trade result with comprehensive analysis
        /// </summary>
        public void RecordTrade(TradeResult trade)
        {
            if (trade == null)
                throw new ArgumentNullException(nameof(trade));

            lock (_lockObject)
            {
                _allTrades.Add(trade);
                
                // Update current equity
                _currentEquity += trade.PnL;
                
                // Update peak equity and drawdown
                if (_currentEquity > _peakEquity)
                {
                    _peakEquity = _currentEquity;
                    _currentMetrics.CurrentDrawdown = 0;
                }
                else
                {
                    _currentMetrics.CurrentDrawdown = (_peakEquity - _currentEquity) / _peakEquity * 100;
                    _currentMetrics.MaxDrawdown = Math.Max(_currentMetrics.MaxDrawdown, _currentMetrics.CurrentDrawdown);
                }

                // Update consecutive win/loss tracking
                if (trade.PnL > 0)
                {
                    _consecutiveWins++;
                    _consecutiveLosses = 0;
                    _currentMetrics.MaxConsecutiveWins = Math.Max(_currentMetrics.MaxConsecutiveWins, _consecutiveWins);
                }
                else
                {
                    _consecutiveLosses++;
                    _consecutiveWins = 0;
                    _currentMetrics.MaxConsecutiveLosses = Math.Max(_currentMetrics.MaxConsecutiveLosses, _consecutiveLosses);
                }

                // Update session-based performance
                UpdateSessionPerformance(trade);
                
                // Update hourly performance
                UpdateHourlyPerformance(trade);
                
                // Recalculate all metrics
                RecalculateMetrics();
                
                // Check for performance alerts
                CheckPerformanceAlerts();
                
                // Keep only last 1000 trades for memory management
                while (_allTrades.Count > 1000)
                {
                    _allTrades.RemoveAt(0);
                }
            }
        }

        /// <summary>
        /// Record signal generation for signal quality tracking
        /// </summary>
        public void RecordSignal(TradingSignal signal, bool wasExecuted)
        {
            if (signal == null)
                throw new ArgumentNullException(nameof(signal));

            lock (_lockObject)
            {
                signal.IsExecuted = wasExecuted;
                signal.ExecutionTime = wasExecuted ? DateTime.UtcNow : null;
                
                _allSignals.Add(signal);
                
                // Update signal metrics
                _currentMetrics.SignalsGenerated++;
                if (wasExecuted)
                {
                    _currentMetrics.SignalsExecuted++;
                }

                // Update average signal confidence and strength
                var totalSignals = _allSignals.Count;
                _currentMetrics.AverageSignalConfidence = _allSignals.Average(s => s.Confidence);
                _currentMetrics.AverageSignalStrength = _allSignals.Average(s => s.Strength);

                // Keep only last 500 signals for memory management
                while (_allSignals.Count > 500)
                {
                    _allSignals.RemoveAt(0);
                }
            }
        }

        /// <summary>
        /// Get current comprehensive performance metrics
        /// </summary>
        public PerformanceMetrics GetPerformanceMetrics()
        {
            lock (_lockObject)
            {
                // Update recent performance before returning
                UpdateRecentPerformance();
                
                return new PerformanceMetrics
                {
                    StartTime = _currentMetrics.StartTime,
                    LastUpdateTime = DateTime.UtcNow,
                    Symbol = _currentMetrics.Symbol,
                    CorrelationId = _currentMetrics.CorrelationId,
                    TotalTrades = _currentMetrics.TotalTrades,
                    WinningTrades = _currentMetrics.WinningTrades,
                    LosingTrades = _currentMetrics.LosingTrades,
                    TotalPnL = _currentMetrics.TotalPnL,
                    TotalWins = _currentMetrics.TotalWins,
                    TotalLosses = _currentMetrics.TotalLosses,
                    MaxDrawdown = _currentMetrics.MaxDrawdown,
                    CurrentDrawdown = _currentMetrics.CurrentDrawdown,
                    MaxConsecutiveLosses = _currentMetrics.MaxConsecutiveLosses,
                    MaxConsecutiveWins = _currentMetrics.MaxConsecutiveWins,
                    SignalsGenerated = _currentMetrics.SignalsGenerated,
                    SignalsExecuted = _currentMetrics.SignalsExecuted,
                    AverageSignalConfidence = _currentMetrics.AverageSignalConfidence,
                    AverageSignalStrength = _currentMetrics.AverageSignalStrength,
                    PerformanceByHour = new Dictionary<string, decimal>(_currentMetrics.PerformanceByHour),
                    PerformanceBySession = new Dictionary<string, decimal>(_currentMetrics.PerformanceBySession),
                    TradesByHour = new Dictionary<string, int>(_currentMetrics.TradesByHour),
                    RecentTrades = new List<TradeResult>(_currentMetrics.RecentTrades),
                    RecentWinRate = _currentMetrics.RecentWinRate,
                    RecentProfitFactor = _currentMetrics.RecentProfitFactor
                };
            }
        }

        /// <summary>
        /// Get performance for specific time period
        /// </summary>
        public PerformanceMetrics GetPerformanceForPeriod(DateTime startTime, DateTime endTime)
        {
            lock (_lockObject)
            {
                var periodTrades = _allTrades.Where(t => t.EntryTime >= startTime && t.EntryTime <= endTime).ToList();
                
                if (periodTrades.Count == 0)
                {
                    return new PerformanceMetrics
                    {
                        StartTime = startTime,
                        LastUpdateTime = endTime,
                        Symbol = _currentMetrics.Symbol
                    };
                }

                return CalculateMetricsForTrades(periodTrades, startTime);
            }
        }

        /// <summary>
        /// Check if performance is degrading based on recent trades
        /// </summary>
        public bool IsPerformanceDegrading(int lookbackTrades = 10)
        {
            lock (_lockObject)
            {
                if (_allTrades.Count < lookbackTrades)
                    return false;

                var recentTrades = _allTrades.TakeLast(lookbackTrades).ToList();
                var recentWinRate = (decimal)recentTrades.Count(t => t.PnL > 0) / recentTrades.Count;
                var recentProfitFactor = CalculateProfitFactor(recentTrades);

                // Consider performance degrading if:
                // 1. Win rate drops below 40%
                // 2. Profit factor drops below 1.0
                // 3. More than 5 consecutive losses
                return recentWinRate < 0.4m || recentProfitFactor < 1.0m || _consecutiveLosses > 5;
            }
        }

        /// <summary>
        /// Get current performance alerts
        /// </summary>
        public List<PerformanceAlert> GetPerformanceAlerts()
        {
            lock (_lockObject)
            {
                // Remove old alerts (older than 1 hour)
                var cutoffTime = DateTime.UtcNow.AddHours(-1);
                _activeAlerts.RemoveAll(a => a.Timestamp < cutoffTime);
                
                return new List<PerformanceAlert>(_activeAlerts);
            }
        }

        /// <summary>
        /// Reset performance tracking
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                _allTrades.Clear();
                _allSignals.Clear();
                _activeAlerts.Clear();
                _currentEquity = 10000m;
                _peakEquity = _currentEquity;
                _consecutiveWins = 0;
                _consecutiveLosses = 0;
                _lastPerformanceCheck = DateTime.UtcNow;
                
                InitializeMetrics();
            }
        }

        #region Private Methods

        private void InitializeMetrics()
        {
            _currentMetrics = new PerformanceMetrics
            {
                StartTime = DateTime.UtcNow,
                LastUpdateTime = DateTime.UtcNow,
                Symbol = "",
                CorrelationId = Guid.NewGuid().ToString("N")[..8],
                PerformanceByHour = new Dictionary<string, decimal>(),
                PerformanceBySession = new Dictionary<string, decimal>(),
                TradesByHour = new Dictionary<string, int>(),
                RecentTrades = new List<TradeResult>()
            };
        }

        private void RecalculateMetrics()
        {
            if (_allTrades.Count == 0)
                return;

            _currentMetrics.TotalTrades = _allTrades.Count;
            _currentMetrics.WinningTrades = _allTrades.Count(t => t.PnL > 0);
            _currentMetrics.LosingTrades = _allTrades.Count(t => t.PnL <= 0);
            
            _currentMetrics.TotalPnL = _allTrades.Sum(t => t.PnL);
            _currentMetrics.TotalWins = _allTrades.Where(t => t.PnL > 0).Sum(t => t.PnL);
            _currentMetrics.TotalLosses = _allTrades.Where(t => t.PnL <= 0).Sum(t => t.PnL);
            
            _currentMetrics.LastUpdateTime = DateTime.UtcNow;
        }

        private void UpdateSessionPerformance(TradeResult trade)
        {
            var session = DetermineSession(trade.EntryTime);
            var sessionKey = session.ToString();

            if (!_currentMetrics.PerformanceBySession.ContainsKey(sessionKey))
                _currentMetrics.PerformanceBySession[sessionKey] = 0;

            _currentMetrics.PerformanceBySession[sessionKey] += trade.PnL;
        }

        private void UpdateHourlyPerformance(TradeResult trade)
        {
            var hour = trade.EntryTime.Hour.ToString("D2");

            if (!_currentMetrics.PerformanceByHour.ContainsKey(hour))
                _currentMetrics.PerformanceByHour[hour] = 0;

            if (!_currentMetrics.TradesByHour.ContainsKey(hour))
                _currentMetrics.TradesByHour[hour] = 0;

            _currentMetrics.PerformanceByHour[hour] += trade.PnL;
            _currentMetrics.TradesByHour[hour]++;
        }

        private void UpdateRecentPerformance()
        {
            var recentTrades = _allTrades.TakeLast(10).ToList();
            _currentMetrics.RecentTrades = recentTrades;

            if (recentTrades.Count > 0)
            {
                _currentMetrics.RecentWinRate = (decimal)recentTrades.Count(t => t.PnL > 0) / recentTrades.Count * 100;
                _currentMetrics.RecentProfitFactor = CalculateProfitFactor(recentTrades);
            }
        }

        private void CheckPerformanceAlerts()
        {
            var now = DateTime.UtcNow;
            
            // Check if enough time has passed since last check (avoid spam)
            if ((now - _lastPerformanceCheck).TotalMinutes < 5)
                return;

            _lastPerformanceCheck = now;

            // Alert for high drawdown
            if (_currentMetrics.CurrentDrawdown > 10)
            {
                AddAlert(AlertType.Warning, "High Drawdown", 
                    $"Current drawdown: {_currentMetrics.CurrentDrawdown:F1}%", 
                    "Consider reducing position size or reviewing strategy parameters");
            }

            // Alert for consecutive losses
            if (_consecutiveLosses >= 5)
            {
                AddAlert(AlertType.Critical, "Consecutive Losses", 
                    $"{_consecutiveLosses} consecutive losing trades", 
                    "Consider pausing trading and reviewing market conditions");
            }

            // Alert for low win rate
            if (_allTrades.Count >= 10 && _currentMetrics.WinRate < 30)
            {
                AddAlert(AlertType.Warning, "Low Win Rate", 
                    $"Win rate: {_currentMetrics.WinRate:F1}%", 
                    "Consider increasing signal threshold for better quality signals");
            }

            // Alert for poor profit factor
            if (_allTrades.Count >= 10 && _currentMetrics.ProfitFactor < 1.0m)
            {
                AddAlert(AlertType.Warning, "Poor Profit Factor", 
                    $"Profit factor: {_currentMetrics.ProfitFactor:F2}", 
                    "Review TP/SL ratios and signal quality");
            }
        }

        private void AddAlert(AlertType type, string message, string details, string recommendedAction)
        {
            // Don't add duplicate alerts
            if (_activeAlerts.Any(a => a.Message == message && a.Details == details))
                return;

            var alert = new PerformanceAlert
            {
                Timestamp = DateTime.UtcNow,
                Type = type,
                Message = message,
                Details = details,
                RecommendedAction = recommendedAction,
                IsAcknowledged = false
            };

            _activeAlerts.Add(alert);

            // Keep only last 20 alerts
            while (_activeAlerts.Count > 20)
            {
                _activeAlerts.RemoveAt(0);
            }
        }

        private TradingSession DetermineSession(DateTime timestamp)
        {
            var utcTime = timestamp.Kind == DateTimeKind.Utc ? timestamp : timestamp.ToUniversalTime();
            var hour = utcTime.Hour;

            // Asian session: 0-8 UTC
            if (hour >= 0 && hour < 8)
                return TradingSession.Asian;
            
            // European session: 7-16 UTC (overlap with Asian 7-8)
            if (hour >= 7 && hour < 16)
                return hour < 8 ? TradingSession.Overlap_AsianEuropean : TradingSession.European;
            
            // US session: 13-22 UTC (overlap with European 13-16)
            if (hour >= 13 && hour < 22)
                return hour < 16 ? TradingSession.Overlap_EuropeanUS : TradingSession.US;

            // Default to 24h for crypto
            return TradingSession.Crypto24H;
        }

        private decimal CalculateProfitFactor(List<TradeResult> trades)
        {
            if (trades.Count == 0)
                return 0;

            var totalWins = trades.Where(t => t.PnL > 0).Sum(t => t.PnL);
            var totalLosses = Math.Abs(trades.Where(t => t.PnL <= 0).Sum(t => t.PnL));

            return totalLosses > 0 ? totalWins / totalLosses : (totalWins > 0 ? 10 : 1);
        }

        private PerformanceMetrics CalculateMetricsForTrades(List<TradeResult> trades, DateTime startTime)
        {
            var metrics = new PerformanceMetrics
            {
                StartTime = startTime,
                LastUpdateTime = DateTime.UtcNow,
                TotalTrades = trades.Count,
                WinningTrades = trades.Count(t => t.PnL > 0),
                LosingTrades = trades.Count(t => t.PnL <= 0),
                TotalPnL = trades.Sum(t => t.PnL),
                TotalWins = trades.Where(t => t.PnL > 0).Sum(t => t.PnL),
                TotalLosses = trades.Where(t => t.PnL <= 0).Sum(t => t.PnL)
            };

            // Calculate drawdown for this period
            decimal peakEquity = 0;
            decimal currentEquity = 0;
            decimal maxDrawdown = 0;

            foreach (var trade in trades.OrderBy(t => t.EntryTime))
            {
                currentEquity += trade.PnL;
                if (currentEquity > peakEquity)
                {
                    peakEquity = currentEquity;
                }
                else
                {
                    var drawdown = peakEquity > 0 ? (peakEquity - currentEquity) / peakEquity * 100 : 0;
                    maxDrawdown = Math.Max(maxDrawdown, drawdown);
                }
            }

            metrics.MaxDrawdown = maxDrawdown;
            metrics.CurrentDrawdown = peakEquity > 0 ? (peakEquity - currentEquity) / peakEquity * 100 : 0;

            return metrics;
        }

        #endregion
    }
}
