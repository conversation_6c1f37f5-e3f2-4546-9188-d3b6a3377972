using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Diagnostics;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Interfaces;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Multi-timeframe analyzer that analyzes 1m, 5m, 15m, and 1h timeframes simultaneously
    /// Provides timeframe-weighted signal synthesis and trend alignment confirmation
    /// </summary>
    public class MultiTimeframeAnalyzer : IMultiTimeframeAnalyzer
    {
        #region Fields

        private readonly IVolumePatternAnalyzer _volumeAnalyzer;
        private readonly IDeltaFlowAnalyzer _deltaAnalyzer;
        private readonly IVolatilityAnalyzer _volatilityAnalyzer;
        private readonly IMarketProfileAnalyzer _marketProfileAnalyzer;
        private readonly IPatternRecognizer _patternRecognizer;

        private readonly object _lockObject = new object();
        private readonly Stopwatch _performanceStopwatch;

        // Configuration and state
        private SymbolProfile _symbolProfile;
        private OptimalSettings _settings;
        private MultiTimeframeConfig _config;
        private MultiTimeframeAnalysisState _currentState;
        private MultiTimeframePerformanceMetrics _performanceMetrics;

        // Timeframe data storage
        private readonly Dictionary<Timeframe, Queue<MarketDataPoint>> _timeframeData;
        private readonly Dictionary<Timeframe, IRealTimeAnalysisEngine> _timeframeEngines;
        private readonly Dictionary<Timeframe, DateTime> _lastAnalysisUpdate;

        #endregion

        #region Properties

        public bool IsInitialized { get; private set; }

        #endregion

        #region Constructor

        public MultiTimeframeAnalyzer(
            IVolumePatternAnalyzer volumeAnalyzer,
            IDeltaFlowAnalyzer deltaAnalyzer,
            IVolatilityAnalyzer volatilityAnalyzer,
            IMarketProfileAnalyzer marketProfileAnalyzer,
            IPatternRecognizer patternRecognizer = null)
        {
            _volumeAnalyzer = volumeAnalyzer ?? throw new ArgumentNullException(nameof(volumeAnalyzer));
            _deltaAnalyzer = deltaAnalyzer ?? throw new ArgumentNullException(nameof(deltaAnalyzer));
            _volatilityAnalyzer = volatilityAnalyzer ?? throw new ArgumentNullException(nameof(volatilityAnalyzer));
            _marketProfileAnalyzer = marketProfileAnalyzer ?? throw new ArgumentNullException(nameof(marketProfileAnalyzer));
            _patternRecognizer = patternRecognizer; // Optional for Phase 4

            _performanceStopwatch = new Stopwatch();
            _timeframeData = new Dictionary<Timeframe, Queue<MarketDataPoint>>();
            _timeframeEngines = new Dictionary<Timeframe, IRealTimeAnalysisEngine>();
            _lastAnalysisUpdate = new Dictionary<Timeframe, DateTime>();

            // Initialize timeframe data structures
            foreach (Timeframe timeframe in Enum.GetValues<Timeframe>())
            {
                _timeframeData[timeframe] = new Queue<MarketDataPoint>();
                _lastAnalysisUpdate[timeframe] = DateTime.MinValue;
            }

            _currentState = new MultiTimeframeAnalysisState();
            _performanceMetrics = new MultiTimeframePerformanceMetrics();
            IsInitialized = false;
        }

        #endregion

        #region Public Methods

        public void Initialize(SymbolProfile symbolProfile, OptimalSettings settings, MultiTimeframeConfig config = null)
        {
            if (symbolProfile == null)
                throw new ArgumentNullException(nameof(symbolProfile));
            if (settings == null)
                throw new ArgumentNullException(nameof(settings));

            lock (_lockObject)
            {
                _symbolProfile = symbolProfile;
                _settings = settings;
                _config = config ?? new MultiTimeframeConfig();

                // Initialize timeframe-specific analysis engines
                InitializeTimeframeEngines();

                // Initialize analysis state
                _currentState = new MultiTimeframeAnalysisState
                {
                    CorrelationId = symbolProfile.CorrelationId,
                    LastUpdate = DateTime.UtcNow
                };

                // Initialize performance metrics
                _performanceMetrics = new MultiTimeframePerformanceMetrics();

                IsInitialized = true;
            }
        }

        public void UpdateMarketData(MarketDataPoint newBar)
        {
            if (!IsInitialized)
                throw new InvalidOperationException("Analyzer must be initialized before updating market data");

            if (newBar == null)
                return;

            lock (_lockObject)
            {
                _performanceStopwatch.Restart();

                // Update all timeframe data
                UpdateTimeframeData(newBar);

                // Perform multi-timeframe analysis
                if (ShouldPerformAnalysis())
                {
                    PerformMultiTimeframeAnalysis();
                }

                _performanceStopwatch.Stop();
                UpdatePerformanceMetrics();
            }
        }

        public MultiTimeframeAnalysisState GetCurrentAnalysisState()
        {
            if (!IsInitialized)
                throw new InvalidOperationException("Analyzer must be initialized before getting analysis state");

            lock (_lockObject)
            {
                // Return a deep copy to prevent external modification
                return CloneMultiTimeframeState(_currentState);
            }
        }

        public AnalysisState GetTimeframeAnalysisState(Timeframe timeframe)
        {
            if (!IsInitialized)
                throw new InvalidOperationException("Analyzer must be initialized before getting timeframe state");

            lock (_lockObject)
            {
                if (_currentState.TimeframeStates.ContainsKey(timeframe))
                {
                    return _currentState.TimeframeStates[timeframe];
                }

                return new AnalysisState(); // Return empty state if not available
            }
        }

        public TrendAlignment AnalyzeTrendAlignment(string analysisType)
        {
            if (!IsInitialized)
                throw new InvalidOperationException("Analyzer must be initialized before analyzing trend alignment");

            lock (_lockObject)
            {
                return analysisType.ToLower() switch
                {
                    "volume" => _currentState.Synthesis.VolumeTrendAlignment ?? new TrendAlignment(),
                    "delta" => _currentState.Synthesis.DeltaTrendAlignment ?? new TrendAlignment(),
                    "volatility" => _currentState.Synthesis.VolatilityTrendAlignment ?? new TrendAlignment(),
                    _ => new TrendAlignment()
                };
            }
        }

        public List<MultiTimeframePattern> DetectCrossTimeframePatterns()
        {
            if (!IsInitialized)
                throw new InvalidOperationException("Analyzer must be initialized before detecting patterns");

            lock (_lockObject)
            {
                return new List<MultiTimeframePattern>(_currentState.DetectedPatterns);
            }
        }

        public decimal GetWeightedConfidence(string analysisType)
        {
            if (!IsInitialized)
                throw new InvalidOperationException("Analyzer must be initialized before getting weighted confidence");

            lock (_lockObject)
            {
                return analysisType.ToLower() switch
                {
                    "volume" => _currentState.Synthesis.WeightedVolumeConfidence,
                    "delta" => _currentState.Synthesis.WeightedDeltaConfidence,
                    "volatility" => _currentState.Synthesis.WeightedVolatilityConfidence,
                    "marketprofile" => _currentState.Synthesis.WeightedMarketProfileConfidence,
                    _ => 0.5m
                };
            }
        }

        public MultiTimeframePerformanceMetrics GetPerformanceMetrics()
        {
            lock (_lockObject)
            {
                return new MultiTimeframePerformanceMetrics
                {
                    LastUpdate = _performanceMetrics.LastUpdate,
                    TimeframeAnalysisTimes = new Dictionary<Timeframe, TimeSpan>(_performanceMetrics.TimeframeAnalysisTimes),
                    TotalSynthesisTime = _performanceMetrics.TotalSynthesisTime,
                    TotalAnalysisTime = _performanceMetrics.TotalAnalysisTime,
                    SignalAccuracy = _performanceMetrics.SignalAccuracy,
                    FalsePositiveRate = _performanceMetrics.FalsePositiveRate,
                    SignalPersistence = _performanceMetrics.SignalPersistence,
                    TotalSignalsGenerated = _performanceMetrics.TotalSignalsGenerated,
                    SuccessfulSignals = _performanceMetrics.SuccessfulSignals,
                    PatternsDetected = _performanceMetrics.PatternsDetected,
                    PatternsConfirmed = _performanceMetrics.PatternsConfirmed,
                    PatternAccuracy = _performanceMetrics.PatternAccuracy
                };
            }
        }

        public void Reset()
        {
            lock (_lockObject)
            {
                foreach (var queue in _timeframeData.Values)
                {
                    queue.Clear();
                }

                foreach (var engine in _timeframeEngines.Values)
                {
                    engine.Reset();
                }

                _currentState = new MultiTimeframeAnalysisState();
                _performanceMetrics = new MultiTimeframePerformanceMetrics();
                IsInitialized = false;
            }
        }

        #endregion

        #region Private Methods

        private void InitializeTimeframeEngines()
        {
            foreach (Timeframe timeframe in Enum.GetValues<Timeframe>())
            {
                var engine = new RealTimeAnalysisEngine(
                    _volumeAnalyzer,
                    _deltaAnalyzer,
                    _volatilityAnalyzer,
                    _marketProfileAnalyzer);

                // Create timeframe-specific configuration
                var timeframeConfig = CreateTimeframeConfig(timeframe);
                engine.Initialize(_symbolProfile, _settings, timeframeConfig);

                _timeframeEngines[timeframe] = engine;
            }
        }

        private RealTimeAnalysisConfig CreateTimeframeConfig(Timeframe timeframe)
        {
            var baseConfig = new RealTimeAnalysisConfig();

            // Adjust analysis windows based on timeframe
            var multiplier = (int)timeframe;
            baseConfig.VolumeAnalysisWindow = Math.Max(10, baseConfig.VolumeAnalysisWindow / multiplier);
            baseConfig.DeltaAnalysisWindow = Math.Max(15, baseConfig.DeltaAnalysisWindow / multiplier);
            baseConfig.VolatilityAnalysisWindow = Math.Max(7, baseConfig.VolatilityAnalysisWindow / multiplier);

            // Adjust update intervals
            baseConfig.AnalysisUpdateInterval = TimeSpan.FromMinutes((int)timeframe);

            return baseConfig;
        }

        private void UpdateTimeframeData(MarketDataPoint newBar)
        {
            // Update 1-minute data directly
            UpdateTimeframeQueue(Timeframe.M1, newBar);

            // Aggregate to higher timeframes
            AggregateToHigherTimeframes(newBar);
        }

        private void UpdateTimeframeQueue(Timeframe timeframe, MarketDataPoint bar)
        {
            var queue = _timeframeData[timeframe];
            queue.Enqueue(bar);

            // Maintain queue size based on configuration
            var maxSize = _config.AnalysisWindows.ContainsKey(timeframe) ? 
                _config.AnalysisWindows[timeframe] : 100;

            while (queue.Count > maxSize)
            {
                queue.Dequeue();
            }

            // Update timeframe engine
            if (_timeframeEngines.ContainsKey(timeframe))
            {
                _timeframeEngines[timeframe].UpdateMarketData(bar);
            }
        }

        private void AggregateToHigherTimeframes(MarketDataPoint newBar)
        {
            // This is a simplified aggregation - in production, you'd want more sophisticated logic
            var timestamp = newBar.Timestamp;

            // 5-minute aggregation
            if (timestamp.Minute % 5 == 0)
            {
                var aggregated5m = AggregateToTimeframe(Timeframe.M1, Timeframe.M5, 5);
                if (aggregated5m != null)
                    UpdateTimeframeQueue(Timeframe.M5, aggregated5m);
            }

            // 15-minute aggregation
            if (timestamp.Minute % 15 == 0)
            {
                var aggregated15m = AggregateToTimeframe(Timeframe.M5, Timeframe.M15, 3);
                if (aggregated15m != null)
                    UpdateTimeframeQueue(Timeframe.M15, aggregated15m);
            }

            // 1-hour aggregation
            if (timestamp.Minute == 0)
            {
                var aggregated1h = AggregateToTimeframe(Timeframe.M15, Timeframe.H1, 4);
                if (aggregated1h != null)
                    UpdateTimeframeQueue(Timeframe.H1, aggregated1h);
            }
        }

        private MarketDataPoint AggregateToTimeframe(Timeframe sourceTimeframe, Timeframe targetTimeframe, int barsToAggregate)
        {
            var sourceQueue = _timeframeData[sourceTimeframe];
            if (sourceQueue.Count < barsToAggregate)
                return null;

            var barsToProcess = sourceQueue.TakeLast(barsToAggregate).ToList();
            
            return new MarketDataPoint
            {
                Timestamp = barsToProcess.Last().Timestamp,
                Open = barsToProcess.First().Open,
                High = barsToProcess.Max(b => b.High),
                Low = barsToProcess.Min(b => b.Low),
                Close = barsToProcess.Last().Close,
                Volume = barsToProcess.Sum(b => b.Volume),

                BuyVolume = barsToProcess.Sum(b => b.BuyVolume),
                SellVolume = barsToProcess.Sum(b => b.SellVolume),
                BarIndex = barsToProcess.Last().BarIndex
            };
        }

        private bool ShouldPerformAnalysis()
        {
            var now = DateTime.UtcNow;
            var timeSinceLastUpdate = now - _currentState.LastUpdate;

            // Perform analysis every 5 seconds or when significant time has passed
            return timeSinceLastUpdate >= TimeSpan.FromSeconds(5) ||
                   _timeframeData.Values.Any(queue => queue.Count >= 10);
        }

        private void PerformMultiTimeframeAnalysis()
        {
            var analysisStartTime = DateTime.UtcNow;

            try
            {
                // Update individual timeframe states
                UpdateTimeframeStates();

                // Perform cross-timeframe synthesis
                PerformTimeframeSynthesis();

                // Detect multi-timeframe patterns
                if (_config.EnablePatternDetection && _patternRecognizer != null)
                {
                    DetectMultiTimeframePatterns();
                }

                // Update overall confidence and alignment
                UpdateOverallMetrics();

                _currentState.LastUpdate = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                // Log error but don't throw - maintain system stability
                _currentState.DetectedPatterns.Add(new MultiTimeframePattern
                {
                    PatternName = "Analysis Error",
                    Type = PatternType.OrderFlowImbalance,
                    Confidence = 0,
                    Description = $"Multi-timeframe analysis error: {ex.Message}"
                });
            }

            var analysisTime = DateTime.UtcNow - analysisStartTime;
            _performanceMetrics.TotalAnalysisTime = analysisTime;
        }

        private void UpdateTimeframeStates()
        {
            foreach (var timeframe in _timeframeEngines.Keys)
            {
                try
                {
                    var engine = _timeframeEngines[timeframe];
                    var state = engine.GetCurrentAnalysisState();
                    _currentState.TimeframeStates[timeframe] = state;

                    // Track timing per timeframe
                    var analysisTime = engine.GetPerformanceMetrics().TotalAnalysisTime;
                    _performanceMetrics.TimeframeAnalysisTimes[timeframe] = analysisTime;
                }
                catch (Exception ex)
                {
                    // Create fallback state for failed timeframe
                    _currentState.TimeframeStates[timeframe] = CreateFallbackAnalysisState(timeframe, ex.Message);
                }
            }
        }

        private AnalysisState CreateFallbackAnalysisState(Timeframe timeframe, string errorMessage)
        {
            return new AnalysisState
            {
                LastUpdate = DateTime.UtcNow,
                CorrelationId = _symbolProfile.CorrelationId,
                BarsProcessed = 0,
                OverallConfidence = 0.3m, // Low confidence for fallback
                Warnings = new List<string> { $"Timeframe {timeframe} analysis failed: {errorMessage}" }
            };
        }

        private void PerformTimeframeSynthesis()
        {
            var synthesis = new TimeframeSynthesis
            {
                LastUpdate = DateTime.UtcNow
            };

            // Calculate weighted confidences
            synthesis.WeightedVolumeConfidence = CalculateWeightedConfidence("Volume");
            synthesis.WeightedDeltaConfidence = CalculateWeightedConfidence("Delta");
            synthesis.WeightedVolatilityConfidence = CalculateWeightedConfidence("Volatility");
            synthesis.WeightedMarketProfileConfidence = CalculateWeightedConfidence("MarketProfile");

            // Analyze trend alignments
            synthesis.VolumeTrendAlignment = AnalyzeTrendAlignmentInternal("Volume");
            synthesis.DeltaTrendAlignment = AnalyzeTrendAlignmentInternal("Delta");
            synthesis.VolatilityTrendAlignment = AnalyzeTrendAlignmentInternal("Volatility");

            _currentState.Synthesis = synthesis;
        }

        private decimal CalculateWeightedConfidence(string analysisType)
        {
            decimal weightedSum = 0;
            decimal totalWeight = 0;

            foreach (var kvp in _currentState.TimeframeStates)
            {
                var timeframe = kvp.Key;
                var state = kvp.Value;
                var weight = _config.TimeframeWeights.ContainsKey(timeframe) ? _config.TimeframeWeights[timeframe] : 0.25m;

                var confidence = analysisType switch
                {
                    "Volume" => state.Volume?.VolumeConfidence ?? 0.5m,
                    "Delta" => state.DeltaFlow?.DeltaConfidence ?? 0.5m,
                    "Volatility" => state.Volatility?.VolatilityConfidence ?? 0.5m,
                    "MarketProfile" => state.MarketProfile?.MarketProfileConfidence ?? 0.5m,
                    _ => 0.5m
                };

                weightedSum += confidence * weight;
                totalWeight += weight;
            }

            return totalWeight > 0 ? weightedSum / totalWeight : 0.5m;
        }

        private TrendAlignment AnalyzeTrendAlignmentInternal(string analysisType)
        {
            var alignment = new TrendAlignment
            {
                TotalTimeframes = _currentState.TimeframeStates.Count
            };

            var directions = new List<TrendDirection>();

            foreach (var kvp in _currentState.TimeframeStates)
            {
                var state = kvp.Value;
                var direction = DetermineTimeframeTrendDirection(state, analysisType);
                directions.Add(direction);
            }

            // Calculate alignment metrics
            var dominantDirection = directions.GroupBy(d => d)
                .OrderByDescending(g => g.Count())
                .First().Key;

            alignment.DominantDirection = dominantDirection;
            alignment.AlignedTimeframes = directions.Count(d => d == dominantDirection);
            alignment.AlignmentScore = (decimal)alignment.AlignedTimeframes / alignment.TotalTimeframes;
            alignment.DirectionConfidence = Math.Min(1.0m, alignment.AlignmentScore * 1.2m);

            // Identify conflicting timeframes
            alignment.ConflictingTimeframes = _currentState.TimeframeStates
                .Where(kvp => DetermineTimeframeTrendDirection(kvp.Value, analysisType) != dominantDirection)
                .Select(kvp => kvp.Key)
                .ToList();

            return alignment;
        }

        private TrendDirection DetermineTimeframeTrendDirection(AnalysisState state, string analysisType)
        {
            // Simplified trend direction determination - in production, this would be more sophisticated
            return analysisType switch
            {
                "Volume" => state.Volume?.VolumeTrend switch
                {
                    VolumeTrend.Increasing => TrendDirection.Bullish,
                    VolumeTrend.Declining => TrendDirection.Bearish,
                    VolumeTrend.Spiking => TrendDirection.Bullish,
                    _ => TrendDirection.Neutral
                },
                "Delta" => state.DeltaFlow?.CurrentBias switch
                {
                    DeltaBias.BuyPressure => TrendDirection.Bullish,
                    DeltaBias.SellPressure => TrendDirection.Bearish,
                    _ => TrendDirection.Neutral
                },
                "Volatility" => state.Volatility?.VolatilityTrend switch
                {
                    VolatilityTrend.Expanding => TrendDirection.Bullish,
                    VolatilityTrend.Contracting => TrendDirection.Bearish,
                    _ => TrendDirection.Neutral
                },
                _ => TrendDirection.Neutral
            };
        }

        private void DetectMultiTimeframePatterns()
        {
            if (_patternRecognizer == null)
                return;

            try
            {
                // Convert timeframe data for pattern recognition
                var timeframeData = _timeframeData.ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value.ToList()
                );

                // Detect patterns using the pattern recognizer
                var volumePatterns = _patternRecognizer.DetectVolumePatterns(timeframeData);
                var institutionalFootprint = _patternRecognizer.DetectInstitutionalFootprint(timeframeData);
                var orderFlowImbalance = _patternRecognizer.DetectOrderFlowImbalance(timeframeData);

                // Convert to multi-timeframe patterns
                _currentState.DetectedPatterns.Clear();
                
                foreach (var pattern in volumePatterns)
                {
                    _currentState.DetectedPatterns.Add(new MultiTimeframePattern
                    {
                        PatternName = pattern.PatternName,
                        Type = pattern.Type,
                        Confidence = pattern.Confidence,
                        DetectedTimeframes = pattern.ConfirmingTimeframes,
                        FirstDetected = pattern.StartTime,
                        LastConfirmed = pattern.EndTime,
                        CurrentPhase = pattern.Phase,
                        Description = pattern.Description
                    });
                }

                // Update synthesis with pattern information
                _currentState.Synthesis.InstitutionalFootprint = institutionalFootprint;
                _currentState.Synthesis.OrderFlowImbalance = orderFlowImbalance;

                _performanceMetrics.PatternsDetected = _currentState.DetectedPatterns.Count;
            }
            catch (Exception ex)
            {
                // Log pattern detection error but continue
                _currentState.DetectedPatterns.Add(new MultiTimeframePattern
                {
                    PatternName = "Pattern Detection Error",
                    Type = PatternType.OrderFlowImbalance,
                    Confidence = 0,
                    Description = $"Pattern detection failed: {ex.Message}"
                });
            }
        }

        private void UpdateOverallMetrics()
        {
            // Calculate overall confidence as weighted average of all analysis types
            var confidences = new[]
            {
                _currentState.Synthesis.WeightedVolumeConfidence,
                _currentState.Synthesis.WeightedDeltaConfidence,
                _currentState.Synthesis.WeightedVolatilityConfidence,
                _currentState.Synthesis.WeightedMarketProfileConfidence
            };

            _currentState.OverallConfidence = confidences.Average();

            // Calculate trend alignment score
            var alignments = new[]
            {
                _currentState.Synthesis.VolumeTrendAlignment?.AlignmentScore ?? 0.5m,
                _currentState.Synthesis.DeltaTrendAlignment?.AlignmentScore ?? 0.5m,
                _currentState.Synthesis.VolatilityTrendAlignment?.AlignmentScore ?? 0.5m
            };

            _currentState.TrendAlignment = alignments.Average();

            // Determine if timeframes are consistent
            _currentState.IsTimeframeConsistent = _currentState.TrendAlignment >= _config.MinTrendAlignmentScore;
        }

        private void UpdatePerformanceMetrics()
        {
            _performanceMetrics.LastUpdate = DateTime.UtcNow;
            _performanceMetrics.TotalSynthesisTime = _performanceStopwatch.Elapsed;
        }

        private MultiTimeframeAnalysisState CloneMultiTimeframeState(MultiTimeframeAnalysisState original)
        {
            // Deep clone implementation - simplified for brevity
            return new MultiTimeframeAnalysisState
            {
                LastUpdate = original.LastUpdate,
                CorrelationId = original.CorrelationId,
                TimeframeStates = new Dictionary<Timeframe, AnalysisState>(original.TimeframeStates),
                Synthesis = original.Synthesis, // Would need deep clone in production
                DetectedPatterns = new List<MultiTimeframePattern>(original.DetectedPatterns),
                OverallConfidence = original.OverallConfidence,
                TrendAlignment = original.TrendAlignment,
                IsTimeframeConsistent = original.IsTimeframeConsistent,
                PerformanceMetrics = original.PerformanceMetrics
            };
        }

        #endregion
    }
}
