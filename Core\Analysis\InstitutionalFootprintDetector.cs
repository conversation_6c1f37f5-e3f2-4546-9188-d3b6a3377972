using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Core.Models.Analysis;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;
using InstitutionalFootprint = SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint;
using InstitutionalActivityType = SmartVolumeStrategy.Core.Models.Analysis.InstitutionalActivityType;

namespace SmartVolumeStrategy.Core.Analysis
{
    /// <summary>
    /// Phase 3.2: Institutional Footprint Detector
    /// Advanced detection of institutional trading activity across timeframes with circuit breaker integration
    /// </summary>
    public class InstitutionalFootprintDetector
    {
        private readonly Action<string> _logAction;
        private readonly AdvancedSignalSynthesisConfig _config;
        private readonly Queue<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> _footprintHistory;
        private readonly Dictionary<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalActivityType, Queue<decimal>> _activityConfidenceHistory;
        private readonly object _lockObject = new object();
        
        // Circuit breaker integration
        private CircuitBreakerManager _circuitBreakerManager;
        
        // Configuration
        private const int MAX_FOOTPRINT_HISTORY = 200;
        private const int CONFIDENCE_HISTORY_SIZE = 50;
        private const decimal LARGE_VOLUME_THRESHOLD = 2.0m; // 2x average volume
        private const decimal BLOCK_TRADE_THRESHOLD = 5.0m; // 5x average volume
        private const decimal ICEBERG_DETECTION_THRESHOLD = 0.3m; // 30% hidden volume indicator

        public InstitutionalFootprintDetector(AdvancedSignalSynthesisConfig config = null, Action<string> logAction = null)
        {
            _logAction = logAction ?? (_ => { });
            _config = config ?? new AdvancedSignalSynthesisConfig();
            _footprintHistory = new Queue<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint>();
            _activityConfidenceHistory = new Dictionary<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalActivityType, Queue<decimal>>();
            
            InitializeActivityHistory();
        }

        /// <summary>
        /// Set circuit breaker manager for fault tolerance
        /// </summary>
        public void SetCircuitBreakerManager(CircuitBreakerManager circuitBreakerManager)
        {
            _circuitBreakerManager = circuitBreakerManager;
        }

        /// <summary>
        /// Detect institutional footprints across multiple timeframes
        /// </summary>
        public List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> DetectInstitutionalFootprints(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            Dictionary<Timeframe, AnalysisState> timeframeAnalysisStates,
            MarketContext marketContext)
        {
            lock (_lockObject)
            {
                var startTime = DateTime.UtcNow;
                var detectedFootprints = new List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint>();
                
                try
                {
                    // Phase 3.2: Check circuit breaker status
                    if (_circuitBreakerManager != null && !_circuitBreakerManager.IsOperationAllowed(StrategyComponent.MultiTimeframeAnalysis))
                    {
                        _logAction("🔴 Institutional footprint detection blocked by circuit breaker");
                        return detectedFootprints;
                    }

                    // Check degradation level
                    if (_circuitBreakerManager != null)
                    {
                        var degradationLevel = _circuitBreakerManager.GetDegradationManager().GetCurrentDegradationLevel();
                        if (degradationLevel >= DegradationLevel.Minimal)
                        {
                            _logAction("⚠️ Institutional footprint detection running in degraded mode");
                            return DetectBasicInstitutionalActivity(timeframeSignals, marketContext);
                        }
                    }

                    if (!_config.EnableInstitutionalDetection)
                    {
                        return detectedFootprints;
                    }

                    _logAction("🔍 Starting institutional footprint detection...");

                    // Detect different types of institutional activity
                    detectedFootprints.AddRange(DetectAccumulationDistribution(timeframeSignals, timeframeAnalysisStates, marketContext));
                    detectedFootprints.AddRange(DetectBlockTrades(timeframeSignals, timeframeAnalysisStates, marketContext));
                    detectedFootprints.AddRange(DetectIcebergOrders(timeframeSignals, timeframeAnalysisStates, marketContext));
                    detectedFootprints.AddRange(DetectSmartMoneyFlow(timeframeSignals, timeframeAnalysisStates, marketContext));
                    detectedFootprints.AddRange(DetectManipulationPatterns(timeframeSignals, timeframeAnalysisStates, marketContext));

                    // Validate detected footprints
                    foreach (var footprint in detectedFootprints)
                    {
                        ValidateInstitutionalFootprint(footprint, timeframeSignals, marketContext);
                    }

                    // Filter footprints by confidence threshold
                    var validFootprints = detectedFootprints
                        .Where(f => f.Confidence >= _config.MinInstitutionalConfidence)
                        .OrderByDescending(f => f.Confidence)
                        .Take(_config.MaxInstitutionalFootprints)
                        .ToList();

                    // Update footprint history
                    foreach (var footprint in validFootprints)
                    {
                        UpdateFootprintHistory(footprint);
                    }

                    // Record successful operation
                    if (_circuitBreakerManager != null)
                    {
                        var responseTime = DateTime.UtcNow - startTime;
                        _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, true, $"Detected {validFootprints.Count} institutional footprints");
                        _circuitBreakerManager.GetComponentHealthTracker().UpdateResponseTime(StrategyComponent.MultiTimeframeAnalysis, responseTime);
                    }

                    _logAction($"✅ Institutional footprint detection completed - Found {validFootprints.Count} valid footprints");

                    return validFootprints;
                }
                catch (Exception ex)
                {
                    _logAction($"❌ Institutional footprint detection error: {ex.Message}");
                    _logAction($"📊 Attempting graceful degradation with basic footprint detection...");

                    // Graceful degradation - try basic detection
                    try
                    {
                        detectedFootprints.AddRange(DetectBasicInstitutionalActivity(timeframeSignals, marketContext));
                        _logAction($"✅ Basic institutional detection completed - {detectedFootprints.Count} footprints found");
                    }
                    catch (Exception fallbackEx)
                    {
                        _logAction($"❌ Basic institutional detection also failed: {fallbackEx.Message}");
                    }

                    // Record failure with circuit breaker
                    if (_circuitBreakerManager != null)
                    {
                        _circuitBreakerManager.RecordOperation(StrategyComponent.MultiTimeframeAnalysis, false, ex.Message, FailureSeverity.Medium);
                    }

                    return detectedFootprints;
                }
            }
        }

        /// <summary>
        /// Get institutional footprint statistics
        /// </summary>
        public AdvancedSignalSynthesisStatistics GetFootprintStatistics()
        {
            lock (_lockObject)
            {
                var stats = new AdvancedSignalSynthesisStatistics
                {
                    InstitutionalDetections = _footprintHistory.Count,
                    LastSynthesis = _footprintHistory.Count > 0 ? _footprintHistory.Last().Timestamp : DateTime.MinValue
                };

                if (_footprintHistory.Count > 0)
                {
                    stats.AverageInstitutionalConfidence = _footprintHistory.Average(f => f.Confidence);
                    
                    // Activity distribution
                    foreach (var footprint in _footprintHistory)
                    {
                        if (!stats.InstitutionalActivityDistribution.ContainsKey(footprint.ActivityType))
                            stats.InstitutionalActivityDistribution[footprint.ActivityType] = 0;
                        stats.InstitutionalActivityDistribution[footprint.ActivityType]++;
                    }
                }

                return stats;
            }
        }

        /// <summary>
        /// Detect accumulation and distribution patterns
        /// </summary>
        private List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> DetectAccumulationDistribution(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            Dictionary<Timeframe, AnalysisState> timeframeAnalysisStates,
            MarketContext marketContext)
        {
            var footprints = new List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint>();

            // Look for sustained volume with minimal price movement (accumulation)
            // or sustained volume with price decline (distribution)
            foreach (var kvp in timeframeAnalysisStates)
            {
                var timeframe = kvp.Key;
                var analysisState = kvp.Value;
                
                if (analysisState.Volume == null) continue;

                var volumeConfidence = analysisState.Volume.VolumeConfidence;
                var isVolumeSpikeActive = analysisState.Volume.IsVolumeSpikeActive;
                
                // Check for accumulation pattern
                if (volumeConfidence >= 0.7m && isVolumeSpikeActive)
                {
                    var signal = timeframeSignals.ContainsKey(timeframe) ? timeframeSignals[timeframe] : null;
                    
                    if (signal != null && signal.Type != SignalType.None)
                    {
                        var activityType = signal.Type == SignalType.Long ?
                            SmartVolumeStrategy.Core.Models.Analysis.InstitutionalActivityType.Accumulation :
                            SmartVolumeStrategy.Core.Models.Analysis.InstitutionalActivityType.Distribution;

                        var footprint = new SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint
                        {
                            Timestamp = DateTime.UtcNow,
                            ActivityType = activityType,
                            Confidence = volumeConfidence * 0.8m, // Slight discount for inference
                            Strength = signal.Confidence,
                            VolumeSignificance = volumeConfidence,
                            SupportingTimeframes = new List<Timeframe> { timeframe },
                            EstimatedDuration = EstimateActivityDuration(timeframe, activityType),
                            PriceImpact = CalculatePriceImpact(signal, volumeConfidence)
                        };

                        footprint.FootprintIndicators["VolumeConfidence"] = volumeConfidence;
                        footprint.FootprintIndicators["SignalConfidence"] = signal.Confidence;
                        footprint.FootprintIndicators["VolumeSpike"] = isVolumeSpikeActive ? 1.0m : 0.0m;

                        footprint.DetectionReasons.Add($"High volume confidence ({volumeConfidence:P1}) with {signal.Type} signal");
                        footprint.DetectionReasons.Add($"Volume spike active on {timeframe} timeframe");

                        footprints.Add(footprint);
                    }
                }
            }

            return footprints;
        }

        /// <summary>
        /// Detect block trades (large volume transactions)
        /// </summary>
        private List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint> DetectBlockTrades(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            Dictionary<Timeframe, AnalysisState> timeframeAnalysisStates,
            MarketContext marketContext)
        {
            var footprints = new List<SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint>();

            // Look for extremely high volume spikes that indicate block trades
            foreach (var kvp in timeframeAnalysisStates)
            {
                var timeframe = kvp.Key;
                var analysisState = kvp.Value;

                if (analysisState.Volume == null) continue;

                var volumeConfidence = analysisState.Volume.VolumeConfidence;
                var isVolumeSpikeActive = analysisState.Volume.IsVolumeSpikeActive;

                // Block trades require very high volume confidence
                if (volumeConfidence >= 0.9m && isVolumeSpikeActive)
                {
                    var signal = timeframeSignals.ContainsKey(timeframe) ? timeframeSignals[timeframe] : null;

                    if (signal != null && signal.Confidence >= 0.8m)
                    {
                        var footprint = new SmartVolumeStrategy.Core.Models.Analysis.InstitutionalFootprint
                        {
                            Timestamp = DateTime.UtcNow,
                            ActivityType = SmartVolumeStrategy.Core.Models.Analysis.InstitutionalActivityType.BlockTrade,
                            Confidence = volumeConfidence * 0.9m,
                            Strength = signal.Confidence,
                            VolumeSignificance = volumeConfidence,
                            SupportingTimeframes = new List<Timeframe> { timeframe },
                            EstimatedDuration = TimeSpan.FromMinutes(5), // Block trades are typically short
                            PriceImpact = CalculatePriceImpact(signal, volumeConfidence) * 1.5m // Higher impact
                        };

                        footprint.FootprintIndicators["VolumeConfidence"] = volumeConfidence;
                        footprint.FootprintIndicators["SignalConfidence"] = signal.Confidence;
                        footprint.FootprintIndicators["BlockTradeIndicator"] = 1.0m;

                        footprint.DetectionReasons.Add($"Extremely high volume confidence ({volumeConfidence:P1})");
                        footprint.DetectionReasons.Add($"High signal confidence ({signal.Confidence:P1}) with volume spike");
                        footprint.DetectionReasons.Add($"Block trade pattern detected on {timeframe}");

                        footprints.Add(footprint);
                    }
                }
            }

            return footprints;
        }

        /// <summary>
        /// Detect iceberg orders (hidden large orders)
        /// </summary>
        private List<InstitutionalFootprint> DetectIcebergOrders(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            Dictionary<Timeframe, AnalysisState> timeframeAnalysisStates,
            MarketContext marketContext)
        {
            var footprints = new List<InstitutionalFootprint>();

            // Look for sustained volume without corresponding price movement
            // This suggests hidden order execution (iceberg orders)
            var volumeTimeframes = timeframeAnalysisStates
                .Where(kvp => kvp.Value.Volume != null && kvp.Value.Volume.VolumeConfidence >= 0.6m)
                .ToList();

            if (volumeTimeframes.Count >= 2)
            {
                var avgVolumeConfidence = volumeTimeframes.Average(kvp => kvp.Value.Volume.VolumeConfidence);
                var signals = volumeTimeframes
                    .Where(kvp => timeframeSignals.ContainsKey(kvp.Key))
                    .Select(kvp => timeframeSignals[kvp.Key])
                    .ToList();

                // Iceberg pattern: high volume, low signal confidence variance
                if (signals.Count >= 2)
                {
                    var avgSignalConfidence = signals.Average(s => s.Confidence);
                    var confidenceVariance = CalculateVariance(signals.Select(s => s.Confidence).ToList());

                    if (avgVolumeConfidence >= 0.7m && confidenceVariance <= ICEBERG_DETECTION_THRESHOLD)
                    {
                        var footprint = new InstitutionalFootprint
                        {
                            Timestamp = DateTime.UtcNow,
                            ActivityType = InstitutionalActivityType.IcebergOrder,
                            Confidence = avgVolumeConfidence * 0.7m, // Discount for inference
                            Strength = avgSignalConfidence,
                            VolumeSignificance = avgVolumeConfidence,
                            SupportingTimeframes = volumeTimeframes.Select(kvp => kvp.Key).ToList(),
                            EstimatedDuration = TimeSpan.FromMinutes(20), // Iceberg orders take time
                            PriceImpact = CalculateAveragePriceImpact(signals, avgVolumeConfidence) * 0.8m // Lower immediate impact
                        };

                        footprint.FootprintIndicators["VolumeConfidence"] = avgVolumeConfidence;
                        footprint.FootprintIndicators["SignalConfidenceVariance"] = confidenceVariance;
                        footprint.FootprintIndicators["IcebergIndicator"] = 1.0m;

                        footprint.DetectionReasons.Add($"High volume confidence ({avgVolumeConfidence:P1}) across multiple timeframes");
                        footprint.DetectionReasons.Add($"Low signal confidence variance ({confidenceVariance:F3}) suggests hidden execution");
                        footprint.DetectionReasons.Add($"Iceberg order pattern detected across {volumeTimeframes.Count} timeframes");

                        footprints.Add(footprint);
                    }
                }
            }

            return footprints;
        }

        /// <summary>
        /// Detect smart money flow patterns
        /// </summary>
        private List<InstitutionalFootprint> DetectSmartMoneyFlow(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            Dictionary<Timeframe, AnalysisState> timeframeAnalysisStates,
            MarketContext marketContext)
        {
            var footprints = new List<InstitutionalFootprint>();

            // Look for coordinated signals across multiple timeframes with high confidence
            var highConfidenceSignals = timeframeSignals
                .Where(kvp => kvp.Value.Confidence >= 0.8m)
                .ToList();

            if (highConfidenceSignals.Count >= 3)
            {
                var dominantType = highConfidenceSignals
                    .GroupBy(kvp => kvp.Value.Type)
                    .OrderByDescending(g => g.Count())
                    .First().Key;

                var alignedSignals = highConfidenceSignals
                    .Where(kvp => kvp.Value.Type == dominantType)
                    .ToList();

                if (alignedSignals.Count >= 3)
                {
                    var avgConfidence = alignedSignals.Average(kvp => kvp.Value.Confidence);
                    var avgStrength = alignedSignals.Average(kvp => kvp.Value.Strength);

                    var footprint = new InstitutionalFootprint
                    {
                        Timestamp = DateTime.UtcNow,
                        ActivityType = InstitutionalActivityType.SmartMoneyFlow,
                        Confidence = avgConfidence * 0.85m,
                        Strength = avgStrength,
                        VolumeSignificance = CalculateVolumeSignificance(alignedSignals, timeframeAnalysisStates),
                        SupportingTimeframes = alignedSignals.Select(kvp => kvp.Key).ToList(),
                        EstimatedDuration = TimeSpan.FromMinutes(30),
                        PriceImpact = avgConfidence * avgStrength
                    };

                    footprint.FootprintIndicators["AlignedTimeframes"] = alignedSignals.Count;
                    footprint.FootprintIndicators["AverageConfidence"] = avgConfidence;
                    footprint.FootprintIndicators["SmartMoneyIndicator"] = 1.0m;

                    footprint.DetectionReasons.Add($"High confidence alignment across {alignedSignals.Count} timeframes");
                    footprint.DetectionReasons.Add($"Average confidence: {avgConfidence:P1}");
                    footprint.DetectionReasons.Add($"Smart money flow pattern: {dominantType}");

                    footprints.Add(footprint);
                }
            }

            return footprints;
        }

        /// <summary>
        /// Detect manipulation patterns
        /// </summary>
        private List<InstitutionalFootprint> DetectManipulationPatterns(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            Dictionary<Timeframe, AnalysisState> timeframeAnalysisStates,
            MarketContext marketContext)
        {
            var footprints = new List<InstitutionalFootprint>();

            // Look for conflicting signals between short and long timeframes
            var shortTimeframes = timeframeSignals.Where(kvp => kvp.Key <= Timeframe.M5).ToList();
            var longTimeframes = timeframeSignals.Where(kvp => kvp.Key >= Timeframe.M15).ToList();

            if (shortTimeframes.Count >= 1 && longTimeframes.Count >= 1)
            {
                var shortSignalTypes = shortTimeframes.Select(kvp => kvp.Value.Type).Distinct().ToList();
                var longSignalTypes = longTimeframes.Select(kvp => kvp.Value.Type).Distinct().ToList();

                // Check for opposing signals (potential manipulation)
                var hasOpposingSignals = shortSignalTypes.Any(st => longSignalTypes.Any(lt => AreOpposingSignals(st, lt)));

                if (hasOpposingSignals)
                {
                    var shortConfidence = shortTimeframes.Average(kvp => kvp.Value.Confidence);
                    var longConfidence = longTimeframes.Average(kvp => kvp.Value.Confidence);

                    // Manipulation pattern: short-term signals oppose long-term with high confidence
                    if (shortConfidence >= 0.7m && longConfidence >= 0.7m)
                    {
                        var footprint = new InstitutionalFootprint
                        {
                            Timestamp = DateTime.UtcNow,
                            ActivityType = InstitutionalActivityType.Manipulation,
                            Confidence = Math.Min(shortConfidence, longConfidence) * 0.6m, // Discount for inference
                            Strength = Math.Abs(shortConfidence - longConfidence),
                            VolumeSignificance = CalculateVolumeSignificance(timeframeSignals.ToList(), timeframeAnalysisStates),
                            SupportingTimeframes = timeframeSignals.Keys.ToList(),
                            EstimatedDuration = TimeSpan.FromMinutes(15),
                            PriceImpact = 0.3m // Manipulation typically has lower immediate impact
                        };

                        footprint.FootprintIndicators["ShortTermConfidence"] = shortConfidence;
                        footprint.FootprintIndicators["LongTermConfidence"] = longConfidence;
                        footprint.FootprintIndicators["ManipulationIndicator"] = 1.0m;

                        footprint.DetectionReasons.Add($"Opposing signals: short-term vs long-term");
                        footprint.DetectionReasons.Add($"Short-term confidence: {shortConfidence:P1}");
                        footprint.DetectionReasons.Add($"Long-term confidence: {longConfidence:P1}");

                        footprints.Add(footprint);
                    }
                }
            }

            return footprints;
        }

        /// <summary>
        /// Validate institutional footprint
        /// </summary>
        private void ValidateInstitutionalFootprint(
            InstitutionalFootprint footprint,
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            MarketContext marketContext)
        {
            var validationScore = 0m;
            var validationFactors = new List<string>();

            // Validate based on supporting timeframes
            var timeframeSupport = (decimal)footprint.SupportingTimeframes.Count / timeframeSignals.Count;
            validationScore += timeframeSupport * 0.3m;
            validationFactors.Add($"Timeframe support: {timeframeSupport:P0}");

            // Validate based on confidence
            validationScore += footprint.Confidence * 0.3m;
            validationFactors.Add($"Confidence: {footprint.Confidence:P1}");

            // Validate based on volume significance
            validationScore += footprint.VolumeSignificance * 0.2m;
            validationFactors.Add($"Volume significance: {footprint.VolumeSignificance:P1}");

            // Validate based on market conditions
            var marketConditionScore = GetMarketConditionScore(footprint.ActivityType, marketContext);
            validationScore += marketConditionScore * 0.2m;
            validationFactors.Add($"Market condition alignment: {marketConditionScore:P1}");

            footprint.ValidationScore = validationScore;
            footprint.IsValidated = validationScore >= 0.6m;
            footprint.DetectionReasons.AddRange(validationFactors);
        }

        /// <summary>
        /// Detect basic institutional activity for degraded mode
        /// </summary>
        private List<InstitutionalFootprint> DetectBasicInstitutionalActivity(
            Dictionary<Timeframe, TradingSignal> timeframeSignals,
            MarketContext marketContext)
        {
            var footprints = new List<InstitutionalFootprint>();

            // Simple high-confidence signal detection
            var highConfidenceSignals = timeframeSignals
                .Where(kvp => kvp.Value.Confidence >= 0.8m)
                .ToList();

            if (highConfidenceSignals.Count >= 2)
            {
                var avgConfidence = highConfidenceSignals.Average(kvp => kvp.Value.Confidence);
                var dominantType = highConfidenceSignals
                    .GroupBy(kvp => kvp.Value.Type)
                    .OrderByDescending(g => g.Count())
                    .First().Key;

                var footprint = new InstitutionalFootprint
                {
                    Timestamp = DateTime.UtcNow,
                    ActivityType = InstitutionalActivityType.SmartMoneyFlow,
                    Confidence = avgConfidence * 0.7m, // Discount for basic mode
                    Strength = avgConfidence,
                    VolumeSignificance = 0.7m,
                    SupportingTimeframes = highConfidenceSignals.Select(kvp => kvp.Key).ToList(),
                    EstimatedDuration = TimeSpan.FromMinutes(20),
                    PriceImpact = avgConfidence * 0.8m,
                    IsValidated = true,
                    ValidationScore = avgConfidence * 0.7m
                };

                footprint.DetectionReasons.Add("Basic institutional activity detection (degraded mode)");
                footprint.DetectionReasons.Add($"High confidence signals: {highConfidenceSignals.Count}");

                footprints.Add(footprint);
            }

            return footprints;
        }

        /// <summary>
        /// Update footprint history
        /// </summary>
        private void UpdateFootprintHistory(InstitutionalFootprint footprint)
        {
            _footprintHistory.Enqueue(footprint);

            while (_footprintHistory.Count > MAX_FOOTPRINT_HISTORY)
            {
                _footprintHistory.Dequeue();
            }

            // Update activity confidence history
            if (_activityConfidenceHistory.ContainsKey(footprint.ActivityType))
            {
                var confidenceHistory = _activityConfidenceHistory[footprint.ActivityType];
                confidenceHistory.Enqueue(footprint.Confidence);

                while (confidenceHistory.Count > CONFIDENCE_HISTORY_SIZE)
                {
                    confidenceHistory.Dequeue();
                }
            }
        }

        /// <summary>
        /// Calculate volume significance across timeframes
        /// </summary>
        private decimal CalculateVolumeSignificance(
            List<KeyValuePair<Timeframe, TradingSignal>> signals,
            Dictionary<Timeframe, AnalysisState> timeframeAnalysisStates)
        {
            var volumeSignificances = new List<decimal>();

            foreach (var kvp in signals)
            {
                if (timeframeAnalysisStates.ContainsKey(kvp.Key) &&
                    timeframeAnalysisStates[kvp.Key].Volume != null)
                {
                    volumeSignificances.Add(timeframeAnalysisStates[kvp.Key].Volume.VolumeConfidence);
                }
            }

            return volumeSignificances.Count > 0 ? volumeSignificances.Average() : 0.5m;
        }

        /// <summary>
        /// Get market condition score for activity type
        /// </summary>
        private decimal GetMarketConditionScore(InstitutionalActivityType activityType, MarketContext marketContext)
        {
            return activityType switch
            {
                InstitutionalActivityType.BlockTrade => marketContext.VolatilityRegime >= VolatilityRegime.High ? 0.8m : 0.6m,
                InstitutionalActivityType.Accumulation => marketContext.VolatilityRegime <= VolatilityRegime.Normal ? 0.8m : 0.5m,
                InstitutionalActivityType.Distribution => marketContext.VolatilityRegime >= VolatilityRegime.Normal ? 0.8m : 0.6m,
                InstitutionalActivityType.SmartMoneyFlow => marketContext.IsOptimalTradingTime ? 0.9m : 0.7m,
                InstitutionalActivityType.IcebergOrder => 0.7m, // Neutral to market conditions
                InstitutionalActivityType.Manipulation => marketContext.VolatilityRegime <= VolatilityRegime.Low ? 0.8m : 0.5m,
                _ => 0.6m
            };
        }

        /// <summary>
        /// Check if two signal types are opposing
        /// </summary>
        private bool AreOpposingSignals(SignalType type1, SignalType type2)
        {
            return (type1 == SignalType.Long && type2 == SignalType.Short) ||
                   (type1 == SignalType.Short && type2 == SignalType.Long);
        }

        private void InitializeActivityHistory()
        {
            foreach (InstitutionalActivityType activityType in Enum.GetValues<InstitutionalActivityType>())
            {
                _activityConfidenceHistory[activityType] = new Queue<decimal>();
            }
        }

        private TimeSpan EstimateActivityDuration(Timeframe timeframe, InstitutionalActivityType activityType)
        {
            var baseDuration = timeframe switch
            {
                Timeframe.M1 => TimeSpan.FromMinutes(5),
                Timeframe.M5 => TimeSpan.FromMinutes(15),
                Timeframe.M15 => TimeSpan.FromMinutes(45),
                Timeframe.H1 => TimeSpan.FromHours(2),
                _ => TimeSpan.FromMinutes(30)
            };

            var activityMultiplier = activityType switch
            {
                InstitutionalActivityType.BlockTrade => 0.3m,
                InstitutionalActivityType.IcebergOrder => 2.0m,
                InstitutionalActivityType.Accumulation => 3.0m,
                InstitutionalActivityType.Distribution => 2.5m,
                InstitutionalActivityType.SmartMoneyFlow => 1.5m,
                _ => 1.0m
            };

            return TimeSpan.FromMinutes(baseDuration.TotalMinutes * (double)activityMultiplier);
        }

        private decimal CalculatePriceImpact(TradingSignal signal, decimal volumeConfidence)
        {
            return signal.Confidence * volumeConfidence * signal.Strength;
        }

        private decimal CalculateAveragePriceImpact(List<TradingSignal> signals, decimal volumeConfidence)
        {
            if (signals.Count == 0) return 0m;

            var avgConfidence = signals.Average(s => s.Confidence);
            var avgStrength = signals.Average(s => s.Strength);

            return avgConfidence * volumeConfidence * avgStrength;
        }

        private decimal CalculateVariance(List<decimal> values)
        {
            if (values.Count < 2) return 0m;

            var mean = values.Average();
            var variance = values.Sum(v => (v - mean) * (v - mean)) / values.Count;
            return variance;
        }

    }
}
