using System;
using System.Collections.Generic;
using System.Linq;
using SmartVolumeStrategy.Core.Analysis;
using SmartVolumeStrategy.Core.Models.Analysis;
using SmartVolumeStrategy.Core.Resilience;
using SmartVolumeStrategy.Core.Models.Resilience;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Tests
{
    /// <summary>
    /// Phase 3.1 Test: Multi-Timeframe Analysis Enhancement
    /// Tests advanced correlation analysis, pattern detection, and weighted signal synthesis
    /// </summary>
    public class Phase3_1_MultiTimeframeAnalysisTest
    {
        private readonly List<string> _testLogs;
        private readonly MultiTimeframeCorrelationAnalyzer _correlationAnalyzer;
        private readonly CrossTimeframePatternDetector _patternDetector;
        private readonly TimeframeWeightedSynthesizer _weightedSynthesizer;
        private readonly CircuitBreakerManager _circuitBreakerManager;

        public Phase3_1_MultiTimeframeAnalysisTest()
        {
            _testLogs = new List<string>();
            var config = new MultiTimeframeAnalysisConfig();
            
            _correlationAnalyzer = new MultiTimeframeCorrelationAnalyzer(config, log => _testLogs.Add(log));
            _patternDetector = new CrossTimeframePatternDetector(config, log => _testLogs.Add(log));
            _weightedSynthesizer = new TimeframeWeightedSynthesizer(config, log => _testLogs.Add(log));
            _circuitBreakerManager = new CircuitBreakerManager(log => _testLogs.Add(log));
            
            // Integrate circuit breaker with all components
            _correlationAnalyzer.SetCircuitBreakerManager(_circuitBreakerManager);
            _patternDetector.SetCircuitBreakerManager(_circuitBreakerManager);
            _weightedSynthesizer.SetCircuitBreakerManager(_circuitBreakerManager);
        }

        /// <summary>
        /// Run comprehensive Phase 3.1 tests
        /// </summary>
        public void RunMultiTimeframeAnalysisTests()
        {
            Console.WriteLine("🧪 PHASE 3.1 TEST: Multi-Timeframe Analysis Enhancement");
            Console.WriteLine(new string('=', 60));

            // Test 1: Multi-timeframe correlation analysis
            TestMultiTimeframeCorrelationAnalysis();

            // Test 2: Cross-timeframe pattern detection
            TestCrossTimeframePatternDetection();

            // Test 3: Timeframe-weighted signal synthesis
            TestTimeframeWeightedSignalSynthesis();

            // Test 4: Conflict resolution strategies
            TestConflictResolutionStrategies();

            // Test 5: Adaptive timeframe weighting
            TestAdaptiveTimeframeWeighting();

            // Test 6: Circuit breaker integration
            TestCircuitBreakerIntegration();

            // Test 7: Integration with Phase 2 systems
            TestIntegrationWithPhase2Systems();

            Console.WriteLine("\n✅ Phase 3.1 Multi-Timeframe Analysis Tests Completed");
            Console.WriteLine($"📊 Total test logs generated: {_testLogs.Count}");
        }

        private void TestMultiTimeframeCorrelationAnalysis()
        {
            Console.WriteLine("\n🎯 Test 1: Multi-Timeframe Correlation Analysis");
            
            var timeframeSignals = CreateAlignedTimeframeSignals();
            var marketContext = CreateTestMarketContext();
            
            var correlationAnalysis = _correlationAnalyzer.AnalyzeCorrelation(timeframeSignals, marketContext);
            
            Console.WriteLine($"  • Overall Correlation: {correlationAnalysis.OverallCorrelation:P1}");
            Console.WriteLine($"  • Consistency Level: {correlationAnalysis.ConsistencyLevel}");
            Console.WriteLine($"  • Aligned Timeframes: {correlationAnalysis.AlignedTimeframes.Count}");
            Console.WriteLine($"  • Divergent Timeframes: {correlationAnalysis.DivergentTimeframes.Count}");
            Console.WriteLine($"  • Has Significant Divergence: {correlationAnalysis.HasSignificantDivergence}");
            
            // Test correlation types
            foreach (var kvp in correlationAnalysis.CorrelationScores)
            {
                Console.WriteLine($"  • {kvp.Key}: {kvp.Value:P1}");
            }
            
            if (correlationAnalysis.OverallCorrelation > 0.7m && correlationAnalysis.AlignedTimeframes.Count >= 3)
            {
                Console.WriteLine("  ✅ Multi-timeframe correlation analysis working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Multi-timeframe correlation analysis not working as expected");
            }
        }

        private void TestCrossTimeframePatternDetection()
        {
            Console.WriteLine("\n🎯 Test 2: Cross-Timeframe Pattern Detection");
            
            var timeframeSignals = CreatePatternTimeframeSignals();
            var marketContext = CreateTestMarketContext();
            var correlationAnalysis = _correlationAnalyzer.AnalyzeCorrelation(timeframeSignals, marketContext);
            
            var detectedPatterns = _patternDetector.DetectPatterns(timeframeSignals, correlationAnalysis, marketContext);
            
            Console.WriteLine($"  • Total Patterns Detected: {detectedPatterns.Count}");
            
            foreach (var pattern in detectedPatterns)
            {
                Console.WriteLine($"  • Pattern: {pattern.PatternName}");
                Console.WriteLine($"    - Type: {pattern.PatternType}");
                Console.WriteLine($"    - Confidence: {pattern.Confidence:P1}");
                Console.WriteLine($"    - Strength: {pattern.PatternStrength:P1}");
                Console.WriteLine($"    - Validated: {pattern.IsValidated}");
                Console.WriteLine($"    - Supporting Timeframes: {pattern.SupportingTimeframes.Count}");
                Console.WriteLine($"    - Estimated Duration: {pattern.EstimatedDuration.TotalMinutes:F0} minutes");
            }
            
            var validatedPatterns = detectedPatterns.Count(p => p.IsValidated);
            if (detectedPatterns.Count > 0 && validatedPatterns > 0)
            {
                Console.WriteLine("  ✅ Cross-timeframe pattern detection working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Cross-timeframe pattern detection not working as expected");
            }
        }

        private void TestTimeframeWeightedSignalSynthesis()
        {
            Console.WriteLine("\n🎯 Test 3: Timeframe-Weighted Signal Synthesis");
            
            var timeframeSignals = CreateMixedTimeframeSignals();
            var marketContext = CreateTestMarketContext();
            var correlationAnalysis = _correlationAnalyzer.AnalyzeCorrelation(timeframeSignals, marketContext);
            var detectedPatterns = _patternDetector.DetectPatterns(timeframeSignals, correlationAnalysis, marketContext);
            
            var weightedSignal = _weightedSynthesizer.SynthesizeWeightedSignal(
                timeframeSignals, correlationAnalysis, detectedPatterns, marketContext);
            
            Console.WriteLine($"  • Synthesized Signal Type: {weightedSignal.Type}");
            Console.WriteLine($"  • Confidence: {weightedSignal.Confidence:P1}");
            Console.WriteLine($"  • Strength: {weightedSignal.Strength:P1}");
            Console.WriteLine($"  • Quality: {weightedSignal.Quality}");
            Console.WriteLine($"  • Weighted Consensus: {weightedSignal.WeightedConsensus:P1}");
            Console.WriteLine($"  • Has Timeframe Conflicts: {weightedSignal.HasTimeframeConflicts}");
            Console.WriteLine($"  • Resolution Strategy: {weightedSignal.ResolutionStrategy}");
            
            // Display timeframe weights
            Console.WriteLine("  • Timeframe Weights:");
            foreach (var kvp in weightedSignal.TimeframeWeights)
            {
                Console.WriteLine($"    - {kvp.Key}: {kvp.Value:P1}");
            }
            
            if (weightedSignal.Type != SignalType.None && weightedSignal.Confidence > 0.5m)
            {
                Console.WriteLine("  ✅ Timeframe-weighted signal synthesis working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Timeframe-weighted signal synthesis not working as expected");
            }
        }

        private void TestConflictResolutionStrategies()
        {
            Console.WriteLine("\n🎯 Test 4: Conflict Resolution Strategies");
            
            var conflictingSignals = CreateConflictingTimeframeSignals();
            var marketContext = CreateTestMarketContext();
            var correlationAnalysis = _correlationAnalyzer.AnalyzeCorrelation(conflictingSignals, marketContext);
            var detectedPatterns = _patternDetector.DetectPatterns(conflictingSignals, correlationAnalysis, marketContext);
            
            var weightedSignal = _weightedSynthesizer.SynthesizeWeightedSignal(
                conflictingSignals, correlationAnalysis, detectedPatterns, marketContext);
            
            Console.WriteLine($"  • Conflicts Detected: {weightedSignal.HasTimeframeConflicts}");
            Console.WriteLine($"  • Resolution Strategy Used: {weightedSignal.ResolutionStrategy}");
            Console.WriteLine($"  • Conflict Resolutions: {weightedSignal.ConflictResolutions.Count}");
            
            foreach (var resolution in weightedSignal.ConflictResolutions)
            {
                Console.WriteLine($"    - {resolution}");
            }
            
            Console.WriteLine($"  • Final Signal Type: {weightedSignal.Type}");
            Console.WriteLine($"  • Final Confidence: {weightedSignal.Confidence:P1}");
            
            if (weightedSignal.ConflictResolutions.Count > 0)
            {
                Console.WriteLine("  ✅ Conflict resolution strategies working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Conflict resolution strategies not working as expected");
            }
        }

        private void TestAdaptiveTimeframeWeighting()
        {
            Console.WriteLine("\n🎯 Test 5: Adaptive Timeframe Weighting");
            
            var initialWeights = _weightedSynthesizer.GetCurrentWeights();
            Console.WriteLine("  • Initial Weights:");
            foreach (var kvp in initialWeights.CurrentWeights)
            {
                Console.WriteLine($"    - {kvp.Key}: {kvp.Value:P1}");
            }
            
            // Simulate multiple synthesis operations to trigger weight adaptation
            for (int i = 0; i < 15; i++)
            {
                var timeframeSignals = CreateVaryingQualityTimeframeSignals(i);
                var marketContext = CreateTestMarketContext();
                var correlationAnalysis = _correlationAnalyzer.AnalyzeCorrelation(timeframeSignals, marketContext);
                var detectedPatterns = _patternDetector.DetectPatterns(timeframeSignals, correlationAnalysis, marketContext);
                
                _weightedSynthesizer.SynthesizeWeightedSignal(timeframeSignals, correlationAnalysis, detectedPatterns, marketContext);
            }
            
            var adaptedWeights = _weightedSynthesizer.GetCurrentWeights();
            Console.WriteLine("  • Adapted Weights:");
            foreach (var kvp in adaptedWeights.CurrentWeights)
            {
                Console.WriteLine($"    - {kvp.Key}: {kvp.Value:P1}");
            }
            
            Console.WriteLine($"  • Weights Were Adjusted: {adaptedWeights.WeightsWereAdjusted}");
            Console.WriteLine($"  • Total Weight Change: {adaptedWeights.TotalWeightChange:P1}");
            Console.WriteLine($"  • Adjustment Reasons: {adaptedWeights.AdjustmentReasons.Count}");
            
            if (adaptedWeights.WeightsWereAdjusted)
            {
                Console.WriteLine("  ✅ Adaptive timeframe weighting working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Adaptive timeframe weighting not working as expected");
            }
        }

        private void TestCircuitBreakerIntegration()
        {
            Console.WriteLine("\n🎯 Test 6: Circuit Breaker Integration");
            
            var systemStatus = _circuitBreakerManager.GetSystemStatus();
            Console.WriteLine($"  • Initial System Health: {systemStatus.OverallSystemHealth:P1}");
            
            // Test normal operation
            var timeframeSignals = CreateAlignedTimeframeSignals();
            var marketContext = CreateTestMarketContext();
            
            var correlationAnalysis = _correlationAnalyzer.AnalyzeCorrelation(timeframeSignals, marketContext);
            var detectedPatterns = _patternDetector.DetectPatterns(timeframeSignals, correlationAnalysis, marketContext);
            var weightedSignal = _weightedSynthesizer.SynthesizeWeightedSignal(
                timeframeSignals, correlationAnalysis, detectedPatterns, marketContext);
            
            Console.WriteLine($"  • Normal Operation Successful: {weightedSignal != null}");
            
            // Test circuit breaker activation
            _circuitBreakerManager.ForceOpen(StrategyComponent.MultiTimeframeAnalysis, "Test circuit breaker");
            
            var blockedCorrelationAnalysis = _correlationAnalyzer.AnalyzeCorrelation(timeframeSignals, marketContext);
            var blockedPatterns = _patternDetector.DetectPatterns(timeframeSignals, correlationAnalysis, marketContext);
            var blockedSignal = _weightedSynthesizer.SynthesizeWeightedSignal(
                timeframeSignals, correlationAnalysis, detectedPatterns, marketContext);
            
            Console.WriteLine($"  • Circuit Breaker Blocked Operations: {blockedPatterns.Count == 0}");
            
            // Test recovery
            _circuitBreakerManager.ForceClose(StrategyComponent.MultiTimeframeAnalysis, "Test recovery");
            
            var recoveredSignal = _weightedSynthesizer.SynthesizeWeightedSignal(
                timeframeSignals, correlationAnalysis, detectedPatterns, marketContext);
            
            Console.WriteLine($"  • Recovery Successful: {recoveredSignal != null}");
            
            if (weightedSignal != null && blockedPatterns.Count == 0 && recoveredSignal != null)
            {
                Console.WriteLine("  ✅ Circuit breaker integration working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Circuit breaker integration not working as expected");
            }
        }

        private void TestIntegrationWithPhase2Systems()
        {
            Console.WriteLine("\n🎯 Test 7: Integration with Phase 2 Systems");
            
            // Test integration with Phase 2.3 circuit breaker patterns
            var resilienceStatus = _circuitBreakerManager.GetSystemStatus();
            Console.WriteLine($"  • System Resilience Health: {resilienceStatus.OverallSystemHealth:P1}");
            Console.WriteLine($"  • Active Circuit Breakers: {resilienceStatus.ActiveCircuitBreakers}");
            
            // Test degradation mode operation
            var degradationManager = _circuitBreakerManager.GetDegradationManager();
            degradationManager.ForceDegradationLevel(DegradationLevel.Minimal, "Test degraded mode");
            
            var timeframeSignals = CreateAlignedTimeframeSignals();
            var marketContext = CreateTestMarketContext();
            
            var degradedCorrelationAnalysis = _correlationAnalyzer.AnalyzeCorrelation(timeframeSignals, marketContext);
            var degradedPatterns = _patternDetector.DetectPatterns(timeframeSignals, degradedCorrelationAnalysis, marketContext);
            var degradedSignal = _weightedSynthesizer.SynthesizeWeightedSignal(
                timeframeSignals, degradedCorrelationAnalysis, degradedPatterns, marketContext);
            
            Console.WriteLine($"  • Degraded Mode Operation: {degradedSignal != null}");
            Console.WriteLine($"  • Degraded Signal Quality: {degradedSignal?.Quality}");
            
            // Restore normal mode
            degradationManager.ForceDegradationLevel(DegradationLevel.Normal, "Test recovery");
            
            var statistics = _correlationAnalyzer.GetCorrelationStatistics();
            Console.WriteLine($"  • Total Correlation Analyses: {statistics.TotalAnalyses}");
            Console.WriteLine($"  • Successful Correlations: {statistics.SuccessfulCorrelations}");
            
            if (degradedSignal != null && statistics.TotalAnalyses > 0)
            {
                Console.WriteLine("  ✅ Integration with Phase 2 systems working correctly");
            }
            else
            {
                Console.WriteLine("  ❌ Integration with Phase 2 systems not working as expected");
            }
        }

        // Helper methods for creating test data
        private Dictionary<Timeframe, TradingSignal> CreateAlignedTimeframeSignals()
        {
            return new Dictionary<Timeframe, TradingSignal>
            {
                [Timeframe.M1] = new TradingSignal { Type = SignalType.Long, Confidence = 0.8m, Strength = 0.7m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow },
                [Timeframe.M5] = new TradingSignal { Type = SignalType.Long, Confidence = 0.85m, Strength = 0.75m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow },
                [Timeframe.M15] = new TradingSignal { Type = SignalType.Long, Confidence = 0.9m, Strength = 0.8m, Quality = SignalQuality.Excellent, Timestamp = DateTime.UtcNow },
                [Timeframe.H1] = new TradingSignal { Type = SignalType.Long, Confidence = 0.88m, Strength = 0.78m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow }
            };
        }

        private Dictionary<Timeframe, TradingSignal> CreatePatternTimeframeSignals()
        {
            return new Dictionary<Timeframe, TradingSignal>
            {
                [Timeframe.M1] = new TradingSignal { Type = SignalType.Long, Confidence = 0.75m, Strength = 0.7m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow },
                [Timeframe.M5] = new TradingSignal { Type = SignalType.Long, Confidence = 0.8m, Strength = 0.75m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow },
                [Timeframe.M15] = new TradingSignal { Type = SignalType.Long, Confidence = 0.85m, Strength = 0.8m, Quality = SignalQuality.Excellent, Timestamp = DateTime.UtcNow },
                [Timeframe.H1] = new TradingSignal { Type = SignalType.Long, Confidence = 0.9m, Strength = 0.85m, Quality = SignalQuality.Excellent, Timestamp = DateTime.UtcNow }
            };
        }

        private Dictionary<Timeframe, TradingSignal> CreateMixedTimeframeSignals()
        {
            return new Dictionary<Timeframe, TradingSignal>
            {
                [Timeframe.M1] = new TradingSignal { Type = SignalType.Long, Confidence = 0.7m, Strength = 0.6m, Quality = SignalQuality.Fair, Timestamp = DateTime.UtcNow },
                [Timeframe.M5] = new TradingSignal { Type = SignalType.Long, Confidence = 0.8m, Strength = 0.75m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow },
                [Timeframe.M15] = new TradingSignal { Type = SignalType.None, Confidence = 0.5m, Strength = 0.5m, Quality = SignalQuality.Fair, Timestamp = DateTime.UtcNow },
                [Timeframe.H1] = new TradingSignal { Type = SignalType.Long, Confidence = 0.85m, Strength = 0.8m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow }
            };
        }

        private Dictionary<Timeframe, TradingSignal> CreateConflictingTimeframeSignals()
        {
            return new Dictionary<Timeframe, TradingSignal>
            {
                [Timeframe.M1] = new TradingSignal { Type = SignalType.Short, Confidence = 0.8m, Strength = 0.7m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow },
                [Timeframe.M5] = new TradingSignal { Type = SignalType.Short, Confidence = 0.75m, Strength = 0.7m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow },
                [Timeframe.M15] = new TradingSignal { Type = SignalType.Long, Confidence = 0.85m, Strength = 0.8m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow },
                [Timeframe.H1] = new TradingSignal { Type = SignalType.Long, Confidence = 0.9m, Strength = 0.85m, Quality = SignalQuality.Excellent, Timestamp = DateTime.UtcNow }
            };
        }

        private Dictionary<Timeframe, TradingSignal> CreateVaryingQualityTimeframeSignals(int iteration)
        {
            var baseConfidence = 0.5m + (iteration * 0.02m);
            return new Dictionary<Timeframe, TradingSignal>
            {
                [Timeframe.M1] = new TradingSignal { Type = SignalType.Long, Confidence = Math.Min(1.0m, baseConfidence + 0.1m), Strength = 0.7m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow },
                [Timeframe.M5] = new TradingSignal { Type = SignalType.Long, Confidence = Math.Min(1.0m, baseConfidence + 0.05m), Strength = 0.75m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow },
                [Timeframe.M15] = new TradingSignal { Type = SignalType.Long, Confidence = Math.Min(1.0m, baseConfidence), Strength = 0.8m, Quality = SignalQuality.Good, Timestamp = DateTime.UtcNow },
                [Timeframe.H1] = new TradingSignal { Type = SignalType.Long, Confidence = Math.Min(1.0m, baseConfidence - 0.05m), Strength = 0.78m, Quality = SignalQuality.Fair, Timestamp = DateTime.UtcNow }
            };
        }

        private MarketContext CreateTestMarketContext()
        {
            return new MarketContext
            {
                VolatilityRegime = VolatilityRegime.Normal,
                CVDTrend = 25m,
                IsOptimalTradingTime = true,
                CurrentSession = TradingSession.Overlap_EuropeanUS,
                DeltaImbalance = 0.3m
            };
        }
    }
}
