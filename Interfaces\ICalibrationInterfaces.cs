using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Interfaces
{
    /// <summary>
    /// Master settings calibrator that coordinates optimization of all strategy parameters
    /// </summary>
    public interface ISettingsCalibrator
    {
        /// <summary>
        /// Calibrate all strategy settings based on symbol analysis
        /// </summary>
        /// <param name="symbolProfile">Symbol analysis results</param>
        /// <param name="marketData">Historical market data for validation</param>
        /// <param name="userConstraints">User-defined constraints (optional)</param>
        /// <returns>Calibrated settings with validation results</returns>
        Task<CalibrationResult> CalibrateSettingsAsync(
            SymbolProfile symbolProfile, 
            List<MarketDataPoint> marketData,
            UserConstraints userConstraints = null);

        /// <summary>
        /// Quick recalibration based on recent performance
        /// </summary>
        /// <param name="currentSettings">Current strategy settings</param>
        /// <param name="recentPerformance">Recent performance metrics</param>
        /// <param name="marketData">Recent market data</param>
        /// <returns>Adjusted settings</returns>
        Task<CalibrationResult> RecalibrateAsync(
            OptimalSettings currentSettings,
            PerformanceMetrics recentPerformance,
            List<MarketDataPoint> marketData);

        /// <summary>
        /// Simple calibration method for adaptive calibrator
        /// </summary>
        /// <param name="marketData">Market data for calibration</param>
        /// <param name="symbolProfile">Symbol profile for context</param>
        /// <returns>Calibration result</returns>
        Task<CalibrationResult> CalibrateAsync(
            List<MarketDataPoint> marketData,
            SymbolProfile symbolProfile);

        /// <summary>
        /// Get calibration progress for UI updates
        /// </summary>
        CalibrationStatus GetCalibrationProgress();

        /// <summary>
        /// Cancel ongoing calibration
        /// </summary>
        void CancelCalibration();

        /// <summary>
        /// Validate settings against market data
        /// </summary>
        /// <param name="settings">Settings to validate</param>
        /// <param name="marketData">Historical market data</param>
        /// <returns>Validation results</returns>
        Task<BacktestValidation> ValidateSettingsAsync(OptimalSettings settings, List<MarketDataPoint> marketData);
    }

    /// <summary>
    /// Threshold optimization interface for signal generation parameters
    /// </summary>
    public interface IThresholdOptimizer
    {
        Task<ThresholdOptimizationResult> OptimizeThresholdsAsync(
            List<MarketDataPoint> marketData,
            VolumeCharacteristics volumeCharacteristics,
            DeltaFlowCharacteristics deltaFlowCharacteristics);

        Task<ThresholdOptimizationResult> QuickOptimizeAsync(
            List<MarketDataPoint> recentData,
            OptimalSettings currentSettings);
    }

    /// <summary>
    /// Risk parameter calibration interface
    /// </summary>
    public interface IRiskCalibrator
    {
        Task<RiskCalibrationResult> CalibrateRiskParametersAsync(
            List<MarketDataPoint> marketData,
            VolatilityCharacteristics volatilityCharacteristics,
            UserRiskProfile userRiskProfile);

        Task<RiskCalibrationResult> QuickCalibrateAsync(
            List<MarketDataPoint> recentData,
            VolatilityCharacteristics currentVolatility,
            OptimalSettings currentSettings);
    }

    /// <summary>
    /// Backtest validation interface
    /// </summary>
    public interface IBacktestValidator
    {
        Task<BacktestValidation> ValidateAsync(OptimalSettings settings, List<MarketDataPoint> marketData);
        Task<BacktestValidation> QuickValidateAsync(OptimalSettings settings, List<MarketDataPoint> recentData);
    }

}
