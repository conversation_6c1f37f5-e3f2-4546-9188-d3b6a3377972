using System;
using System.Collections.Generic;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Models.Analysis
{
    /// <summary>
    /// Phase 3.1: Multi-timeframe correlation types
    /// </summary>
    public enum CorrelationType
    {
        DirectionalCorrelation,    // Signal direction alignment
        StrengthCorrelation,      // Signal strength alignment
        TemporalCorrelation,      // Timing alignment
        PatternCorrelation        // Pattern similarity
    }

    /// <summary>
    /// Multi-timeframe pattern types
    /// </summary>
    public enum MultiTimeframePatternType
    {
        TrendAlignment,           // Trend alignment across timeframes
        TrendDivergence,         // Trend divergence across timeframes
        ReversalPattern,         // Reversal signals across timeframes
        ContinuationPattern,     // Continuation signals across timeframes
        BreakoutPattern,         // Breakout patterns across timeframes
        ConsolidationPattern     // Consolidation patterns across timeframes
    }

    /// <summary>
    /// Timeframe conflict resolution strategies
    /// </summary>
    public enum ConflictResolutionStrategy
    {
        HigherTimeframeBias,     // Longer timeframes get higher weight
        ConfidenceWeighted,      // Higher confidence signals get priority
        MarketConditionAdaptive, // Adjust weights based on market conditions
        HistoricalPerformance,   // Weight based on past accuracy
        VolumeWeighted,          // Weight based on volume confirmation
        ConsensusRequired        // Require majority agreement
    }

    /// <summary>
    /// Signal consistency levels for multi-timeframe analysis
    /// </summary>
    public enum SignalConsistency
    {
        VeryLow,    // Very low consistency across timeframes
        Low,        // Low consistency across timeframes
        Medium,     // Medium consistency across timeframes
        High,       // High consistency across timeframes
        VeryHigh    // Very high consistency across timeframes
    }

    /// <summary>
    /// Multi-timeframe correlation analysis result
    /// </summary>
    public class MultiTimeframeCorrelationAnalysis
    {
        public DateTime Timestamp { get; set; }
        public Dictionary<CorrelationType, decimal> CorrelationScores { get; set; } = new Dictionary<CorrelationType, decimal>();
        public decimal OverallCorrelation { get; set; }
        public List<Timeframe> AlignedTimeframes { get; set; } = new List<Timeframe>();
        public List<Timeframe> DivergentTimeframes { get; set; } = new List<Timeframe>();
        public Dictionary<Timeframe, decimal> TimeframeReliability { get; set; } = new Dictionary<Timeframe, decimal>();
        public List<string> CorrelationFactors { get; set; } = new List<string>();
        public bool HasSignificantDivergence { get; set; }
        public decimal DivergenceStrength { get; set; }
        public SignalConsistency ConsistencyLevel { get; set; }
    }

    /// <summary>
    /// Cross-timeframe pattern detection result
    /// </summary>
    public class CrossTimeframePattern
    {
        public DateTime Timestamp { get; set; }
        public MultiTimeframePatternType PatternType { get; set; }
        public string PatternName { get; set; }
        public decimal Confidence { get; set; }
        public Dictionary<Timeframe, decimal> TimeframeContributions { get; set; } = new Dictionary<Timeframe, decimal>();
        public Dictionary<Timeframe, TradingSignal> ContributingSignals { get; set; } = new Dictionary<Timeframe, TradingSignal>();
        public List<Timeframe> SupportingTimeframes { get; set; } = new List<Timeframe>();
        public List<Timeframe> ConflictingTimeframes { get; set; } = new List<Timeframe>();
        public decimal PatternStrength { get; set; }
        public TimeSpan EstimatedDuration { get; set; }
        public List<string> PatternFactors { get; set; } = new List<string>();
        public bool IsValidated { get; set; }
        public decimal ValidationScore { get; set; }
    }

    /// <summary>
    /// Timeframe-weighted signal synthesis result
    /// </summary>
    public class TimeframeWeightedSignal
    {
        public DateTime Timestamp { get; set; }
        public SignalType Type { get; set; }
        public decimal Confidence { get; set; }
        public decimal Strength { get; set; }
        public SignalQuality Quality { get; set; }
        public Dictionary<Timeframe, decimal> TimeframeWeights { get; set; } = new Dictionary<Timeframe, decimal>();
        public Dictionary<Timeframe, TradingSignal> ContributingSignals { get; set; } = new Dictionary<Timeframe, TradingSignal>();
        public ConflictResolutionStrategy ResolutionStrategy { get; set; }
        public List<string> ConflictResolutions { get; set; } = new List<string>();
        public decimal WeightedConsensus { get; set; }
        public bool HasTimeframeConflicts { get; set; }
        public List<CrossTimeframePattern> SupportingPatterns { get; set; } = new List<CrossTimeframePattern>();
    }

    /// <summary>
    /// Multi-timeframe analysis configuration
    /// </summary>
    public class MultiTimeframeAnalysisConfig
    {
        public List<Timeframe> EnabledTimeframes { get; set; } = new List<Timeframe> { Timeframe.M1, Timeframe.M5, Timeframe.M15, Timeframe.H1 };
        public Dictionary<Timeframe, decimal> BaseTimeframeWeights { get; set; } = new Dictionary<Timeframe, decimal>();
        public ConflictResolutionStrategy DefaultConflictResolution { get; set; } = ConflictResolutionStrategy.HigherTimeframeBias;
        public decimal MinCorrelationThreshold { get; set; } = 0.6m;
        public decimal MinPatternConfidence { get; set; } = 0.7m;
        public bool EnablePatternDetection { get; set; } = true;
        public bool EnableCorrelationAnalysis { get; set; } = true;
        public bool EnableAdaptiveWeighting { get; set; } = true;
        public int MaxPatternHistory { get; set; } = 100;
        public int CorrelationAnalysisWindow { get; set; } = 50;
        
        public MultiTimeframeAnalysisConfig()
        {
            InitializeDefaultWeights();
        }
        
        private void InitializeDefaultWeights()
        {
            BaseTimeframeWeights[Timeframe.M1] = 0.15m;   // 15% - Short-term signals
            BaseTimeframeWeights[Timeframe.M5] = 0.25m;   // 25% - Medium-term signals
            BaseTimeframeWeights[Timeframe.M15] = 0.35m;  // 35% - Primary timeframe
            BaseTimeframeWeights[Timeframe.H1] = 0.25m;   // 25% - Long-term trend
        }
    }

    /// <summary>
    /// Multi-timeframe health metrics
    /// </summary>
    public class MultiTimeframeHealthMetrics
    {
        public DateTime Timestamp { get; set; }
        public Dictionary<Timeframe, decimal> TimeframeHealthScores { get; set; } = new Dictionary<Timeframe, decimal>();
        public decimal OverallMultiTimeframeHealth { get; set; }
        public decimal CorrelationAnalysisHealth { get; set; }
        public decimal PatternDetectionHealth { get; set; }
        public decimal SignalSynthesisHealth { get; set; }
        public int ActiveTimeframes { get; set; }
        public int HealthyTimeframes { get; set; }
        public int DegradedTimeframes { get; set; }
        public int FailedTimeframes { get; set; }
        public List<string> HealthIssues { get; set; } = new List<string>();
        public Dictionary<string, decimal> CustomHealthMetrics { get; set; } = new Dictionary<string, decimal>();
    }

    /// <summary>
    /// Timeframe reliability tracking
    /// </summary>
    public class TimeframeReliabilityMetrics
    {
        public Timeframe Timeframe { get; set; }
        public decimal ReliabilityScore { get; set; }
        public decimal AccuracyScore { get; set; }
        public decimal ConsistencyScore { get; set; }
        public int TotalSignals { get; set; }
        public int SuccessfulSignals { get; set; }
        public int FailedSignals { get; set; }
        public decimal SuccessRate { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public DateTime LastUpdate { get; set; }
        public List<string> ReliabilityFactors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Multi-timeframe analysis statistics
    /// </summary>
    public class MultiTimeframeAnalysisStatistics
    {
        public int TotalAnalyses { get; set; }
        public int SuccessfulCorrelations { get; set; }
        public int DetectedPatterns { get; set; }
        public int ValidatedPatterns { get; set; }
        public int ConflictResolutions { get; set; }
        public decimal AverageCorrelationScore { get; set; }
        public decimal AveragePatternConfidence { get; set; }
        public Dictionary<ConflictResolutionStrategy, int> ResolutionStrategyUsage { get; set; } = new Dictionary<ConflictResolutionStrategy, int>();
        public Dictionary<MultiTimeframePatternType, int> PatternTypeDistribution { get; set; } = new Dictionary<MultiTimeframePatternType, int>();
        public Dictionary<Timeframe, TimeframeReliabilityMetrics> TimeframeReliability { get; set; } = new Dictionary<Timeframe, TimeframeReliabilityMetrics>();
        public DateTime LastAnalysis { get; set; }
        public TimeSpan AverageAnalysisTime { get; set; }
    }

    /// <summary>
    /// Multi-timeframe signal conflict
    /// </summary>
    public class TimeframeSignalConflict
    {
        public DateTime Timestamp { get; set; }
        public List<Timeframe> ConflictingTimeframes { get; set; } = new List<Timeframe>();
        public Dictionary<Timeframe, TradingSignal> ConflictingSignals { get; set; } = new Dictionary<Timeframe, TradingSignal>();
        public string ConflictReason { get; set; }
        public decimal ConflictSeverity { get; set; }
        public ConflictResolutionStrategy RecommendedResolution { get; set; }
        public bool WasResolved { get; set; }
        public string ResolutionDetails { get; set; }
    }

    /// <summary>
    /// Adaptive timeframe weighting result
    /// </summary>
    public class AdaptiveTimeframeWeights
    {
        public DateTime Timestamp { get; set; }
        public Dictionary<Timeframe, decimal> CurrentWeights { get; set; } = new Dictionary<Timeframe, decimal>();
        public Dictionary<Timeframe, decimal> BaseWeights { get; set; } = new Dictionary<Timeframe, decimal>();
        public Dictionary<Timeframe, decimal> WeightAdjustments { get; set; } = new Dictionary<Timeframe, decimal>();
        public List<string> AdjustmentReasons { get; set; } = new List<string>();
        public decimal TotalWeightChange { get; set; }
        public bool WeightsWereAdjusted { get; set; }
        public MarketContext MarketCondition { get; set; }
    }

    /// <summary>
    /// Multi-timeframe analysis event
    /// </summary>
    public class MultiTimeframeAnalysisEvent
    {
        public DateTime Timestamp { get; set; }
        public string EventType { get; set; }
        public string Description { get; set; }
        public Dictionary<string, object> EventData { get; set; } = new Dictionary<string, object>();
        public List<Timeframe> AffectedTimeframes { get; set; } = new List<Timeframe>();
        public decimal Impact { get; set; }
        public string Severity { get; set; }
    }
}
