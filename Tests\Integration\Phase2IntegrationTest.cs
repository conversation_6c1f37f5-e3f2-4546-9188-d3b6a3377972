using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SmartVolumeStrategy.Core.Analysis;
using SmartVolumeStrategy.Core.Strategy;
using SmartVolumeStrategy.Models;
using SmartVolumeStrategy.Interfaces;

namespace SmartVolumeStrategy.Tests.Integration
{
    /// <summary>
    /// Integration test for Phase 2: Real-time analysis integration with signal generation
    /// Tests the complete flow from market data to enhanced signal confidence calculation
    /// </summary>
    public class Phase2IntegrationTest
    {
        public static async Task<bool> RunIntegrationTest()
        {
            try
            {
                Console.WriteLine("🧪 === PHASE 2 INTEGRATION TEST STARTING ===");
                
                // Step 1: Initialize all Phase 1 analyzers
                Console.WriteLine("🔧 Initializing Phase 1 analyzers...");
                var volumeAnalyzer = new VolumePatternAnalyzer();
                var deltaAnalyzer = new DeltaFlowAnalyzer();
                var volatilityAnalyzer = new VolatilityAnalyzer();
                var marketProfileAnalyzer = new MarketProfileAnalyzer();
                
                // Step 2: Initialize RealTimeAnalysisEngine
                Console.WriteLine("🔧 Initializing RealTimeAnalysisEngine...");
                var realTimeEngine = new RealTimeAnalysisEngine(
                    volumeAnalyzer, deltaAnalyzer, volatilityAnalyzer, marketProfileAnalyzer);
                
                // Step 3: Create test symbol profile and settings
                Console.WriteLine("📊 Creating test symbol profile and settings...");
                var symbolProfile = CreateTestSymbolProfile();
                var optimalSettings = CreateTestOptimalSettings();
                
                // Step 4: Initialize the engine
                realTimeEngine.Initialize(symbolProfile, optimalSettings);
                Console.WriteLine("✅ RealTimeAnalysisEngine initialized successfully");
                
                // Step 5: Generate test market data
                Console.WriteLine("📈 Generating test market data...");
                var testData = GenerateTestMarketData(50);
                
                // Step 6: Feed data to the engine
                Console.WriteLine("📊 Feeding market data to RealTimeAnalysisEngine...");
                foreach (var dataPoint in testData)
                {
                    realTimeEngine.UpdateMarketData(dataPoint);
                }
                
                // Step 7: Get real-time market context
                Console.WriteLine("🎯 Getting real-time MarketContext...");
                var currentBar = testData.Last();
                var marketContext = realTimeEngine.GetCurrentMarketContext(currentBar);
                
                // Step 8: Verify MarketContext contains real data (not placeholders)
                Console.WriteLine("✅ Verifying MarketContext contains real analysis data...");
                var hasRealData = VerifyMarketContextHasRealData(marketContext);
                
                if (!hasRealData)
                {
                    Console.WriteLine("❌ INTEGRATION TEST FAILED: MarketContext contains placeholder data");
                    return false;
                }
                
                // Step 9: Test enhanced signal generation
                Console.WriteLine("🎯 Testing enhanced signal generation...");
                var signalGenerator = new SignalGenerator(Console.WriteLine);

                // Create a test volume block
                var volumeBlock = CreateTestVolumeBlock();
                
                // Generate signal with enhanced confidence calculation
                var signal = signalGenerator.GenerateSignal(currentBar, volumeBlock, marketContext, optimalSettings);
                
                // Step 10: Verify enhanced confidence calculation
                Console.WriteLine("✅ Verifying enhanced confidence calculation...");
                var hasEnhancedConfidence = VerifyEnhancedConfidence(signal, marketContext);
                
                if (!hasEnhancedConfidence)
                {
                    Console.WriteLine("❌ INTEGRATION TEST FAILED: Enhanced confidence calculation not working");
                    return false;
                }
                
                // Step 11: Performance verification
                Console.WriteLine("⚡ Verifying performance metrics...");
                var performanceMetrics = realTimeEngine.GetPerformanceMetrics();
                var hasGoodPerformance = VerifyPerformanceMetrics(performanceMetrics);
                
                if (!hasGoodPerformance)
                {
                    Console.WriteLine("❌ INTEGRATION TEST FAILED: Performance metrics indicate issues");
                    return false;
                }
                
                Console.WriteLine("🎉 === PHASE 2 INTEGRATION TEST COMPLETED SUCCESSFULLY ===");
                Console.WriteLine($"✅ Real-time MarketContext: CVD={marketContext.CVDTrend:F2}, Volatility={marketContext.VolatilityRegime}");
                Console.WriteLine($"✅ Enhanced Signal: Type={signal.Type}, Confidence={signal.Confidence:P1}, Quality={signal.Quality}");
                Console.WriteLine($"✅ Performance: CacheHitRate={performanceMetrics.CacheHitRate:P1}, AvgAnalysisTime={performanceMetrics.AverageAnalysisTimeMs:F1}ms");
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ INTEGRATION TEST EXCEPTION: {ex.Message}");
                return false;
            }
        }
        
        private static SymbolProfile CreateTestSymbolProfile()
        {
            return new SymbolProfile
            {
                Symbol = "BTCUSDT",
                VolumeAnalysisResult = new VolumeAnalysisResult
                {
                    AverageVolume = 1000000,
                    VolumeStandardDeviation = 200000,
                    VolumeRegime = VolumeRegime.Normal
                },
                DeltaFlowAnalysisResult = new DeltaFlowAnalysisResult
                {
                    AverageDeltaImbalance = 0.3m,
                    DeltaFlowRegime = DeltaFlowRegime.Balanced
                },
                VolatilityAnalysisResult = new VolatilityAnalysisResult
                {
                    AverageVolatility = 0.015m,
                    VolatilityRegime = VolatilityRegime.Normal
                },
                MarketProfileAnalysisResult = new MarketProfileAnalysisResult
                {
                    MostActiveSession = TradingSession.US,
                    Is24HourMarket = true
                }
            };
        }
        
        private static OptimalSettings CreateTestOptimalSettings()
        {
            return new OptimalSettings
            {
                VolumeThreshold = 2.0m,
                SignalThreshold = 1.5m,
                DeltaImbalanceThreshold = 0.4m,
                TakeProfitPercent = 1.2m,
                StopLossPercent = 0.8m,
                EnableSessionAdjustments = true
            };
        }
        
        private static List<MarketDataPoint> GenerateTestMarketData(int count)
        {
            var data = new List<MarketDataPoint>();
            var random = new Random(42); // Fixed seed for reproducible tests
            var basePrice = 50000m;
            var baseVolume = 1000000m;
            
            for (int i = 0; i < count; i++)
            {
                var price = basePrice + (decimal)(random.NextDouble() - 0.5) * 1000;
                var volume = baseVolume + (decimal)(random.NextDouble() - 0.5) * 500000;
                var delta = (decimal)(random.NextDouble() - 0.5) * volume * 0.4m;
                
                data.Add(new MarketDataPoint
                {
                    Timestamp = DateTime.UtcNow.AddMinutes(-count + i),
                    Open = price - 10,
                    High = price + 20,
                    Low = price - 20,
                    Close = price,
                    Volume = volume,
                    BuyVolume = volume * 0.6m,
                    SellVolume = volume * 0.4m,
                    BarIndex = i
                });
            }
            
            return data;
        }
        
        private static VolumeBlockResult CreateTestVolumeBlock()
        {
            return new VolumeBlockResult
            {
                IsVolumeBlock = true,
                CumulativeImpact = 0.025m,
                VolumeRatio = 2.5m,
                Confidence = 0.8m,
                Type = VolumeBlockType.BuyingBlock,
                Description = "Test volume block for integration testing"
            };
        }
        
        private static bool VerifyMarketContextHasRealData(MarketContext context)
        {
            // Verify that MarketContext contains real analysis data, not placeholders
            var hasRealCVD = context.CVDTrend != 0; // Should not be placeholder zero
            var hasRealVolatility = context.VolatilityRegime != VolatilityRegime.Normal || context.MarketConditions.Any();
            var hasRealSession = context.CurrentSession != TradingSession.Unknown;
            
            Console.WriteLine($"  • CVD Trend: {context.CVDTrend:F2} (Real: {hasRealCVD})");
            Console.WriteLine($"  • Volatility Regime: {context.VolatilityRegime} (Real: {hasRealVolatility})");
            Console.WriteLine($"  • Current Session: {context.CurrentSession} (Real: {hasRealSession})");
            Console.WriteLine($"  • Market Conditions: {context.MarketConditions.Count} conditions");
            
            return hasRealCVD || hasRealVolatility || hasRealSession;
        }
        
        private static bool VerifyEnhancedConfidence(TradingSignal signal, MarketContext context)
        {
            // Verify that signal confidence is calculated using enhanced factors
            var hasReasonableConfidence = signal.Confidence >= 0.1m && signal.Confidence <= 1.0m;
            var hasQuality = signal.Quality != SignalQuality.Poor || signal.Confidence < 0.5m;
            var hasReason = !string.IsNullOrEmpty(signal.PrimaryReason);
            
            Console.WriteLine($"  • Signal Confidence: {signal.Confidence:P1} (Valid: {hasReasonableConfidence})");
            Console.WriteLine($"  • Signal Quality: {signal.Quality} (Valid: {hasQuality})");
            Console.WriteLine($"  • Primary Reason: {signal.PrimaryReason} (Valid: {hasReason})");
            
            return hasReasonableConfidence && hasQuality && hasReason;
        }
        
        private static bool VerifyPerformanceMetrics(AnalysisPerformanceMetrics metrics)
        {
            var hasReasonableAnalysisTime = metrics.AverageAnalysisTimeMs < 100; // Should be fast
            var hasValidCacheRate = metrics.CacheHitRate >= 0 && metrics.CacheHitRate <= 1;
            var hasLowErrorRate = metrics.ErrorCount == 0 || (metrics.ErrorCount / (double)metrics.TotalAnalysisCount) < 0.1;
            
            Console.WriteLine($"  • Average Analysis Time: {metrics.AverageAnalysisTimeMs:F1}ms (Good: {hasReasonableAnalysisTime})");
            Console.WriteLine($"  • Cache Hit Rate: {metrics.CacheHitRate:P1} (Valid: {hasValidCacheRate})");
            Console.WriteLine($"  • Error Rate: {metrics.ErrorCount}/{metrics.TotalAnalysisCount} (Good: {hasLowErrorRate})");
            
            return hasReasonableAnalysisTime && hasValidCacheRate && hasLowErrorRate;
        }
    }
}
