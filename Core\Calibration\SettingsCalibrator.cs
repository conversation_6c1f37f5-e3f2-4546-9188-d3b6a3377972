using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SmartVolumeStrategy.Interfaces;
using SmartVolumeStrategy.Models;

namespace SmartVolumeStrategy.Core.Calibration
{
    /// <summary>
    /// Master settings calibrator that coordinates optimization of all strategy parameters based on symbol analysis
    /// </summary>
    public class SettingsCalibrator : ISettingsCalibrator
    {
        private readonly IThresholdOptimizer _thresholdOptimizer;
        private readonly IRiskCalibrator _riskCalibrator;
        private readonly IBacktestValidator _backtestValidator;
        
        private CalibrationStatus _calibrationStatus;
        private CancellationTokenSource _cancellationTokenSource;
        private readonly object _statusLock = new object();

        public SettingsCalibrator(
            IThresholdOptimizer thresholdOptimizer,
            IRiskCalibrator riskCalibrator,
            IBacktestValidator backtestValidator)
        {
            _thresholdOptimizer = thresholdOptimizer ?? throw new ArgumentNullException(nameof(thresholdOptimizer));
            _riskCalibrator = riskCalibrator ?? throw new ArgumentNullException(nameof(riskCalibrator));
            _backtestValidator = backtestValidator ?? throw new ArgumentNullException(nameof(backtestValidator));
            
            InitializeCalibrationStatus();
        }

        /// <summary>
        /// Calibrate all strategy settings based on symbol analysis
        /// </summary>
        public async Task<CalibrationResult> CalibrateSettingsAsync(
            SymbolProfile symbolProfile, 
            List<MarketDataPoint> marketData,
            UserConstraints userConstraints = null)
        {
            if (symbolProfile == null)
                throw new ArgumentNullException(nameof(symbolProfile));
            
            if (marketData == null || marketData.Count < 100)
                throw new ArgumentException("Need at least 100 bars for calibration", nameof(marketData));

            var correlationId = symbolProfile.CorrelationId;
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                var cancellationToken = _cancellationTokenSource.Token;

                UpdateCalibrationStatus(CalibrationPhase.DataCollection, 0, "Starting calibration process");

                var calibrationResult = new CalibrationResult
                {
                    Symbol = symbolProfile.Symbol,
                    CalibrationTimestamp = DateTime.UtcNow,
                    CorrelationId = correlationId,
                    IsSuccessful = false
                };

                // Phase 1: Threshold Optimization (30% progress)
                UpdateCalibrationStatus(CalibrationPhase.ThresholdOptimization, 10, "Optimizing volume and signal thresholds");
                cancellationToken.ThrowIfCancellationRequested();

                var thresholdResult = await OptimizeThresholdsAsync(symbolProfile, marketData, cancellationToken);
                ApplyThresholdResults(calibrationResult.Settings, thresholdResult);

                // Phase 2: Risk Parameter Calibration (60% progress)
                UpdateCalibrationStatus(CalibrationPhase.RiskCalibration, 40, "Calibrating risk management parameters");
                cancellationToken.ThrowIfCancellationRequested();

                var riskResult = await CalibrateRiskParametersAsync(symbolProfile, marketData, userConstraints, cancellationToken);
                ApplyRiskResults(calibrationResult.Settings, riskResult);

                // Phase 3: Session Adjustments (70% progress)
                UpdateCalibrationStatus(CalibrationPhase.SettingsFinalization, 70, "Applying session-based adjustments");
                cancellationToken.ThrowIfCancellationRequested();

                ApplySessionAdjustments(calibrationResult.Settings, symbolProfile.MarketProfile);

                // Phase 4: User Constraints Application (80% progress)
                UpdateCalibrationStatus(CalibrationPhase.SettingsFinalization, 80, "Applying user constraints");
                
                ApplyUserConstraints(calibrationResult.Settings, userConstraints);

                // Phase 5: Backtest Validation (90% progress)
                UpdateCalibrationStatus(CalibrationPhase.BacktestValidation, 90, "Validating calibrated settings");
                cancellationToken.ThrowIfCancellationRequested();

                calibrationResult.Validation = await _backtestValidator.ValidateAsync(calibrationResult.Settings, marketData);

                // Phase 6: Final Analysis and Confidence Calculation (100% progress)
                UpdateCalibrationStatus(CalibrationPhase.SettingsFinalization, 95, "Calculating confidence and finalizing results");

                FinalizeCalibrationResult(calibrationResult, symbolProfile, thresholdResult, riskResult, stopwatch.Elapsed);

                UpdateCalibrationStatus(CalibrationPhase.Completed, 100, $"Calibration completed for {symbolProfile.Symbol}");

                return calibrationResult;
            }
            catch (OperationCanceledException)
            {
                UpdateCalibrationStatus(CalibrationPhase.Failed, 0, "Calibration was cancelled");
                throw;
            }
            catch (Exception ex)
            {
                UpdateCalibrationStatus(CalibrationPhase.Failed, 0, $"Calibration failed: {ex.Message}");
                throw;
            }
            finally
            {
                stopwatch.Stop();
                _cancellationTokenSource?.Dispose();
            }
        }

        /// <summary>
        /// Quick recalibration based on recent performance
        /// </summary>
        public async Task<CalibrationResult> RecalibrateAsync(
            OptimalSettings currentSettings,
            PerformanceMetrics recentPerformance,
            List<MarketDataPoint> marketData)
        {
            if (currentSettings == null)
                throw new ArgumentNullException(nameof(currentSettings));
            
            if (recentPerformance == null)
                throw new ArgumentNullException(nameof(recentPerformance));

            // Quick recalibration focuses on adjusting existing settings based on performance
            var adjustedSettings = new OptimalSettings
            {
                // Copy current settings
                VolumeThreshold = currentSettings.VolumeThreshold,
                SignalThreshold = currentSettings.SignalThreshold,
                LookbackPeriod = currentSettings.LookbackPeriod,
                DeltaImbalanceThreshold = currentSettings.DeltaImbalanceThreshold,
                DeltaSignificanceThreshold = currentSettings.DeltaSignificanceThreshold,
                CVDLookbackPeriod = currentSettings.CVDLookbackPeriod,
                TakeProfitPercent = currentSettings.TakeProfitPercent,
                StopLossPercent = currentSettings.StopLossPercent,
                PositionSizeUSDT = currentSettings.PositionSizeUSDT,
                RiskAdjustmentFactor = currentSettings.RiskAdjustmentFactor
            };

            var adjustments = new List<CalibrationAdjustment>();

            // Adjust based on win rate
            if (recentPerformance.WinRate < 40)
            {
                // Increase selectivity
                adjustedSettings.SignalThreshold *= 1.2m;
                adjustments.Add(new CalibrationAdjustment
                {
                    Parameter = "SignalThreshold",
                    OldValue = currentSettings.SignalThreshold.ToString("F2"),
                    AdjustedValue = adjustedSettings.SignalThreshold.ToString("F2"),
                    Reason = $"Low win rate ({recentPerformance.WinRate:F1}%) - increasing selectivity",
                    ConfidenceInAdjustment = 0.8m,
                    Type = AdjustmentType.Performance
                });
            }
            else if (recentPerformance.WinRate > 75)
            {
                // Decrease selectivity for more signals
                adjustedSettings.SignalThreshold *= 0.9m;
                adjustments.Add(new CalibrationAdjustment
                {
                    Parameter = "SignalThreshold",
                    OldValue = currentSettings.SignalThreshold.ToString("F2"),
                    AdjustedValue = adjustedSettings.SignalThreshold.ToString("F2"),
                    Reason = $"High win rate ({recentPerformance.WinRate:F1}%) - decreasing selectivity for more signals",
                    ConfidenceInAdjustment = 0.7m,
                    Type = AdjustmentType.Performance
                });
            }

            // Adjust based on profit factor
            if (recentPerformance.ProfitFactor < 1.2m)
            {
                // Tighten stops or widen targets
                adjustedSettings.StopLossPercent *= 0.9m;
                adjustments.Add(new CalibrationAdjustment
                {
                    Parameter = "StopLossPercent",
                    OldValue = currentSettings.StopLossPercent.ToString("F2"),
                    AdjustedValue = adjustedSettings.StopLossPercent.ToString("F2"),
                    Reason = $"Low profit factor ({recentPerformance.ProfitFactor:F2}) - tightening stops",
                    ConfidenceInAdjustment = 0.6m,
                    Type = AdjustmentType.RiskManagement
                });
            }

            var result = new CalibrationResult
            {
                Symbol = recentPerformance.Symbol,
                CalibrationTimestamp = DateTime.UtcNow,
                CorrelationId = Guid.NewGuid().ToString("N")[..8],
                IsSuccessful = true,
                Settings = adjustedSettings,
                Adjustments = adjustments,
                OverallConfidence = 0.7m // Lower confidence for quick recalibration
            };

            // Quick validation
            result.Validation = await _backtestValidator.QuickValidateAsync(adjustedSettings, marketData.TakeLast(100).ToList());

            return result;
        }

        /// <summary>
        /// Get calibration progress for UI updates
        /// </summary>
        public CalibrationStatus GetCalibrationProgress()
        {
            lock (_statusLock)
            {
                return new CalibrationStatus
                {
                    CurrentPhase = _calibrationStatus.CurrentPhase,
                    ProgressPercent = _calibrationStatus.ProgressPercent,
                    CurrentTask = _calibrationStatus.CurrentTask,
                    StartTime = _calibrationStatus.StartTime,
                    EstimatedTimeRemaining = _calibrationStatus.EstimatedTimeRemaining,
                    CompletedTasks = new List<string>(_calibrationStatus.CompletedTasks),
                    PendingTasks = new List<string>(_calibrationStatus.PendingTasks),
                    HasErrors = _calibrationStatus.HasErrors,
                    Errors = new List<string>(_calibrationStatus.Errors)
                };
            }
        }

        /// <summary>
        /// Cancel ongoing calibration
        /// </summary>
        public void CancelCalibration()
        {
            _cancellationTokenSource?.Cancel();
            UpdateCalibrationStatus(CalibrationPhase.Failed, 0, "Calibration cancelled by user");
        }

        /// <summary>
        /// Validate settings against market data
        /// </summary>
        public async Task<BacktestValidation> ValidateSettingsAsync(OptimalSettings settings, List<MarketDataPoint> marketData)
        {
            return await _backtestValidator.ValidateAsync(settings, marketData);
        }

        /// <summary>
        /// Simple calibration method for adaptive calibrator
        /// </summary>
        public async Task<CalibrationResult> CalibrateAsync(
            List<MarketDataPoint> marketData,
            SymbolProfile symbolProfile)
        {
            // This is a simplified version of the full calibration for adaptive use
            return await CalibrateSettingsAsync(symbolProfile, marketData, null);
        }

        #region Private Methods

        private void InitializeCalibrationStatus()
        {
            lock (_statusLock)
            {
                _calibrationStatus = new CalibrationStatus
                {
                    CurrentPhase = CalibrationPhase.NotStarted,
                    ProgressPercent = 0,
                    CurrentTask = "Ready to calibrate",
                    StartTime = DateTime.UtcNow,
                    PendingTasks = new List<string>
                    {
                        "Threshold Optimization",
                        "Risk Parameter Calibration",
                        "Session Adjustments",
                        "User Constraints Application",
                        "Backtest Validation",
                        "Confidence Calculation"
                    }
                };
            }
        }

        private void UpdateCalibrationStatus(CalibrationPhase phase, decimal progressPercent, string currentTask)
        {
            lock (_statusLock)
            {
                _calibrationStatus.CurrentPhase = phase;
                _calibrationStatus.ProgressPercent = progressPercent;
                _calibrationStatus.CurrentTask = currentTask;
                
                // Update completed/pending tasks
                if (progressPercent > _calibrationStatus.ProgressPercent)
                {
                    var taskToComplete = _calibrationStatus.PendingTasks.FirstOrDefault();
                    if (taskToComplete != null)
                    {
                        _calibrationStatus.CompletedTasks.Add(taskToComplete);
                        _calibrationStatus.PendingTasks.Remove(taskToComplete);
                    }
                }

                // Estimate time remaining
                if (progressPercent > 0)
                {
                    var elapsed = DateTime.UtcNow - _calibrationStatus.StartTime;
                    var totalEstimated = TimeSpan.FromTicks((long)(elapsed.Ticks / (double)progressPercent * 100));
                    _calibrationStatus.EstimatedTimeRemaining = totalEstimated - elapsed;
                }
            }
        }

        private async Task<ThresholdOptimizationResult> OptimizeThresholdsAsync(
            SymbolProfile symbolProfile, 
            List<MarketDataPoint> marketData, 
            CancellationToken cancellationToken)
        {
            return await _thresholdOptimizer.OptimizeThresholdsAsync(
                marketData,
                symbolProfile.Volume,
                symbolProfile.DeltaFlow);
        }

        private async Task<RiskCalibrationResult> CalibrateRiskParametersAsync(
            SymbolProfile symbolProfile,
            List<MarketDataPoint> marketData,
            UserConstraints userConstraints,
            CancellationToken cancellationToken)
        {
            var userRiskProfile = CreateUserRiskProfile(userConstraints);
            
            return await _riskCalibrator.CalibrateRiskParametersAsync(
                marketData,
                symbolProfile.Volatility,
                userRiskProfile);
        }

        private UserRiskProfile CreateUserRiskProfile(UserConstraints userConstraints)
        {
            var riskProfile = new UserRiskProfile
            {
                Tolerance = RiskTolerance.Moderate,
                PreferredRiskReward = 2.0m,
                MaxDrawdownTolerance = 0.15m,
                PreferHigherWinRate = true,
                AccountBalance = 10000m, // Default
                RiskPercentPerTrade = 0.02m // 2% default
            };

            if (userConstraints != null)
            {
                if (userConstraints.MaxDrawdown.HasValue)
                    riskProfile.MaxDrawdownTolerance = userConstraints.MaxDrawdown.Value;
                
                if (userConstraints.MaxRiskPercentPerTrade.HasValue)
                    riskProfile.RiskPercentPerTrade = userConstraints.MaxRiskPercentPerTrade.Value;
                
                if (userConstraints.ConservativeMode)
                    riskProfile.Tolerance = RiskTolerance.Conservative;
            }

            return riskProfile;
        }

        private void ApplyThresholdResults(OptimalSettings settings, ThresholdOptimizationResult thresholdResult)
        {
            settings.VolumeThreshold = thresholdResult.OptimalVolumeThreshold;
            settings.SignalThreshold = thresholdResult.OptimalSignalThreshold;
            settings.DeltaImbalanceThreshold = thresholdResult.OptimalDeltaImbalanceThreshold;
            settings.DeltaSignificanceThreshold = thresholdResult.OptimalDeltaSignificanceThreshold;
        }

        private void ApplyRiskResults(OptimalSettings settings, RiskCalibrationResult riskResult)
        {
            settings.TakeProfitPercent = riskResult.OptimalTakeProfitPercent;
            settings.StopLossPercent = riskResult.OptimalStopLossPercent;
            settings.PositionSizeUSDT = riskResult.OptimalPositionSizeUSDT;
            settings.RiskAdjustmentFactor = riskResult.RiskAdjustmentFactor;
        }

        private void ApplySessionAdjustments(OptimalSettings settings, MarketProfileCharacteristics marketProfile)
        {
            // Enable session adjustments if there's significant variation
            if (marketProfile.SessionVolatilityVariation > 0.3m)
            {
                settings.EnableSessionAdjustments = true;
                settings.AsianSessionMultiplier = marketProfile.AsianSessionActivity;
                settings.EuropeanSessionMultiplier = marketProfile.EuropeanSessionActivity;
                settings.USSessionMultiplier = marketProfile.USSessionActivity;
            }
        }

        private void ApplyUserConstraints(OptimalSettings settings, UserConstraints userConstraints)
        {
            if (userConstraints == null)
                return;

            if (userConstraints.MaxPositionSizeUSDT.HasValue)
                settings.PositionSizeUSDT = Math.Min(settings.PositionSizeUSDT, userConstraints.MaxPositionSizeUSDT.Value);
            
            if (userConstraints.MinPositionSizeUSDT.HasValue)
                settings.PositionSizeUSDT = Math.Max(settings.PositionSizeUSDT, userConstraints.MinPositionSizeUSDT.Value);

            if (userConstraints.ConservativeMode)
            {
                // Apply conservative adjustments
                settings.SignalThreshold *= 1.3m; // More selective
                settings.StopLossPercent *= 0.8m; // Tighter stops
                settings.PositionSizeUSDT *= 0.7m; // Smaller positions
            }
        }

        private void FinalizeCalibrationResult(
            CalibrationResult result,
            SymbolProfile symbolProfile,
            ThresholdOptimizationResult thresholdResult,
            RiskCalibrationResult riskResult,
            TimeSpan duration)
        {
            result.IsSuccessful = true;
            
            // Calculate overall confidence
            var confidenceFactors = new List<decimal>
            {
                symbolProfile.AnalysisConfidence,
                thresholdResult.OptimizationConfidence,
                riskResult.CalibrationConfidence,
                result.Validation.ValidationPerformed ? Math.Min(1.0m, result.Validation.WinRate * 2) : 0.5m
            };
            
            result.OverallConfidence = confidenceFactors.Average();

            // Add calibration details
            result.Details = new CalibrationDetails
            {
                BarsAnalyzed = symbolProfile.BarsAnalyzed,
                AnalysisDuration = duration,
                DataStartTime = DateTime.UtcNow.AddDays(-symbolProfile.BarsAnalyzed / 24), // Approximate
                DataEndTime = DateTime.UtcNow,
                VolumeAnalysisConfidence = symbolProfile.Volume.VolumeConsistency,
                DeltaAnalysisConfidence = symbolProfile.DeltaFlow.HasStrongInstitutionalActivity ? 0.9m : 0.7m,
                VolatilityAnalysisConfidence = symbolProfile.Volatility.IsHighVolatilitySymbol ? 0.8m : 0.9m,
                MarketProfileConfidence = symbolProfile.MarketProfile.Is24HourMarket ? 0.9m : 0.8m,
                SymbolCategory = DetermineSymbolCategory(symbolProfile),
                SymbolTags = DetermineSymbolTags(symbolProfile)
            };

            // Add recommendations
            AddCalibrationRecommendations(result, symbolProfile);
        }

        private string DetermineSymbolCategory(SymbolProfile symbolProfile)
        {
            if (symbolProfile.Volume.IsHighVolumeSymbol && symbolProfile.Volatility.IsHighVolatilitySymbol)
                return "HighVolumeHighVolatility";
            
            if (symbolProfile.Volume.IsHighVolumeSymbol)
                return "HighVolume";
            
            if (symbolProfile.Volatility.IsHighVolatilitySymbol)
                return "HighVolatility";
            
            if (symbolProfile.DeltaFlow.HasStrongInstitutionalActivity)
                return "InstitutionalActivity";

            return "Standard";
        }

        private List<string> DetermineSymbolTags(SymbolProfile symbolProfile)
        {
            var tags = new List<string>();

            if (symbolProfile.DeltaFlow.HasStrongInstitutionalActivity)
                tags.Add("Institutional");
            
            if (symbolProfile.Volume.VolumeSpikeFrequency > 20)
                tags.Add("VolatileVolume");
            
            if (symbolProfile.Volatility.TrendingFrequency > 0.7m)
                tags.Add("Trending");
            else if (symbolProfile.Volatility.RangingFrequency > 0.7m)
                tags.Add("Ranging");

            if (symbolProfile.MarketProfile.Is24HourMarket)
                tags.Add("24Hour");

            return tags;
        }

        private void AddCalibrationRecommendations(CalibrationResult result, SymbolProfile symbolProfile)
        {
            if (result.OverallConfidence < 0.7m)
            {
                result.Recommendations.Add("Consider manual review due to low calibration confidence");
            }

            if (symbolProfile.Volume.Regime == VolumeRegime.Erratic)
            {
                result.Recommendations.Add("Monitor volume patterns closely - erratic behavior detected");
            }

            if (symbolProfile.Volatility.Regime == VolatilityRegime.Extreme)
            {
                result.Recommendations.Add("Use smaller position sizes due to extreme volatility");
            }

            if (result.Validation.WinRate < 0.5m)
            {
                result.Recommendations.Add("Consider increasing signal threshold for better win rate");
            }

            if (result.Validation.SignalFrequency < 1)
            {
                result.Recommendations.Add("Consider decreasing signal threshold for more trading opportunities");
            }
        }

        #endregion
    }
}
