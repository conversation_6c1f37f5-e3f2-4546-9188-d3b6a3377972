using System;
using System.Collections.Generic;

namespace SmartVolumeStrategy.Models
{
    /// <summary>
    /// Trading signal with confidence and reasoning
    /// </summary>
    public class TradingSignal
    {
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string CorrelationId { get; set; } = Guid.NewGuid().ToString("N")[..8];
        public SignalType Type { get; set; } = SignalType.None;
        public SignalAction Action { get; set; } = SignalAction.None;
        
        // Signal Strength
        public decimal Confidence { get; set; } // 0-1 confidence score
        public decimal Strength { get; set; } // Raw signal strength
        public SignalQuality Quality { get; set; } = SignalQuality.Unknown;
        
        // Market Context
        public decimal CurrentPrice { get; set; }
        public decimal VolumeRatio { get; set; } // Current volume vs average
        public decimal DeltaImbalance { get; set; }
        public decimal CumulativeImpact { get; set; }
        
        // Signal Components
        public VolumeSignalComponent VolumeComponent { get; set; } = new();
        public DeltaSignalComponent DeltaComponent { get; set; } = new();
        public VolatilitySignalComponent VolatilityComponent { get; set; } = new();
        
        // Reasoning
        public string PrimaryReason { get; set; } = "";
        public List<string> SecondaryReasons { get; set; } = new();
        public List<string> SupportingFactors { get; set; } = new();
        public List<string> RiskFactors { get; set; } = new();
        
        // Execution Details
        public decimal RecommendedPositionSize { get; set; }
        public decimal RecommendedTakeProfit { get; set; }
        public decimal RecommendedStopLoss { get; set; }
        public bool IsExecuted { get; set; }
        public DateTime? ExecutionTime { get; set; }
    }

    /// <summary>
    /// Volume component of the trading signal
    /// </summary>
    public class VolumeSignalComponent
    {
        public decimal VolumeRatio { get; set; } // Current vs average volume
        public decimal VolumeThreshold { get; set; } // Threshold used
        public bool IsVolumeSpike { get; set; }
        public decimal VolumeConfidence { get; set; } // 0-1 confidence in volume signal
        public string VolumePattern { get; set; } = ""; // "Spike", "Sustained", "Building", etc.
    }

    /// <summary>
    /// Delta flow component of the trading signal
    /// </summary>
    public class DeltaSignalComponent
    {
        public decimal DeltaImbalance { get; set; }
        public decimal CVDTrend { get; set; }
        public DeltaBias Bias { get; set; } = DeltaBias.Neutral;
        public decimal DeltaConfidence { get; set; } // 0-1 confidence in delta signal
        public bool IsInstitutionalActivity { get; set; }
        public string DeltaPattern { get; set; } = ""; // "Accumulation", "Distribution", "Absorption", etc.
    }

    /// <summary>
    /// Volatility component of the trading signal
    /// </summary>
    public class VolatilitySignalComponent
    {
        public decimal CurrentVolatility { get; set; }
        public decimal AverageVolatility { get; set; }
        public VolatilityRegime Regime { get; set; } = VolatilityRegime.Normal;
        public decimal VolatilityConfidence { get; set; } // 0-1 confidence
        public bool IsVolatilityExpansion { get; set; }
        public string VolatilityPattern { get; set; } = ""; // "Expansion", "Contraction", "Breakout", etc.
    }

    /// <summary>
    /// Performance metrics for tracking strategy effectiveness
    /// </summary>
    public class PerformanceMetrics
    {
        public DateTime StartTime { get; set; }
        public DateTime LastUpdateTime { get; set; } = DateTime.UtcNow;
        public string Symbol { get; set; } = "";
        public string CorrelationId { get; set; } = "";
        
        // Trade Statistics
        public int TotalTrades { get; set; }
        public int WinningTrades { get; set; }
        public int LosingTrades { get; set; }
        public decimal WinRate => TotalTrades > 0 ? (decimal)WinningTrades / TotalTrades * 100 : 0;
        
        // P&L Statistics
        public decimal TotalPnL { get; set; }
        public decimal TotalWins { get; set; }
        public decimal TotalLosses { get; set; }
        public decimal AverageWin => WinningTrades > 0 ? TotalWins / WinningTrades : 0;
        public decimal AverageLoss => LosingTrades > 0 ? Math.Abs(TotalLosses) / LosingTrades : 0;
        public decimal ProfitFactor => Math.Abs(TotalLosses) > 0 ? TotalWins / Math.Abs(TotalLosses) : 0;
        
        // Risk Metrics
        public decimal MaxDrawdown { get; set; }
        public decimal CurrentDrawdown { get; set; }
        public decimal MaxConsecutiveLosses { get; set; }
        public decimal MaxConsecutiveWins { get; set; }
        
        // Signal Quality Metrics
        public int SignalsGenerated { get; set; }
        public int SignalsExecuted { get; set; }
        public decimal SignalExecutionRate => SignalsGenerated > 0 ? (decimal)SignalsExecuted / SignalsGenerated * 100 : 0;
        public decimal AverageSignalConfidence { get; set; }
        public decimal AverageSignalStrength { get; set; }
        
        // Time-based Performance
        public Dictionary<string, decimal> PerformanceByHour { get; set; } = new();
        public Dictionary<string, decimal> PerformanceBySession { get; set; } = new();
        public Dictionary<string, int> TradesByHour { get; set; } = new();
        
        // Recent Performance (Last 10 trades)
        public List<TradeResult> RecentTrades { get; set; } = new();
        public decimal RecentWinRate { get; set; }
        public decimal RecentProfitFactor { get; set; }

        // Performance degradation indicator
        public bool IsPerformanceDegrading { get; set; }

        // Additional performance metrics
        public decimal SharpeRatio { get; set; }
        public string PerformanceStatus { get; set; } = "Normal";
        public decimal TotalReturn { get; set; }
    }

    /// <summary>
    /// Individual trade result
    /// </summary>
    public class TradeResult
    {
        public DateTime EntryTime { get; set; }
        public DateTime? ExitTime { get; set; }
        public decimal EntryPrice { get; set; }
        public decimal ExitPrice { get; set; }
        public decimal Quantity { get; set; }
        public SignalType Direction { get; set; }
        public decimal PnL { get; set; }
        public decimal PnLPercent { get; set; }
        public string ExitReason { get; set; } = ""; // "TakeProfit", "StopLoss", "Signal", "Manual"
        public decimal SignalConfidence { get; set; }
        public decimal SignalStrength { get; set; }
        public TimeSpan HoldTime { get; set; }
        public string CorrelationId { get; set; } = "";
    }

    /// <summary>
    /// Market data point for analysis
    /// </summary>
    public class MarketDataPoint
    {
        public DateTime Timestamp { get; set; }
        public decimal Open { get; set; }
        public decimal High { get; set; }
        public decimal Low { get; set; }
        public decimal Close { get; set; }
        public decimal Volume { get; set; }
        public decimal BuyVolume { get; set; }
        public decimal SellVolume { get; set; }
        public decimal Delta => BuyVolume - SellVolume;
        public decimal DeltaPercent => Volume > 0 ? Delta / Volume * 100 : 0;
        public decimal PriceChange => Close - Open;
        public decimal PriceChangePercent => Open > 0 ? PriceChange / Open * 100 : 0;
        public decimal TrueRange { get; set; }
        public int BarIndex { get; set; }
    }

    /// <summary>
    /// Signal types
    /// </summary>
    public enum SignalType
    {
        None,
        Long,
        Short,
        Exit
    }

    /// <summary>
    /// Position direction for tracking
    /// </summary>
    public enum PositionDirection
    {
        None,
        Long,
        Short
    }

    /// <summary>
    /// Signal actions
    /// </summary>
    public enum SignalAction
    {
        None,
        Entry,
        Exit,
        Hold,
        Reduce,
        Increase
    }

    /// <summary>
    /// Signal quality classification
    /// </summary>
    public enum SignalQuality
    {
        Unknown,
        Poor,
        Fair,
        Good,
        Excellent,
        Exceptional
    }

    /// <summary>
    /// Delta bias classification
    /// </summary>
    public enum DeltaBias
    {
        StrongSell,
        Sell,
        SellPressure,
        Neutral,
        BuyPressure,
        Buy,
        StrongBuy
    }

    /// <summary>
    /// Trading state for position management
    /// </summary>
    public enum TradingState
    {
        Ready,
        InLongPosition,
        InShortPosition,
        Cooldown,
        Error,
        Calibrating,
        Disabled
    }

    /// <summary>
    /// Performance alert types
    /// </summary>
    public enum AlertType
    {
        Info,
        Warning,
        Critical
    }

    /// <summary>
    /// Performance alert for strategy monitoring
    /// </summary>
    public class PerformanceAlert
    {
        public DateTime Timestamp { get; set; }
        public AlertType Type { get; set; }
        public string Message { get; set; } = "";
        public string Details { get; set; } = "";
        public string RecommendedAction { get; set; } = "";
        public bool IsAcknowledged { get; set; }
    }

    /// <summary>
    /// Position information for tracking
    /// </summary>
    public class PositionInfo
    {
        public bool HasPosition { get; set; }
        public SignalType Direction { get; set; }
        public decimal EntryPrice { get; set; }
        public decimal Quantity { get; set; }
        public decimal TakeProfit { get; set; }
        public decimal StopLoss { get; set; }
        public DateTime EntryTime { get; set; }
        public decimal UnrealizedPnL { get; set; }
        public decimal UnrealizedPnLPercent { get; set; }
        public string CorrelationId { get; set; } = "";
        public TradingState State { get; set; }
    }

    /// <summary>
    /// Market context for signal generation
    /// </summary>
    public class MarketContext
    {
        public decimal CurrentPrice { get; set; }
        public decimal PriceChange { get; set; }
        public decimal PriceChangePercent { get; set; }
        public decimal DeltaImbalance { get; set; }
        public decimal CVDTrend { get; set; }
        public VolatilityRegime VolatilityRegime { get; set; }
        public TradingSession CurrentSession { get; set; }
        public bool IsOptimalTradingTime { get; set; }
        public List<string> MarketConditions { get; set; } = new();
    }

    /// <summary>
    /// Signal statistics for tracking
    /// </summary>
    public class SignalStatistics
    {
        public int TotalSignalsGenerated { get; set; }
        public int SignalsExecuted { get; set; }
        public decimal AverageConfidence { get; set; }
        public decimal AverageStrength { get; set; }
        public Dictionary<SignalType, int> SignalsByType { get; set; } = new();
        public Dictionary<SignalQuality, int> SignalsByQuality { get; set; } = new();
        public DateTime LastSignalTime { get; set; }
        public TimeSpan AverageTimeBetweenSignals { get; set; }
    }

    /// <summary>
    /// User constraints for calibration
    /// </summary>
    public class UserConstraints
    {
        public decimal? MaxPositionSizeUSDT { get; set; }
        public decimal? MinPositionSizeUSDT { get; set; }
        public decimal? MaxRiskPercentPerTrade { get; set; }
        public decimal? MaxDrawdown { get; set; }
        public bool ConservativeMode { get; set; }
        public bool PreferHigherWinRate { get; set; }
        public decimal? PreferredRiskRewardRatio { get; set; }
    }

    /// <summary>
    /// Threshold optimization result
    /// </summary>
    public class ThresholdOptimizationResult
    {
        public DateTime OptimizationTimestamp { get; set; }
        public int BarsAnalyzed { get; set; }
        public decimal OptimalVolumeThreshold { get; set; }
        public decimal OptimalSignalThreshold { get; set; }
        public decimal OptimalDeltaImbalanceThreshold { get; set; }
        public decimal OptimalDeltaSignificanceThreshold { get; set; }
        public decimal VolumeThresholdConfidence { get; set; }
        public decimal SignalThresholdConfidence { get; set; }
        public decimal OptimizationConfidence { get; set; }
    }

    /// <summary>
    /// Risk calibration result
    /// </summary>
    public class RiskCalibrationResult
    {
        public DateTime CalibrationTimestamp { get; set; }
        public int BarsAnalyzed { get; set; }
        public decimal OptimalTakeProfitPercent { get; set; }
        public decimal OptimalStopLossPercent { get; set; }
        public decimal OptimalPositionSizeUSDT { get; set; }
        public decimal RiskAdjustmentFactor { get; set; }
        public decimal TPSLConfidence { get; set; }
        public decimal PositionSizingConfidence { get; set; }
        public decimal CalibrationConfidence { get; set; }
    }



    /// <summary>
    /// User risk profile
    /// </summary>
    public class UserRiskProfile
    {
        public RiskTolerance Tolerance { get; set; }
        public decimal PreferredRiskReward { get; set; }
        public decimal MaxDrawdownTolerance { get; set; }
        public bool PreferHigherWinRate { get; set; }
        public decimal AccountBalance { get; set; }
        public decimal RiskPercentPerTrade { get; set; }
    }

    /// <summary>
    /// Risk tolerance enumeration
    /// </summary>
    public enum RiskTolerance
    {
        Conservative,
        Moderate,
        Aggressive
    }

    /// <summary>
    /// Adaptive parameters for adjustment
    /// </summary>
    public class AdaptiveParameters
    {
        public bool EnableAutoAdjustment { get; set; }
        public int MinTradesBeforeAdjustment { get; set; }
        public decimal MinWinRateThreshold { get; set; }
        public decimal MaxDrawdownThreshold { get; set; }
        public decimal AdjustmentSensitivity { get; set; }
        public TimeSpan AdjustmentCooldown { get; set; }
        public bool ConservativeMode { get; set; }
    }

    /// <summary>
    /// Settings adjustment record
    /// </summary>
    public class SettingsAdjustment
    {
        public string Parameter { get; set; } = "";
        public string OldValue { get; set; } = "";
        public string AdjustedValue { get; set; } = "";
        public string Reason { get; set; } = "";
        public decimal Confidence { get; set; }
        public decimal ConfidenceInAdjustment { get; set; }
        public AdjustmentType Type { get; set; }
        public bool IsApplied { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }



    /// <summary>
    /// Volume block result
    /// </summary>
    public class VolumeBlockResult
    {
        public bool IsVolumeBlock { get; set; }
        public decimal VolumeRatio { get; set; }
        public decimal Impact { get; set; }
        public decimal CumulativeImpact { get; set; }
        public VolumeBlockType Type { get; set; } = VolumeBlockType.None;
        public decimal Confidence { get; set; }
        public string Description { get; set; } = "";
    }

    /// <summary>
    /// Volume block type enumeration
    /// </summary>
    public enum VolumeBlockType
    {
        None,
        BuyingBlock,
        SellingBlock,
        AbsorptionBlock,
        DistributionBlock
    }



    /// <summary>
    /// Threshold combination for testing
    /// </summary>
    public class ThresholdCombination
    {
        public decimal VolumeThreshold { get; set; }
        public decimal SignalThreshold { get; set; }
        public decimal DeltaImbalanceThreshold { get; set; }
        public decimal DeltaSignificanceThreshold { get; set; }
    }

    /// <summary>
    /// Risk parameters for testing
    /// </summary>
    public class RiskParameters
    {
        public decimal TakeProfitPercent { get; set; }
        public decimal StopLossPercent { get; set; }
        public decimal PositionSizeUSDT { get; set; }
        public decimal RiskPercentPerTrade { get; set; }
    }

    /// <summary>
    /// Risk validation result
    /// </summary>
    public class RiskValidationResult
    {
        public bool IsValid { get; set; }
        public decimal MaxDrawdown { get; set; }
        public decimal WinRate { get; set; }
        public decimal ProfitFactor { get; set; }
        public decimal SharpeRatio { get; set; }
        public List<string> RiskWarnings { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

}
